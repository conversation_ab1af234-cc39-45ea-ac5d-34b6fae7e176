#!/bin/bash

# Git 忽略配置验证脚本
# 用于检查 .gitignore 是否正确配置

echo "🔍 验证 Git 忽略配置..."
echo "================================"

# 检查是否在 git 仓库中
if ! git rev-parse --git-dir > /dev/null 2>&1; then
    echo "❌ 错误：当前目录不是 Git 仓库"
    exit 1
fi

# 检查 .gitignore 文件是否存在
if [ ! -f ".gitignore" ]; then
    echo "❌ 错误：.gitignore 文件不存在"
    exit 1
fi

echo "✅ .gitignore 文件存在"

# 检查大文件是否被忽略
echo ""
echo "🔍 检查大文件 (>10MB)..."
large_files=$(find . -size +10M -not -path "./.git/*" -not -path "./node_modules/*" -not -path "./target/*" -not -path "./build_env/*" -type f 2>/dev/null)

if [ -n "$large_files" ]; then
    echo "⚠️  发现大文件："
    echo "$large_files"
    
    # 检查这些大文件是否在 git 跟踪中
    tracked_large_files=""
    while IFS= read -r file; do
        if [ -n "$file" ] && git ls-files --error-unmatch "$file" > /dev/null 2>&1; then
            tracked_large_files="$tracked_large_files$file\n"
        fi
    done <<< "$large_files"
    
    if [ -n "$tracked_large_files" ]; then
        echo "❌ 以下大文件被 Git 跟踪，应该被忽略："
        echo -e "$tracked_large_files"
    else
        echo "✅ 大文件已被正确忽略"
    fi
else
    echo "✅ 没有发现大文件"
fi

# 检查模型文件
echo ""
echo "🔍 检查模型文件..."
model_extensions=("*.pt" "*.pth" "*.onnx" "*.bin" "*.safetensors" "*.h5" "*.ckpt" "*.pkl")
tracked_models=""

for ext in "${model_extensions[@]}"; do
    files=$(find . -name "$ext" -not -path "./.git/*" 2>/dev/null)
    if [ -n "$files" ]; then
        while IFS= read -r file; do
            if [ -n "$file" ] && git ls-files --error-unmatch "$file" > /dev/null 2>&1; then
                tracked_models="$tracked_models$file\n"
            fi
        done <<< "$files"
    fi
done

if [ -n "$tracked_models" ]; then
    echo "❌ 以下模型文件被 Git 跟踪，应该被忽略："
    echo -e "$tracked_models"
else
    echo "✅ 模型文件已被正确忽略"
fi

# 检查虚拟环境
echo ""
echo "🔍 检查虚拟环境..."
venv_dirs=("venv" ".venv" "env" "ENV" "build_env")
tracked_venvs=""

for dir in "${venv_dirs[@]}"; do
    if [ -d "$dir" ]; then
        if git ls-files --error-unmatch "$dir" > /dev/null 2>&1; then
            tracked_venvs="$tracked_venvs$dir/\n"
        fi
    fi
done

if [ -n "$tracked_venvs" ]; then
    echo "❌ 以下虚拟环境目录被 Git 跟踪，应该被忽略："
    echo -e "$tracked_venvs"
else
    echo "✅ 虚拟环境已被正确忽略"
fi

# 检查构建输出
echo ""
echo "🔍 检查构建输出..."
build_dirs=("dist" "build" "target" "node_modules" "__pycache__" ".next" ".nuxt" "bundle")
tracked_builds=""

for dir in "${build_dirs[@]}"; do
    files=$(find . -name "$dir" -type d -not -path "./.git/*" 2>/dev/null)
    if [ -n "$files" ]; then
        while IFS= read -r file; do
            if [ -n "$file" ] && git ls-files --error-unmatch "$file" > /dev/null 2>&1; then
                tracked_builds="$tracked_builds$file/\n"
            fi
        done <<< "$files"
    fi
done

if [ -n "$tracked_builds" ]; then
    echo "❌ 以下构建目录被 Git 跟踪，应该被忽略："
    echo -e "$tracked_builds"
else
    echo "✅ 构建输出已被正确忽略"
fi

# 检查日志和临时文件
echo ""
echo "🔍 检查日志和临时文件..."
temp_patterns=("*.log" "logs/" "temp/" "tmp/" "outputs/" "uploads/")
tracked_temps=""

for pattern in "${temp_patterns[@]}"; do
    files=$(find . -name "$pattern" -not -path "./.git/*" 2>/dev/null)
    if [ -n "$files" ]; then
        while IFS= read -r file; do
            if [ -n "$file" ] && git ls-files --error-unmatch "$file" > /dev/null 2>&1; then
                tracked_temps="$tracked_temps$file\n"
            fi
        done <<< "$files"
    fi
done

if [ -n "$tracked_temps" ]; then
    echo "❌ 以下临时文件被 Git 跟踪，应该被忽略："
    echo -e "$tracked_temps"
else
    echo "✅ 日志和临时文件已被正确忽略"
fi

# 总结
echo ""
echo "================================"
echo "🎯 验证完成！"

# 显示当前 git 状态摘要
untracked_count=$(git status --porcelain | grep "^??" | wc -l)
modified_count=$(git status --porcelain | grep "^.M" | wc -l)
staged_count=$(git status --porcelain | grep "^M" | wc -l)

echo "📊 Git 状态摘要："
echo "   - 未跟踪文件: $untracked_count"
echo "   - 已修改文件: $modified_count" 
echo "   - 已暂存文件: $staged_count"

if [ $untracked_count -gt 50 ]; then
    echo "⚠️  未跟踪文件较多，请检查是否有文件应该被忽略"
fi

echo ""
echo "💡 建议："
echo "   - 定期运行此脚本检查配置"
echo "   - 添加新文件类型时更新 .gitignore"
echo "   - 使用 'git add .' 前先检查文件列表"
