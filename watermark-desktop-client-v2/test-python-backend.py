#!/usr/bin/env python3
"""
测试 Python 后端直接启动方案
验证新的启动逻辑是否正常工作
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def detect_python():
    """检测 Python 环境"""
    python_commands = ["python", "python3", "py"]
    
    for cmd in python_commands:
        try:
            result = subprocess.run([cmd, "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                version = result.stdout.strip()
                print(f"✅ 找到 Python: {cmd} - {version}")
                return cmd
        except FileNotFoundError:
            continue
    
    print("❌ 未找到 Python 环境")
    return None

def check_dependencies(python_cmd, backend_dir):
    """检查 Python 依赖"""
    print("🔍 检查 Python 依赖...")
    
    requirements_file = backend_dir / "requirements.txt"
    if not requirements_file.exists():
        print("❌ requirements.txt 文件不存在")
        return False
    
    # 检查关键依赖
    check_cmd = [python_cmd, "-c", "import fastapi, uvicorn, torch; print('Dependencies OK')"]
    
    try:
        result = subprocess.run(check_cmd, capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Python 依赖已安装")
            return True
        else:
            print("⚠️ 部分依赖缺失")
            return False
    except Exception as e:
        print(f"❌ 依赖检查失败: {e}")
        return False

def install_dependencies(python_cmd, backend_dir):
    """安装 Python 依赖"""
    print("📦 正在安装 Python 依赖...")
    
    try:
        result = subprocess.run(
            [python_cmd, "-m", "pip", "install", "-r", "requirements.txt"],
            cwd=backend_dir,
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            print("✅ Python 依赖安装成功")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 无法执行依赖安装: {e}")
        return False

def start_backend(python_cmd, backend_dir):
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    main_py = backend_dir / "main.py"
    if not main_py.exists():
        print(f"❌ 后端主文件不存在: {main_py}")
        return None
    
    try:
        process = subprocess.Popen(
            [python_cmd, "main.py"],
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE
        )
        
        print(f"✅ 后端服务已启动，PID: {process.pid}")
        return process
    except Exception as e:
        print(f"❌ 启动后端服务失败: {e}")
        return None

def test_backend_health():
    """测试后端健康状态"""
    print("🔍 测试后端健康状态...")
    
    health_url = "http://localhost:8000/api/v1/health"
    
    for attempt in range(10):
        try:
            response = requests.get(health_url, timeout=5)
            if response.status_code == 200:
                print("✅ 后端服务健康检查通过")
                return True
        except requests.exceptions.RequestException:
            pass
        
        print(f"⏳ 等待后端启动... ({attempt + 1}/10)")
        time.sleep(2)
    
    print("❌ 后端服务健康检查失败")
    return False

def main():
    """主函数"""
    print("🧪 测试 Python 后端直接启动方案")
    print("=" * 50)
    
    # 获取项目路径
    project_root = Path(__file__).parent
    backend_dir = project_root / "python-backend"
    
    print(f"📁 项目根目录: {project_root}")
    print(f"📁 后端目录: {backend_dir}")
    
    if not backend_dir.exists():
        print("❌ 后端目录不存在")
        return False
    
    # 检测 Python 环境
    python_cmd = detect_python()
    if not python_cmd:
        print("\n💡 请安装 Python 3.8+ 并添加到 PATH")
        return False
    
    # 检查依赖
    if not check_dependencies(python_cmd, backend_dir):
        print("\n📦 尝试安装依赖...")
        if not install_dependencies(python_cmd, backend_dir):
            print("❌ 依赖安装失败")
            return False
    
    # 启动后端
    backend_process = start_backend(python_cmd, backend_dir)
    if not backend_process:
        return False
    
    try:
        # 测试后端健康状态
        if test_backend_health():
            print("\n🎉 测试成功！")
            print("✅ Python 后端可以正常启动和运行")
            print("✅ 新的启动方案验证通过")
            
            print("\n📋 测试结果:")
            print(f"  - Python 命令: {python_cmd}")
            print(f"  - 后端目录: {backend_dir}")
            print(f"  - 进程 ID: {backend_process.pid}")
            print(f"  - 健康检查: 通过")
            
            return True
        else:
            print("\n❌ 后端服务无法正常响应")
            return False
    
    finally:
        # 清理：终止后端进程
        if backend_process:
            print("\n🛑 正在停止后端服务...")
            backend_process.terminate()
            try:
                backend_process.wait(timeout=5)
                print("✅ 后端服务已停止")
            except subprocess.TimeoutExpired:
                backend_process.kill()
                print("🔪 强制终止后端服务")

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎯 结论：新方案可行！")
        print("\n💡 优势:")
        print("  - 无需 PyInstaller 打包")
        print("  - 构建速度快")
        print("  - 安装包小")
        print("  - 调试方便")
        print("  - 更新容易")
        
        print("\n🚀 下一步:")
        print("  1. 运行 'pnpm tauri build' 构建桌面应用")
        print("  2. 测试完整的应用功能")
    else:
        print("\n❌ 测试失败，请检查环境配置")
    
    sys.exit(0 if success else 1)
