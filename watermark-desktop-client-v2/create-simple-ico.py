#!/usr/bin/env python3
"""
创建一个简单的 ICO 文件
"""

import struct
import os

def create_simple_ico():
    """创建一个最简单的 32x32 ICO 文件"""
    
    # ICO 文件头 (6 bytes)
    ico_header = struct.pack('<HHH', 0, 1, 1)  # Reserved, Type (1=ICO), Count
    
    # ICO 目录条目 (16 bytes)
    width = 32
    height = 32
    colors = 0  # 0 = 256+ colors
    reserved = 0
    planes = 1
    bits_per_pixel = 32
    image_size = width * height * 4  # 32 bits per pixel
    image_offset = 22  # Header (6) + Directory (16)
    
    ico_directory = struct.pack('<BBBBHHLL', 
                               width, height, colors, reserved,
                               planes, bits_per_pixel, image_size, image_offset)
    
    # 创建一个简单的 32x32 蓝色图像数据
    # BMP 信息头 (40 bytes)
    bmp_header = struct.pack('<LLLHHLLLLLL',
                            40,  # Header size
                            width, height * 2,  # Width, Height (doubled for ICO)
                            1, 32,  # Planes, Bits per pixel
                            0, image_size,  # Compression, Image size
                            0, 0, 0, 0)  # X/Y pixels per meter, Colors used, Important colors
    
    # 图像数据 (32x32 pixels, 32 bits each = BGRA)
    image_data = bytearray()
    for y in range(height):
        for x in range(width):
            # 创建一个简单的渐变效果
            blue = min(255, x * 8)
            green = min(255, y * 8)
            red = 100
            alpha = 255
            image_data.extend([blue, green, red, alpha])  # BGRA format
    
    # AND 掩码 (1 bit per pixel, padded to 32-bit boundary)
    and_mask = bytearray(width * height // 8)  # All transparent
    
    # 组合所有数据
    ico_data = ico_header + ico_directory + bmp_header + image_data + and_mask
    
    return ico_data

def main():
    """主函数"""
    print("🎨 创建简单的 ICO 文件...")
    
    ico_data = create_simple_ico()
    
    # 保存到文件
    ico_path = "src-tauri/icons/icon.ico"
    with open(ico_path, 'wb') as f:
        f.write(ico_data)
    
    print(f"✅ ICO 文件已创建: {ico_path}")
    print(f"📊 文件大小: {len(ico_data)} bytes")

if __name__ == "__main__":
    main()
