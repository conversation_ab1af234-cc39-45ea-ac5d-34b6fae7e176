/**
 * 文件状态更新测试
 * 验证文件处理完成后状态和路径是否正确更新
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { useFileStore } from '../src/stores/fileStore'
import { FileStatus } from '../src/types'

describe('文件状态更新测试', () => {
  beforeEach(() => {
    // 清空 store
    useFileStore.getState().clearFiles()
  })

  it('应该正确更新文件状态为完成', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 50,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 验证初始状态
    let file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Processing)
    expect(file?.processedPath).toBeUndefined()

    // 更新状态为完成
    store.updateFileStatus('test-file-1', FileStatus.Completed)

    // 验证状态已更新
    file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
  })

  it('应该正确设置处理后的文件路径', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 100,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 设置处理后的路径
    const processedPath = 'outputs/processed_test-image.jpg'
    store.updateFileProcessedPath('test-file-1', processedPath)

    // 验证路径已设置
    const file = store.getFileById('test-file-1')
    expect(file?.processedPath).toBe(processedPath)
    
    // 验证状态没有被强制改变（这是我们修复的重点）
    expect(file?.status).toBe(FileStatus.Processing)
  })

  it('应该正确设置处理结果信息', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 100,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 设置处理结果
    const endTime = new Date()
    store.updateFileProcessingResult('test-file-1', {
      processingEndTime: endTime,
      detectionCount: 3,
      avgConfidence: 0.85
    })

    // 验证结果已设置
    const file = store.getFileById('test-file-1')
    expect(file?.processingEndTime).toBe(endTime)
    expect(file?.detectionCount).toBe(3)
    expect(file?.avgConfidence).toBe(0.85)
  })

  it('应该正确处理完整的文件完成流程', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 50,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 模拟 WebSocket 完成消息处理
    // 1. 更新状态为完成
    store.updateFileStatus('test-file-1', FileStatus.Completed)
    
    // 2. 设置结束时间和处理结果
    const endTime = new Date()
    store.updateFileProcessingResult('test-file-1', {
      processingEndTime: endTime,
      detectionCount: 2,
      avgConfidence: 0.92
    })

    // 3. 模拟轮询设置处理路径
    const processedPath = 'outputs/processed_test-image.jpg'
    store.updateFileProcessedPath('test-file-1', processedPath)

    // 验证最终状态
    const file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBe(processedPath)
    expect(file?.processingEndTime).toBe(endTime)
    expect(file?.detectionCount).toBe(2)
    expect(file?.avgConfidence).toBe(0.92)
  })

  it('应该正确处理多个文件的状态更新', () => {
    const store = useFileStore.getState()
    
    // 添加多个测试文件
    const testFiles = [
      {
        id: 'test-file-1',
        name: 'test-image-1.jpg',
        path: '/test/path/test-image-1.jpg',
        size: 1024000,
        type: 'image/jpeg',
        status: FileStatus.Processing,
        progress: 50,
        detectionCount: 0,
        avgConfidence: 0,
        processingStartTime: new Date(),
        dimensions: { width: 1920, height: 1080 }
      },
      {
        id: 'test-file-2',
        name: 'test-image-2.jpg',
        path: '/test/path/test-image-2.jpg',
        size: 2048000,
        type: 'image/jpeg',
        status: FileStatus.Ready,
        progress: 0,
        detectionCount: 0,
        avgConfidence: 0,
        dimensions: { width: 1280, height: 720 }
      }
    ]

    store.addFileItems(testFiles)

    // 完成第一个文件
    store.updateFileStatus('test-file-1', FileStatus.Completed)
    store.updateFileProcessedPath('test-file-1', 'outputs/processed_test-image-1.jpg')

    // 开始处理第二个文件
    store.updateFileStatus('test-file-2', FileStatus.Processing)
    store.updateFileProgress('test-file-2', 25)

    // 验证状态
    const file1 = store.getFileById('test-file-1')
    const file2 = store.getFileById('test-file-2')

    expect(file1?.status).toBe(FileStatus.Completed)
    expect(file1?.processedPath).toBe('outputs/processed_test-image-1.jpg')

    expect(file2?.status).toBe(FileStatus.Processing)
    expect(file2?.progress).toBe(25)
    expect(file2?.processedPath).toBeUndefined()
  })

  it('应该正确处理文件状态回退情况', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Completed,
      progress: 100,
      processedPath: 'outputs/processed_test-image.jpg',
      detectionCount: 2,
      avgConfidence: 0.85,
      processingStartTime: new Date(),
      processingEndTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 验证初始完成状态
    let file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBe('outputs/processed_test-image.jpg')

    // 重新处理文件（状态回退）
    store.updateFileStatus('test-file-1', FileStatus.Processing)
    store.updateFileProgress('test-file-1', 30)

    // 验证状态已回退，但 processedPath 保持不变
    file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Processing)
    expect(file?.progress).toBe(30)
    expect(file?.processedPath).toBe('outputs/processed_test-image.jpg') // 保持原有路径
  })
})
