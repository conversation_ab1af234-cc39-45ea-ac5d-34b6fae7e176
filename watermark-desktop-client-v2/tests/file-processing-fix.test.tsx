import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFileStore } from '../src/stores/fileStore'

// Mock fetch for API calls
global.fetch = vi.fn()

// Mock URL.createObjectURL
const mockCreateObjectURL = vi.fn()
const mockRevokeObjectURL = vi.fn()

Object.defineProperty(global.URL, 'createObjectURL', {
  value: mockCreateObjectURL,
  writable: true
})

Object.defineProperty(global.URL, 'revokeObjectURL', {
  value: mockRevokeObjectURL,
  writable: true
})

describe('File Processing Fix', () => {
  beforeEach(() => {
    // 清理状态
    localStorage.clear()
    useFileStore.setState({ 
      files: [], 
      selectedFile: null, 
      fileObjects: new Map() 
    })
    
    // 重置 mocks
    vi.clearAllMocks()
    mockCreateObjectURL.mockReturnValue('blob:http://localhost:5173/mock-url')
    
    // Mock fetch 成功响应
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      json: async () => ({
        message: 'Files uploaded successfully',
        files: [{
          name: 'test.jpg',
          path: '/uploads/test.jpg',
          size: 1024,
          type: 'image/jpeg'
        }]
      })
    })
  })

  describe('API Path Fix', () => {
    it('should use correct API path without duplication', () => {
      // 测试 API 路径配置
      const baseURL = 'http://127.0.0.1:8000/api/v1'
      const endpoint = '/files/upload'
      const fullURL = baseURL + endpoint
      
      expect(fullURL).toBe('http://127.0.0.1:8000/api/v1/files/upload')
      expect(fullURL).not.toContain('/api/v1/api/v1/')
    })
  })

  describe('File Path Detection', () => {
    it('should correctly identify valid local paths', () => {
      const testCases = [
        // Valid paths (Tauri environment)
        { path: '/absolute/path/to/file.jpg', expected: true },
        { path: 'C:\\Windows\\path\\file.jpg', expected: true },
        { path: './relative/path/file.jpg', expected: true },
        
        // Invalid paths (browser environment)
        { path: 'file.jpg', expected: false },
        { path: 'blob:http://localhost:5173/uuid', expected: false },
        { path: '', expected: false },
        { path: undefined, expected: false }
      ]

      testCases.forEach(({ path, expected }) => {
        const hasValidPath = path && 
                            path !== 'file.jpg' && 
                            (path.includes('/') || path.includes('\\')) &&
                            !path.startsWith('blob:')
        
        expect(Boolean(hasValidPath)).toBe(expected)
      })
    })
  })

  describe('File Processing Logic', () => {
    it('should handle browser environment file upload', async () => {
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      // 模拟文件处理逻辑
      const processFiles = async (files: any[], getFileObject: Function) => {
        const filePaths = []
        const invalidFiles = []

        for (const fileItem of files) {
          const hasValidPath = fileItem.path && 
                              fileItem.path !== fileItem.name && 
                              (fileItem.path.includes('/') || fileItem.path.includes('\\')) &&
                              !fileItem.path.startsWith('blob:')

          if (hasValidPath) {
            filePaths.push(fileItem.path)
          } else {
            const fileObject = getFileObject(fileItem.id)
            if (fileObject) {
              // 模拟上传成功
              filePaths.push('/uploads/test.jpg')
            } else {
              invalidFiles.push(fileItem.name)
            }
          }
        }

        return { filePaths, invalidFiles }
      }

      const store = useFileStore.getState()
      store.addFiles([mockFile])
      
      const files = store.files
      const result = await processFiles(files, store.getFileObject)
      
      expect(result.filePaths).toHaveLength(1)
      expect(result.filePaths[0]).toBe('/uploads/test.jpg')
      expect(result.invalidFiles).toHaveLength(0)
    })

    it('should handle Tauri environment with local paths', async () => {
      // 模拟 Tauri 环境的文件
      const mockFileItem = {
        id: 'test-id',
        name: 'test.jpg',
        path: '/absolute/path/to/test.jpg',
        size: 1024,
        type: 'image/jpeg'
      }

      const processFiles = async (files: any[], getFileObject: Function) => {
        const filePaths = []
        
        for (const fileItem of files) {
          const hasValidPath = fileItem.path && 
                              fileItem.path !== fileItem.name && 
                              (fileItem.path.includes('/') || fileItem.path.includes('\\')) &&
                              !fileItem.path.startsWith('blob:')

          if (hasValidPath) {
            filePaths.push(fileItem.path)
          }
        }

        return { filePaths }
      }

      const mockGetFileObject = vi.fn()
      const result = await processFiles([mockFileItem], mockGetFileObject)
      
      expect(result.filePaths).toHaveLength(1)
      expect(result.filePaths[0]).toBe('/absolute/path/to/test.jpg')
      expect(mockGetFileObject).not.toHaveBeenCalled() // 不需要上传
    })
  })

  describe('Preview and Processing Separation', () => {
    it('should allow preview without affecting file processing', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      // 模拟预览 URL 生成
      const getImageUrl = (file: any, getFileObject: Function) => {
        const fileObject = getFileObject(file.id)
        
        if (fileObject) {
          return URL.createObjectURL(fileObject)
        }
        
        return file.path
      }

      const store = useFileStore.getState()
      store.addFiles([mockFile])
      
      const files = store.files
      const previewUrl = getImageUrl(files[0], store.getFileObject)
      
      // 预览功能正常工作
      expect(mockCreateObjectURL).toHaveBeenCalledWith(mockFile)
      expect(previewUrl).toBe('blob:http://localhost:5173/mock-url')
      
      // 原始文件对象仍然可用于处理
      const originalFile = store.getFileObject(files[0].id)
      expect(originalFile).toBe(mockFile)
      expect(originalFile?.name).toBe('test.jpg')
    })

    it('should handle blob URLs correctly in path detection', () => {
      const blobUrl = 'blob:http://localhost:5173/uuid-string'
      
      const hasValidPath = blobUrl && 
                          blobUrl !== 'test.jpg' && 
                          (blobUrl.includes('/') || blobUrl.includes('\\')) &&
                          !blobUrl.startsWith('blob:')
      
      // blob URL 应该被识别为无效路径，需要上传
      expect(hasValidPath).toBe(false)
    })
  })

  describe('Error Handling', () => {
    it('should handle upload failures gracefully', async () => {
      // Mock fetch 失败
      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))
      
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      const processFiles = async (files: any[], getFileObject: Function) => {
        const filePaths = []
        const invalidFiles = []

        for (const fileItem of files) {
          const hasValidPath = false // 模拟浏览器环境
          
          if (!hasValidPath) {
            const fileObject = getFileObject(fileItem.id)
            if (fileObject) {
              try {
                // 模拟上传失败
                throw new Error('Upload failed')
              } catch (error) {
                invalidFiles.push(fileItem.name)
              }
            }
          }
        }

        return { filePaths, invalidFiles }
      }

      const store = useFileStore.getState()
      store.addFiles([mockFile])
      
      const files = store.files
      const result = await processFiles(files, store.getFileObject)
      
      expect(result.filePaths).toHaveLength(0)
      expect(result.invalidFiles).toHaveLength(1)
      expect(result.invalidFiles[0]).toBe('test.jpg')
    })
  })
})
