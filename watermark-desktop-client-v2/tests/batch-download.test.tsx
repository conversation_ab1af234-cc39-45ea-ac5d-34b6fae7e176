/**
 * 批量下载功能测试
 * 验证批量选择、路径管理、下载功能等
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import { useBatchSelectionStore } from '../src/stores/batchSelectionStore'
import { FileStatus } from '../src/types'

// Mock Tauri APIs
vi.mock('@tauri-apps/plugin-dialog', () => ({
  open: vi.fn()
}))

vi.mock('@tauri-apps/api/path', () => ({
  join: vi.fn(),
  basename: vi.fn(),
  downloadDir: vi.fn(),
  documentDir: vi.fn(),
  homeDir: vi.fn()
}))

vi.mock('@tauri-apps/plugin-fs', () => ({
  exists: vi.fn(),
  createDir: vi.fn(),
  copyFile: vi.fn()
}))

vi.mock('@tauri-apps/plugin-opener', () => ({
  open: vi.fn()
}))

vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn()
}))

describe('批量下载功能测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    useBatchSelectionStore.getState().clearSelection()
  })

  describe('批量选择状态管理', () => {
    it('应该正确管理文件选择状态', () => {
      const store = useBatchSelectionStore.getState()
      
      // 初始状态
      expect(store.selectedFileIds.size).toBe(0)
      expect(store.isSelectionMode).toBe(false)

      // 选择文件
      store.toggleFileSelection('file-1')
      expect(store.selectedFileIds.has('file-1')).toBe(true)
      expect(store.isSelectionMode).toBe(true)

      // 取消选择
      store.toggleFileSelection('file-1')
      expect(store.selectedFileIds.has('file-1')).toBe(false)
      expect(store.isSelectionMode).toBe(false)
    })

    it('应该支持批量选择多个文件', () => {
      const store = useBatchSelectionStore.getState()
      
      const fileIds = ['file-1', 'file-2', 'file-3']
      store.selectFiles(fileIds)

      expect(store.selectedFileIds.size).toBe(3)
      fileIds.forEach(id => {
        expect(store.selectedFileIds.has(id)).toBe(true)
      })
      expect(store.isSelectionMode).toBe(true)
    })

    it('应该正确处理全选已完成文件', () => {
      const store = useBatchSelectionStore.getState()
      
      const files = [
        {
          id: 'file-1',
          name: 'test1.jpg',
          path: '/test1.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test1.jpg',
          progress: 100,
          detectionCount: 1,
          avgConfidence: 0.8,
          dimensions: { width: 100, height: 100 }
        },
        {
          id: 'file-2',
          name: 'test2.jpg',
          path: '/test2.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Processing,
          progress: 50,
          detectionCount: 0,
          avgConfidence: 0,
          dimensions: { width: 100, height: 100 }
        },
        {
          id: 'file-3',
          name: 'test3.jpg',
          path: '/test3.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test3.jpg',
          progress: 100,
          detectionCount: 2,
          avgConfidence: 0.9,
          dimensions: { width: 100, height: 100 }
        }
      ]

      // 全选已完成文件
      store.selectAllCompleted(files)

      // 应该只选择已完成且有 processedPath 的文件
      expect(store.selectedFileIds.size).toBe(2)
      expect(store.selectedFileIds.has('file-1')).toBe(true)
      expect(store.selectedFileIds.has('file-2')).toBe(false) // 处理中，不应被选择
      expect(store.selectedFileIds.has('file-3')).toBe(true)
    })

    it('应该正确判断是否可以下载', () => {
      const store = useBatchSelectionStore.getState()
      
      const files = [
        {
          id: 'file-1',
          name: 'test1.jpg',
          path: '/test1.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test1.jpg',
          progress: 100,
          detectionCount: 1,
          avgConfidence: 0.8,
          dimensions: { width: 100, height: 100 }
        },
        {
          id: 'file-2',
          name: 'test2.jpg',
          path: '/test2.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Processing,
          progress: 50,
          detectionCount: 0,
          avgConfidence: 0,
          dimensions: { width: 100, height: 100 }
        }
      ]

      // 没有选择文件时不能下载
      expect(store.canDownload(files)).toBe(false)

      // 选择已完成文件时可以下载
      store.toggleFileSelection('file-1')
      expect(store.canDownload(files)).toBe(true)

      // 选择处理中文件时不能下载
      store.clearSelection()
      store.toggleFileSelection('file-2')
      expect(store.canDownload(files)).toBe(false)
    })
  })

  describe('路径管理', () => {
    it('应该正确管理下载路径', () => {
      const store = useBatchSelectionStore.getState()
      
      const testPath = '/Users/<USER>/Downloads'
      
      // 设置默认路径
      store.setDefaultDownloadPath(testPath)
      expect(store.defaultDownloadPath).toBe(testPath)
      expect(store.currentDownloadPath).toBe(testPath)

      // 设置当前路径
      const newPath = '/Users/<USER>/Desktop'
      store.setCurrentDownloadPath(newPath)
      expect(store.currentDownloadPath).toBe(newPath)
      expect(store.defaultDownloadPath).toBe(testPath) // 默认路径不变

      // 重置当前路径
      store.resetCurrentPath()
      expect(store.currentDownloadPath).toBe(testPath)
    })
  })

  describe('下载进度管理', () => {
    it('应该正确管理下载进度', async () => {
      const store = useBatchSelectionStore.getState()
      
      const files = [
        {
          id: 'file-1',
          name: 'test1.jpg',
          path: '/test1.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test1.jpg',
          progress: 100,
          detectionCount: 1,
          avgConfidence: 0.8,
          dimensions: { width: 100, height: 100 }
        }
      ]

      // 开始下载
      await store.startBatchDownload(files)
      expect(store.isDownloading).toBe(true)
      expect(store.downloadProgress).toHaveLength(1)
      expect(store.downloadProgress[0].fileId).toBe('file-1')
      expect(store.downloadProgress[0].status).toBe('pending')

      // 更新进度
      store.updateFileProgress('file-1', 50, 'downloading')
      expect(store.downloadProgress[0].progress).toBe(50)
      expect(store.downloadProgress[0].status).toBe('downloading')
      expect(store.totalProgress).toBe(50)

      // 完成下载
      store.updateFileProgress('file-1', 100, 'completed')
      expect(store.downloadProgress[0].progress).toBe(100)
      expect(store.downloadProgress[0].status).toBe('completed')
      expect(store.totalProgress).toBe(100)

      // 结束下载
      store.completeBatchDownload()
      expect(store.isDownloading).toBe(false)
      expect(store.downloadProgress).toHaveLength(0)
      expect(store.totalProgress).toBe(0)
    })

    it('应该正确处理下载错误', () => {
      const store = useBatchSelectionStore.getState()
      
      const files = [
        {
          id: 'file-1',
          name: 'test1.jpg',
          path: '/test1.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test1.jpg',
          progress: 100,
          detectionCount: 1,
          avgConfidence: 0.8,
          dimensions: { width: 100, height: 100 }
        }
      ]

      // 开始下载
      store.startBatchDownload(files)
      
      // 设置错误
      const errorMessage = '文件不存在'
      store.setFileError('file-1', errorMessage)
      
      expect(store.downloadProgress[0].status).toBe('failed')
      expect(store.downloadProgress[0].error).toBe(errorMessage)
    })
  })

  describe('Getters 功能', () => {
    it('应该正确获取选中的文件', () => {
      const store = useBatchSelectionStore.getState()
      
      const files = [
        {
          id: 'file-1',
          name: 'test1.jpg',
          path: '/test1.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test1.jpg',
          progress: 100,
          detectionCount: 1,
          avgConfidence: 0.8,
          dimensions: { width: 100, height: 100 }
        },
        {
          id: 'file-2',
          name: 'test2.jpg',
          path: '/test2.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test2.jpg',
          progress: 100,
          detectionCount: 2,
          avgConfidence: 0.9,
          dimensions: { width: 100, height: 100 }
        }
      ]

      // 选择第一个文件
      store.toggleFileSelection('file-1')
      
      const selectedFiles = store.getSelectedFiles(files)
      expect(selectedFiles).toHaveLength(1)
      expect(selectedFiles[0].id).toBe('file-1')
    })

    it('应该正确获取已完成的文件', () => {
      const store = useBatchSelectionStore.getState()
      
      const files = [
        {
          id: 'file-1',
          name: 'test1.jpg',
          path: '/test1.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Completed,
          processedPath: 'output/test1.jpg',
          progress: 100,
          detectionCount: 1,
          avgConfidence: 0.8,
          dimensions: { width: 100, height: 100 }
        },
        {
          id: 'file-2',
          name: 'test2.jpg',
          path: '/test2.jpg',
          size: 1000,
          type: 'image/jpeg',
          status: FileStatus.Processing,
          progress: 50,
          detectionCount: 0,
          avgConfidence: 0,
          dimensions: { width: 100, height: 100 }
        }
      ]

      const completedFiles = store.getCompletedFiles(files)
      expect(completedFiles).toHaveLength(1)
      expect(completedFiles[0].id).toBe('file-1')
    })
  })
})
