/**
 * 立即结果检查测试
 * 验证 WebSocket 完成消息后立即触发的结果检查功能
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useFileStore } from '../src/stores/fileStore'
import { FileStatus } from '../src/types'

// Mock API client
const mockApiClient = {
  processing: {
    getStatus: vi.fn()
  }
}

vi.mock('../src/lib/api', () => ({
  apiClient: mockApiClient
}))

describe('立即结果检查测试', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    useFileStore.getState().clearFiles()
  })

  it('应该在 WebSocket 完成后立即获取 processedPath', async () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 100,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // Mock API 响应
    mockApiClient.processing.getStatus.mockResolvedValue({
      status: 'completed',
      result: {
        processed_files: [{
          input_path: '/test/path/test-image.jpg',
          output_path: 'outputs/processed_test-image.jpg',
          success: true,
          detection_count: 2,
          avg_confidence: 0.85
        }]
      }
    })

    // 模拟 WebSocket 完成消息
    store.updateFileStatus('test-file-1', FileStatus.Completed)
    store.updateFileProcessingResult('test-file-1', {
      processingEndTime: new Date()
    })

    // 验证初始状态：有状态但没有 processedPath
    let file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBeUndefined()

    // 模拟立即检查函数调用
    // 注意：在实际代码中，这会在 WebSocket 消息处理中自动调用
    const checkSingleFileResult = async (taskId: string, fileName: string, filePath: string) => {
      try {
        const status = await mockApiClient.processing.getStatus(taskId)
        
        if (status.status === 'completed' && status.result?.processed_files) {
          const processedFile = status.result.processed_files.find((pf: any) => 
            pf.input_path === filePath
          )
          
          if (processedFile && processedFile.success && processedFile.output_path) {
            // 获取最新的 store 状态
            const currentStore = useFileStore.getState()
            const fileItem = currentStore.files.find(f => f.name === fileName)
            if (fileItem && !fileItem.processedPath) {
              currentStore.updateFileProcessedPath(fileItem.id, processedFile.output_path)

              if (processedFile.detection_count !== undefined || processedFile.avg_confidence !== undefined) {
                currentStore.updateFileProcessingResult(fileItem.id, {
                  detectionCount: processedFile.detection_count,
                  avgConfidence: processedFile.avg_confidence
                })
              }
            }
          }
        }
      } catch (error) {
        console.warn('检查失败:', error)
      }
    }

    // 执行立即检查
    await checkSingleFileResult('test-task-id', 'test-image.jpg', '/test/path/test-image.jpg')

    // 验证结果：现在应该有 processedPath 了
    file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBe('outputs/processed_test-image.jpg')
    expect(file?.detectionCount).toBe(2)
    expect(file?.avgConfidence).toBe(0.85)

    // 验证 API 被调用
    expect(mockApiClient.processing.getStatus).toHaveBeenCalledWith('test-task-id')

    console.log('✅ 立即检查测试通过:')
    console.log('  - WebSocket 完成后状态:', FileStatus.Completed)
    console.log('  - 立即检查后 processedPath:', file?.processedPath)
    console.log('  - PreviewPanel 现在可以显示:', file?.processedPath ? '处理后图片' : '原图')
  })

  it('应该处理 API 调用失败的情况', async () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Completed,
      progress: 100,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      processingEndTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // Mock API 失败
    mockApiClient.processing.getStatus.mockRejectedValue(new Error('Network error'))

    // 模拟立即检查函数调用
    const checkSingleFileResult = async (taskId: string, fileName: string, filePath: string) => {
      try {
        const status = await mockApiClient.processing.getStatus(taskId)
        // ... 处理逻辑
      } catch (error) {
        console.warn('检查失败:', error)
        // 不抛出错误，让正常的轮询机制继续工作
      }
    }

    // 执行立即检查（应该不会抛出错误）
    await expect(checkSingleFileResult('test-task-id', 'test-image.jpg', '/test/path/test-image.jpg'))
      .resolves.toBeUndefined()

    // 验证文件状态没有被破坏
    const file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBeUndefined() // 仍然没有，但不影响正常轮询

    console.log('✅ 错误处理测试通过: API 失败不影响文件状态')
  })

  it('应该避免重复设置 processedPath', async () => {
    const store = useFileStore.getState()
    
    // 添加已有 processedPath 的文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Completed,
      processedPath: 'outputs/existing_processed_test-image.jpg', // 已有路径
      progress: 100,
      detectionCount: 1,
      avgConfidence: 0.75,
      processingStartTime: new Date(),
      processingEndTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // Mock API 响应
    mockApiClient.processing.getStatus.mockResolvedValue({
      status: 'completed',
      result: {
        processed_files: [{
          input_path: '/test/path/test-image.jpg',
          output_path: 'outputs/new_processed_test-image.jpg', // 不同的路径
          success: true,
          detection_count: 3,
          avg_confidence: 0.95
        }]
      }
    })

    // 模拟立即检查函数调用
    const checkSingleFileResult = async (taskId: string, fileName: string, filePath: string) => {
      try {
        const status = await mockApiClient.processing.getStatus(taskId)
        
        if (status.status === 'completed' && status.result?.processed_files) {
          const processedFile = status.result.processed_files.find((pf: any) => 
            pf.input_path === filePath
          )
          
          if (processedFile && processedFile.success && processedFile.output_path) {
            const currentStore = useFileStore.getState()
            const fileItem = currentStore.files.find(f => f.name === fileName)
            if (fileItem && !fileItem.processedPath) { // 关键：只在没有 processedPath 时设置
              currentStore.updateFileProcessedPath(fileItem.id, processedFile.output_path)
            }
          }
        }
      } catch (error) {
        console.warn('检查失败:', error)
      }
    }

    // 执行立即检查
    await checkSingleFileResult('test-task-id', 'test-image.jpg', '/test/path/test-image.jpg')

    // 验证：processedPath 应该保持原值，不被覆盖
    const file = store.getFileById('test-file-1')
    expect(file?.processedPath).toBe('outputs/existing_processed_test-image.jpg') // 保持原值
    expect(file?.detectionCount).toBe(1) // 保持原值
    expect(file?.avgConfidence).toBe(0.75) // 保持原值

    console.log('✅ 重复设置防护测试通过: 已有 processedPath 不会被覆盖')
  })

  it('应该验证完整的用户体验流程', async () => {
    const store = useFileStore.getState()
    
    // 添加处理中的文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 95,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // Mock API 响应
    mockApiClient.processing.getStatus.mockResolvedValue({
      status: 'completed',
      result: {
        processed_files: [{
          input_path: '/test/path/test-image.jpg',
          output_path: 'outputs/processed_test-image.jpg',
          success: true,
          detection_count: 2,
          avg_confidence: 0.88
        }]
      }
    })

    console.log('🔄 模拟用户体验流程:')
    
    // 1. 初始状态：处理中
    let file = store.getFileById('test-file-1')
    console.log('1. 初始状态:')
    console.log('   - FileListItem 显示:', file?.status === FileStatus.Processing ? '处理中' : '其他')
    console.log('   - PreviewPanel 显示:', file?.processedPath ? '处理后图片' : '原图')
    console.log('   - 用户可以查看结果:', !!file?.processedPath)

    // 2. WebSocket 完成消息：状态变为完成，但还没有 processedPath
    store.updateFileStatus('test-file-1', FileStatus.Completed)
    store.updateFileProcessingResult('test-file-1', {
      processingEndTime: new Date()
    })

    file = store.getFileById('test-file-1')
    console.log('2. WebSocket 完成后:')
    console.log('   - FileListItem 显示:', file?.status === FileStatus.Completed ? '已完成' : '其他')
    console.log('   - PreviewPanel 显示:', file?.processedPath ? '处理后图片' : '原图')
    console.log('   - 用户可以查看结果:', !!file?.processedPath)

    // 3. 立即检查：获取 processedPath
    const checkSingleFileResult = async (taskId: string, fileName: string, filePath: string) => {
      const status = await mockApiClient.processing.getStatus(taskId)
      const processedFile = status.result.processed_files.find((pf: any) => 
        pf.input_path === filePath
      )
      
      if (processedFile && processedFile.success) {
        const currentStore = useFileStore.getState()
        const fileItem = currentStore.files.find(f => f.name === fileName)
        if (fileItem && !fileItem.processedPath) {
          currentStore.updateFileProcessedPath(fileItem.id, processedFile.output_path)
          currentStore.updateFileProcessingResult(fileItem.id, {
            detectionCount: processedFile.detection_count,
            avgConfidence: processedFile.avg_confidence
          })
        }
      }
    }

    await checkSingleFileResult('test-task-id', 'test-image.jpg', '/test/path/test-image.jpg')

    file = store.getFileById('test-file-1')
    console.log('3. 立即检查后:')
    console.log('   - FileListItem 显示:', file?.status === FileStatus.Completed ? '已完成' : '其他')
    console.log('   - PreviewPanel 显示:', file?.processedPath ? '处理后图片' : '原图')
    console.log('   - 用户可以查看结果:', !!file?.processedPath)
    console.log('   - 检测结果:', `${file?.detectionCount} 个水印，置信度 ${((file?.avgConfidence || 0) * 100).toFixed(1)}%`)

    // 验证最终状态
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBe('outputs/processed_test-image.jpg')
    expect(file?.detectionCount).toBe(2)
    expect(file?.avgConfidence).toBe(0.88)

    console.log('✅ 用户体验流程测试通过: 用户可以立即查看处理结果')
  })
})
