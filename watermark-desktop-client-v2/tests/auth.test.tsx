import { describe, it, expect, beforeEach } from 'vitest'
import { useAuthStore } from '../src/stores/authStore'

// 清理 localStorage 在每个测试之前
beforeEach(() => {
  localStorage.clear()
})

describe('Auth Store', () => {
  it('should initialize with unauthenticated state', () => {
    const store = useAuthStore.getState()

    expect(store.isAuthenticated).toBe(false)
    expect(store.user).toBe(null)
    expect(store.isLoading).toBe(false)
    expect(store.error).toBe(null)
  })

  it('should handle successful login', async () => {
    const store = useAuthStore.getState()

    await store.login('<EMAIL>', 'password')

    const updatedStore = useAuthStore.getState()
    expect(updatedStore.isAuthenticated).toBe(true)
    expect(updatedStore.user).toEqual({
      id: '1',
      email: '<EMAIL>',
      name: 'test',
      avatar: undefined
    })
    expect(updatedStore.isLoading).toBe(false)
    expect(updatedStore.error).toBe(null)
  })

  it('should handle logout', async () => {
    const store = useAuthStore.getState()

    // 首先登录
    await store.login('<EMAIL>', 'password')

    // 然后登出
    store.logout()

    const updatedStore = useAuthStore.getState()
    expect(updatedStore.isAuthenticated).toBe(false)
    expect(updatedStore.user).toBe(null)
    expect(updatedStore.error).toBe(null)
  })

  it('should clear error', () => {
    const store = useAuthStore.getState()

    store.clearError()

    const updatedStore = useAuthStore.getState()
    expect(updatedStore.error).toBe(null)
  })
})
