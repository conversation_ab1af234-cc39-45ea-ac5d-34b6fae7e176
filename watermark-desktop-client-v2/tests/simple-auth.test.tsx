import { describe, it, expect, beforeEach } from 'vitest'
import { useAuthStore } from '../src/stores/authStore'

// 清理 localStorage 在每个测试之前
beforeEach(() => {
  localStorage.clear()
})

describe('Simple Auth Store', () => {
  it('should initialize with unauthenticated state', () => {
    const store = useAuthStore.getState()
    
    expect(store.isAuthenticated).toBe(false)
  })

  it('should handle login', () => {
    const store = useAuthStore.getState()
    
    store.login()
    
    const updatedStore = useAuthStore.getState()
    expect(updatedStore.isAuthenticated).toBe(true)
  })

  it('should handle logout', () => {
    const store = useAuthStore.getState()
    
    // 首先登录
    store.login()
    
    // 然后登出
    store.logout()
    
    const updatedStore = useAuthStore.getState()
    expect(updatedStore.isAuthenticated).toBe(false)
  })
})
