/**
 * 文件完成状态测试
 * 验证文件处理完成后状态和预览显示是否正确
 */

import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { PreviewPanel } from '../src/components/PreviewPanel'
import { useFileStore } from '../src/stores/fileStore'
import { FileStatus } from '../src/types'

// Mock useFileStore
vi.mock('../src/stores/fileStore')
const mockUseFileStore = vi.mocked(useFileStore)

// Mock message component
vi.mock('../src/components/ui/message', () => ({
  message: {
    success: vi.fn(),
    error: vi.fn(),
    warning: vi.fn(),
  }
}))

// Mock ImageComparison component
vi.mock('../src/components/ImageComparison', () => ({
  ImageComparison: ({ beforeLabel, afterLabel }: any) => (
    <div data-testid="image-comparison">
      <span>{beforeLabel}</span>
      <span>{afterLabel}</span>
    </div>
  )
}))

describe('文件完成状态测试', () => {
  const mockFile = {
    id: 'test-file-1',
    name: 'test-image.jpg',
    path: '/test/path/test-image.jpg',
    size: 1024000,
    type: 'image/jpeg',
    status: FileStatus.Completed,
    progress: 100,
    processedPath: 'outputs/processed_test-image.jpg',
    detectionCount: 2,
    avgConfidence: 0.85,
    processingStartTime: new Date('2025-01-01T10:00:00Z'),
    processingEndTime: new Date('2025-01-01T10:00:06Z'),
    dimensions: { width: 1920, height: 1080 }
  }

  beforeEach(() => {
    vi.clearAllMocks()
    
    // Mock store state
    mockUseFileStore.mockReturnValue({
      files: [mockFile],
      selectedFile: mockFile,
      fileObjects: new Map(),
      addFiles: vi.fn(),
      addFileItems: vi.fn(),
      removeFile: vi.fn(),
      clearFiles: vi.fn(),
      selectFile: vi.fn(),
      updateFileStatus: vi.fn(),
      updateFileProgress: vi.fn(),
      updateFilePath: vi.fn(),
      updateFileProcessedPath: vi.fn(),
      updateFileProcessingResult: vi.fn(),
      setFileTaskId: vi.fn(),
      getFileById: vi.fn(),
      getFileObject: vi.fn(),
    })
  })

  it('应该显示已完成状态', () => {
    render(<PreviewPanel />)
    
    // 验证状态显示
    expect(screen.getByText('已完成')).toBeInTheDocument()
  })

  it('应该显示处理结果信息', () => {
    render(<PreviewPanel />)
    
    // 验证检测结果
    expect(screen.getByText('检测到水印:')).toBeInTheDocument()
    expect(screen.getByText('2 个')).toBeInTheDocument()
    
    // 验证置信度
    expect(screen.getByText('平均置信度:')).toBeInTheDocument()
    expect(screen.getByText('85.0%')).toBeInTheDocument()
    
    // 验证处理时间
    expect(screen.getByText('处理时间:')).toBeInTheDocument()
    expect(screen.getByText('6.0s')).toBeInTheDocument()
  })

  it('应该显示对比查看和下载按钮', () => {
    render(<PreviewPanel />)
    
    // 验证按钮存在
    expect(screen.getByText('对比查看')).toBeInTheDocument()
    expect(screen.getByText('下载结果')).toBeInTheDocument()
  })

  it('应该显示处理后的图片', () => {
    render(<PreviewPanel />)
    
    // 验证图片元素存在
    const processedImage = screen.getByAltText('处理后的图片')
    expect(processedImage).toBeInTheDocument()
    expect(processedImage).toHaveAttribute('src', 'http://localhost:8000/outputs/processed_test-image.jpg')
  })

  it('应该在没有processedPath时显示原图', () => {
    const fileWithoutProcessedPath = {
      ...mockFile,
      processedPath: undefined
    }

    mockUseFileStore.mockReturnValue({
      files: [fileWithoutProcessedPath],
      selectedFile: fileWithoutProcessedPath,
      fileObjects: new Map(),
      addFiles: vi.fn(),
      addFileItems: vi.fn(),
      removeFile: vi.fn(),
      clearFiles: vi.fn(),
      selectFile: vi.fn(),
      updateFileStatus: vi.fn(),
      updateFileProgress: vi.fn(),
      updateFilePath: vi.fn(),
      updateFileProcessedPath: vi.fn(),
      updateFileProcessingResult: vi.fn(),
      setFileTaskId: vi.fn(),
      getFileById: vi.fn(),
      getFileObject: vi.fn(),
    })

    render(<PreviewPanel />)
    
    // 验证显示原图
    const originalImage = screen.getByAltText('test-image.jpg')
    expect(originalImage).toBeInTheDocument()
    
    // 验证没有对比和下载按钮
    expect(screen.queryByText('对比查看')).not.toBeInTheDocument()
    expect(screen.queryByText('下载结果')).not.toBeInTheDocument()
  })

  it('应该正确处理处理中状态', () => {
    const processingFile = {
      ...mockFile,
      status: FileStatus.Processing,
      progress: 75,
      processedPath: undefined
    }

    mockUseFileStore.mockReturnValue({
      files: [processingFile],
      selectedFile: processingFile,
      fileObjects: new Map(),
      addFiles: vi.fn(),
      addFileItems: vi.fn(),
      removeFile: vi.fn(),
      clearFiles: vi.fn(),
      selectFile: vi.fn(),
      updateFileStatus: vi.fn(),
      updateFileProgress: vi.fn(),
      updateFilePath: vi.fn(),
      updateFileProcessedPath: vi.fn(),
      updateFileProcessingResult: vi.fn(),
      setFileTaskId: vi.fn(),
      getFileById: vi.fn(),
      getFileObject: vi.fn(),
    })

    render(<PreviewPanel />)
    
    // 验证处理中状态
    expect(screen.getByText('处理中 (75%)')).toBeInTheDocument()
    
    // 验证显示原图
    const originalImage = screen.getByAltText('test-image.jpg')
    expect(originalImage).toBeInTheDocument()
    
    // 验证没有处理结果信息
    expect(screen.queryByText('检测到水印:')).not.toBeInTheDocument()
    expect(screen.queryByText('对比查看')).not.toBeInTheDocument()
  })

  it('应该正确处理失败状态', () => {
    const failedFile = {
      ...mockFile,
      status: FileStatus.Failed,
      processedPath: undefined,
      error: '处理失败：模型加载错误'
    }

    mockUseFileStore.mockReturnValue({
      files: [failedFile],
      selectedFile: failedFile,
      fileObjects: new Map(),
      addFiles: vi.fn(),
      addFileItems: vi.fn(),
      removeFile: vi.fn(),
      clearFiles: vi.fn(),
      selectFile: vi.fn(),
      updateFileStatus: vi.fn(),
      updateFileProgress: vi.fn(),
      updateFilePath: vi.fn(),
      updateFileProcessedPath: vi.fn(),
      updateFileProcessingResult: vi.fn(),
      setFileTaskId: vi.fn(),
      getFileById: vi.fn(),
      getFileObject: vi.fn(),
    })

    render(<PreviewPanel />)
    
    // 验证失败状态
    expect(screen.getByText('失败')).toBeInTheDocument()
    
    // 验证显示原图
    const originalImage = screen.getByAltText('test-image.jpg')
    expect(originalImage).toBeInTheDocument()
    
    // 验证没有处理结果信息
    expect(screen.queryByText('检测到水印:')).not.toBeInTheDocument()
    expect(screen.queryByText('对比查看')).not.toBeInTheDocument()
  })
})
