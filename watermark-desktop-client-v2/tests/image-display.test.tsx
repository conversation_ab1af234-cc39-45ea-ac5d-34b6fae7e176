import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { useFileStore } from '../src/stores/fileStore'

// Mock URL.createObjectURL and URL.revokeObjectURL
const mockCreateObjectURL = vi.fn()
const mockRevokeObjectURL = vi.fn()

Object.defineProperty(global.URL, 'createObjectURL', {
  value: mockCreateObjectURL,
  writable: true
})

Object.defineProperty(global.URL, 'revokeObjectURL', {
  value: mockRevokeObjectURL,
  writable: true
})

describe('Image Display Functionality', () => {
  beforeEach(() => {
    // 清理状态
    localStorage.clear()
    useFileStore.setState({ 
      files: [], 
      selectedFile: null, 
      fileObjects: new Map() 
    })
    
    // 重置 mocks
    vi.clearAllMocks()
    mockCreateObjectURL.mockReturnValue('blob:http://localhost:5173/mock-url')
  })

  afterEach(() => {
    vi.clearAllMocks()
  })

  describe('File Object URL Creation', () => {
    it('should create object URL for File objects', () => {
      const mockFile = new File(['test image data'], 'test.jpg', { 
        type: 'image/jpeg' 
      })
      
      const store = useFileStore.getState()
      store.addFiles([mockFile])
      
      const files = useFileStore.getState().files
      expect(files).toHaveLength(1)
      
      const fileObject = store.getFileObject(files[0].id)
      expect(fileObject).toBe(mockFile)
    })

    it('should handle multiple files correctly', () => {
      const mockFiles = [
        new File(['test1'], 'test1.jpg', { type: 'image/jpeg' }),
        new File(['test2'], 'test2.png', { type: 'image/png' }),
        new File(['test3'], 'test3.webp', { type: 'image/webp' })
      ]
      
      const store = useFileStore.getState()
      store.addFiles(mockFiles)
      
      const files = useFileStore.getState().files
      expect(files).toHaveLength(3)
      
      // 验证每个文件都有对应的 File 对象
      files.forEach((file, index) => {
        const fileObject = store.getFileObject(file.id)
        expect(fileObject).toBe(mockFiles[index])
        expect(fileObject?.name).toBe(mockFiles[index].name)
        expect(fileObject?.type).toBe(mockFiles[index].type)
      })
    })
  })

  describe('Image URL Generation', () => {
    it('should generate blob URL for browser environment', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      // 模拟 getImageUrl 函数的逻辑
      const getImageUrl = (file: any, getFileObject: Function) => {
        const fileObject = getFileObject(file.id)
        
        if (fileObject) {
          return URL.createObjectURL(fileObject)
        }
        
        return file.path
      }
      
      const store = useFileStore.getState()
      store.addFiles([mockFile])
      
      const files = useFileStore.getState().files
      const imageUrl = getImageUrl(files[0], store.getFileObject)
      
      expect(mockCreateObjectURL).toHaveBeenCalledWith(mockFile)
      expect(imageUrl).toBe('blob:http://localhost:5173/mock-url')
    })

    it('should handle HTTP URLs correctly', () => {
      const getImageUrl = (file: any, getFileObject: Function) => {
        const fileObject = getFileObject(file.id)
        
        if (fileObject) {
          return URL.createObjectURL(fileObject)
        }
        
        const path = file.path
        if (path.startsWith('http')) {
          return path
        }
        
        return path
      }
      
      const mockFileItem = {
        id: 'test-id',
        path: 'http://example.com/image.jpg'
      }
      
      const mockGetFileObject = vi.fn().mockReturnValue(null)
      const imageUrl = getImageUrl(mockFileItem, mockGetFileObject)
      
      expect(imageUrl).toBe('http://example.com/image.jpg')
      expect(mockCreateObjectURL).not.toHaveBeenCalled()
    })

    it('should handle local file paths for Tauri environment', () => {
      const getImageUrl = (file: any, getFileObject: Function) => {
        const fileObject = getFileObject(file.id)
        
        if (fileObject) {
          return URL.createObjectURL(fileObject)
        }
        
        const path = file.path
        if (path.startsWith('http')) {
          return path
        }

        // Tauri 环境处理
        if (path.startsWith('/')) {
          return `http://localhost:8000/api/v1/files/local?path=${encodeURIComponent(path)}`
        }

        return `asset://localhost/${path}`
      }
      
      const mockFileItem = {
        id: 'test-id',
        path: '/absolute/path/to/image.jpg'
      }
      
      const mockGetFileObject = vi.fn().mockReturnValue(null)
      const imageUrl = getImageUrl(mockFileItem, mockGetFileObject)
      
      expect(imageUrl).toBe('http://localhost:8000/api/v1/files/local?path=%2Fabsolute%2Fpath%2Fto%2Fimage.jpg')
      expect(mockCreateObjectURL).not.toHaveBeenCalled()
    })
  })

  describe('File Store Integration', () => {
    it('should maintain file objects mapping correctly', () => {
      const mockFiles = [
        new File(['data1'], 'file1.jpg', { type: 'image/jpeg' }),
        new File(['data2'], 'file2.png', { type: 'image/png' })
      ]
      
      const store = useFileStore.getState()
      store.addFiles(mockFiles)
      
      const state = useFileStore.getState()
      
      // 验证文件对象映射
      expect(state.fileObjects.size).toBe(2)
      
      // 验证可以通过 ID 获取文件对象
      state.files.forEach((file, index) => {
        const fileObject = store.getFileObject(file.id)
        expect(fileObject).toBe(mockFiles[index])
      })
    })

    it('should handle file removal correctly', () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      const store = useFileStore.getState()
      store.addFiles([mockFile])
      
      const files = useFileStore.getState().files
      expect(files).toHaveLength(1)
      
      // 移除文件
      store.removeFile(files[0].id)
      
      const updatedState = useFileStore.getState()
      expect(updatedState.files).toHaveLength(0)
      expect(updatedState.fileObjects.size).toBe(0)
    })

    it('should clear all files and objects correctly', () => {
      const mockFiles = [
        new File(['data1'], 'file1.jpg', { type: 'image/jpeg' }),
        new File(['data2'], 'file2.png', { type: 'image/png' })
      ]
      
      const store = useFileStore.getState()
      store.addFiles(mockFiles)
      
      expect(useFileStore.getState().files).toHaveLength(2)
      expect(useFileStore.getState().fileObjects.size).toBe(2)
      
      // 清空所有文件
      store.clearFiles()
      
      const finalState = useFileStore.getState()
      expect(finalState.files).toHaveLength(0)
      expect(finalState.fileObjects.size).toBe(0)
      expect(finalState.selectedFile).toBe(null)
    })
  })
})
