#!/usr/bin/env python3
"""
构建结果验证脚本
检查构建输出是否正确生成
"""

import os
import sys
from pathlib import Path
import subprocess

def check_backend_build():
    """检查后端构建结果"""
    print("🔍 检查后端构建结果...")
    
    project_root = Path(__file__).parent.parent
    backend_dir = project_root / "python-backend"
    
    # 检查 dist 目录
    dist_dir = backend_dir / "dist"
    if not dist_dir.exists():
        print("❌ dist 目录不存在")
        return False
    
    # 检查可执行文件
    exe_name = "watermark-backend.exe" if os.name == 'nt' else "watermark-backend"
    exe_path = dist_dir / exe_name
    
    if exe_path.exists():
        size_mb = exe_path.stat().st_size / 1024 / 1024
        print(f"✅ 后端可执行文件: {exe_path.name} ({size_mb:.1f} MB)")
        return True
    else:
        print(f"❌ 后端可执行文件不存在: {exe_name}")
        return False

def check_tauri_resources():
    """检查 Tauri 资源目录"""
    print("🔍 检查 Tauri 资源...")
    
    project_root = Path(__file__).parent.parent
    resources_dir = project_root / "src-tauri" / "resources"
    
    if not resources_dir.exists():
        print("❌ Tauri 资源目录不存在")
        return False
    
    # 检查后端可执行文件
    backend_exe = resources_dir / "watermark-backend"
    if backend_exe.exists():
        size_mb = backend_exe.stat().st_size / 1024 / 1024
        print(f"✅ Tauri 后端文件: {size_mb:.1f} MB")
    else:
        print("❌ Tauri 后端文件不存在")
        return False
    
    # 检查模型文件
    models_dir = resources_dir / "models"
    if models_dir.exists():
        total_size = sum(f.stat().st_size for f in models_dir.rglob('*') if f.is_file())
        size_mb = total_size / 1024 / 1024
        file_count = len(list(models_dir.rglob('*')))
        print(f"✅ 模型文件: {file_count} 个文件, {size_mb:.1f} MB")
    else:
        print("❌ 模型文件目录不存在")
        return False
    
    return True

def check_frontend_build():
    """检查前端构建结果"""
    print("🔍 检查前端构建结果...")
    
    project_root = Path(__file__).parent.parent
    
    # 检查 dist 目录
    dist_dir = project_root / "dist"
    if dist_dir.exists():
        file_count = len(list(dist_dir.rglob('*')))
        print(f"✅ 前端构建输出: {file_count} 个文件")
    else:
        print("⚠️ 前端 dist 目录不存在（可能还未构建）")
    
    # 检查 node_modules
    node_modules = project_root / "node_modules"
    if node_modules.exists():
        print("✅ 前端依赖已安装")
    else:
        print("❌ 前端依赖未安装")
        return False
    
    return True

def check_tauri_build():
    """检查 Tauri 构建结果"""
    print("🔍 检查 Tauri 构建结果...")
    
    project_root = Path(__file__).parent.parent
    bundle_dir = project_root / "src-tauri" / "target" / "release" / "bundle"
    
    if not bundle_dir.exists():
        print("⚠️ Tauri 构建输出不存在（可能还未构建）")
        return False
    
    # 检查不同平台的构建输出
    found_bundles = []
    
    for bundle_type in bundle_dir.iterdir():
        if bundle_type.is_dir():
            for bundle_file in bundle_type.iterdir():
                if bundle_file.is_file():
                    size_mb = bundle_file.stat().st_size / 1024 / 1024
                    found_bundles.append((bundle_file.name, size_mb))
                    print(f"✅ {bundle_type.name}: {bundle_file.name} ({size_mb:.1f} MB)")
    
    if found_bundles:
        print(f"✅ 找到 {len(found_bundles)} 个安装包")
        return True
    else:
        print("❌ 未找到安装包文件")
        return False

def test_backend_executable():
    """测试后端可执行文件"""
    print("🧪 测试后端可执行文件...")
    
    project_root = Path(__file__).parent.parent
    backend_dir = project_root / "python-backend"
    
    exe_name = "watermark-backend.exe" if os.name == 'nt' else "watermark-backend"
    exe_path = backend_dir / "dist" / exe_name
    
    if not exe_path.exists():
        print("❌ 可执行文件不存在，无法测试")
        return False
    
    try:
        # 尝试运行 --help 命令（如果支持的话）
        result = subprocess.run(
            [str(exe_path), "--help"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        print("✅ 可执行文件可以运行")
        return True
    except subprocess.TimeoutExpired:
        print("⚠️ 可执行文件运行超时（可能正常，因为是服务器程序）")
        return True
    except Exception as e:
        print(f"❌ 可执行文件测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 构建结果验证")
    print("=" * 40)
    
    results = []
    
    # 检查各个组件
    results.append(("后端构建", check_backend_build()))
    results.append(("Tauri 资源", check_tauri_resources()))
    results.append(("前端构建", check_frontend_build()))
    results.append(("Tauri 构建", check_tauri_build()))
    results.append(("后端测试", test_backend_executable()))
    
    print("\n" + "=" * 40)
    print("📊 验证结果汇总:")
    
    passed = 0
    for name, success in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"  {status} {name}")
        if success:
            passed += 1
    
    total = len(results)
    print(f"\n📈 成功率: {passed}/{total} ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有检查通过！应用构建成功")
        print("\n📦 可以在以下位置找到安装包:")
        print("   src-tauri/target/release/bundle/")
        return True
    else:
        print(f"\n⚠️ {total-passed} 项检查失败")
        print("\n💡 建议:")
        print("   1. 确保后端构建完成: python python-backend/build_standalone.py")
        print("   2. 安装前端依赖: pnpm install")
        print("   3. 构建前端: pnpm build")
        print("   4. 构建 Tauri 应用: pnpm tauri build")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
