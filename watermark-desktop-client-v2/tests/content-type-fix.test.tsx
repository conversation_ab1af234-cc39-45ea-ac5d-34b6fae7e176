import { describe, it, expect, vi, beforeEach } from 'vitest'
import { HTTPClient } from '../src/lib/http-client'

// Mock fetch
global.fetch = vi.fn()

describe('Content-Type Fix', () => {
  let httpClient: HTTPClient
  
  beforeEach(() => {
    // 重置 mocks
    vi.clearAllMocks()
    
    // 创建 HTTP 客户端实例
    httpClient = new HTTPClient('http://127.0.0.1:8000/api/v1')
    
    // Mock fetch 成功响应
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      json: async () => ({ success: true })
    })
  })

  describe('FormData Content-Type Handling', () => {
    it('should not include Content-Type header for FormData', async () => {
      const formData = new FormData()
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('files', mockFile)

      await httpClient.post('/files/upload', formData)

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证 URL
      expect(url).toBe('http://127.0.0.1:8000/api/v1/files/upload')
      
      // 验证请求选项
      expect(options.method).toBe('POST')
      expect(options.body).toBeInstanceOf(FormData)
      
      // 关键验证：Content-Type 不应该存在
      expect(options.headers).not.toHaveProperty('Content-Type')
      
      // 验证其他默认 headers 仍然存在（如果有的话）
      console.log('Request headers:', options.headers)
    })

    it('should include Content-Type header for JSON data', async () => {
      const jsonData = { name: 'test', value: 123 }

      await httpClient.post('/api/test', jsonData)

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证请求选项
      expect(options.method).toBe('POST')
      expect(options.body).toBe(JSON.stringify(jsonData))
      
      // 验证 Content-Type 被正确设置
      expect(options.headers['Content-Type']).toBe('application/json')
    })

    it('should handle empty data without Content-Type issues', async () => {
      await httpClient.post('/api/test')

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证请求选项
      expect(options.method).toBe('POST')
      expect(options.body).toBeUndefined()
      
      // 对于空数据，应该有默认的 Content-Type
      expect(options.headers).toHaveProperty('Content-Type')
    })
  })

  describe('Headers Filtering Logic', () => {
    it('should filter out undefined headers', () => {
      // 模拟 headers 过滤逻辑
      const filterHeaders = (headers: Record<string, any>): Record<string, string> => {
        const filtered: Record<string, string> = {}
        Object.entries(headers).forEach(([key, value]) => {
          if (value !== undefined) {
            filtered[key] = value
          }
        })
        return filtered
      }

      const testHeaders = {
        'Accept': 'application/json',
        'Content-Type': undefined,
        'Authorization': 'Bearer token',
        'X-Custom': null,
        'User-Agent': 'test'
      }

      const filtered = filterHeaders(testHeaders)

      expect(filtered).toEqual({
        'Accept': 'application/json',
        'Authorization': 'Bearer token',
        'X-Custom': null,  // null 值保留
        'User-Agent': 'test'
      })
      
      expect(filtered).not.toHaveProperty('Content-Type')
    })
  })

  describe('PUT Method FormData Handling', () => {
    it('should handle FormData correctly in PUT requests', async () => {
      const formData = new FormData()
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('files', mockFile)

      await httpClient.put('/files/update', formData)

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证请求选项
      expect(options.method).toBe('PUT')
      expect(options.body).toBeInstanceOf(FormData)
      
      // 关键验证：Content-Type 不应该存在
      expect(options.headers).not.toHaveProperty('Content-Type')
    })
  })

  describe('Real-world Scenario', () => {
    it('should create correct request for file upload scenario', async () => {
      // 模拟真实的文件上传场景
      const file = new File(['image data'], 'test-image.jpg', { type: 'image/jpeg' })
      const formData = new FormData()
      formData.append('files', file)

      await httpClient.post('/files/upload', formData)

      const [url, options] = (global.fetch as any).mock.calls[0]

      // 验证 URL 正确
      expect(url).toBe('http://127.0.0.1:8000/api/v1/files/upload')
      
      // 验证方法正确
      expect(options.method).toBe('POST')
      
      // 验证 body 是 FormData
      expect(options.body).toBeInstanceOf(FormData)
      
      // 验证 FormData 内容
      const sentFormData = options.body as FormData
      const sentFile = sentFormData.get('files') as File
      expect(sentFile.name).toBe('test-image.jpg')
      expect(sentFile.type).toBe('image/jpeg')
      
      // 最重要：验证没有 Content-Type header
      expect(options.headers).not.toHaveProperty('Content-Type')
      
      // 验证其他可能的 headers
      console.log('Final request headers:', options.headers)
    })

    it('should maintain other headers while removing Content-Type for FormData', async () => {
      const formData = new FormData()
      formData.append('test', 'value')

      // 传递自定义 headers
      await httpClient.post('/files/upload', formData, {
        headers: {
          'X-Custom-Header': 'custom-value',
          'Authorization': 'Bearer token'
        }
      })

      const [url, options] = (global.fetch as any).mock.calls[0]

      // 验证自定义 headers 保留
      expect(options.headers['X-Custom-Header']).toBe('custom-value')
      expect(options.headers['Authorization']).toBe('Bearer token')
      
      // 验证 Content-Type 被移除
      expect(options.headers).not.toHaveProperty('Content-Type')
    })
  })
})
