#!/usr/bin/env python3
"""
构建过程测试脚本
验证构建环境和依赖是否正确配置
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path

class BuildTester:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backend_dir = self.project_root / "python-backend"
        self.test_results = []
        
    def log_result(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f": {message}"
        print(result)
        self.test_results.append((test_name, success, message))
        
    def test_python_environment(self):
        """测试 Python 环境"""
        print("\n🐍 测试 Python 环境...")
        
        # 检查 Python 版本
        version = sys.version_info
        if version.major >= 3 and version.minor >= 8:
            self.log_result("Python 版本", True, f"{version.major}.{version.minor}.{version.micro}")
        else:
            self.log_result("Python 版本", False, f"需要 Python 3.8+，当前: {version.major}.{version.minor}")
            
        # 检查 pip
        try:
            import pip
            self.log_result("pip", True, pip.__version__)
        except ImportError:
            self.log_result("pip", False, "pip 未安装")
            
    def test_node_environment(self):
        """测试 Node.js 环境"""
        print("\n📦 测试 Node.js 环境...")
        
        # 检查 Node.js
        try:
            result = subprocess.run(["node", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                self.log_result("Node.js", True, result.stdout.strip())
            else:
                self.log_result("Node.js", False, "无法获取版本")
        except FileNotFoundError:
            self.log_result("Node.js", False, "未安装")
            
        # 检查 pnpm
        try:
            result = subprocess.run(["pnpm", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                self.log_result("pnpm", True, result.stdout.strip())
            else:
                self.log_result("pnpm", False, "无法获取版本")
        except FileNotFoundError:
            self.log_result("pnpm", False, "未安装")
            
    def test_rust_environment(self):
        """测试 Rust 环境"""
        print("\n🦀 测试 Rust 环境...")
        
        # 检查 Cargo
        try:
            result = subprocess.run(["cargo", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                self.log_result("Cargo", True, result.stdout.strip())
            else:
                self.log_result("Cargo", False, "无法获取版本")
        except FileNotFoundError:
            self.log_result("Cargo", False, "未安装")
            
        # 检查 Tauri CLI
        try:
            result = subprocess.run(["cargo", "tauri", "--version"], capture_output=True, text=True)
            if result.returncode == 0:
                self.log_result("Tauri CLI", True, result.stdout.strip())
            else:
                self.log_result("Tauri CLI", False, "无法获取版本")
        except FileNotFoundError:
            self.log_result("Tauri CLI", False, "未安装")
            
    def test_python_dependencies(self):
        """测试 Python 依赖"""
        print("\n📚 测试 Python 依赖...")
        
        required_packages = [
            ("fastapi", "FastAPI"),
            ("uvicorn", "Uvicorn"),
            ("torch", "PyTorch"),
            ("cv2", "OpenCV"),
            ("PIL", "Pillow"),
            ("numpy", "NumPy"),
            ("ultralytics", "Ultralytics"),
            ("loguru", "Loguru"),
            ("pydantic", "Pydantic")
        ]
        
        for package_name, display_name in required_packages:
            try:
                if package_name == "cv2":
                    import cv2
                    version = cv2.__version__
                elif package_name == "PIL":
                    import PIL
                    version = PIL.__version__
                else:
                    module = importlib.import_module(package_name)
                    version = getattr(module, "__version__", "unknown")
                
                self.log_result(display_name, True, version)
            except ImportError:
                self.log_result(display_name, False, "未安装")
                
    def test_project_structure(self):
        """测试项目结构"""
        print("\n📁 测试项目结构...")
        
        required_files = [
            ("package.json", "前端配置文件"),
            ("src-tauri/tauri.conf.json", "Tauri 配置文件"),
            ("python-backend/main.py", "后端主文件"),
            ("python-backend/requirements.txt", "Python 依赖文件"),
            ("python-backend/build_standalone.py", "后端构建脚本")
        ]
        
        for file_path, description in required_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.log_result(description, True, str(file_path))
            else:
                self.log_result(description, False, f"文件不存在: {file_path}")
                
    def test_model_files(self):
        """测试模型文件"""
        print("\n🤖 测试模型文件...")
        
        models_dir = self.backend_dir / "models"
        if not models_dir.exists():
            self.log_result("模型目录", False, "models 目录不存在")
            return
            
        self.log_result("模型目录", True, str(models_dir))
        
        # 检查 YOLO 模型文件
        yolo_dir = models_dir / "yolo"
        if yolo_dir.exists():
            yolo_files = list(yolo_dir.glob("*.pt"))
            if yolo_files:
                for yolo_file in yolo_files:
                    size_mb = yolo_file.stat().st_size / 1024 / 1024
                    self.log_result(f"YOLO 模型 ({yolo_file.name})", True, f"{size_mb:.1f} MB")
            else:
                self.log_result("YOLO 模型", False, "未找到 .pt 文件")
        else:
            self.log_result("YOLO 模型", False, "yolo 目录不存在")

        # 检查 LaMa 模型文件
        lama_dir = models_dir / "lama"
        if lama_dir.exists():
            # 检查 big-lama 目录
            big_lama_dir = lama_dir / "big-lama"
            if big_lama_dir.exists():
                # 检查配置文件
                config_file = big_lama_dir / "config.yaml"
                if config_file.exists():
                    self.log_result("LaMa 配置", True, "config.yaml")
                else:
                    self.log_result("LaMa 配置", False, "config.yaml 不存在")

                # 检查模型文件
                models_subdir = big_lama_dir / "models"
                if models_subdir.exists():
                    model_files = list(models_subdir.glob("*"))
                    if model_files:
                        total_size = sum(f.stat().st_size for f in model_files if f.is_file())
                        size_mb = total_size / 1024 / 1024
                        self.log_result("LaMa 模型文件", True, f"{len(model_files)} 个文件, {size_mb:.1f} MB")
                    else:
                        self.log_result("LaMa 模型文件", False, "models 目录为空")
                else:
                    self.log_result("LaMa 模型文件", False, "models 子目录不存在")
            else:
                self.log_result("LaMa 模型", False, "big-lama 目录不存在")
        else:
            self.log_result("LaMa 模型", False, "lama 目录不存在")
                
    def test_build_tools(self):
        """测试构建工具"""
        print("\n🔧 测试构建工具...")
        
        # 检查 PyInstaller
        try:
            import PyInstaller
            self.log_result("PyInstaller", True, PyInstaller.__version__)
        except ImportError:
            self.log_result("PyInstaller", False, "未安装")
            
        # 检查前端依赖
        package_json = self.project_root / "package.json"
        if package_json.exists():
            self.log_result("package.json", True, "存在")
            
            # 检查 node_modules
            node_modules = self.project_root / "node_modules"
            if node_modules.exists():
                self.log_result("node_modules", True, "已安装")
            else:
                self.log_result("node_modules", False, "需要运行 pnpm install")
        else:
            self.log_result("package.json", False, "不存在")
            
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始构建环境测试...")
        print("=" * 50)
        
        self.test_python_environment()
        self.test_node_environment()
        self.test_rust_environment()
        self.test_python_dependencies()
        self.test_project_structure()
        self.test_model_files()
        self.test_build_tools()
        
        print("\n" + "=" * 50)
        print("📊 测试结果汇总:")
        
        passed = sum(1 for _, success, _ in self.test_results if success)
        total = len(self.test_results)
        
        print(f"✅ 通过: {passed}")
        print(f"❌ 失败: {total - passed}")
        print(f"📈 成功率: {passed/total*100:.1f}%")
        
        if passed == total:
            print("\n🎉 所有测试通过！可以开始构建")
            return True
        else:
            print("\n⚠️ 部分测试失败，请检查环境配置")
            print("\n失败的测试:")
            for name, success, message in self.test_results:
                if not success:
                    print(f"  ❌ {name}: {message}")
            return False

def main():
    """主函数"""
    tester = BuildTester()
    success = tester.run_all_tests()
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
