import { describe, it, expect, vi, beforeEach } from 'vitest'
import { HTTPClient } from '../src/lib/http-client'

// Mock fetch
global.fetch = vi.fn()

describe('FormData Fix', () => {
  let httpClient: HTTPClient
  
  beforeEach(() => {
    // 重置 mocks
    vi.clearAllMocks()
    
    // 创建 HTTP 客户端实例
    httpClient = new HTTPClient('http://127.0.0.1:8000/api/v1')
    
    // Mock fetch 成功响应
    ;(global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      json: async () => ({
        message: 'Success',
        files: [{
          name: 'test.jpg',
          path: '/uploads/test.jpg',
          size: 1024,
          type: 'image/jpeg'
        }]
      })
    })
  })

  describe('HTTP Client FormData Handling', () => {
    it('should handle FormData correctly in POST requests', async () => {
      const formData = new FormData()
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('files', mockFile)

      await httpClient.post('/files/upload', formData)

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证 URL
      expect(url).toBe('http://127.0.0.1:8000/api/v1/files/upload')
      
      // 验证请求选项
      expect(options.method).toBe('POST')
      expect(options.body).toBeInstanceOf(FormData)
      
      // 验证 Content-Type 没有被设置（让浏览器自动设置）
      expect(options.headers).not.toHaveProperty('Content-Type')
      
      // 验证 FormData 内容
      const sentFormData = options.body as FormData
      expect(sentFormData.get('files')).toBe(mockFile)
    })

    it('should handle JSON data correctly in POST requests', async () => {
      const jsonData = { name: 'test', value: 123 }

      await httpClient.post('/api/test', jsonData)

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证 URL
      expect(url).toBe('http://127.0.0.1:8000/api/v1/api/test')
      
      // 验证请求选项
      expect(options.method).toBe('POST')
      expect(options.body).toBe(JSON.stringify(jsonData))
      
      // 验证 Content-Type 被正确设置
      expect(options.headers['Content-Type']).toBe('application/json')
    })

    it('should handle empty data correctly in POST requests', async () => {
      await httpClient.post('/api/test')

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证请求选项
      expect(options.method).toBe('POST')
      expect(options.body).toBeUndefined()
    })

    it('should handle FormData correctly in PUT requests', async () => {
      const formData = new FormData()
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      formData.append('files', mockFile)

      await httpClient.put('/files/update', formData)

      // 验证 fetch 被正确调用
      expect(global.fetch).toHaveBeenCalledTimes(1)
      
      const [url, options] = (global.fetch as any).mock.calls[0]
      
      // 验证请求选项
      expect(options.method).toBe('PUT')
      expect(options.body).toBeInstanceOf(FormData)
      
      // 验证 Content-Type 没有被设置
      expect(options.headers).not.toHaveProperty('Content-Type')
    })
  })

  describe('Content-Type Header Management', () => {
    it('should not set Content-Type for FormData', () => {
      const formData = new FormData()
      formData.append('test', 'value')
      
      // 模拟 HTTP 客户端的逻辑
      const processRequestData = (data: any) => {
        let body: any
        let headers: Record<string, string> = { 'Content-Type': 'application/json' }
        
        if (data instanceof FormData) {
          body = data
          delete headers['Content-Type']
        } else if (data) {
          body = JSON.stringify(data)
          headers['Content-Type'] = 'application/json'
        } else {
          body = undefined
        }
        
        return { body, headers }
      }
      
      const result = processRequestData(formData)
      
      expect(result.body).toBe(formData)
      expect(result.headers).not.toHaveProperty('Content-Type')
    })

    it('should set Content-Type for JSON data', () => {
      const jsonData = { test: 'value' }
      
      // 模拟 HTTP 客户端的逻辑
      const processRequestData = (data: any) => {
        let body: any
        let headers: Record<string, string> = {}
        
        if (data instanceof FormData) {
          body = data
          delete headers['Content-Type']
        } else if (data) {
          body = JSON.stringify(data)
          headers['Content-Type'] = 'application/json'
        } else {
          body = undefined
        }
        
        return { body, headers }
      }
      
      const result = processRequestData(jsonData)
      
      expect(result.body).toBe(JSON.stringify(jsonData))
      expect(result.headers['Content-Type']).toBe('application/json')
    })
  })

  describe('File Upload API Integration', () => {
    it('should create correct FormData for file upload', () => {
      const mockFile = new File(['test content'], 'test.jpg', { type: 'image/jpeg' })
      
      // 模拟 fileAPI.uploadFile 的逻辑
      const createUploadFormData = (file: File) => {
        const formData = new FormData()
        formData.append('files', file)
        return formData
      }
      
      const formData = createUploadFormData(mockFile)
      
      expect(formData).toBeInstanceOf(FormData)
      expect(formData.get('files')).toBe(mockFile)
      
      // 验证文件属性
      const uploadedFile = formData.get('files') as File
      expect(uploadedFile.name).toBe('test.jpg')
      expect(uploadedFile.type).toBe('image/jpeg')
      expect(uploadedFile.size).toBeGreaterThan(0)
    })

    it('should handle multiple files in FormData', () => {
      const file1 = new File(['content1'], 'test1.jpg', { type: 'image/jpeg' })
      const file2 = new File(['content2'], 'test2.png', { type: 'image/png' })
      
      const formData = new FormData()
      formData.append('files', file1)
      formData.append('files', file2)
      
      // 验证多文件上传
      const files = formData.getAll('files')
      expect(files).toHaveLength(2)
      expect(files[0]).toBe(file1)
      expect(files[1]).toBe(file2)
    })
  })

  describe('Error Handling', () => {
    it('should handle fetch errors correctly', async () => {
      // Mock fetch 失败
      ;(global.fetch as any).mockRejectedValue(new Error('Network error'))
      
      const formData = new FormData()
      formData.append('test', 'value')
      
      await expect(httpClient.post('/files/upload', formData)).rejects.toThrow('Network error')
    })

    it('should handle HTTP error responses', async () => {
      // Mock HTTP 错误响应
      ;(global.fetch as any).mockResolvedValue({
        ok: false,
        status: 400,
        statusText: 'Bad Request',
        json: async () => ({ error: 'Invalid file format' })
      })
      
      const formData = new FormData()
      formData.append('test', 'value')
      
      await expect(httpClient.post('/files/upload', formData)).rejects.toThrow()
    })
  })
})
