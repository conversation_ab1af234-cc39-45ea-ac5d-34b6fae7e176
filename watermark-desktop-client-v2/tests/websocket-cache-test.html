<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket缓存机制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-step {
            margin: 10px 0;
            padding: 10px;
            border-left: 4px solid #007bff;
            background-color: #f8f9fa;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background-color: #ffc107; }
        .status-running { background-color: #17a2b8; }
        .status-success { background-color: #28a745; }
        .status-error { background-color: #dc3545; }
    </style>
</head>
<body>
    <h1>🧪 WebSocket缓存机制测试</h1>
    
    <div class="test-container">
        <h2>📋 测试计划</h2>
        <div class="test-step">
            <strong>测试目标：</strong>验证WebSocket消息缓存机制是否正常工作
        </div>
        <div class="test-step">
            <strong>测试场景：</strong>API调用慢于WebSocket消息到达的情况
        </div>
        <div class="test-step">
            <strong>预期结果：</strong>早期到达的WebSocket消息被缓存并在API调用完成后重新处理
        </div>
    </div>

    <div class="test-container">
        <h2>🔍 关键日志检查点</h2>
        <div id="checkpoints">
            <div class="test-result warning">
                <span class="status-indicator status-pending"></span>
                <strong>检查点1：</strong>等待API状态设置 - 期望看到 "🎯 开始等待API响应，缓存WebSocket消息"
            </div>
            <div class="test-result warning">
                <span class="status-indicator status-pending"></span>
                <strong>检查点2：</strong>等待状态验证 - 期望看到 "🔍 等待API状态: true"
            </div>
            <div class="test-result warning">
                <span class="status-indicator status-pending"></span>
                <strong>检查点3：</strong>消息缓存 - 期望看到 "📦 缓存早期到达的WebSocket消息"
            </div>
            <div class="test-result warning">
                <span class="status-indicator status-pending"></span>
                <strong>检查点4：</strong>API调用完成 - 期望看到 "🎉 批量处理任务启动成功!"
            </div>
            <div class="test-result warning">
                <span class="status-indicator status-pending"></span>
                <strong>检查点5：</strong>缓存消息处理 - 期望看到 "📦 处理 X 条缓存的WebSocket消息"
            </div>
            <div class="test-result warning">
                <span class="status-indicator status-pending"></span>
                <strong>检查点6：</strong>任务ID匹配 - 期望看到 "📡 是否为当前任务: true"
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>🎮 测试控制</h2>
        <button onclick="startTest()">开始测试</button>
        <button onclick="clearLogs()">清空日志</button>
        <button onclick="exportLogs()">导出日志</button>
        <button onclick="analyzeResults()">分析结果</button>
    </div>

    <div class="test-container">
        <h2>📊 实时日志监控</h2>
        <div id="logOutput" class="log-output">等待测试开始...</div>
    </div>

    <div class="test-container">
        <h2>📈 测试结果分析</h2>
        <div id="analysisResult">
            <div class="test-result warning">
                <strong>状态：</strong>等待测试执行
            </div>
        </div>
    </div>

    <script>
        let logBuffer = [];
        let testStartTime = null;
        let checkpoints = {
            apiWaitStart: false,
            waitingStateTrue: false,
            messageCache: false,
            apiComplete: false,
            cacheProcess: false,
            taskMatch: false
        };

        // 监听控制台日志
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const timestamp = new Date().toISOString().substr(11, 12);
            const logMessage = args.join(' ');
            logBuffer.push(`[${timestamp}] ${logMessage}`);
            
            // 更新实时日志显示
            updateLogDisplay();
            
            // 检查关键检查点
            checkLogForKeywords(logMessage);
        };

        function updateLogDisplay() {
            const logOutput = document.getElementById('logOutput');
            logOutput.textContent = logBuffer.slice(-50).join('\n'); // 只显示最近50条
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function checkLogForKeywords(message) {
            // 检查点1：等待API状态设置
            if (message.includes('🎯 开始等待API响应，缓存WebSocket消息')) {
                updateCheckpoint(1, true, '检测到API等待状态设置');
                checkpoints.apiWaitStart = true;
            }
            
            // 检查点2：等待状态验证
            if (message.includes('🔍 等待API状态: true')) {
                updateCheckpoint(2, true, '检测到等待状态为true');
                checkpoints.waitingStateTrue = true;
            } else if (message.includes('🔍 等待API状态: false')) {
                updateCheckpoint(2, false, '⚠️ 等待状态为false，可能存在问题');
            }
            
            // 检查点3：消息缓存
            if (message.includes('📦 缓存早期到达的WebSocket消息')) {
                updateCheckpoint(3, true, '检测到WebSocket消息缓存');
                checkpoints.messageCache = true;
            }
            
            // 检查点4：API调用完成
            if (message.includes('🎉 批量处理任务启动成功!')) {
                updateCheckpoint(4, true, '检测到API调用完成');
                checkpoints.apiComplete = true;
            }
            
            // 检查点5：缓存消息处理
            if (message.includes('📦 处理') && message.includes('条缓存的WebSocket消息')) {
                updateCheckpoint(5, true, '检测到缓存消息处理');
                checkpoints.cacheProcess = true;
            }
            
            // 检查点6：任务ID匹配
            if (message.includes('📡 是否为当前任务: true')) {
                updateCheckpoint(6, true, '检测到任务ID匹配成功');
                checkpoints.taskMatch = true;
            }
        }

        function updateCheckpoint(number, success, message) {
            const checkpointElements = document.querySelectorAll('#checkpoints .test-result');
            const checkpoint = checkpointElements[number - 1];
            
            if (success) {
                checkpoint.className = 'test-result success';
                checkpoint.querySelector('.status-indicator').className = 'status-indicator status-success';
                checkpoint.innerHTML = checkpoint.innerHTML.replace(/期望看到.*/, `✅ ${message}`);
            } else {
                checkpoint.className = 'test-result error';
                checkpoint.querySelector('.status-indicator').className = 'status-indicator status-error';
                checkpoint.innerHTML = checkpoint.innerHTML.replace(/期望看到.*/, `❌ ${message}`);
            }
        }

        function startTest() {
            testStartTime = Date.now();
            logBuffer = [];
            
            // 重置检查点
            Object.keys(checkpoints).forEach(key => checkpoints[key] = false);
            
            console.log('🧪 ========== 开始WebSocket缓存机制测试 ==========');
            console.log('📅 测试时间:', new Date().toISOString());
            console.log('🎯 测试目标: 验证WebSocket消息缓存机制');
            console.log('');
            
            // 提示用户进行操作
            alert('测试已开始！\n\n请按以下步骤操作：\n1. 选择2个图片文件\n2. 点击"开始处理"按钮\n3. 观察控制台日志输出\n\n测试页面将自动监控关键日志并分析结果。');
        }

        function clearLogs() {
            logBuffer = [];
            updateLogDisplay();
            console.log('🧹 日志已清空');
        }

        function exportLogs() {
            const logContent = logBuffer.join('\n');
            const blob = new Blob([logContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            
            const a = document.createElement('a');
            a.href = url;
            a.download = `websocket-test-logs-${new Date().toISOString().substr(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            console.log('📁 日志已导出');
        }

        function analyzeResults() {
            const analysisDiv = document.getElementById('analysisResult');
            const testDuration = testStartTime ? (Date.now() - testStartTime) / 1000 : 0;
            
            let passedChecks = 0;
            let totalChecks = Object.keys(checkpoints).length;
            
            Object.values(checkpoints).forEach(passed => {
                if (passed) passedChecks++;
            });
            
            let resultClass = 'error';
            let resultIcon = '❌';
            let resultText = '测试失败';
            
            if (passedChecks === totalChecks) {
                resultClass = 'success';
                resultIcon = '✅';
                resultText = '测试完全通过';
            } else if (passedChecks > totalChecks / 2) {
                resultClass = 'warning';
                resultIcon = '⚠️';
                resultText = '测试部分通过';
            }
            
            analysisDiv.innerHTML = `
                <div class="test-result ${resultClass}">
                    <h3>${resultIcon} ${resultText}</h3>
                    <p><strong>通过检查点：</strong>${passedChecks}/${totalChecks}</p>
                    <p><strong>测试耗时：</strong>${testDuration.toFixed(1)}秒</p>
                    <p><strong>成功率：</strong>${((passedChecks/totalChecks)*100).toFixed(1)}%</p>
                </div>
                
                <div class="test-result">
                    <h4>📊 详细分析：</h4>
                    <ul>
                        <li>API等待状态设置: ${checkpoints.apiWaitStart ? '✅ 通过' : '❌ 失败'}</li>
                        <li>等待状态验证: ${checkpoints.waitingStateTrue ? '✅ 通过' : '❌ 失败'}</li>
                        <li>消息缓存功能: ${checkpoints.messageCache ? '✅ 通过' : '❌ 失败'}</li>
                        <li>API调用完成: ${checkpoints.apiComplete ? '✅ 通过' : '❌ 失败'}</li>
                        <li>缓存消息处理: ${checkpoints.cacheProcess ? '✅ 通过' : '❌ 失败'}</li>
                        <li>任务ID匹配: ${checkpoints.taskMatch ? '✅ 通过' : '❌ 失败'}</li>
                    </ul>
                </div>
                
                <div class="test-result">
                    <h4>🔧 问题诊断：</h4>
                    ${generateDiagnostics()}
                </div>
            `;
            
            console.log('📈 ========== 测试结果分析 ==========');
            console.log(`📊 通过检查点: ${passedChecks}/${totalChecks}`);
            console.log(`⏱️ 测试耗时: ${testDuration.toFixed(1)}秒`);
            console.log(`📈 成功率: ${((passedChecks/totalChecks)*100).toFixed(1)}%`);
            console.log('🔧 问题诊断:', generateDiagnostics());
        }

        function generateDiagnostics() {
            const issues = [];
            
            if (!checkpoints.apiWaitStart) {
                issues.push('❌ 未检测到API等待状态设置，可能setIsWaitingForApi(true)未执行');
            }
            
            if (!checkpoints.waitingStateTrue) {
                issues.push('❌ 等待状态不为true，可能useEffect依赖数组问题未解决');
            }
            
            if (!checkpoints.messageCache) {
                issues.push('❌ 未检测到消息缓存，可能缓存逻辑有问题');
            }
            
            if (!checkpoints.cacheProcess) {
                issues.push('❌ 未检测到缓存消息处理，可能重新处理逻辑有问题');
            }
            
            if (!checkpoints.taskMatch) {
                issues.push('❌ 任务ID匹配失败，可能双重匹配机制有问题');
            }
            
            if (issues.length === 0) {
                return '<p style="color: green;">✅ 所有功能正常工作！</p>';
            }
            
            return '<ul>' + issues.map(issue => `<li>${issue}</li>`).join('') + '</ul>';
        }

        // 页面加载完成后的初始化
        window.onload = function() {
            console.log('🧪 WebSocket缓存机制测试页面已加载');
            console.log('📋 请点击"开始测试"按钮开始测试');
        };
    </script>
</body>
</html>
