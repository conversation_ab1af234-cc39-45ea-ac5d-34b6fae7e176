/**
 * 预览状态同步测试
 * 验证 FileListItem 显示的状态和 PreviewPanel 显示的内容是否一致
 */

import { describe, it, expect, beforeEach } from 'vitest'
import { useFileStore } from '../src/stores/fileStore'
import { FileStatus } from '../src/types'

describe('预览状态同步测试', () => {
  beforeEach(() => {
    // 清空 store
    useFileStore.getState().clearFiles()
  })

  it('应该在 WebSocket 完成但轮询未设置 processedPath 时正确显示状态', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 100,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 模拟 WebSocket 完成消息：只设置状态，不设置 processedPath
    store.updateFileStatus('test-file-1', FileStatus.Completed)
    store.updateFileProcessingResult('test-file-1', {
      processingEndTime: new Date(),
      detectionCount: 2,
      avgConfidence: 0.85
    })

    // 验证文件状态
    const file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBeUndefined() // 关键：processedPath 还没有设置

    // 这就是问题所在：
    // - FileListItem 会显示 "已完成" (因为 status === 'completed')
    // - PreviewPanel 会显示原图 (因为 processedPath 不存在)
    console.log('🔍 问题重现:')
    console.log('  - FileListItem 显示状态:', file?.status === FileStatus.Completed ? '已完成' : '其他')
    console.log('  - PreviewPanel 显示内容:', file?.processedPath ? '处理后图片' : '原图')
  })

  it('应该在轮询设置 processedPath 后正确显示处理结果', () => {
    const store = useFileStore.getState()
    
    // 添加测试文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Completed, // 已经通过 WebSocket 设置为完成
      progress: 100,
      detectionCount: 2,
      avgConfidence: 0.85,
      processingStartTime: new Date(),
      processingEndTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 模拟轮询设置 processedPath
    const processedPath = 'outputs/processed_test-image.jpg'
    store.updateFileProcessedPath('test-file-1', processedPath)

    // 验证最终状态
    const file = store.getFileById('test-file-1')
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBe(processedPath)

    console.log('✅ 修复后状态:')
    console.log('  - FileListItem 显示状态:', file?.status === FileStatus.Completed ? '已完成' : '其他')
    console.log('  - PreviewPanel 显示内容:', file?.processedPath ? '处理后图片' : '原图')
  })

  it('应该验证 FileListItem 和 PreviewPanel 的判断条件差异', () => {
    const store = useFileStore.getState()
    
    // 创建两种状态的文件进行对比
    const files = [
      {
        id: 'file-websocket-only',
        name: 'websocket-only.jpg',
        path: '/test/websocket-only.jpg',
        size: 1024000,
        type: 'image/jpeg',
        status: FileStatus.Completed, // WebSocket 设置
        // processedPath: undefined, // 轮询还没设置
        progress: 100,
        detectionCount: 2,
        avgConfidence: 0.85,
        processingStartTime: new Date(),
        processingEndTime: new Date(),
        dimensions: { width: 1920, height: 1080 }
      },
      {
        id: 'file-complete',
        name: 'complete.jpg',
        path: '/test/complete.jpg',
        size: 1024000,
        type: 'image/jpeg',
        status: FileStatus.Completed, // WebSocket 设置
        processedPath: 'outputs/processed_complete.jpg', // 轮询设置
        progress: 100,
        detectionCount: 3,
        avgConfidence: 0.92,
        processingStartTime: new Date(),
        processingEndTime: new Date(),
        dimensions: { width: 1920, height: 1080 }
      }
    ]

    store.addFileItems(files)

    const file1 = store.getFileById('file-websocket-only')
    const file2 = store.getFileById('file-complete')

    // 验证 FileListItem 的显示逻辑
    const fileListItem1_showsCompleted = file1?.status === FileStatus.Completed
    const fileListItem1_showsButtons = file1?.status === FileStatus.Completed && !!file1?.processedPath
    
    const fileListItem2_showsCompleted = file2?.status === FileStatus.Completed
    const fileListItem2_showsButtons = file2?.status === FileStatus.Completed && !!file2?.processedPath

    // 验证 PreviewPanel 的显示逻辑
    const previewPanel1_showsProcessed = !!file1?.processedPath
    const previewPanel2_showsProcessed = !!file2?.processedPath

    console.log('📊 状态对比分析:')
    console.log('文件1 (WebSocket完成，轮询未完成):')
    console.log('  - FileListItem 显示"已完成":', fileListItem1_showsCompleted)
    console.log('  - FileListItem 显示操作按钮:', fileListItem1_showsButtons)
    console.log('  - PreviewPanel 显示处理后图片:', previewPanel1_showsProcessed)
    
    console.log('文件2 (完全完成):')
    console.log('  - FileListItem 显示"已完成":', fileListItem2_showsCompleted)
    console.log('  - FileListItem 显示操作按钮:', fileListItem2_showsButtons)
    console.log('  - PreviewPanel 显示处理后图片:', previewPanel2_showsProcessed)

    // 验证问题：文件1 显示已完成但无法查看处理结果
    expect(fileListItem1_showsCompleted).toBe(true) // FileListItem 显示已完成
    expect(previewPanel1_showsProcessed).toBe(false) // 但 PreviewPanel 显示原图
    expect(fileListItem1_showsButtons).toBe(false) // 且没有操作按钮

    // 验证正常情况：文件2 完全正常
    expect(fileListItem2_showsCompleted).toBe(true)
    expect(previewPanel2_showsProcessed).toBe(true)
    expect(fileListItem2_showsButtons).toBe(true)
  })

  it('应该模拟实际的时序问题', async () => {
    const store = useFileStore.getState()
    
    // 添加处理中的文件
    const testFile = {
      id: 'test-file-1',
      name: 'test-image.jpg',
      path: '/test/path/test-image.jpg',
      size: 1024000,
      type: 'image/jpeg',
      status: FileStatus.Processing,
      progress: 50,
      detectionCount: 0,
      avgConfidence: 0,
      processingStartTime: new Date(),
      dimensions: { width: 1920, height: 1080 }
    }

    store.addFileItems([testFile])

    // 模拟 WebSocket 完成消息（立即）
    console.log('🔄 模拟 WebSocket 完成消息...')
    store.updateFileStatus('test-file-1', FileStatus.Completed)
    store.updateFileProcessingResult('test-file-1', {
      processingEndTime: new Date(),
      detectionCount: 2,
      avgConfidence: 0.85
    })

    let file = store.getFileById('test-file-1')
    console.log('📊 WebSocket 完成后状态:')
    console.log('  - status:', file?.status)
    console.log('  - processedPath:', file?.processedPath)
    console.log('  - FileListItem 显示:', file?.status === FileStatus.Completed ? '已完成' : '处理中')
    console.log('  - PreviewPanel 显示:', file?.processedPath ? '处理后图片' : '原图')

    // 模拟轮询延迟（几秒后）
    await new Promise(resolve => setTimeout(resolve, 100)) // 模拟延迟

    console.log('🔄 模拟轮询设置 processedPath...')
    store.updateFileProcessedPath('test-file-1', 'outputs/processed_test-image.jpg')

    file = store.getFileById('test-file-1')
    console.log('📊 轮询完成后状态:')
    console.log('  - status:', file?.status)
    console.log('  - processedPath:', file?.processedPath)
    console.log('  - FileListItem 显示:', file?.status === FileStatus.Completed ? '已完成' : '处理中')
    console.log('  - PreviewPanel 显示:', file?.processedPath ? '处理后图片' : '原图')

    // 验证最终状态正确
    expect(file?.status).toBe(FileStatus.Completed)
    expect(file?.processedPath).toBe('outputs/processed_test-image.jpg')
  })
})
