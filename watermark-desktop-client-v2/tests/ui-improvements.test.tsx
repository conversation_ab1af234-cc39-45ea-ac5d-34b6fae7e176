import { describe, it, expect, beforeEach } from 'vitest'
import { useUIStore } from '../src/stores/uiStore'
import { useFileStore } from '../src/stores/fileStore'

// 清理状态在每个测试之前
beforeEach(() => {
  localStorage.clear()
  // 重置 stores
  useUIStore.setState({ sidebarCollapsed: false })
  useFileStore.setState({ files: [], selectedFile: null, fileObjects: new Map() })
})

describe('UI Improvements', () => {
  describe('Sidebar Collapse', () => {
    it('should initialize with expanded sidebar', () => {
      const store = useUIStore.getState()
      expect(store.sidebarCollapsed).toBe(false)
    })

    it('should toggle sidebar collapse state', () => {
      const store = useUIStore.getState()
      
      // 初始状态：展开
      expect(store.sidebarCollapsed).toBe(false)
      
      // 切换到折叠
      store.toggleSidebar()
      const updatedStore = useUIStore.getState()
      expect(updatedStore.sidebarCollapsed).toBe(true)
      
      // 再次切换到展开
      store.toggleSidebar()
      const finalStore = useUIStore.getState()
      expect(finalStore.sidebarCollapsed).toBe(false)
    })

    it('should set sidebar collapsed state directly', () => {
      const store = useUIStore.getState()
      
      store.setSidebarCollapsed(true)
      expect(useUIStore.getState().sidebarCollapsed).toBe(true)
      
      store.setSidebarCollapsed(false)
      expect(useUIStore.getState().sidebarCollapsed).toBe(false)
    })
  })

  describe('File Store Integration', () => {
    it('should handle empty file list', () => {
      const store = useFileStore.getState()
      expect(store.files).toEqual([])
      expect(store.selectedFile).toBe(null)
    })

    it('should add files to store', () => {
      const store = useFileStore.getState()
      
      // 模拟文件对象
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      
      store.addFiles([mockFile])
      
      const updatedStore = useFileStore.getState()
      expect(updatedStore.files).toHaveLength(1)
      expect(updatedStore.files[0].name).toBe('test.jpg')
    })

    it('should clear files from store', () => {
      const store = useFileStore.getState()
      
      // 先添加文件
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' })
      store.addFiles([mockFile])
      
      // 验证文件已添加
      expect(useFileStore.getState().files).toHaveLength(1)
      
      // 清空文件
      store.clearFiles()
      
      // 验证文件已清空
      const finalStore = useFileStore.getState()
      expect(finalStore.files).toHaveLength(0)
      expect(finalStore.selectedFile).toBe(null)
    })
  })

  describe('State Persistence', () => {
    it('should persist sidebar state', () => {
      const store = useUIStore.getState()
      
      // 设置折叠状态
      store.setSidebarCollapsed(true)
      
      // 创建新的 store 实例来模拟页面刷新
      const newStore = useUIStore.getState()
      expect(newStore.sidebarCollapsed).toBe(true)
    })
  })
})
