#!/bin/bash

# 构建后端服务脚本
# 用于确保 Python 后端在打包时包含所有必要的依赖

set -e

echo "🔧 开始构建后端服务..."

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
BACKEND_DIR="$PROJECT_ROOT/python-backend"

echo "📁 项目根目录: $PROJECT_ROOT"
echo "📁 后端目录: $BACKEND_DIR"

# 检查后端目录是否存在
if [ ! -d "$BACKEND_DIR" ]; then
    echo "❌ 后端目录不存在: $BACKEND_DIR"
    exit 1
fi

cd "$BACKEND_DIR"

# 检查 Python 环境
if ! command -v python &> /dev/null; then
    echo "❌ Python 未安装或不在 PATH 中"
    exit 1
fi

echo "🐍 Python 版本: $(python --version)"

# 检查依赖文件
if [ ! -f "requirements.txt" ]; then
    echo "❌ requirements.txt 不存在"
    exit 1
fi

# 创建虚拟环境（如果不存在）
if [ ! -d "venv" ]; then
    echo "📦 创建虚拟环境..."
    python -m venv venv
fi

# 激活虚拟环境
echo "🔄 激活虚拟环境..."
source venv/bin/activate

# 升级 pip
echo "⬆️ 升级 pip..."
pip install --upgrade pip

# 安装依赖
echo "📦 安装 Python 依赖..."
pip install -r requirements.txt

# 检查关键依赖
echo "🔍 检查关键依赖..."
python -c "import fastapi; print('✅ FastAPI 已安装')"
python -c "import uvicorn; print('✅ Uvicorn 已安装')"
python -c "import torch; print('✅ PyTorch 已安装')"
python -c "import cv2; print('✅ OpenCV 已安装')"

# 测试后端服务启动
echo "🧪 测试后端服务..."
timeout 10s python main.py &
BACKEND_PID=$!

# 等待服务启动
sleep 5

# 检查服务是否响应
if curl -f http://localhost:8000/api/v1/health > /dev/null 2>&1; then
    echo "✅ 后端服务测试通过"
else
    echo "⚠️ 后端服务测试失败，但继续构建"
fi

# 停止测试服务
kill $BACKEND_PID 2>/dev/null || true

echo "✅ 后端服务构建完成"

# 创建启动脚本
echo "📝 创建启动脚本..."
cat > start_backend.sh << 'EOF'
#!/bin/bash
# 后端服务启动脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 激活虚拟环境
if [ -d "venv" ]; then
    source venv/bin/activate
fi

# 启动服务
python main.py
EOF

chmod +x start_backend.sh

echo "🎉 后端服务构建完成！"
echo "📋 构建摘要:"
echo "   - Python 环境: $(python --version)"
echo "   - 虚拟环境: $BACKEND_DIR/venv"
echo "   - 启动脚本: $BACKEND_DIR/start_backend.sh"
echo "   - 主文件: $BACKEND_DIR/main.py"
