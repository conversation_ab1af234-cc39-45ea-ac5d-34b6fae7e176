@echo off
setlocal enabledelayedexpansion

REM 构建后端服务脚本 (Windows)
REM 用于确保 Python 后端在打包时包含所有必要的依赖

echo 🔧 开始构建后端服务...

REM 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."
set "BACKEND_DIR=%PROJECT_ROOT%\python-backend"

echo 📁 项目根目录: %PROJECT_ROOT%
echo 📁 后端目录: %BACKEND_DIR%

REM 检查后端目录是否存在
if not exist "%BACKEND_DIR%" (
    echo ❌ 后端目录不存在: %BACKEND_DIR%
    exit /b 1
)

cd /d "%BACKEND_DIR%"

REM 检查 Python 环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在 PATH 中
    exit /b 1
)

echo 🐍 Python 版本:
python --version

REM 检查依赖文件
if not exist "requirements.txt" (
    echo ❌ requirements.txt 不存在
    exit /b 1
)

REM 创建虚拟环境（如果不存在）
if not exist "venv" (
    echo 📦 创建虚拟环境...
    python -m venv venv
)

REM 激活虚拟环境
echo 🔄 激活虚拟环境...
call venv\Scripts\activate.bat

REM 升级 pip
echo ⬆️ 升级 pip...
python -m pip install --upgrade pip

REM 安装依赖
echo 📦 安装 Python 依赖...
pip install -r requirements.txt

REM 检查关键依赖
echo 🔍 检查关键依赖...
python -c "import fastapi; print('✅ FastAPI 已安装')"
python -c "import uvicorn; print('✅ Uvicorn 已安装')"
python -c "import torch; print('✅ PyTorch 已安装')"
python -c "import cv2; print('✅ OpenCV 已安装')"

REM 创建启动脚本
echo 📝 创建启动脚本...
(
echo @echo off
echo REM 后端服务启动脚本
echo.
echo set "SCRIPT_DIR=%%~dp0"
echo cd /d "%%SCRIPT_DIR%%"
echo.
echo REM 激活虚拟环境
echo if exist "venv\Scripts\activate.bat" ^(
echo     call venv\Scripts\activate.bat
echo ^)
echo.
echo REM 启动服务
echo python main.py
) > start_backend.bat

echo ✅ 后端服务构建完成
echo 📋 构建摘要:
python --version
echo    - 虚拟环境: %BACKEND_DIR%\venv
echo    - 启动脚本: %BACKEND_DIR%\start_backend.bat
echo    - 主文件: %BACKEND_DIR%\main.py

pause
