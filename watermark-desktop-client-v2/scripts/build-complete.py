#!/usr/bin/env python3
"""
完整的应用构建脚本
自动构建后端和前端，生成最终的桌面应用
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json
import time

class BuildManager:
    def __init__(self):
        self.script_dir = Path(__file__).parent
        self.project_root = self.script_dir.parent
        self.backend_dir = self.project_root / "python-backend"
        self.frontend_dir = self.project_root
        self.build_log = []
        
    def log(self, message, level="INFO"):
        """记录构建日志"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        print(log_entry)
        self.build_log.append(log_entry)
    
    def run_command(self, cmd, cwd=None, description=""):
        """运行命令并处理错误"""
        if description:
            self.log(f"🚀 {description}")
        
        try:
            result = subprocess.run(
                cmd, 
                cwd=cwd or self.project_root,
                check=True,
                capture_output=True,
                text=True,
                shell=True if isinstance(cmd, str) else False
            )
            self.log(f"✅ {description or '命令'} 成功")
            return True
        except subprocess.CalledProcessError as e:
            self.log(f"❌ {description or '命令'} 失败: {e}", "ERROR")
            if e.stdout:
                self.log(f"输出: {e.stdout}", "DEBUG")
            if e.stderr:
                self.log(f"错误: {e.stderr}", "ERROR")
            return False
    
    def check_prerequisites(self):
        """检查构建前提条件"""
        self.log("🔍 检查构建前提条件...")
        
        # 检查 Python
        try:
            python_version = subprocess.check_output([sys.executable, "--version"], text=True).strip()
            self.log(f"✅ Python: {python_version}")
        except:
            self.log("❌ Python 未找到", "ERROR")
            return False
        
        # 检查 Node.js 和 pnpm
        try:
            node_version = subprocess.check_output(["node", "--version"], text=True).strip()
            self.log(f"✅ Node.js: {node_version}")
        except:
            self.log("❌ Node.js 未找到", "ERROR")
            return False
        
        try:
            pnpm_version = subprocess.check_output(["pnpm", "--version"], text=True).strip()
            self.log(f"✅ pnpm: {pnpm_version}")
        except:
            self.log("❌ pnpm 未找到", "ERROR")
            return False
        
        # 检查 Rust 和 Tauri
        try:
            cargo_version = subprocess.check_output(["cargo", "--version"], text=True).strip()
            self.log(f"✅ Cargo: {cargo_version}")
        except:
            self.log("❌ Cargo 未找到", "ERROR")
            return False
        
        # 检查项目文件
        required_files = [
            self.backend_dir / "main.py",
            self.backend_dir / "requirements.txt",
            self.frontend_dir / "package.json",
            self.frontend_dir / "src-tauri" / "tauri.conf.json"
        ]
        
        for file_path in required_files:
            if file_path.exists():
                self.log(f"✅ {file_path.name}")
            else:
                self.log(f"❌ 缺少文件: {file_path}", "ERROR")
                return False
        
        return True
    
    def install_dependencies(self):
        """安装依赖"""
        self.log("📦 安装依赖...")
        
        # 安装 Python 依赖
        if not self.run_command(
            [sys.executable, "-m", "pip", "install", "-r", "requirements.txt"],
            cwd=self.backend_dir,
            description="安装 Python 依赖"
        ):
            return False
        
        # 安装 PyInstaller
        if not self.run_command(
            [sys.executable, "-m", "pip", "install", "pyinstaller"],
            description="安装 PyInstaller"
        ):
            return False
        
        # 安装前端依赖
        if not self.run_command(
            ["pnpm", "install"],
            cwd=self.frontend_dir,
            description="安装前端依赖"
        ):
            return False
        
        return True
    
    def build_backend(self):
        """构建后端"""
        self.log("🔧 构建 Python 后端...")
        
        # 运行后端构建脚本
        build_script = self.backend_dir / "build_standalone.py"
        if not build_script.exists():
            self.log("❌ 后端构建脚本不存在", "ERROR")
            return False
        
        return self.run_command(
            [sys.executable, str(build_script)],
            cwd=self.backend_dir,
            description="构建后端可执行文件"
        )
    
    def build_frontend(self):
        """构建前端"""
        self.log("🎨 构建前端应用...")
        
        # 构建前端
        if not self.run_command(
            ["pnpm", "build"],
            cwd=self.frontend_dir,
            description="构建前端资源"
        ):
            return False
        
        # 构建 Tauri 应用
        return self.run_command(
            ["pnpm", "tauri", "build"],
            cwd=self.frontend_dir,
            description="构建 Tauri 应用"
        )
    
    def verify_build(self):
        """验证构建结果"""
        self.log("🔍 验证构建结果...")
        
        # 检查后端可执行文件
        backend_exe = self.project_root / "src-tauri" / "resources" / "watermark-backend"
        if backend_exe.exists():
            size_mb = backend_exe.stat().st_size / 1024 / 1024
            self.log(f"✅ 后端可执行文件: {size_mb:.1f} MB")
        else:
            self.log("❌ 后端可执行文件不存在", "ERROR")
            return False
        
        # 检查模型文件
        models_dir = self.project_root / "src-tauri" / "resources" / "models"
        if models_dir.exists():
            total_size = sum(f.stat().st_size for f in models_dir.rglob('*') if f.is_file())
            self.log(f"✅ 模型文件: {total_size / 1024 / 1024:.1f} MB")
        else:
            self.log("⚠️ 模型文件目录不存在", "WARNING")
        
        # 检查 Tauri 构建输出
        bundle_dir = self.project_root / "src-tauri" / "target" / "release" / "bundle"
        if bundle_dir.exists():
            self.log("✅ Tauri 构建输出存在")
            
            # 列出生成的安装包
            for bundle_type in bundle_dir.iterdir():
                if bundle_type.is_dir():
                    for bundle_file in bundle_type.iterdir():
                        if bundle_file.is_file():
                            size_mb = bundle_file.stat().st_size / 1024 / 1024
                            self.log(f"📦 {bundle_file.name}: {size_mb:.1f} MB")
        else:
            self.log("❌ Tauri 构建输出不存在", "ERROR")
            return False
        
        return True
    
    def save_build_log(self):
        """保存构建日志"""
        log_file = self.project_root / "build.log"
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(self.build_log))
        self.log(f"📝 构建日志已保存: {log_file}")
    
    def build_all(self):
        """执行完整构建流程"""
        self.log("🚀 开始完整构建流程...")
        
        try:
            # 检查前提条件
            if not self.check_prerequisites():
                self.log("❌ 前提条件检查失败", "ERROR")
                return False
            
            # 安装依赖
            if not self.install_dependencies():
                self.log("❌ 依赖安装失败", "ERROR")
                return False
            
            # 构建后端
            if not self.build_backend():
                self.log("❌ 后端构建失败", "ERROR")
                return False
            
            # 构建前端
            if not self.build_frontend():
                self.log("❌ 前端构建失败", "ERROR")
                return False
            
            # 验证构建
            if not self.verify_build():
                self.log("❌ 构建验证失败", "ERROR")
                return False
            
            self.log("🎉 完整构建成功！")
            return True
            
        except Exception as e:
            self.log(f"❌ 构建过程中发生异常: {e}", "ERROR")
            return False
        finally:
            self.save_build_log()

def main():
    """主函数"""
    print("🎯 水印去除应用完整构建工具")
    print("=" * 50)
    
    builder = BuildManager()
    success = builder.build_all()
    
    print("=" * 50)
    if success:
        print("🎉 构建完成！可以在 src-tauri/target/release/bundle 目录找到安装包")
    else:
        print("❌ 构建失败，请检查错误信息")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
