#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

echo
echo "========================================"
print_info "🎯 水印去除应用一键构建工具"
echo "========================================"
echo

# 获取脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"
BACKEND_DIR="$PROJECT_ROOT/python-backend"
BUILD_SCRIPT="$PROJECT_ROOT/scripts/build-complete.py"

print_info "📁 项目根目录: $PROJECT_ROOT"
print_info "📁 后端目录: $BACKEND_DIR"
echo

# 检查 Python
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        print_error "❌ Python 未安装或不在 PATH 中"
        print_error "请先安装 Python 3.8+ 并添加到 PATH"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

print_success "✅ Python 检查通过"
$PYTHON_CMD --version

# 检查 Node.js
if ! command -v node &> /dev/null; then
    print_error "❌ Node.js 未安装或不在 PATH 中"
    print_error "请先安装 Node.js 并添加到 PATH"
    exit 1
fi

print_success "✅ Node.js 检查通过"
node --version

# 检查 pnpm
if ! command -v pnpm &> /dev/null; then
    print_warning "❌ pnpm 未安装，正在安装..."
    npm install -g pnpm
    if [ $? -ne 0 ]; then
        print_error "❌ pnpm 安装失败"
        exit 1
    fi
fi

print_success "✅ pnpm 检查通过"
pnpm --version

# 检查 Rust
if ! command -v cargo &> /dev/null; then
    print_error "❌ Rust 未安装或不在 PATH 中"
    print_error "请先安装 Rust 并添加到 PATH"
    print_error "访问: https://rustup.rs/"
    exit 1
fi

print_success "✅ Rust 检查通过"
cargo --version

echo
print_info "🚀 开始构建流程..."
echo

# 运行完整构建脚本
if [ -f "$BUILD_SCRIPT" ]; then
    print_info "📋 使用完整构建脚本: $BUILD_SCRIPT"
    $PYTHON_CMD "$BUILD_SCRIPT"
    BUILD_RESULT=$?
else
    print_info "📋 使用简化构建流程"
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 安装前端依赖
    print_info "📦 安装前端依赖..."
    pnpm install
    if [ $? -ne 0 ]; then
        print_error "❌ 前端依赖安装失败"
        exit 1
    fi
    
    # 安装后端依赖
    print_info "📦 安装后端依赖..."
    cd "$BACKEND_DIR"
    $PYTHON_CMD -m pip install -r requirements.txt
    if [ $? -ne 0 ]; then
        print_error "❌ 后端依赖安装失败"
        exit 1
    fi
    
    # 安装 PyInstaller
    print_info "📦 安装 PyInstaller..."
    $PYTHON_CMD -m pip install pyinstaller
    if [ $? -ne 0 ]; then
        print_error "❌ PyInstaller 安装失败"
        exit 1
    fi
    
    # 构建后端
    print_info "🔧 构建后端..."
    $PYTHON_CMD build_standalone.py
    if [ $? -ne 0 ]; then
        print_error "❌ 后端构建失败"
        exit 1
    fi
    
    # 切换回项目根目录
    cd "$PROJECT_ROOT"
    
    # 构建前端
    print_info "🎨 构建前端..."
    pnpm build
    if [ $? -ne 0 ]; then
        print_error "❌ 前端构建失败"
        exit 1
    fi
    
    # 构建 Tauri 应用
    print_info "📦 构建 Tauri 应用..."
    pnpm tauri build
    if [ $? -ne 0 ]; then
        print_error "❌ Tauri 构建失败"
        exit 1
    fi
    
    BUILD_RESULT=0
fi

echo
echo "========================================"
if [ $BUILD_RESULT -eq 0 ]; then
    print_success "🎉 构建完成！"
    echo
    print_info "📦 安装包位置:"
    print_info "   $PROJECT_ROOT/src-tauri/target/release/bundle/"
    echo
    print_info "🔍 查看构建输出:"
    
    # 检查不同平台的构建输出
    if [ -d "$PROJECT_ROOT/src-tauri/target/release/bundle/deb" ]; then
        print_info "   Linux DEB: src-tauri/target/release/bundle/deb/"
    fi
    
    if [ -d "$PROJECT_ROOT/src-tauri/target/release/bundle/appimage" ]; then
        print_info "   Linux AppImage: src-tauri/target/release/bundle/appimage/"
    fi
    
    if [ -d "$PROJECT_ROOT/src-tauri/target/release/bundle/dmg" ]; then
        print_info "   macOS DMG: src-tauri/target/release/bundle/dmg/"
    fi
    
    if [ -d "$PROJECT_ROOT/src-tauri/target/release/bundle/app" ]; then
        print_info "   macOS APP: src-tauri/target/release/bundle/app/"
    fi
    
    # 列出实际生成的文件
    echo
    print_info "📋 生成的文件:"
    find "$PROJECT_ROOT/src-tauri/target/release/bundle" -type f -name "*" 2>/dev/null | while read file; do
        size=$(du -h "$file" | cut -f1)
        basename_file=$(basename "$file")
        print_info "   $basename_file ($size)"
    done
    
else
    print_error "❌ 构建失败！"
    print_error "请检查上面的错误信息"
fi
echo "========================================"

exit $BUILD_RESULT
