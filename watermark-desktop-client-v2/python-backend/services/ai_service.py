#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 服务模块
使用本地复制的完整水印去除管道
"""

import asyncio
import gc
import time
from pathlib import Path
from typing import Dict, Any, Optional, List

import torch
import numpy as np
from PIL import Image
from loguru import logger

# 使用本地复制的模块，不需要添加外部路径
from models.watermark_removal_pipeline import WatermarkRemovalPipeline
from utils.config import settings

logger.info("✅ 使用本地复制的水印去除管道")


class AIService:
    """AI 服务类 - 使用完整的水印去除管道"""
    
    def __init__(self):
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.is_ready = False
        
        logger.info(f"🔧 AI 服务初始化，设备: {self.device}")
    
    async def initialize(self):
        """初始化水印去除管道"""
        try:
            logger.info("🔄 正在加载水印去除管道...")
            
            # 使用根目录的完整水印去除管道
            await self._load_pipeline()
            
            self.is_ready = True
            logger.success("✅ 水印去除管道加载完成")
            
        except Exception as e:
            logger.error(f"❌ 水印去除管道加载失败: {e}")
            # 不抛出异常，允许应用启动，在实际使用时再尝试加载
            self.pipeline = None
            self.is_ready = False
    
    async def _load_pipeline(self):
        """加载水印去除管道"""
        try:
            logger.info("🔄 正在初始化水印去除管道...")
            
            # 检查模型文件是否存在
            if not settings.yolo_model_exists:
                logger.warning(f"⚠️ YOLO 模型文件不存在: {settings.yolo_model_path}")
            
            if not settings.lama_model_exists:
                logger.warning(f"⚠️ LAMA 模型文件不存在: {settings.lama_model_path}")
            
            # 创建水印去除管道
            self.pipeline = WatermarkRemovalPipeline(
                detector_model_path=settings.yolo_model_path,
                inpainter_model_path=settings.lama_model_path,
                device=str(self.device)
            )
            
            logger.success("✅ 水印去除管道初始化成功")
            
        except Exception as e:
            logger.error(f"❌ 水印去除管道加载失败: {e}")
            logger.warning("⚠️ 将在首次使用时重试加载")
            self.pipeline = None

    def ensure_pipeline_loaded(self):
        """确保管道已加载，如果未加载则尝试加载"""
        if self.pipeline is None:
            try:
                logger.info("🔄 尝试延迟加载水印去除管道...")
                self.pipeline = WatermarkRemovalPipeline(
                    detector_model_path=settings.yolo_model_path,
                    inpainter_model_path=settings.lama_model_path,
                    device=str(self.device)
                )
                logger.success("✅ 延迟加载成功")
                self.is_ready = True
            except Exception as e:
                logger.error(f"❌ 延迟加载失败: {e}")
                raise RuntimeError(f"水印去除管道加载失败: {e}")

    async def process_image(
        self,
        input_path: str,
        output_path: str,
        confidence_threshold: float = 0.3,
        enhance_mask: bool = True,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12,
        progress_callback=None,
        task_id: str = None
    ) -> Dict[str, Any]:
        """处理单张图像 - 使用完整的水印去除管道"""
        
        start_time = time.time()
        
        try:
            logger.info(f"🖼️ 开始处理图像: {input_path}")
            logger.info(f"🔧 进度回调函数: {progress_callback is not None}, 任务ID: {task_id}")

            # 进度回调函数
            async def report_progress(stage: str, progress: float, message: str = ""):
                logger.info(f"📡 尝试发送进度更新: {stage} - {progress}% - {message}")
                if progress_callback:
                    try:
                        await progress_callback(task_id, stage, progress, message)
                        logger.info(f"✅ 进度更新发送成功: {stage}")
                    except Exception as e:
                        logger.error(f"❌ 进度更新发送失败: {e}")
                else:
                    logger.warning("⚠️ 没有进度回调函数")

            # 阶段1: 初始化 (0-10%)
            await report_progress("initializing", 5, "正在初始化处理管道...")

            # 确保管道已加载
            self.ensure_pipeline_loaded()

            # 检查输入文件
            input_file = Path(input_path)
            if not input_file.exists():
                raise FileNotFoundError(f"输入文件不存在: {input_path}")

            await report_progress("loading", 10, "正在加载图像...")

            # 加载图像
            image = Image.open(input_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 阶段2: YOLO检测 (10-60%)
            await report_progress("detecting", 15, f"正在检测水印 (置信度: {confidence_threshold})...")
            logger.info(f"🔍 开始 YOLO 水印检测，置信度阈值: {confidence_threshold}")

            # 阶段3: LaMa修复 (60-95%)
            await report_progress("inpainting", 60, f"正在修复图像 (尺寸: {image.size})...")
            logger.info(f"🎨 开始 LaMa 图像修复，图像尺寸: {image.size}")

            # 使用完整的水印去除管道
            result_info = self.pipeline.remove_watermark(
                image=image,
                confidence_threshold=confidence_threshold,
                enhance_mask=enhance_mask,
                use_smart_enhancement=use_smart_enhancement,
                context_expansion_ratio=context_expansion_ratio,
                return_intermediate=True
            )

            # 阶段4: 保存结果 (95-100%)
            await report_progress("saving", 95, "正在保存处理结果...")
            
            # 保存结果图像
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            result_info['result_image'].save(output_path, quality=95)

            processing_time = time.time() - start_time

            # 阶段5: 完成 (100%)
            await report_progress("completed", 100, f"处理完成! 耗时: {processing_time:.1f}s")

            logger.success(f"✅ 图像处理完成: {output_path}, 耗时: {processing_time:.2f}s")
            logger.info(f"🎯 检测到 {result_info.get('detection_count', 0)} 个水印区域")
            logger.info(f"📊 平均置信度: {result_info.get('avg_confidence', 0):.3f}")

            # 清理内存
            del image
            if 'result_image' in result_info:
                del result_info['result_image']
            gc.collect()

            # 如果使用 GPU，清理 GPU 缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            return {
                "success": True,
                "input_path": input_path,
                "output_path": output_path,
                "processing_time": processing_time,
                "detections": result_info.get('detection_count', 0),
                "avg_confidence": result_info.get('avg_confidence', 0),
                "image_size": (0, 0),  # 避免保留图像尺寸引用
                "detection_time": result_info.get('detection_time', 0),
                "inpainting_time": result_info.get('inpainting_time', 0)
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ 图像处理失败: {input_path}, 错误: {e}")
            
            return {
                "success": False,
                "input_path": input_path,
                "output_path": output_path,
                "processing_time": processing_time,
                "error": str(e)
            }

    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 正在清理 AI 服务资源...")

            # 清理水印去除管道
            if self.pipeline:
                # 如果管道有清理方法，调用它
                if hasattr(self.pipeline, 'cleanup'):
                    self.pipeline.cleanup()
                del self.pipeline
                self.pipeline = None

            # 清理 GPU 缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            self.is_ready = False
            logger.info("✅ AI 服务资源清理完成")

        except Exception as e:
            logger.error(f"❌ 清理资源失败: {e}")
