#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI 服务模块
使用根目录的完整水印去除管道
"""

import asyncio
import time
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List

import torch
import numpy as np
from PIL import Image
from loguru import logger

# 添加根目录到 Python 路径以导入水印去除管道
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from models.watermark_removal_pipeline import WatermarkRemovalPipeline
from utils.config import settings


class AIService:
    """AI 服务类 - 使用完整的水印去除管道"""

    def __init__(self):
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.is_ready = False

        logger.info(f"🔧 AI 服务初始化，设备: {self.device}")

    async def initialize(self):
        """初始化水印去除管道"""
        try:
            logger.info("🔄 正在加载水印去除管道...")

            # 使用根目录的完整水印去除管道
            await self._load_pipeline()

            self.is_ready = True
            logger.success("✅ 水印去除管道加载完成")

        except Exception as e:
            logger.error(f"❌ 水印去除管道加载失败: {e}")
            # 不抛出异常，允许应用启动，在实际使用时再尝试加载
            self.pipeline = None
            self.is_ready = False
    
    async def _load_pipeline(self):
        """加载水印去除管道"""
        try:
            logger.info("🔄 正在初始化水印去除管道...")

            # 检查模型文件是否存在
            if not settings.yolo_model_exists:
                logger.warning(f"⚠️ YOLO 模型文件不存在: {settings.yolo_model_path}")

            if not settings.lama_model_exists:
                logger.warning(f"⚠️ LAMA 模型文件不存在: {settings.lama_model_path}")

            # 创建水印去除管道
            self.pipeline = WatermarkRemovalPipeline(
                detector_model_path=settings.yolo_model_path,
                inpainter_model_path=settings.lama_model_path,
                device=str(self.device)
            )

            logger.success("✅ 水印去除管道初始化成功")

        except Exception as e:
            logger.error(f"❌ 水印去除管道加载失败: {e}")
            logger.warning("⚠️ 将在首次使用时重试加载")
            self.pipeline = None
    
    def ensure_pipeline_loaded(self):
        """确保管道已加载，如果未加载则尝试加载"""
        if self.pipeline is None:
            try:
                logger.info("🔄 尝试延迟加载水印去除管道...")
                self.pipeline = WatermarkRemovalPipeline(
                    detector_model_path=settings.yolo_model_path,
                    inpainter_model_path=settings.lama_model_path,
                    device=str(self.device)
                )
                logger.success("✅ 延迟加载成功")
                self.is_ready = True
            except Exception as e:
                logger.error(f"❌ 延迟加载失败: {e}")
                raise RuntimeError(f"水印去除管道加载失败: {e}")

    async def process_image(
        self,
        input_path: str,
        output_path: str,
        confidence_threshold: float = 0.3,
        enhance_mask: bool = True,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12
    ) -> Dict[str, Any]:
        """处理单张图像 - 使用完整的水印去除管道"""

        start_time = time.time()

        try:
            logger.info(f"🖼️ 开始处理图像: {input_path}")

            # 确保管道已加载
            self.ensure_pipeline_loaded()

            # 检查输入文件
            input_file = Path(input_path)
            if not input_file.exists():
                raise FileNotFoundError(f"输入文件不存在: {input_path}")

            # 加载图像
            image = Image.open(input_path)
            if image.mode != 'RGB':
                image = image.convert('RGB')

            logger.info(f"🔍 开始 YOLO 水印检测，置信度阈值: {confidence_threshold}")
            logger.info(f"🎨 开始 LaMa 图像修复，图像尺寸: {image.size}")

            # 使用完整的水印去除管道
            result_info = self.pipeline.remove_watermark(
                image=image,
                confidence_threshold=confidence_threshold,
                enhance_mask=enhance_mask,
                use_smart_enhancement=use_smart_enhancement,
                context_expansion_ratio=context_expansion_ratio,
                return_intermediate=True
            )

            # 保存结果图像
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            result_info['result_image'].save(output_path, quality=95)

            processing_time = time.time() - start_time

            logger.success(f"✅ 图像处理完成: {output_path}, 耗时: {processing_time:.2f}s")
            logger.info(f"🎯 检测到 {result_info.get('detection_count', 0)} 个水印区域")
            logger.info(f"📊 平均置信度: {result_info.get('avg_confidence', 0):.3f}")

            return {
                "success": True,
                "input_path": input_path,
                "output_path": output_path,
                "processing_time": processing_time,
                "detections": result_info.get('detection_count', 0),
                "avg_confidence": result_info.get('avg_confidence', 0),
                "image_size": image.size,
                "detection_time": result_info.get('detection_time', 0),
                "inpainting_time": result_info.get('inpainting_time', 0)
            }

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"❌ 图像处理失败: {input_path}, 错误: {e}")

            return {
                "success": False,
                "input_path": input_path,
                "output_path": output_path,
                "processing_time": processing_time,
                "error": str(e)
            }
    
    # 不再需要单独的检测方法，使用完整管道

    def _parse_yolo_results(self, results) -> List[Dict]:
        """解析 YOLO 检测结果"""
        detections = []

        try:
            for result in results:
                if hasattr(result, 'boxes') and result.boxes is not None:
                    boxes = result.boxes
                    for i in range(len(boxes)):
                        # 获取边界框坐标
                        bbox = boxes.xyxy[i].cpu().numpy().tolist()  # [x1, y1, x2, y2]
                        confidence = float(boxes.conf[i].cpu().numpy())
                        class_id = int(boxes.cls[i].cpu().numpy())

                        detections.append({
                            "bbox": bbox,
                            "confidence": confidence,
                            "class": "watermark",
                            "class_id": class_id
                        })

        except Exception as e:
            logger.error(f"❌ 解析 YOLO 结果失败: {e}")

        return detections

    async def _generate_mask(
        self, 
        image: Image.Image, 
        detections: List[Dict],
        enhance_mask: bool,
        context_expansion_ratio: float
    ) -> Image.Image:
        """生成修复掩码"""
        
        # TODO: 实际的掩码生成实现
        await asyncio.sleep(0.2)
        
        # 创建空白掩码
        mask = Image.new('L', image.size, 0)
        
        # 模拟掩码生成
        if detections:
            # 这里应该根据检测结果生成实际的掩码
            pass
        
        return mask
    
    async def _inpaint_image(
        self,
        image: Image.Image,
        mask: Image.Image,
        use_smart_enhancement: bool
    ) -> Image.Image:
        """图像修复"""

        logger.info(f"🎨 LaMa 开始图像修复，图像尺寸: {image.size}")

        if self.inpainter:
            try:
                # 实际的 LaMa 修复
                logger.info("🤖 调用 LaMa 模型进行图像修复...")

                if use_smart_enhancement:
                    logger.info("✨ 启用智能增强模式")

                result_image = self.inpainter.inpaint(image, mask)
                logger.success("🎉 LaMa 图像修复完成")

                return result_image

            except Exception as e:
                logger.error(f"❌ LaMa 修复失败: {e}")
                # 降级到模拟修复
                await asyncio.sleep(5.0)  # 模拟修复时间
                return image.copy()
        else:
            logger.warning("⚠️ LaMa 模型未加载，使用模拟修复")
            await asyncio.sleep(5.0)  # 模拟修复时间

            # 返回原图（实际应该返回修复后的图像）
            return image.copy()
    
    async def cleanup(self):
        """清理资源"""
        try:
            logger.info("🧹 正在清理 AI 服务资源...")
            
            # 清理模型
            if self.detector:
                del self.detector
                self.detector = None
            
            if self.inpainter:
                del self.inpainter
                self.inpainter = None
            
            # 清理 GPU 缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.is_ready = False
            
            logger.info("✅ AI 服务资源清理完成")
            
        except Exception as e:
            logger.error(f"❌ AI 服务资源清理失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        return {
            "is_ready": self.is_ready,
            "device": str(self.device),
            "detector_loaded": self.detector is not None,
            "inpainter_loaded": self.inpainter is not None,
            "models_available": {
                "yolo": settings.yolo_model_exists,
                "lama": settings.lama_model_exists
            }
        }
