#!/usr/bin/env python3
"""
优化的后端构建脚本
解决 PyInstaller 卡住的问题
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json
import time

def create_optimized_spec_file():
    """创建优化的 PyInstaller spec 文件"""
    backend_dir = Path(__file__).parent
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目路径
backend_dir = Path(r"{backend_dir}")
models_dir = backend_dir / "models"

# 数据文件
datas = [
    (str(backend_dir / "api"), "api"),
    (str(backend_dir / "services"), "services"),
    (str(backend_dir / "utils"), "utils"),
]

# 只添加存在的模型文件
if models_dir.exists():
    datas.append((str(models_dir), "models"))

# 隐式导入 - 精简版本
hiddenimports = [
    'uvicorn.lifespan.on',
    'uvicorn.lifespan.off',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.protocols.http.auto',
    'fastapi',
    'fastapi.routing',
    'pydantic',
    'loguru',
    'multipart',
    'aiofiles',
]

# 排除不必要的模块以减少构建时间
excludes = [
    'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
    'tkinter', 'matplotlib', 'IPython', 'jupyter',
    'notebook', 'pandas', 'scipy', 'sklearn',
    'tensorflow', 'keras', 'jax',
]

a = Analysis(
    ['main.py'],
    pathex=[str(backend_dir)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=excludes,
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='watermark-backend',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # 禁用 UPX 压缩以避免问题
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,  # 保留控制台以便调试
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
'''
    
    spec_file = backend_dir / "watermark-backend.spec"
    with open(spec_file, 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    return spec_file

def build_with_timeout():
    """使用超时机制构建"""
    backend_dir = Path(__file__).parent
    
    print("🔧 创建优化的 spec 文件...")
    spec_file = create_optimized_spec_file()
    print(f"✅ Spec 文件已创建: {spec_file}")
    
    # 清理之前的构建
    dist_dir = backend_dir / "dist"
    build_dir = backend_dir / "build"
    
    if dist_dir.exists():
        print("🧹 清理 dist 目录...")
        shutil.rmtree(dist_dir)
    
    if build_dir.exists():
        print("🧹 清理 build 目录...")
        shutil.rmtree(build_dir)
    
    # 使用 spec 文件构建
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--clean",
        "--noconfirm",
        str(spec_file)
    ]
    
    print("🚀 开始 PyInstaller 构建...")
    print(f"📋 命令: {' '.join(cmd)}")
    
    try:
        # 设置较长的超时时间，但提供进度反馈
        process = subprocess.Popen(
            cmd,
            cwd=backend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("⏳ 构建进行中，请耐心等待...")
        print("💡 这可能需要 10-20 分钟，特别是首次构建")
        
        # 实时显示输出
        start_time = time.time()
        last_output_time = start_time
        
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            
            if output:
                current_time = time.time()
                elapsed = current_time - start_time
                
                # 显示关键信息
                if any(keyword in output.lower() for keyword in [
                    'analyzing', 'collecting', 'building', 'warning', 'error'
                ]):
                    print(f"[{elapsed:.0f}s] {output.strip()}")
                
                last_output_time = current_time
            
            # 检查是否超时（30分钟）
            if time.time() - last_output_time > 1800:
                print("❌ 构建超时，终止进程")
                process.terminate()
                return False
        
        return_code = process.poll()
        
        if return_code == 0:
            print("✅ PyInstaller 构建成功")
            return True
        else:
            print(f"❌ PyInstaller 构建失败，返回码: {return_code}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生异常: {e}")
        return False

def build_lightweight_version():
    """构建轻量级版本（不包含模型文件）"""
    print("🔧 尝试构建轻量级版本...")
    
    backend_dir = Path(__file__).parent
    
    # 临时移动模型文件
    models_dir = backend_dir / "models"
    models_backup = backend_dir / "models_backup"
    
    if models_dir.exists():
        print("📦 临时移动模型文件...")
        shutil.move(str(models_dir), str(models_backup))
    
    try:
        # 创建简化的 spec 文件
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

backend_dir = Path(r"{backend_dir}")

datas = [
    (str(backend_dir / "api"), "api"),
    (str(backend_dir / "services"), "services"),
    (str(backend_dir / "utils"), "utils"),
]

hiddenimports = [
    'uvicorn.lifespan.on',
    'uvicorn.lifespan.off',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.protocols.http.auto',
    'fastapi',
    'pydantic',
    'loguru',
]

excludes = [
    'PyQt5', 'PyQt6', 'PySide2', 'PySide6',
    'tkinter', 'matplotlib', 'IPython', 'jupyter',
    'torch', 'torchvision', 'cv2', 'ultralytics',  # 排除大型库
]

a = Analysis(['main.py'], pathex=[str(backend_dir)], datas=datas, 
            hiddenimports=hiddenimports, excludes=excludes)
pyz = PYZ(a.pure)
exe = EXE(pyz, a.scripts, a.binaries, a.datas, [], name='watermark-backend-lite',
          debug=False, console=True, upx=False)
'''
        
        spec_file = backend_dir / "watermark-backend-lite.spec"
        with open(spec_file, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        # 构建轻量级版本
        cmd = [sys.executable, "-m", "PyInstaller", "--clean", "--noconfirm", str(spec_file)]
        
        result = subprocess.run(cmd, cwd=backend_dir, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 轻量级版本构建成功")
            return True
        else:
            print(f"❌ 轻量级版本构建失败: {result.stderr}")
            return False
            
    finally:
        # 恢复模型文件
        if models_backup.exists():
            print("📦 恢复模型文件...")
            shutil.move(str(models_backup), str(models_dir))

def main():
    """主函数"""
    print("🔧 优化的后端构建工具")
    print("=" * 50)
    
    backend_dir = Path(__file__).parent
    print(f"📁 后端目录: {backend_dir}")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
    
    print("\n🎯 选择构建方式:")
    print("1. 完整版本（包含所有依赖和模型）- 推荐但可能较慢")
    print("2. 轻量级版本（不包含AI模型）- 快速构建用于测试")
    
    choice = input("\n请选择 (1/2) [默认: 1]: ").strip() or "1"
    
    if choice == "2":
        success = build_lightweight_version()
    else:
        success = build_with_timeout()
    
    if success:
        print("\n🎉 构建完成！")
        
        # 检查生成的文件
        dist_dir = backend_dir / "dist"
        if dist_dir.exists():
            for exe_file in dist_dir.glob("watermark-backend*"):
                if exe_file.is_file():
                    size_mb = exe_file.stat().st_size / 1024 / 1024
                    print(f"📦 生成文件: {exe_file.name} ({size_mb:.1f} MB)")
        
        print("\n💡 下一步:")
        print("1. 运行 'python tests/verify-build.py' 验证构建")
        print("2. 或直接运行 'pnpm tauri build' 构建完整应用")
    else:
        print("\n❌ 构建失败")
        print("\n💡 建议:")
        print("1. 尝试轻量级版本: python build_optimized.py")
        print("2. 检查磁盘空间（需要至少 5GB）")
        print("3. 关闭其他占用内存的程序")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
