#!/usr/bin/env python3
import os
import json

def split_models():
    print("🔧 开始分割大型模型文件...")
    
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    models_dir = os.path.join(backend_dir, "models")
    
    # 查找大于30MB的文件
    large_files = []
    for root, dirs, files in os.walk(models_dir):
        for file in files:
            file_path = os.path.join(root, file)
            if os.path.getsize(file_path) > 30 * 1024 * 1024:  # 30MB
                large_files.append(file_path)
    
    print(f"�� 找到 {len(large_files)} 个大文件需要分割:")
    for file_path in large_files:
        size_mb = os.path.getsize(file_path) / (1024 * 1024)
        print(f"  - {file_path}: {size_mb:.1f} MB")
    
    # 分割文件
    for file_path in large_files:
        print(f"\n🔪 分割文件: {file_path}")
        
        chunk_dir = f"{file_path}.chunks"
        os.makedirs(chunk_dir, exist_ok=True)
        
        chunks = []
        chunk_size = 50 * 1024 * 1024  # 50MB
        
        with open(file_path, 'rb') as f:
            chunk_num = 0
            while True:
                chunk_data = f.read(chunk_size)
                if not chunk_data:
                    break
                
                chunk_path = os.path.join(chunk_dir, f"chunk_{chunk_num:03d}")
                with open(chunk_path, 'wb') as chunk_file:
                    chunk_file.write(chunk_data)
                
                chunks.append(chunk_path)
                chunk_num += 1
        
        print(f"  ✅ 分割成 {len(chunks)} 个块")
        
        # 创建清单
        manifest = {
            "original_file": os.path.basename(file_path),
            "original_size": os.path.getsize(file_path),
            "chunks": [os.path.basename(chunk) for chunk in chunks],
            "chunk_count": len(chunks)
        }
        
        manifest_path = os.path.join(chunk_dir, "manifest.json")
        with open(manifest_path, 'w') as f:
            json.dump(manifest, f, indent=2)
        
        print(f"  ✅ 创建清单: {manifest_path}")
    
    print("\n🎉 模型文件分割完成！")

if __name__ == "__main__":
    split_models()
