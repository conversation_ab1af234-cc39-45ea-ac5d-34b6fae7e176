#!/usr/bin/env python3
"""
快速构建脚本 - 解决 PyInstaller 卡住问题
使用目录模式而不是单文件模式
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def main():
    print("🚀 快速后端构建工具")
    print("=" * 40)
    
    backend_dir = Path(__file__).parent
    project_root = backend_dir.parent
    
    print(f"📁 后端目录: {backend_dir}")
    
    # 检查 PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
    
    # 切换到后端目录
    os.chdir(backend_dir)
    
    # 清理之前的构建
    dist_dir = backend_dir / "dist"
    build_dir = backend_dir / "build"
    
    if dist_dir.exists():
        print("🧹 清理之前的构建...")
        shutil.rmtree(dist_dir)
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    # 使用目录模式构建（更快，更稳定）
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--name", "watermark-backend",
        "--distpath", str(dist_dir),
        "--workpath", str(build_dir),
        "--noconfirm",
        "--clean",
        "--console",
        # 注意：不使用 --onefile，使用目录模式
        
        # 添加数据文件
        "--add-data", f"api{os.pathsep}api",
        "--add-data", f"services{os.pathsep}services", 
        "--add-data", f"utils{os.pathsep}utils",
        
        # 基本的隐式导入
        "--hidden-import", "uvicorn.lifespan.on",
        "--hidden-import", "uvicorn.lifespan.off",
        "--hidden-import", "fastapi",
        "--hidden-import", "pydantic",
        "--hidden-import", "loguru",
        
        # 排除不需要的模块
        "--exclude-module", "PyQt5",
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib",
        
        "main.py"
    ]
    
    print("🚀 开始构建（目录模式）...")
    print("💡 目录模式更快更稳定，避免单文件模式的问题")
    
    try:
        # 显示实时输出
        process = subprocess.Popen(
            cmd, 
            stdout=subprocess.PIPE, 
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("⏳ 构建进行中...")
        
        # 实时显示关键输出
        for line in process.stdout:
            line = line.strip()
            if line and any(keyword in line.lower() for keyword in [
                'analyzing', 'collecting', 'building', 'warning', 'error', 'completed'
            ]):
                print(f"📋 {line}")
        
        process.wait()
        
        if process.returncode == 0:
            print("✅ 构建成功")
            
            # 检查生成的目录
            app_dir = dist_dir / "watermark-backend"
            if app_dir.exists():
                print(f"✅ 应用目录已生成: {app_dir}")
                
                # 计算总大小
                total_size = sum(f.stat().st_size for f in app_dir.rglob('*') if f.is_file())
                print(f"📊 应用大小: {total_size / 1024 / 1024:.1f} MB")
                
                # 复制模型文件
                copy_models_to_app(app_dir, backend_dir)
                
                # 复制到 Tauri 资源目录
                copy_to_tauri_resources(app_dir, project_root)
                
                print("🎉 构建完成！")
                return True
            else:
                print("❌ 应用目录未找到")
                return False
        else:
            print(f"❌ 构建失败，返回码: {process.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ 构建过程中发生异常: {e}")
        return False

def copy_models_to_app(app_dir, backend_dir):
    """复制模型文件到应用目录"""
    print("📦 复制模型文件...")
    
    models_source = backend_dir / "models"
    models_target = app_dir / "models"
    
    if models_source.exists():
        if models_target.exists():
            shutil.rmtree(models_target)
        
        shutil.copytree(models_source, models_target)
        
        # 计算模型文件大小
        total_size = sum(f.stat().st_size for f in models_target.rglob('*') if f.is_file())
        print(f"✅ 模型文件已复制: {total_size / 1024 / 1024:.1f} MB")
    else:
        print("⚠️ 模型目录不存在")

def copy_to_tauri_resources(app_dir, project_root):
    """复制应用到 Tauri 资源目录"""
    print("📦 复制到 Tauri 资源目录...")
    
    tauri_resources = project_root / "src-tauri" / "resources"
    tauri_resources.mkdir(exist_ok=True)
    
    target_dir = tauri_resources / "watermark-backend"
    
    if target_dir.exists():
        shutil.rmtree(target_dir)
    
    shutil.copytree(app_dir, target_dir)
    print(f"✅ 已复制到: {target_dir}")
    
    # 在 Unix 系统上设置执行权限
    if os.name != 'nt':
        exe_file = target_dir / "watermark-backend"
        if exe_file.exists():
            os.chmod(exe_file, 0o755)
            print("✅ 执行权限已设置")

if __name__ == "__main__":
    success = main()
    if success:
        print("\n💡 下一步:")
        print("1. 运行 'python tests/verify-build.py' 验证构建")
        print("2. 或直接运行 'pnpm tauri build' 构建完整应用")
    else:
        print("\n❌ 构建失败")
        print("💡 如果仍然有问题，请尝试:")
        print("1. 重启终端")
        print("2. 检查磁盘空间")
        print("3. 关闭其他程序释放内存")
    
    sys.exit(0 if success else 1)
