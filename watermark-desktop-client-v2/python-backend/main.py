#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水印去除桌面客户端 V2 - Python 后端服务
基于 FastAPI 的 AI 处理服务
"""

import os
import sys
import json
from pathlib import Path
from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from api.routes import processing, health, files, websocket
from services.ai_service import AIService
from utils.config import settings


# 全局 AI 服务实例
ai_service: AIService = None


def reassemble_models():
    """
    重新组装分割的模型文件
    """
    logger.info("🔧 检查并重组模型文件...")

    backend_dir = Path(__file__).parent
    models_dir = backend_dir / "models"

    # 查找所有 .chunks 目录
    chunks_dirs = []
    for root, dirs, files in os.walk(models_dir):
        for dir_name in dirs:
            if dir_name.endswith('.chunks'):
                chunks_dirs.append(Path(root) / dir_name)

    if not chunks_dirs:
        logger.info("📊 没有找到需要重组的分割文件")
        return

    logger.info(f"📊 找到 {len(chunks_dirs)} 个分割文件需要重组")

    for chunks_dir in chunks_dirs:
        logger.info(f"🔧 检查文件: {chunks_dir}")

        manifest_path = chunks_dir / "manifest.json"

        if not manifest_path.exists():
            logger.warning(f"❌ 清单文件不存在: {manifest_path}")
            continue

        with open(manifest_path, 'r') as f:
            manifest = json.load(f)

        # 重组文件
        output_path = chunks_dir.parent / manifest["original_file"]

        # 如果文件已存在且大小正确，跳过
        if output_path.exists() and output_path.stat().st_size == manifest["original_size"]:
            logger.info(f"✅ 文件已存在且完整: {output_path}")
            continue

        logger.info(f"🔧 重组到: {output_path}")

        try:
            with open(output_path, 'wb') as output_file:
                for chunk_name in manifest["chunks"]:
                    chunk_path = chunks_dir / chunk_name
                    if not chunk_path.exists():
                        logger.error(f"❌ 块文件不存在: {chunk_path}")
                        break

                    with open(chunk_path, 'rb') as chunk_file:
                        output_file.write(chunk_file.read())

            # 验证文件大小
            if output_path.stat().st_size != manifest["original_size"]:
                logger.error(f"❌ 重组文件大小不匹配: {output_path}")
                output_path.unlink()
            else:
                size_mb = output_path.stat().st_size / (1024 * 1024)
                logger.info(f"✅ 重组完成: {output_path} ({size_mb:.1f} MB)")

        except Exception as e:
            logger.error(f"❌ 重组失败: {e}")

    logger.info("🎉 模型文件重组检查完成！")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global ai_service
    
    # 启动时初始化
    logger.info("🚀 启动水印去除后端服务...")

    try:
        # 首先重组模型文件
        reassemble_models()

        # 初始化 AI 服务
        ai_service = AIService()
        await ai_service.initialize()
        
        # 将 AI 服务添加到应用状态
        app.state.ai_service = ai_service
        
        logger.success("✅ AI 服务初始化完成")
        
    except Exception as e:
        logger.error(f"❌ AI 服务初始化失败: {e}")
        raise
    
    yield
    
    # 关闭时清理
    logger.info("🔄 正在关闭服务...")
    
    if ai_service:
        await ai_service.cleanup()
    
    logger.info("✅ 服务已关闭")


# 创建 FastAPI 应用
app = FastAPI(
    title="Watermark Remover V2 API",
    description="智能水印去除服务 API",
    version="0.1.0",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:1420",
        "tauri://localhost",
        "http://localhost:5173",# Vite 开发服务器
        "http://localhost:5174",
        "http://localhost:3000",  # 备用端口
        "http://127.0.0.1:1420",
        "http://127.0.0.1:5173",
        "http://127.0.0.1:5174",
        "http://127.0.0.1:3000"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
app.include_router(processing.router, prefix="/api/v1", tags=["图像处理"])
app.include_router(files.router, prefix="/api/v1", tags=["文件管理"])
app.include_router(websocket.router, prefix="/api/v1", tags=["WebSocket"])

# 静态文件服务
if not (project_root / "static").exists():
    (project_root / "static").mkdir(exist_ok=True)

if not (project_root / "outputs").exists():
    (project_root / "outputs").mkdir(exist_ok=True)

app.mount("/static", StaticFiles(directory=str(project_root / "static")), name="static")
app.mount("/outputs", StaticFiles(directory=str(project_root / "outputs")), name="outputs")


@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "Watermark Remover V2 API",
        "version": "0.1.0",
        "status": "running"
    }


def main():
    """主函数"""
    # 配置日志
    logger.remove()
    logger.add(
        sys.stdout,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>",
        level="INFO"
    )
    
    # 添加文件日志
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logger.add(
        log_dir / "app.log",
        rotation="10 MB",
        retention="7 days",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        level="DEBUG"
    )
    
    logger.info("🎯 启动水印去除后端服务")
    logger.info(f"📁 项目根目录: {project_root}")
    logger.info(f"🔧 配置: {settings}")
    
    # 启动服务器
    # 在打包环境中禁用 reload 以避免端口冲突
    is_packaged = hasattr(sys, '_MEIPASS')

    if is_packaged:
        # 在打包环境中直接传递 app 对象
        uvicorn.run(
            app,
            host=settings.host,
            port=settings.port,
            log_level="info"
        )
    else:
        # 在开发环境中使用模块字符串
        uvicorn.run(
            "main:app",
            host=settings.host,
            port=settings.port,
            reload=settings.debug,
            log_level="info" if not settings.debug else "debug"
        )


if __name__ == "__main__":
    main()
