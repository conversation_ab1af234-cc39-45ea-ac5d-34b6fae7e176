"""
性能监控和内存管理模块
提供GPU内存管理、性能监控等功能
"""

import gc
import psutil
import torch
import time
import threading
from typing import Dict, Any, Optional
from dataclasses import dataclass
from contextlib import contextmanager
import logging

logger = logging.getLogger(__name__)


@dataclass
class MemoryInfo:
    """内存信息数据类"""
    total: float
    used: float
    available: float
    percent: float


@dataclass
class GPUMemoryInfo:
    """GPU内存信息数据类"""
    total: float
    used: float
    free: float
    percent: float


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.metrics = {}
        self.lock = threading.Lock()
    
    def record_metric(self, name: str, value: float, tags: Optional[Dict[str, str]] = None):
        """记录性能指标"""
        with self.lock:
            if name not in self.metrics:
                self.metrics[name] = []
            
            self.metrics[name].append({
                'value': value,
                'timestamp': time.time(),
                'tags': tags or {}
            })
    
    def get_metrics(self, name: str) -> list:
        """获取指定指标的历史数据"""
        with self.lock:
            return self.metrics.get(name, [])
    
    def get_average(self, name: str, last_n: Optional[int] = None) -> float:
        """获取指标的平均值"""
        metrics = self.get_metrics(name)
        if not metrics:
            return 0.0
        
        if last_n:
            metrics = metrics[-last_n:]
        
        return sum(m['value'] for m in metrics) / len(metrics)
    
    def clear_metrics(self, name: Optional[str] = None):
        """清除指标数据"""
        with self.lock:
            if name:
                self.metrics.pop(name, None)
            else:
                self.metrics.clear()


class MemoryManager:
    """内存管理器"""
    
    def __init__(self, gpu_memory_threshold: float = 0.8, cleanup_interval: int = 300):
        """
        初始化内存管理器
        
        Args:
            gpu_memory_threshold: GPU内存使用阈值（0-1）
            cleanup_interval: 清理间隔（秒）
        """
        self.gpu_memory_threshold = gpu_memory_threshold
        self.cleanup_interval = cleanup_interval
        self.last_cleanup = time.time()
    
    def get_system_memory(self) -> MemoryInfo:
        """获取系统内存信息"""
        memory = psutil.virtual_memory()
        return MemoryInfo(
            total=memory.total / 1024**3,  # GB
            used=memory.used / 1024**3,
            available=memory.available / 1024**3,
            percent=memory.percent
        )
    
    def get_gpu_memory(self, device_id: int = 0) -> Optional[GPUMemoryInfo]:
        """获取GPU内存信息"""
        if not torch.cuda.is_available():
            return None
        
        try:
            torch.cuda.set_device(device_id)
            total = torch.cuda.get_device_properties(device_id).total_memory / 1024**3
            allocated = torch.cuda.memory_allocated(device_id) / 1024**3
            cached = torch.cuda.memory_reserved(device_id) / 1024**3
            free = total - cached
            
            return GPUMemoryInfo(
                total=total,
                used=allocated,
                free=free,
                percent=(cached / total) * 100
            )
        except Exception as e:
            logger.warning(f"获取GPU内存信息失败: {e}")
            return None
    
    def cleanup_memory(self, force: bool = False):
        """清理内存"""
        current_time = time.time()
        
        if not force and (current_time - self.last_cleanup) < self.cleanup_interval:
            return
        
        logger.info("开始内存清理...")
        
        # Python垃圾回收
        collected = gc.collect()
        logger.info(f"Python GC回收了 {collected} 个对象")
        
        # GPU内存清理
        if torch.cuda.is_available():
            try:
                torch.cuda.empty_cache()
                logger.info("GPU缓存已清理")
            except Exception as e:
                logger.warning(f"GPU缓存清理失败: {e}")
        
        self.last_cleanup = current_time
    
    def check_memory_usage(self) -> Dict[str, Any]:
        """检查内存使用情况"""
        system_memory = self.get_system_memory()
        gpu_memory = self.get_gpu_memory()
        
        status = {
            'system_memory': {
                'total_gb': system_memory.total,
                'used_gb': system_memory.used,
                'available_gb': system_memory.available,
                'usage_percent': system_memory.percent
            },
            'needs_cleanup': False
        }
        
        if gpu_memory:
            status['gpu_memory'] = {
                'total_gb': gpu_memory.total,
                'used_gb': gpu_memory.used,
                'free_gb': gpu_memory.free,
                'usage_percent': gpu_memory.percent
            }
            
            # 检查是否需要清理
            if gpu_memory.percent > (self.gpu_memory_threshold * 100):
                status['needs_cleanup'] = True
                logger.warning(f"GPU内存使用率过高: {gpu_memory.percent:.1f}%")
        
        # 检查系统内存
        if system_memory.percent > 85:
            status['needs_cleanup'] = True
            logger.warning(f"系统内存使用率过高: {system_memory.percent:.1f}%")
        
        return status
    
    def auto_cleanup_if_needed(self):
        """根据内存使用情况自动清理"""
        status = self.check_memory_usage()
        if status['needs_cleanup']:
            self.cleanup_memory(force=True)


@contextmanager
def memory_monitor(operation_name: str, auto_cleanup: bool = True):
    """内存监控上下文管理器"""
    memory_manager = MemoryManager()
    
    # 记录开始状态
    start_status = memory_manager.check_memory_usage()
    start_time = time.time()
    
    logger.info(f"开始操作: {operation_name}")
    logger.info(f"初始内存状态 - 系统: {start_status['system_memory']['usage_percent']:.1f}%")
    
    if 'gpu_memory' in start_status:
        logger.info(f"初始GPU内存: {start_status['gpu_memory']['usage_percent']:.1f}%")
    
    try:
        yield memory_manager
    finally:
        # 记录结束状态
        end_time = time.time()
        duration = end_time - start_time
        
        end_status = memory_manager.check_memory_usage()
        
        logger.info(f"完成操作: {operation_name} - 耗时: {duration:.3f}s")
        logger.info(f"结束内存状态 - 系统: {end_status['system_memory']['usage_percent']:.1f}%")
        
        if 'gpu_memory' in end_status:
            logger.info(f"结束GPU内存: {end_status['gpu_memory']['usage_percent']:.1f}%")
        
        # 自动清理
        if auto_cleanup:
            memory_manager.auto_cleanup_if_needed()


class ModelCache:
    """模型缓存管理器"""
    
    def __init__(self, max_cache_size: int = 2):
        """
        初始化模型缓存
        
        Args:
            max_cache_size: 最大缓存模型数量
        """
        self.max_cache_size = max_cache_size
        self.cache = {}
        self.access_times = {}
        self.lock = threading.Lock()
    
    def get(self, key: str):
        """获取缓存的模型"""
        with self.lock:
            if key in self.cache:
                self.access_times[key] = time.time()
                return self.cache[key]
            return None
    
    def put(self, key: str, model):
        """缓存模型"""
        with self.lock:
            # 如果缓存已满，移除最久未使用的模型
            if len(self.cache) >= self.max_cache_size and key not in self.cache:
                oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
                self._remove(oldest_key)
            
            self.cache[key] = model
            self.access_times[key] = time.time()
    
    def _remove(self, key: str):
        """移除缓存项"""
        if key in self.cache:
            model = self.cache.pop(key)
            self.access_times.pop(key, None)
            
            # 清理模型内存
            if hasattr(model, 'cpu'):
                model.cpu()
            del model
            
            # 强制垃圾回收
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
    
    def clear(self):
        """清空缓存"""
        with self.lock:
            for key in list(self.cache.keys()):
                self._remove(key)
    
    def size(self) -> int:
        """获取缓存大小"""
        return len(self.cache)


# 全局实例
performance_monitor = PerformanceMonitor()
memory_manager = MemoryManager()
model_cache = ModelCache()


def log_system_info():
    """记录系统信息"""
    logger.info("=== 系统信息 ===")
    logger.info(f"CPU核心数: {psutil.cpu_count()}")
    
    memory = psutil.virtual_memory()
    logger.info(f"系统内存: {memory.total / 1024**3:.1f}GB")
    
    if torch.cuda.is_available():
        logger.info(f"CUDA可用: 是")
        logger.info(f"GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            logger.info(f"GPU {i}: {props.name} ({props.total_memory / 1024**3:.1f}GB)")
    else:
        logger.info("CUDA可用: 否")
    
    logger.info("================")


def get_performance_stats() -> Dict[str, Any]:
    """获取性能统计信息"""
    memory_status = memory_manager.check_memory_usage()
    
    stats = {
        'memory': memory_status,
        'model_cache_size': model_cache.size(),
        'performance_metrics': {
            name: {
                'count': len(metrics),
                'average': performance_monitor.get_average(name),
                'recent_average': performance_monitor.get_average(name, last_n=10)
            }
            for name, metrics in performance_monitor.metrics.items()
        }
    }
    
    return stats
