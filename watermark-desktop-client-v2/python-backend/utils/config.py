#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
from pathlib import Path
from typing import Optional

from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 服务器配置
    host: str = Field(default="127.0.0.1", env="HOST")
    port: int = Field(default=8000, env="PORT")
    debug: bool = Field(default=True, env="DEBUG")
    
    # AI 模型配置
    yolo_model_path: str = Field(default="models/yolo/yolo11x-train28-best.pt", env="YOLO_MODEL_PATH")
    lama_model_path: str = Field(default="models/lama/big-lama/models/best.ckpt", env="LAMA_MODEL_PATH")
    
    # 处理配置
    max_concurrent_tasks: int = Field(default=4, env="MAX_CONCURRENT_TASKS")
    max_file_size: int = Field(default=50 * 1024 * 1024, env="MAX_FILE_SIZE")  # 50MB
    supported_formats: list = Field(default=["jpg", "jpeg", "png", "bmp", "tiff", "webp"])
    
    # 存储配置
    upload_dir: str = Field(default="uploads", env="UPLOAD_DIR")
    output_dir: str = Field(default="outputs", env="OUTPUT_DIR")
    temp_dir: str = Field(default="temp", env="TEMP_DIR")
    
    # 安全配置
    allowed_origins: list = Field(default=["http://localhost:1420", "tauri://localhost"])
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 确保目录存在
        self._ensure_directories()
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        dirs = [self.upload_dir, self.output_dir, self.temp_dir, "logs"]
        
        for dir_path in dirs:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    @property
    def yolo_model_exists(self) -> bool:
        """检查 YOLO 模型是否存在"""
        return Path(self.yolo_model_path).exists()
    
    @property
    def lama_model_exists(self) -> bool:
        """检查 LAMA 模型是否存在"""
        return Path(self.lama_model_path).exists()


# 全局配置实例
settings = Settings()
