#!/usr/bin/env python3
"""
构建独立的 Python 后端可执行文件
使用 PyInstaller 将 Python 应用打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil

def main():
    print("🔧 开始构建独立的 Python 后端...")

    # 获取当前目录
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(backend_dir)
    
    print(f"📁 后端目录: {backend_dir}")
    print(f"📁 项目根目录: {project_root}")
    
    # 检查 PyInstaller 是否安装
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装完成")
    
    # 切换到后端目录
    os.chdir(backend_dir)
    
    # 清理之前的构建
    dist_dir = backend_dir / "dist"
    build_dir = backend_dir / "build"
    
    if dist_dir.exists():
        print("🧹 清理之前的构建...")
        shutil.rmtree(dist_dir)
    
    if build_dir.exists():
        shutil.rmtree(build_dir)
    
    # 使用更简单的命令行方式，避免复杂的 spec 文件
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--name", "watermark-backend",
        "--distpath", str(dist_dir),
        "--workpath", str(build_dir),
        "--specpath", str(backend_dir),
        "--noconfirm",
        "--clean",
        # 添加数据文件
        "--add-data", "api:api",
        "--add-data", "models:models",
        "--add-data", "services:services",
        "--add-data", "utils:utils",
        # 隐式导入
        "--hidden-import", "uvicorn.lifespan.on",
        "--hidden-import", "uvicorn.lifespan.off",
        "--hidden-import", "uvicorn.protocols.websockets.auto",
        "--hidden-import", "uvicorn.protocols.http.auto",
        "--hidden-import", "fastapi",
        "--hidden-import", "torch",
        "--hidden-import", "cv2",
        "--hidden-import", "PIL",
        "--hidden-import", "numpy",
        # 排除问题模块
        "--exclude-module", "PyQt5",
        "--exclude-module", "PySide6",
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib",
        "--exclude-module", "IPython",
        "--exclude-module", "jupyter",
        # 主文件
        "main.py"
    ]
    
    print("🚀 开始 PyInstaller 构建...")
    
    try:
        result = subprocess.run(cmd, check=True)
        print("✅ PyInstaller 构建成功")
        
        # 检查生成的文件
        executable_path = dist_dir / "watermark-backend"
        if executable_path.exists():
            print(f"✅ 可执行文件已生成: {executable_path}")
            print(f"📊 文件大小: {executable_path.stat().st_size / 1024 / 1024:.1f} MB")
        else:
            print("❌ 可执行文件未找到")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 构建失败: {e}")
        return False
    
    # 复制到 Tauri 资源目录
    tauri_resources = project_root / "src-tauri" / "resources"
    tauri_resources.mkdir(exist_ok=True)
    
    target_path = tauri_resources / "watermark-backend"
    if executable_path.exists():
        shutil.copy2(executable_path, target_path)
        print(f"✅ 可执行文件已复制到: {target_path}")
        
        # 设置执行权限
        os.chmod(target_path, 0o755)
        print("✅ 执行权限已设置")
    
    print("🎉 独立后端构建完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
