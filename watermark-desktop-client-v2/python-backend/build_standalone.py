#!/usr/bin/env python3
"""
构建独立的 Python 后端可执行文件
使用 PyInstaller 将 Python 应用打包成独立的可执行文件
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
import json

def check_dependencies():
    """检查必要的依赖是否已安装"""
    print("🔍 检查依赖...")

    required_packages = [
        'fastapi', 'uvicorn', 'torch', 'cv2', 'PIL', 'numpy',
        'ultralytics', 'loguru', 'pydantic'
    ]

    missing_packages = []
    for package in required_packages:
        try:
            if package == 'cv2':
                import cv2
            elif package == 'PIL':
                import PIL
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")

    if missing_packages:
        print(f"❌ 缺少依赖: {missing_packages}")
        print("请先运行: pip install -r requirements.txt")
        return False

    print("✅ 所有依赖检查通过")
    return True

def prepare_models():
    """准备模型文件"""
    print("📦 准备模型文件...")

    backend_dir = Path(__file__).parent
    models_dir = backend_dir / "models"

    if not models_dir.exists():
        print("❌ 模型目录不存在")
        return False

    # 检查关键模型文件
    required_models = [
        "yolo/best.pt",
        "lama/big-lama.ckpt"
    ]

    for model_path in required_models:
        full_path = models_dir / model_path
        if not full_path.exists():
            # 检查是否有分割文件
            chunks_dir = full_path.parent / f"{full_path.name}.chunks"
            if chunks_dir.exists():
                print(f"📦 发现分割文件: {chunks_dir}")
            else:
                print(f"⚠️ 模型文件不存在: {full_path}")

    print("✅ 模型文件检查完成")
    return True

def main():
    print("🔧 开始构建独立的 Python 后端...")

    # 获取当前目录
    backend_dir = Path(__file__).parent.absolute()
    project_root = backend_dir.parent

    print(f"📁 后端目录: {backend_dir}")
    print(f"📁 项目根目录: {project_root}")

    # 检查依赖
    if not check_dependencies():
        return False

    # 准备模型文件
    if not prepare_models():
        return False

    # 检查 PyInstaller 是否安装
    try:
        import PyInstaller
        print(f"✅ PyInstaller 版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ PyInstaller 未安装，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装完成")

    # 切换到后端目录
    os.chdir(backend_dir)

    # 清理之前的构建
    dist_dir = backend_dir / "dist"
    build_dir = backend_dir / "build"

    if dist_dir.exists():
        print("🧹 清理之前的构建...")
        shutil.rmtree(dist_dir)

    if build_dir.exists():
        shutil.rmtree(build_dir)

    # 创建优化的 PyInstaller 命令
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--name", "watermark-backend",
        "--distpath", str(dist_dir),
        "--workpath", str(build_dir),
        "--specpath", str(backend_dir),
        "--noconfirm",
        "--clean",
        "--console",  # 保留控制台输出用于调试

        # 添加数据文件 - 使用正确的分隔符
        "--add-data", f"api{os.pathsep}api",
        "--add-data", f"models{os.pathsep}models",
        "--add-data", f"services{os.pathsep}services",
        "--add-data", f"utils{os.pathsep}utils",

        # 隐式导入 - 添加更多必要的模块
        "--hidden-import", "uvicorn.lifespan.on",
        "--hidden-import", "uvicorn.lifespan.off",
        "--hidden-import", "uvicorn.protocols.websockets.auto",
        "--hidden-import", "uvicorn.protocols.http.auto",
        "--hidden-import", "uvicorn.protocols.websockets.websockets_impl",
        "--hidden-import", "uvicorn.protocols.http.h11_impl",
        "--hidden-import", "fastapi",
        "--hidden-import", "fastapi.routing",
        "--hidden-import", "fastapi.middleware",
        "--hidden-import", "fastapi.middleware.cors",
        "--hidden-import", "torch",
        "--hidden-import", "torchvision",
        "--hidden-import", "cv2",
        "--hidden-import", "PIL",
        "--hidden-import", "PIL.Image",
        "--hidden-import", "numpy",
        "--hidden-import", "ultralytics",
        "--hidden-import", "loguru",
        "--hidden-import", "pydantic",
        "--hidden-import", "pydantic_settings",
        "--hidden-import", "multipart",
        "--hidden-import", "aiofiles",
        "--hidden-import", "websockets",

        # 排除不需要的模块以减小体积
        "--exclude-module", "PyQt5",
        "--exclude-module", "PySide6",
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib",
        "--exclude-module", "IPython",
        "--exclude-module", "jupyter",
        "--exclude-module", "notebook",
        "--exclude-module", "pandas",
        "--exclude-module", "scipy",

        # 主文件
        "main.py"
    ]

    print("🚀 开始 PyInstaller 构建...")
    print(f"📋 构建命令: {' '.join(cmd[:10])}...")  # 显示部分命令

    try:
        # 运行 PyInstaller
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ PyInstaller 构建成功")

        # 检查生成的文件
        executable_name = "watermark-backend.exe" if os.name == 'nt' else "watermark-backend"
        executable_path = dist_dir / executable_name

        if executable_path.exists():
            file_size_mb = executable_path.stat().st_size / 1024 / 1024
            print(f"✅ 可执行文件已生成: {executable_path}")
            print(f"📊 文件大小: {file_size_mb:.1f} MB")

            # 验证可执行文件
            if verify_executable(executable_path):
                print("✅ 可执行文件验证通过")
            else:
                print("⚠️ 可执行文件验证失败")
        else:
            print("❌ 可执行文件未找到")
            return False

    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 构建失败: {e}")
        if e.stdout:
            print(f"标准输出: {e.stdout}")
        if e.stderr:
            print(f"错误输出: {e.stderr}")
        return False

    # 复制到 Tauri 资源目录
    return copy_to_tauri_resources(executable_path, project_root)

def verify_executable(executable_path):
    """验证可执行文件是否正常"""
    try:
        # 尝试运行 --help 命令
        result = subprocess.run([str(executable_path), "--help"],
                              capture_output=True, text=True, timeout=10)
        return True
    except (subprocess.TimeoutExpired, subprocess.CalledProcessError, FileNotFoundError):
        return False

def copy_to_tauri_resources(executable_path, project_root):
    """复制可执行文件到 Tauri 资源目录"""
    print("📦 复制到 Tauri 资源目录...")

    tauri_resources = project_root / "src-tauri" / "resources"
    tauri_resources.mkdir(exist_ok=True)

    # 目标文件名（不包含 .exe 扩展名，Tauri 会自动处理）
    target_name = "watermark-backend"
    target_path = tauri_resources / target_name

    if executable_path.exists():
        try:
            shutil.copy2(executable_path, target_path)
            print(f"✅ 可执行文件已复制到: {target_path}")

            # 在 Unix 系统上设置执行权限
            if os.name != 'nt':
                os.chmod(target_path, 0o755)
                print("✅ 执行权限已设置")

            # 复制模型文件到资源目录
            copy_models_to_resources(project_root)

            print("🎉 独立后端构建完成！")
            return True
        except Exception as e:
            print(f"❌ 复制文件失败: {e}")
            return False
    else:
        print("❌ 源文件不存在")
        return False

def copy_models_to_resources(project_root):
    """复制模型文件到 Tauri 资源目录"""
    print("📦 复制模型文件...")

    backend_dir = project_root / "python-backend"
    models_source = backend_dir / "models"
    tauri_resources = project_root / "src-tauri" / "resources"
    models_target = tauri_resources / "models"

    if models_source.exists():
        if models_target.exists():
            shutil.rmtree(models_target)

        shutil.copytree(models_source, models_target)
        print(f"✅ 模型文件已复制到: {models_target}")

        # 计算模型文件总大小
        total_size = sum(f.stat().st_size for f in models_target.rglob('*') if f.is_file())
        print(f"📊 模型文件总大小: {total_size / 1024 / 1024:.1f} MB")
    else:
        print("⚠️ 模型目录不存在，跳过复制")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
