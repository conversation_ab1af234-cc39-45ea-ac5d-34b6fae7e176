#!/usr/bin/env python3
import os
import sys
import subprocess
import shutil

def main():
    print("🔧 开始构建独立的 Python 后端...")
    
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(backend_dir)
    
    print(f"📁 后端目录: {backend_dir}")
    print(f"📁 项目根目录: {project_root}")
    
    os.chdir(backend_dir)
    
    # 清理之前的构建
    dist_dir = os.path.join(backend_dir, "dist")
    build_dir = os.path.join(backend_dir, "build")
    
    if os.path.exists(dist_dir):
        print("🧹 清理之前的构建...")
        shutil.rmtree(dist_dir)
    
    if os.path.exists(build_dir):
        shutil.rmtree(build_dir)
    
    # PyInstaller 命令，包含必要的隐式导入
    cmd = [
        sys.executable, "-m", "PyInstaller",
        "--onefile",
        "--name", "watermark-backend",
        "--noconfirm",
        "--clean",
        # 添加数据文件 (排除大型模型文件)
        "--add-data", "api:api",
        "--add-data", "services:services",
        "--add-data", "utils:utils",
        # 隐式导入
        "--hidden-import", "uvicorn.lifespan.on",
        "--hidden-import", "uvicorn.lifespan.off",
        "--hidden-import", "uvicorn.protocols.websockets.auto",
        "--hidden-import", "uvicorn.protocols.http.auto",
        "--hidden-import", "fastapi",
        "--hidden-import", "torch",
        "--hidden-import", "cv2",
        "--hidden-import", "PIL",
        "--hidden-import", "numpy",
        "--hidden-import", "huggingface_hub",
        "--hidden-import", "transformers",
        "--hidden-import", "ultralytics",
        "--hidden-import", "websockets",
        "--hidden-import", "websockets.server",
        "--hidden-import", "websockets.client",
        "--hidden-import", "skimage",
        "--hidden-import", "skimage.metrics",
        "--hidden-import", "imageio",
        "--hidden-import", "tifffile",
        # 排除问题模块
        "--exclude-module", "PyQt5",
        "--exclude-module", "PySide6",
        "--exclude-module", "tkinter",
        "--exclude-module", "matplotlib.backends.backend_qt5agg",
        "--exclude-module", "matplotlib.backends.backend_tkagg",
        "--exclude-module", "IPython",
        "--exclude-module", "jupyter",
        "main.py"
    ]
    
    print("🚀 开始 PyInstaller 构建...")
    
    try:
        subprocess.run(cmd, check=True)
        print("✅ PyInstaller 构建成功")
        
        executable_path = os.path.join(dist_dir, "watermark-backend")
        if os.path.exists(executable_path):
            file_size = os.path.getsize(executable_path)
            file_size_mb = file_size / (1024 * 1024)
            print(f"✅ 可执行文件已生成: {executable_path}")
            print(f"📊 文件大小: {file_size_mb:.1f} MB")
            
            # 复制到 Tauri 资源目录
            tauri_resources = os.path.join(project_root, "src-tauri", "resources")
            os.makedirs(tauri_resources, exist_ok=True)
            
            target_path = os.path.join(tauri_resources, "watermark-backend")
            shutil.copy2(executable_path, target_path)
            os.chmod(target_path, 0o755)
            print(f"✅ 可执行文件已复制到: {target_path}")

            # 手动复制模型文件（包括分割的块文件）
            models_source = os.path.join(backend_dir, "models")
            models_target = os.path.join(tauri_resources, "models")

            if os.path.exists(models_source):
                if os.path.exists(models_target):
                    shutil.rmtree(models_target)
                shutil.copytree(models_source, models_target)
                print(f"✅ 模型文件已复制到: {models_target}")

                # 检查分割的模型文件
                chunk_count = 0
                for root, dirs, files in os.walk(models_target):
                    for dir_name in dirs:
                        if dir_name.endswith('.chunks'):
                            chunks_dir = os.path.join(root, dir_name)
                            manifest_path = os.path.join(chunks_dir, 'manifest.json')
                            if os.path.exists(manifest_path):
                                with open(manifest_path, 'r') as f:
                                    import json
                                    manifest = json.load(f)
                                    original_size_mb = manifest["original_size"] / (1024 * 1024)
                                    chunk_count += manifest["chunk_count"]
                                    print(f"  📊 {manifest['original_file']}: {original_size_mb:.1f} MB ({manifest['chunk_count']} 块)")

                print(f"  📦 总共 {chunk_count} 个文件块")

        else:
            print("❌ 可执行文件未找到")
            return False
            
    except subprocess.CalledProcessError as e:
        print(f"❌ PyInstaller 构建失败: {e}")
        return False
    
    print("🎉 独立后端构建完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
