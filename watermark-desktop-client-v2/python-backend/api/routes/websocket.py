"""
WebSocket 路由
提供实时通信功能
"""

import json
import asyncio
from typing import Dict, Set
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from loguru import logger

router = APIRouter()

# 存储活动的 WebSocket 连接
active_connections: Dict[str, WebSocket] = {}
connection_groups: Dict[str, Set[str]] = {}

class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_groups: Dict[str, Set[str]] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        """接受 WebSocket 连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket client {client_id} connected")
        
        # 发送连接确认消息
        await self.send_personal_message({
            "type": "connection_established",
            "client_id": client_id,
            "message": "WebSocket connection established"
        }, client_id)

    def disconnect(self, client_id: str):
        """断开 WebSocket 连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        
        # 从所有组中移除
        for group_name in list(self.connection_groups.keys()):
            if client_id in self.connection_groups[group_name]:
                self.connection_groups[group_name].remove(client_id)
                if not self.connection_groups[group_name]:
                    del self.connection_groups[group_name]
        
        logger.info(f"WebSocket client {client_id} disconnected")

    async def send_personal_message(self, message: dict, client_id: str):
        """发送个人消息"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Failed to send message to {client_id}: {e}")
                self.disconnect(client_id)

    async def send_group_message(self, message: dict, group_name: str):
        """发送组消息"""
        if group_name in self.connection_groups:
            disconnected_clients = []
            for client_id in self.connection_groups[group_name]:
                try:
                    await self.send_personal_message(message, client_id)
                except Exception as e:
                    logger.error(f"Failed to send group message to {client_id}: {e}")
                    disconnected_clients.append(client_id)
            
            # 清理断开的连接
            for client_id in disconnected_clients:
                self.disconnect(client_id)

    async def broadcast(self, message: dict):
        """广播消息给所有连接"""
        disconnected_clients = []
        for client_id in list(self.active_connections.keys()):
            try:
                await self.send_personal_message(message, client_id)
            except Exception as e:
                logger.error(f"Failed to broadcast to {client_id}: {e}")
                disconnected_clients.append(client_id)
        
        # 清理断开的连接
        for client_id in disconnected_clients:
            self.disconnect(client_id)

    def join_group(self, client_id: str, group_name: str):
        """加入组"""
        if group_name not in self.connection_groups:
            self.connection_groups[group_name] = set()
        self.connection_groups[group_name].add(client_id)
        logger.info(f"Client {client_id} joined group {group_name}")

    def leave_group(self, client_id: str, group_name: str):
        """离开组"""
        if group_name in self.connection_groups and client_id in self.connection_groups[group_name]:
            self.connection_groups[group_name].remove(client_id)
            if not self.connection_groups[group_name]:
                del self.connection_groups[group_name]
            logger.info(f"Client {client_id} left group {group_name}")

    def get_connection_count(self) -> int:
        """获取连接数量"""
        return len(self.active_connections)

    def get_group_count(self) -> int:
        """获取组数量"""
        return len(self.connection_groups)

# 全局连接管理器
manager = ConnectionManager()

@router.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket 端点"""
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            # 接收客户端消息
            data = await websocket.receive_text()
            
            try:
                message = json.loads(data)
                await handle_websocket_message(message, client_id)
            except json.JSONDecodeError:
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Invalid JSON format"
                }, client_id)
            except Exception as e:
                logger.error(f"Error handling WebSocket message from {client_id}: {e}")
                await manager.send_personal_message({
                    "type": "error",
                    "message": str(e)
                }, client_id)
                
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        manager.disconnect(client_id)

async def handle_websocket_message(message: dict, client_id: str):
    """处理 WebSocket 消息"""
    message_type = message.get("type")
    
    if message_type == "ping":
        # 心跳检测
        await manager.send_personal_message({
            "type": "pong",
            "timestamp": message.get("timestamp")
        }, client_id)
        
    elif message_type == "join_group":
        # 加入组
        group_name = message.get("group")
        if group_name:
            manager.join_group(client_id, group_name)
            await manager.send_personal_message({
                "type": "group_joined",
                "group": group_name
            }, client_id)
        
    elif message_type == "leave_group":
        # 离开组
        group_name = message.get("group")
        if group_name:
            manager.leave_group(client_id, group_name)
            await manager.send_personal_message({
                "type": "group_left",
                "group": group_name
            }, client_id)
        
    elif message_type == "get_status":
        # 获取连接状态
        await manager.send_personal_message({
            "type": "status",
            "connections": manager.get_connection_count(),
            "groups": manager.get_group_count(),
            "client_id": client_id
        }, client_id)
        
    else:
        # 未知消息类型
        await manager.send_personal_message({
            "type": "error",
            "message": f"Unknown message type: {message_type}"
        }, client_id)

# 用于其他模块发送 WebSocket 消息的函数
async def send_processing_update(task_id: str, update: dict):
    """发送处理更新消息"""
    message = {
        "type": "processing_update",
        "task_id": task_id,
        **update
    }
    # 广播给所有连接的客户端，让前端根据task_id过滤
    await manager.broadcast(message)

async def send_system_notification(notification: dict):
    """发送系统通知"""
    message = {
        "type": "system_notification",
        **notification
    }
    await manager.broadcast(message)

async def send_error_notification(error: dict, client_id: str = None):
    """发送错误通知"""
    message = {
        "type": "error_notification",
        **error
    }
    
    if client_id:
        await manager.send_personal_message(message, client_id)
    else:
        await manager.broadcast(message)

# 健康检查端点
@router.get("/ws/health")
async def websocket_health():
    """WebSocket 健康检查"""
    return {
        "status": "healthy",
        "active_connections": manager.get_connection_count(),
        "active_groups": manager.get_group_count(),
        "websocket_url": "/api/v1/ws/{client_id}"
    }

# 获取连接统计
@router.get("/ws/stats")
async def websocket_stats():
    """获取 WebSocket 连接统计"""
    return {
        "active_connections": manager.get_connection_count(),
        "active_groups": manager.get_group_count(),
        "groups": {
            group_name: len(clients) 
            for group_name, clients in manager.connection_groups.items()
        }
    }
