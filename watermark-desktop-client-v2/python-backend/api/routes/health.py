#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查路由
"""

from fastapi import APIRouter, Request
from pydantic import BaseModel
from typing import Dict, Any

from utils.config import settings

router = APIRouter()


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    version: str
    ai_service_status: str
    models_status: Dict[str, bool]
    system_info: Dict[str, Any]


@router.get("/health", response_model=HealthResponse)
async def health_check(request: Request):
    """健康检查端点"""
    
    # 检查 AI 服务状态
    ai_service = getattr(request.app.state, 'ai_service', None)
    ai_service_status = "ready" if ai_service and ai_service.is_ready else "not_ready"
    
    # 检查模型状态
    models_status = {
        "yolo": settings.yolo_model_exists,
        "lama": settings.lama_model_exists
    }
    
    # 系统信息
    system_info = {
        "host": settings.host,
        "port": settings.port,
        "debug": settings.debug,
        "max_concurrent_tasks": settings.max_concurrent_tasks,
        "supported_formats": settings.supported_formats
    }
    
    return HealthResponse(
        status="healthy",
        version="0.1.0",
        ai_service_status=ai_service_status,
        models_status=models_status,
        system_info=system_info
    )


@router.get("/models/status")
async def models_status():
    """模型状态检查"""
    return {
        "yolo": {
            "path": settings.yolo_model_path,
            "exists": settings.yolo_model_exists,
            "status": "available" if settings.yolo_model_exists else "missing"
        },
        "lama": {
            "path": settings.lama_model_path,
            "exists": settings.lama_model_exists,
            "status": "available" if settings.lama_model_exists else "missing"
        }
    }
