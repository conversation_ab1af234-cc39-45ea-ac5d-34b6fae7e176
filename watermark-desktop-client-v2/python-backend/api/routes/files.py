#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理路由
"""

import os
import shutil
from pathlib import Path
from typing import List

from fastapi import APIRouter, HTTPException, UploadFile, File, Query
from fastapi.responses import FileResponse
from pydantic import BaseModel
from loguru import logger

from utils.config import settings

router = APIRouter()


class FileInfo(BaseModel):
    """文件信息"""
    name: str
    path: str
    size: int
    type: str
    exists: bool


@router.post("/files/upload")
async def upload_files(files: List[UploadFile] = File(...)):
    """上传文件"""
    
    if not files:
        raise HTTPException(status_code=400, detail="没有提供文件")
    
    uploaded_files = []
    
    for file in files:
        try:
            # 检查文件格式
            file_ext = Path(file.filename).suffix.lower().lstrip('.')
            if file_ext not in settings.supported_formats:
                raise HTTPException(
                    status_code=400, 
                    detail=f"不支持的文件格式: {file_ext}"
                )
            
            # 检查文件大小
            if file.size > settings.max_file_size:
                raise HTTPException(
                    status_code=400,
                    detail=f"文件过大: {file.filename}, 最大支持 {settings.max_file_size / 1024 / 1024:.1f}MB"
                )
            
            # 保存文件
            upload_path = Path(settings.upload_dir) / file.filename
            
            with open(upload_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            
            uploaded_files.append({
                "name": file.filename,
                "path": str(upload_path),
                "size": file.size,
                "type": file.content_type
            })
            
            logger.info(f"📁 文件上传成功: {file.filename}")
            
        except Exception as e:
            logger.error(f"❌ 文件上传失败: {file.filename}, 错误: {e}")
            raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")
    
    return {
        "message": f"成功上传 {len(uploaded_files)} 个文件",
        "files": uploaded_files
    }


@router.get("/files/info")
async def get_file_info(file_path: str):
    """获取文件信息"""
    
    path = Path(file_path)
    
    if not path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    
    try:
        stat = path.stat()
        
        return FileInfo(
            name=path.name,
            path=str(path),
            size=stat.st_size,
            type=path.suffix.lower(),
            exists=True
        )
        
    except Exception as e:
        logger.error(f"❌ 获取文件信息失败: {file_path}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"获取文件信息失败: {str(e)}")


@router.get("/files/download")
async def download_file(file_path: str):
    """下载文件"""
    
    path = Path(file_path)
    
    if not path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    
    if not path.is_file():
        raise HTTPException(status_code=400, detail="不是有效的文件")
    
    return FileResponse(
        path=str(path),
        filename=path.name,
        media_type='application/octet-stream'
    )


@router.delete("/files/delete")
async def delete_file(file_path: str):
    """删除文件"""
    
    path = Path(file_path)
    
    if not path.exists():
        raise HTTPException(status_code=404, detail="文件不存在")
    
    try:
        if path.is_file():
            path.unlink()
        elif path.is_dir():
            shutil.rmtree(path)
        
        logger.info(f"🗑️ 文件删除成功: {file_path}")
        
        return {"message": "文件删除成功"}
        
    except Exception as e:
        logger.error(f"❌ 文件删除失败: {file_path}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/files/local")
async def get_local_file(path: str = Query(..., description="本地文件路径")):
    """
    获取本地文件
    用于在开发环境中访问本地图片文件
    """
    try:
        # 验证文件路径
        file_path = Path(path)

        # 安全检查：确保文件存在且是文件
        if not file_path.exists():
            raise HTTPException(status_code=404, detail=f"文件不存在: {path}")

        if not file_path.is_file():
            raise HTTPException(status_code=400, detail=f"路径不是文件: {path}")

        # 检查文件类型（只允许图片文件）
        allowed_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'}
        if file_path.suffix.lower() not in allowed_extensions:
            raise HTTPException(status_code=400, detail=f"不支持的文件类型: {file_path.suffix}")

        logger.info(f"📁 访问本地文件: {path}")

        # 返回文件
        return FileResponse(
            path=str(file_path),
            media_type=f"image/{file_path.suffix[1:]}",
            filename=file_path.name
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 访问本地文件失败: {path}, 错误: {e}")
        raise HTTPException(status_code=500, detail=f"访问文件失败: {str(e)}")


@router.post("/files/cleanup")
async def cleanup_temp_files():
    """清理临时文件"""

    try:
        temp_dir = Path(settings.temp_dir)

        if temp_dir.exists():
            for file_path in temp_dir.iterdir():
                if file_path.is_file():
                    file_path.unlink()
                elif file_path.is_dir():
                    shutil.rmtree(file_path)

        logger.info("🧹 临时文件清理完成")

        return {"message": "临时文件清理完成"}

    except Exception as e:
        logger.error(f"❌ 临时文件清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")
