#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图像处理路由
"""

import asyncio
import time
import uuid
import gc
import psutil
from typing import List, Optional
from pathlib import Path

from fastapi import APIRouter, Request, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from loguru import logger

router = APIRouter()


class ProcessingSettings(BaseModel):
    """处理设置"""
    confidence_threshold: float = Field(default=0.3, ge=0.1, le=0.9)
    enhance_mask: bool = Field(default=True)
    use_smart_enhancement: bool = Field(default=True)
    context_expansion_ratio: float = Field(default=0.12, ge=0.05, le=0.3)
    output_directory: Optional[str] = None
    keep_original_name: bool = Field(default=True)


class ProcessingRequest(BaseModel):
    """处理请求"""
    files: List[str]
    settings: ProcessingSettings


class ProcessedFile(BaseModel):
    """处理结果文件"""
    input_path: str
    output_path: str
    success: bool
    error: Optional[str] = None
    processing_time: Optional[float] = None


class ProcessingResult(BaseModel):
    """处理结果"""
    processed_files: List[ProcessedFile]
    total_time: float
    success_count: int
    failure_count: int


class ProcessingResponse(BaseModel):
    """处理响应"""
    task_id: str
    status: str
    message: str


class TaskStatus(BaseModel):
    """任务状态"""
    task_id: str
    status: str
    progress: float
    result: Optional[ProcessingResult] = None
    error: Optional[str] = None


# 全局任务存储（生产环境应使用数据库）
tasks_storage = {}

# 资源管理配置
MAX_FILE_SIZE_MB = 50  # 单个文件最大50MB
MAX_MEMORY_USAGE_PERCENT = 90  # 最大内存使用率90% (提高限制)
MAX_CONCURRENT_FILES = 3  # 最大同时处理文件数
MAX_IMAGE_PIXELS = 4000 * 4000  # 最大图像像素数 (4000x4000)


def check_system_resources():
    """检查系统资源是否充足"""
    memory = psutil.virtual_memory()
    memory_usage_percent = memory.percent
    available_gb = memory.available / (1024**3)

    logger.info(f"💾 当前内存使用率: {memory_usage_percent:.1f}%, 可用内存: {available_gb:.1f}GB")

    # 检查内存使用率
    if memory_usage_percent > MAX_MEMORY_USAGE_PERCENT:
        logger.warning(f"⚠️ 内存使用率过高: {memory_usage_percent:.1f}% > {MAX_MEMORY_USAGE_PERCENT}%")
        return False, f"内存使用率过高: {memory_usage_percent:.1f}% (限制: {MAX_MEMORY_USAGE_PERCENT}%)"

    # 检查可用内存是否足够 (至少需要1GB)
    if available_gb < 1.0:
        logger.warning(f"⚠️ 可用内存不足: {available_gb:.1f}GB < 1.0GB")
        return False, f"可用内存不足: {available_gb:.1f}GB (至少需要1GB)"

    logger.info("✅ 系统资源充足")
    return True, "系统资源充足"


def check_file_size(file_path: str):
    """检查文件大小是否合适"""
    file_size_mb = Path(file_path).stat().st_size / (1024 * 1024)

    if file_size_mb > MAX_FILE_SIZE_MB:
        return False, f"文件过大: {file_size_mb:.1f}MB (最大 {MAX_FILE_SIZE_MB}MB)"

    return True, f"文件大小合适: {file_size_mb:.1f}MB"


def check_image_size(file_path: str):
    """检查图像尺寸是否合适"""
    try:
        from PIL import Image
        with Image.open(file_path) as img:
            width, height = img.size
            total_pixels = width * height

            if total_pixels > MAX_IMAGE_PIXELS:
                return False, f"图像尺寸过大: {width}x{height} ({total_pixels:,} 像素，最大 {MAX_IMAGE_PIXELS:,} 像素)"

            return True, f"图像尺寸合适: {width}x{height}"
    except Exception as e:
        return False, f"无法读取图像: {e}"


def force_garbage_collection():
    """强制垃圾回收"""
    gc.collect()
    logger.debug("🧹 执行垃圾回收")


@router.post("/processing/start", response_model=ProcessingResponse)
async def start_processing(
    request: ProcessingRequest,
    background_tasks: BackgroundTasks,
    app_request: Request
):
    """开始处理图像"""
    
    # 验证文件
    if not request.files:
        raise HTTPException(status_code=400, detail="没有提供文件")

    # 限制同时处理的文件数量
    if len(request.files) > MAX_CONCURRENT_FILES:
        raise HTTPException(
            status_code=400,
            detail=f"文件数量过多，最多同时处理 {MAX_CONCURRENT_FILES} 个文件"
        )

    # 检查系统资源
    resource_ok, resource_msg = check_system_resources()
    if not resource_ok:
        raise HTTPException(status_code=503, detail=f"系统资源不足: {resource_msg}")

    # 检查文件是否存在和大小
    for file_path in request.files:
        if not Path(file_path).exists():
            raise HTTPException(status_code=400, detail=f"文件不存在: {file_path}")

        # 检查文件大小
        size_ok, size_msg = check_file_size(file_path)
        if not size_ok:
            raise HTTPException(status_code=400, detail=size_msg)

        # 检查图像尺寸
        image_ok, image_msg = check_image_size(file_path)
        if not image_ok:
            raise HTTPException(status_code=400, detail=image_msg)
    
    # 生成任务 ID
    task_id = str(uuid.uuid4())
    
    # 初始化任务状态
    tasks_storage[task_id] = TaskStatus(
        task_id=task_id,
        status="pending",
        progress=0.0
    )
    
    # 获取 AI 服务
    ai_service = getattr(app_request.app.state, 'ai_service', None)
    if not ai_service:
        raise HTTPException(status_code=500, detail="AI 服务未初始化")
    
    # 启动后台处理任务
    background_tasks.add_task(
        process_images_background,
        task_id,
        request.files,
        request.settings,
        ai_service
    )
    
    logger.info(f"🎯 开始处理任务: {task_id}, 文件数量: {len(request.files)}")
    
    return ProcessingResponse(
        task_id=task_id,
        status="started",
        message=f"已开始处理 {len(request.files)} 个文件"
    )


@router.get("/processing/status/{task_id}", response_model=TaskStatus)
async def get_processing_status(task_id: str):
    """获取处理状态"""
    
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return tasks_storage[task_id]


@router.post("/processing/cancel/{task_id}")
async def cancel_processing(task_id: str):
    """取消处理任务"""
    
    if task_id not in tasks_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = tasks_storage[task_id]
    if task.status in ["completed", "failed"]:
        raise HTTPException(status_code=400, detail="任务已完成，无法取消")
    
    # 更新任务状态
    task.status = "cancelled"
    task.error = "用户取消"
    
    logger.info(f"❌ 任务已取消: {task_id}")
    
    return {"message": "任务已取消"}


async def process_images_background(
    task_id: str,
    file_paths: List[str],
    settings: ProcessingSettings,
    ai_service
):
    """后台处理图像"""

    try:
        # 导入WebSocket发送函数
        from api.routes.websocket import send_processing_update

        # 进度回调函数
        current_file_info = {"name": None, "index": 0, "total": 0}  # 当前处理的文件信息

        async def progress_callback(task_id: str, stage: str, progress: float, message: str = ""):
            """发送详细的进度更新到前端"""
            await send_processing_update(task_id, {
                "stage": stage,
                "progress": progress,
                "message": message,
                "file_name": current_file_info["name"],  # 当前文件名
                "current_file": current_file_info["index"],  # 当前文件索引
                "total_files": current_file_info["total"],  # 总文件数
                "timestamp": time.time()
            })
            logger.info(f"📡 进度更新: {stage} - {progress}% - {message} - 文件: {current_file_info['name']}")

        # 更新任务状态
        task = tasks_storage[task_id]
        task.status = "processing"

        # 设置总文件数
        current_file_info["total"] = len(file_paths)

        # 发送开始处理的通知
        await send_processing_update(task_id, {
            "stage": "started",
            "progress": 0,
            "message": f"开始处理 {len(file_paths)} 个文件",
            "total_files": len(file_paths)
        })

        logger.info(f"🔄 开始处理任务: {task_id}")

        processed_files = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            try:
                # 在处理每个文件前检查资源
                resource_ok, resource_msg = check_system_resources()
                if not resource_ok:
                    logger.warning(f"⚠️ 资源不足，跳过文件: {file_path}, {resource_msg}")
                    processed_files.append(ProcessedFile(
                        input_path=file_path,
                        output_path="",
                        success=False,
                        error=f"系统资源不足: {resource_msg}",
                        processing_time=0.0
                    ))
                    continue

                # 更新文件级别进度
                file_progress_base = (i / total_files) * 100
                task.progress = file_progress_base

                # 设置当前处理的文件信息
                current_file_info["name"] = Path(file_path).name
                current_file_info["index"] = i + 1

                # 发送文件开始处理的通知
                await send_processing_update(task_id, {
                    "stage": "file_started",
                    "progress": file_progress_base,
                    "message": f"开始处理文件 {i+1}/{total_files}: {current_file_info['name']}",
                    "current_file": i + 1,
                    "total_files": total_files,
                    "file_name": current_file_info["name"]
                })

                logger.info(f"📸 处理文件 {i+1}/{total_files}: {file_path}")

                # 记录开始时间
                start_time = asyncio.get_event_loop().time()

                # 生成输出路径
                output_filename = f"processed_{Path(file_path).name}"
                output_path = f"outputs/{output_filename}"

                # 调用 AI 服务处理图像，传入进度回调
                logger.info(f"🔍 开始 YOLO 水印检测: {Path(file_path).name}")
                result = await ai_service.process_image(
                    input_path=file_path,
                    output_path=output_path,
                    confidence_threshold=settings.confidence_threshold,
                    enhance_mask=settings.enhance_mask,
                    use_smart_enhancement=settings.use_smart_enhancement,
                    context_expansion_ratio=settings.context_expansion_ratio,
                    progress_callback=progress_callback,
                    task_id=task_id
                )
                logger.info(f"🎨 LaMa 图像修复完成: {Path(file_path).name}")

                # 强制垃圾回收，释放内存
                force_garbage_collection()

                # 计算处理时间
                processing_time = asyncio.get_event_loop().time() - start_time

                if result and result.get('success'):
                    output_path = result.get('output_path', f"outputs/processed_{Path(file_path).name}")
                    logger.success(f"✅ 文件处理成功: {Path(file_path).name} → {output_path}")

                    processed_files.append(ProcessedFile(
                        input_path=file_path,
                        output_path=output_path,
                        success=True,
                        processing_time=processing_time
                    ))
                else:
                    error_msg = result.get('error', '处理失败') if result else '处理失败'
                    logger.error(f"❌ 文件处理失败: {Path(file_path).name}, 错误: {error_msg}")

                    processed_files.append(ProcessedFile(
                        input_path=file_path,
                        output_path="",
                        success=False,
                        error=error_msg,
                        processing_time=processing_time
                    ))
                
            except Exception as e:
                logger.error(f"❌ 处理文件失败: {file_path}, 错误: {e}")
                
                processed_files.append(ProcessedFile(
                    input_path=file_path,
                    output_path="",
                    success=False,
                    error=str(e)
                ))
        
        # 完成处理
        success_count = sum(1 for f in processed_files if f.success)
        failure_count = len(processed_files) - success_count
        total_processing_time = sum(f.processing_time or 0 for f in processed_files)

        task.status = "completed"
        task.progress = 100.0
        task.result = ProcessingResult(
            processed_files=processed_files,
            total_time=total_processing_time,
            success_count=success_count,
            failure_count=failure_count
        )
        
        logger.success(f"✅ 任务完成: {task_id}, 成功: {success_count}, 失败: {failure_count}")
        
    except Exception as e:
        logger.error(f"❌ 任务处理失败: {task_id}, 错误: {e}")
        
        task = tasks_storage[task_id]
        task.status = "failed"
        task.error = str(e)
