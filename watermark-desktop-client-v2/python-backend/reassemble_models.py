#!/usr/bin/env python3
import os
import json

def reassemble_models():
    print("🔧 开始重新组装模型文件...")
    
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    models_dir = os.path.join(backend_dir, "models")
    
    # 查找所有 .chunks 目录
    chunks_dirs = []
    for root, dirs, files in os.walk(models_dir):
        for dir_name in dirs:
            if dir_name.endswith('.chunks'):
                chunks_dirs.append(os.path.join(root, dir_name))
    
    print(f"📊 找到 {len(chunks_dirs)} 个分割文件需要重组:")
    
    for chunks_dir in chunks_dirs:
        print(f"\n🔧 重组文件: {chunks_dir}")
        
        manifest_path = os.path.join(chunks_dir, "manifest.json")
        
        if not os.path.exists(manifest_path):
            print(f"  ❌ 清单文件不存在: {manifest_path}")
            continue
        
        with open(manifest_path, 'r') as f:
            manifest = json.load(f)
        
        # 重组文件
        output_path = os.path.join(os.path.dirname(chunks_dir), manifest["original_file"])
        
        # 如果文件已存在且大小正确，跳过
        if os.path.exists(output_path) and os.path.getsize(output_path) == manifest["original_size"]:
            print(f"  ✅ 文件已存在且完整: {output_path}")
            continue
        
        print(f"  🔧 重组到: {output_path}")
        
        with open(output_path, 'wb') as output_file:
            for chunk_name in manifest["chunks"]:
                chunk_path = os.path.join(chunks_dir, chunk_name)
                if not os.path.exists(chunk_path):
                    print(f"  ❌ 块文件不存在: {chunk_path}")
                    break
                
                with open(chunk_path, 'rb') as chunk_file:
                    output_file.write(chunk_file.read())
        
        # 验证文件大小
        if os.path.getsize(output_path) != manifest["original_size"]:
            print(f"  ❌ 重组文件大小不匹配: {output_path}")
            os.remove(output_path)
        else:
            size_mb = os.path.getsize(output_path) / (1024 * 1024)
            print(f"  ✅ 重组完成: {output_path} ({size_mb:.1f} MB)")
    
    print("\n🎉 模型文件重组完成！")

if __name__ == "__main__":
    reassemble_models()
