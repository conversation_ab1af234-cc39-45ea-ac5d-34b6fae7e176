"""
调试工具和对比分析模块
用于隔离检测和修复阶段的问题，提供详细的性能分析
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2
from typing import List, Dict, Any, Tuple, Optional, Union
import logging
from pathlib import Path
import time
import json
from dataclasses import dataclass
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import io

from .watermark_detector import WatermarkDetector
from .lama_inpainter import LamaInpainter
from .visualization import WatermarkVisualization
from .validation import LamaValidationFramework, ValidationResult

logger = logging.getLogger(__name__)


@dataclass
class DetectionDebugInfo:
    """检测调试信息"""
    detection_time: float
    preprocessing_time: float
    inference_time: float
    postprocessing_time: float
    model_confidence_scores: List[float]
    detection_boxes: List[List[int]]
    mask_quality_score: float
    false_positive_likelihood: float


@dataclass
class InpaintingDebugInfo:
    """修复调试信息"""
    inpainting_time: float
    preprocessing_time: float
    model_inference_time: float
    postprocessing_time: float
    mask_coverage_ratio: float
    inpainting_method: str
    quality_metrics: Dict[str, float]


@dataclass
class PipelineDebugResult:
    """管道调试结果"""
    image_id: str
    detection_debug: DetectionDebugInfo
    inpainting_debug: InpaintingDebugInfo
    overall_success: bool
    bottleneck_stage: str
    recommendations: List[str]
    error_analysis: Dict[str, Any]


class WatermarkDebuggingTool:
    """水印处理调试工具"""
    
    def __init__(
        self,
        detector: WatermarkDetector,
        inpainter: LamaInpainter,
        visualizer: Optional[WatermarkVisualization] = None,
        validator: Optional[LamaValidationFramework] = None
    ):
        """
        初始化调试工具
        
        Args:
            detector: 水印检测器
            inpainter: LAMA修复器
            visualizer: 可视化器
            validator: 验证框架
        """
        self.detector = detector
        self.inpainter = inpainter
        self.visualizer = visualizer or WatermarkVisualization()
        self.validator = validator or LamaValidationFramework(detector, inpainter, visualizer)
    
    def debug_detection_stage(
        self,
        image: Union[Image.Image, str, Path],
        confidence_threshold: float = 0.5,
        save_debug_images: bool = False,
        output_dir: Optional[str] = None
    ) -> DetectionDebugInfo:
        """
        调试检测阶段
        
        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            save_debug_images: 是否保存调试图像
            output_dir: 输出目录
            
        Returns:
            检测调试信息
        """
        start_time = time.time()
        
        # 加载图像
        if isinstance(image, (str, Path)):
            image = Image.open(image).convert('RGB')
        
        # 预处理时间
        preprocess_start = time.time()
        # 这里可以添加预处理步骤的时间测量
        preprocessing_time = time.time() - preprocess_start
        
        # 检测推理时间
        inference_start = time.time()
        mask, avg_confidence, detection_info = self.detector.detect(
            image=image,
            confidence_threshold=confidence_threshold,
            return_detailed_info=True
        )
        inference_time = time.time() - inference_start
        
        # 后处理时间
        postprocess_start = time.time()
        # 计算掩码质量分数
        mask_quality_score = self._calculate_mask_quality(mask, image.size)
        
        # 计算假阳性可能性
        false_positive_likelihood = self._calculate_false_positive_likelihood(
            detection_info, image
        )
        postprocessing_time = time.time() - postprocess_start
        
        detection_time = time.time() - start_time
        
        # 保存调试图像
        if save_debug_images and output_dir:
            self._save_detection_debug_images(
                image, mask, detection_info, output_dir
            )
        
        return DetectionDebugInfo(
            detection_time=detection_time,
            preprocessing_time=preprocessing_time,
            inference_time=inference_time,
            postprocessing_time=postprocessing_time,
            model_confidence_scores=[d['confidence'] for d in detection_info],
            detection_boxes=[d['bbox'] for d in detection_info],
            mask_quality_score=mask_quality_score,
            false_positive_likelihood=false_positive_likelihood
        )
    
    def debug_inpainting_stage(
        self,
        image: Union[Image.Image, str, Path],
        mask: np.ndarray,
        save_debug_images: bool = False,
        output_dir: Optional[str] = None
    ) -> InpaintingDebugInfo:
        """
        调试修复阶段
        
        Args:
            image: 输入图像
            mask: 检测掩码
            save_debug_images: 是否保存调试图像
            output_dir: 输出目录
            
        Returns:
            修复调试信息
        """
        start_time = time.time()
        
        # 加载图像
        if isinstance(image, (str, Path)):
            image = Image.open(image).convert('RGB')
        
        # 预处理时间
        preprocess_start = time.time()
        # 计算掩码覆盖率
        mask_coverage_ratio = np.sum(mask > 127) / (mask.shape[0] * mask.shape[1])
        preprocessing_time = time.time() - preprocess_start
        
        # 模型推理时间
        model_start = time.time()
        restored_image = self.inpainter.inpaint(image, mask)
        model_inference_time = time.time() - model_start
        
        # 后处理时间
        postprocess_start = time.time()
        # 计算质量指标
        quality_metrics = self._calculate_inpainting_quality_metrics(
            image, restored_image, mask
        )
        postprocessing_time = time.time() - postprocess_start
        
        inpainting_time = time.time() - start_time
        
        # 保存调试图像
        if save_debug_images and output_dir:
            self._save_inpainting_debug_images(
                image, restored_image, mask, output_dir
            )
        
        # 确定使用的修复方法
        inpainting_method = "LAMA" if hasattr(self.inpainter, 'model') and self.inpainter.model != "opencv_fallback" else "OpenCV"
        
        return InpaintingDebugInfo(
            inpainting_time=inpainting_time,
            preprocessing_time=preprocessing_time,
            model_inference_time=model_inference_time,
            postprocessing_time=postprocessing_time,
            mask_coverage_ratio=mask_coverage_ratio,
            inpainting_method=inpainting_method,
            quality_metrics=quality_metrics
        )
    
    def debug_full_pipeline(
        self,
        image: Union[Image.Image, str, Path],
        confidence_threshold: float = 0.5,
        save_debug_results: bool = False,
        output_dir: Optional[str] = None
    ) -> PipelineDebugResult:
        """
        调试完整管道
        
        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            save_debug_results: 是否保存调试结果
            output_dir: 输出目录
            
        Returns:
            管道调试结果
        """
        image_id = f"debug_{int(time.time())}"
        
        try:
            # 调试检测阶段
            detection_debug = self.debug_detection_stage(
                image, confidence_threshold, save_debug_results, output_dir
            )
            
            # 如果检测失败，直接返回
            if not detection_debug.detection_boxes:
                return PipelineDebugResult(
                    image_id=image_id,
                    detection_debug=detection_debug,
                    inpainting_debug=InpaintingDebugInfo(0, 0, 0, 0, 0, "None", {}),
                    overall_success=False,
                    bottleneck_stage="detection",
                    recommendations=["检查图像质量", "调整置信度阈值", "尝试不同的检测参数"],
                    error_analysis={"stage": "detection", "issue": "未检测到水印"}
                )
            
            # 获取检测掩码
            if isinstance(image, (str, Path)):
                image = Image.open(image).convert('RGB')
            
            mask, _, _ = self.detector.detect(image, confidence_threshold)
            
            # 调试修复阶段
            inpainting_debug = self.debug_inpainting_stage(
                image, mask, save_debug_results, output_dir
            )
            
            # 分析瓶颈
            bottleneck_stage = self._identify_bottleneck(detection_debug, inpainting_debug)
            
            # 生成建议
            recommendations = self._generate_recommendations(detection_debug, inpainting_debug)
            
            # 错误分析
            error_analysis = self._analyze_errors(detection_debug, inpainting_debug)
            
            # 保存调试结果
            if save_debug_results and output_dir:
                self._save_pipeline_debug_results(
                    image_id, detection_debug, inpainting_debug, 
                    bottleneck_stage, recommendations, error_analysis, output_dir
                )
            
            return PipelineDebugResult(
                image_id=image_id,
                detection_debug=detection_debug,
                inpainting_debug=inpainting_debug,
                overall_success=True,
                bottleneck_stage=bottleneck_stage,
                recommendations=recommendations,
                error_analysis=error_analysis
            )
            
        except Exception as e:
            logger.error(f"管道调试失败: {e}")
            return PipelineDebugResult(
                image_id=image_id,
                detection_debug=DetectionDebugInfo(0, 0, 0, 0, [], [], 0, 0),
                inpainting_debug=InpaintingDebugInfo(0, 0, 0, 0, 0, "Error", {}),
                overall_success=False,
                bottleneck_stage="unknown",
                recommendations=["检查输入图像", "查看错误日志"],
                error_analysis={"stage": "pipeline", "error": str(e)}
            )
    
    def _calculate_mask_quality(self, mask: np.ndarray, image_size: Tuple[int, int]) -> float:
        """计算掩码质量分数"""
        try:
            if np.sum(mask) == 0:
                return 0.0
            
            # 计算掩码的连通性
            num_labels, labels = cv2.connectedComponents(mask)
            
            # 计算掩码的紧凑性
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            if not contours:
                return 0.0
            
            total_area = np.sum(mask > 0)
            total_perimeter = sum(cv2.arcLength(contour, True) for contour in contours)
            
            # 紧凑性分数 (越接近圆形越好)
            compactness = (4 * np.pi * total_area) / (total_perimeter ** 2) if total_perimeter > 0 else 0
            
            # 大小合理性 (不应该太大或太小)
            size_ratio = total_area / (image_size[0] * image_size[1])
            size_score = 1.0 - abs(0.1 - size_ratio) / 0.1 if size_ratio <= 0.2 else 0.5
            
            # 综合质量分数
            quality_score = (compactness + size_score) / 2
            return min(1.0, max(0.0, quality_score))
            
        except Exception:
            return 0.0
    
    def _calculate_false_positive_likelihood(
        self, 
        detection_info: List[Dict[str, Any]], 
        image: Image.Image
    ) -> float:
        """计算假阳性可能性"""
        try:
            if not detection_info:
                return 0.0
            
            # 基于置信度的分析
            confidences = [d['confidence'] for d in detection_info]
            avg_confidence = np.mean(confidences)
            
            # 基于位置的分析 (水印通常在特定位置)
            positions = []
            for d in detection_info:
                if 'relative_position' in d:
                    pos = d['relative_position']
                    positions.append((pos['x_ratio'], pos['y_ratio']))
            
            # 低置信度 + 异常位置 = 高假阳性可能性
            confidence_factor = 1.0 - avg_confidence
            
            # 简化的假阳性评估
            false_positive_likelihood = confidence_factor * 0.5
            
            return min(1.0, max(0.0, false_positive_likelihood))
            
        except Exception:
            return 0.5  # 默认中等可能性

    def _calculate_inpainting_quality_metrics(
        self,
        original: Image.Image,
        restored: Image.Image,
        mask: np.ndarray
    ) -> Dict[str, float]:
        """计算修复质量指标"""
        try:
            # 转换为numpy数组
            original_array = np.array(original)
            restored_array = np.array(restored)

            # 确保大小一致
            if restored_array.shape != original_array.shape:
                restored = restored.resize(original.size, Image.LANCZOS)
                restored_array = np.array(restored)

            # 计算修复区域的指标
            binary_mask = (mask > 127).astype(np.uint8)

            if np.sum(binary_mask) == 0:
                return {"error": "empty_mask"}

            # 修复区域的颜色一致性
            color_consistency = self._calculate_color_consistency(
                original_array, restored_array, binary_mask
            )

            # 修复区域的纹理一致性
            texture_consistency = self._calculate_texture_consistency(
                original_array, restored_array, binary_mask
            )

            # 边界平滑度
            boundary_smoothness = self._calculate_boundary_smoothness(
                restored_array, binary_mask
            )

            return {
                "color_consistency": color_consistency,
                "texture_consistency": texture_consistency,
                "boundary_smoothness": boundary_smoothness,
                "overall_quality": (color_consistency + texture_consistency + boundary_smoothness) / 3
            }

        except Exception as e:
            logger.warning(f"计算修复质量指标失败: {e}")
            return {"error": str(e)}

    def _calculate_color_consistency(
        self,
        original: np.ndarray,
        restored: np.ndarray,
        mask: np.ndarray
    ) -> float:
        """计算颜色一致性"""
        try:
            # 获取修复区域周围的颜色
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (15, 15))
            dilated_mask = cv2.dilate(mask, kernel, iterations=1)
            border_mask = dilated_mask - mask

            if np.sum(border_mask) == 0:
                return 0.5

            # 计算边界区域的平均颜色
            border_colors = original[border_mask > 0]
            avg_border_color = np.mean(border_colors, axis=0)

            # 计算修复区域的平均颜色
            restored_colors = restored[mask > 0]
            avg_restored_color = np.mean(restored_colors, axis=0)

            # 计算颜色差异
            color_diff = np.linalg.norm(avg_border_color - avg_restored_color)

            # 归一化到0-1范围
            consistency = 1.0 - min(1.0, color_diff / 255.0)

            return consistency

        except Exception:
            return 0.5

    def _calculate_texture_consistency(
        self,
        original: np.ndarray,
        restored: np.ndarray,
        mask: np.ndarray
    ) -> float:
        """计算纹理一致性"""
        try:
            # 转换为灰度图
            original_gray = cv2.cvtColor(original, cv2.COLOR_RGB2GRAY)
            restored_gray = cv2.cvtColor(restored, cv2.COLOR_RGB2GRAY)

            # 计算局部二值模式 (LBP) 或简化的纹理特征
            # 这里使用简化的方法：计算梯度方差

            # 获取修复区域周围的纹理
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (10, 10))
            dilated_mask = cv2.dilate(mask, kernel, iterations=1)
            border_mask = dilated_mask - mask

            if np.sum(border_mask) == 0:
                return 0.5

            # 计算梯度
            grad_x = cv2.Sobel(original_gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(original_gray, cv2.CV_64F, 0, 1, ksize=3)
            original_gradient = np.sqrt(grad_x**2 + grad_y**2)

            grad_x_restored = cv2.Sobel(restored_gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y_restored = cv2.Sobel(restored_gray, cv2.CV_64F, 0, 1, ksize=3)
            restored_gradient = np.sqrt(grad_x_restored**2 + grad_y_restored**2)

            # 计算边界区域的纹理特征
            border_texture = np.var(original_gradient[border_mask > 0])

            # 计算修复区域的纹理特征
            restored_texture = np.var(restored_gradient[mask > 0])

            # 计算纹理一致性
            if border_texture == 0:
                return 0.5

            texture_ratio = min(restored_texture, border_texture) / max(restored_texture, border_texture)

            return texture_ratio

        except Exception:
            return 0.5

    def _calculate_boundary_smoothness(
        self,
        restored: np.ndarray,
        mask: np.ndarray
    ) -> float:
        """计算边界平滑度"""
        try:
            # 获取掩码边界
            contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

            if not contours:
                return 0.0

            # 创建边界掩码
            boundary_mask = np.zeros_like(mask)
            cv2.drawContours(boundary_mask, contours, -1, 255, thickness=3)

            # 转换为灰度图
            restored_gray = cv2.cvtColor(restored, cv2.COLOR_RGB2GRAY)

            # 计算边界处的梯度
            grad_x = cv2.Sobel(restored_gray, cv2.CV_64F, 1, 0, ksize=3)
            grad_y = cv2.Sobel(restored_gray, cv2.CV_64F, 0, 1, ksize=3)
            gradient_magnitude = np.sqrt(grad_x**2 + grad_y**2)

            # 计算边界处的平均梯度
            boundary_gradient = np.mean(gradient_magnitude[boundary_mask > 0])

            # 平滑度分数 (梯度越小越平滑)
            smoothness = 1.0 - min(1.0, boundary_gradient / 100.0)

            return smoothness

        except Exception:
            return 0.5

    def _identify_bottleneck(
        self,
        detection_debug: DetectionDebugInfo,
        inpainting_debug: InpaintingDebugInfo
    ) -> str:
        """识别性能瓶颈"""
        detection_time = detection_debug.detection_time
        inpainting_time = inpainting_debug.inpainting_time

        if detection_time > inpainting_time * 2:
            return "detection"
        elif inpainting_time > detection_time * 2:
            return "inpainting"
        else:
            return "balanced"

    def _generate_recommendations(
        self,
        detection_debug: DetectionDebugInfo,
        inpainting_debug: InpaintingDebugInfo
    ) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 检测阶段建议
        if detection_debug.false_positive_likelihood > 0.7:
            recommendations.append("检测可能存在假阳性，建议提高置信度阈值")

        if detection_debug.mask_quality_score < 0.5:
            recommendations.append("检测掩码质量较低，建议调整检测参数或使用掩码增强")

        if detection_debug.detection_time > 5.0:
            recommendations.append("检测时间过长，建议优化图像大小或使用更快的推理设置")

        # 修复阶段建议
        if inpainting_debug.mask_coverage_ratio > 0.3:
            recommendations.append("水印覆盖面积过大，可能影响修复质量")

        if inpainting_debug.inpainting_method == "OpenCV":
            recommendations.append("当前使用OpenCV修复，建议检查LAMA模型是否正确加载")

        if inpainting_debug.inpainting_time > 10.0:
            recommendations.append("修复时间过长，建议优化模型或使用GPU加速")

        # 质量建议
        quality_metrics = inpainting_debug.quality_metrics
        if "overall_quality" in quality_metrics and quality_metrics["overall_quality"] < 0.6:
            recommendations.append("修复质量较低，建议检查掩码准确性或尝试不同的修复参数")

        if not recommendations:
            recommendations.append("整体性能良好，无需特别优化")

        return recommendations

    def _analyze_errors(
        self,
        detection_debug: DetectionDebugInfo,
        inpainting_debug: InpaintingDebugInfo
    ) -> Dict[str, Any]:
        """分析错误和问题"""
        error_analysis = {
            "detection_issues": [],
            "inpainting_issues": [],
            "performance_issues": []
        }

        # 检测问题分析
        if not detection_debug.detection_boxes:
            error_analysis["detection_issues"].append("未检测到任何水印")

        if detection_debug.false_positive_likelihood > 0.8:
            error_analysis["detection_issues"].append("高假阳性风险")

        if detection_debug.mask_quality_score < 0.3:
            error_analysis["detection_issues"].append("掩码质量极低")

        # 修复问题分析
        if "error" in inpainting_debug.quality_metrics:
            error_analysis["inpainting_issues"].append(f"修复质量计算错误: {inpainting_debug.quality_metrics['error']}")

        if inpainting_debug.mask_coverage_ratio > 0.5:
            error_analysis["inpainting_issues"].append("水印覆盖面积过大")

        # 性能问题分析
        if detection_debug.detection_time > 10.0:
            error_analysis["performance_issues"].append("检测速度过慢")

        if inpainting_debug.inpainting_time > 20.0:
            error_analysis["performance_issues"].append("修复速度过慢")

        return error_analysis

    def _save_detection_debug_images(
        self,
        image: Image.Image,
        mask: np.ndarray,
        detection_info: List[Dict[str, Any]],
        output_dir: str
    ):
        """保存检测调试图像"""
        try:
            debug_dir = Path(output_dir) / "detection_debug"
            debug_dir.mkdir(parents=True, exist_ok=True)

            # 保存原图
            image.save(debug_dir / "original.jpg", 'JPEG', quality=95)

            # 保存掩码
            mask_image = Image.fromarray(mask)
            mask_image.save(debug_dir / "mask.png")

            # 保存检测框图像
            detection_boxes = self.visualizer.draw_detection_boxes(
                image, detection_info, show_confidence=True
            )
            detection_boxes.save(debug_dir / "detection_boxes.jpg", 'JPEG', quality=95)

            # 保存掩码叠加图像
            mask_overlay = self.visualizer.create_mask_overlay(
                image, mask, alpha=0.5
            )
            mask_overlay.save(debug_dir / "mask_overlay.jpg", 'JPEG', quality=95)

        except Exception as e:
            logger.error(f"保存检测调试图像失败: {e}")

    def _save_inpainting_debug_images(
        self,
        original: Image.Image,
        restored: Image.Image,
        mask: np.ndarray,
        output_dir: str
    ):
        """保存修复调试图像"""
        try:
            debug_dir = Path(output_dir) / "inpainting_debug"
            debug_dir.mkdir(parents=True, exist_ok=True)

            # 保存原图和修复图
            original.save(debug_dir / "original.jpg", 'JPEG', quality=95)
            restored.save(debug_dir / "restored.jpg", 'JPEG', quality=95)

            # 保存对比图
            comparison = self.visualizer.create_side_by_side_comparison(
                original, restored, titles=("修复前", "修复后")
            )
            comparison.save(debug_dir / "comparison.jpg", 'JPEG', quality=95)

            # 保存差异图
            diff_image = self._create_difference_image(original, restored)
            diff_image.save(debug_dir / "difference.jpg", 'JPEG', quality=95)

        except Exception as e:
            logger.error(f"保存修复调试图像失败: {e}")

    def _create_difference_image(self, img1: Image.Image, img2: Image.Image) -> Image.Image:
        """创建差异图像"""
        try:
            if img1.size != img2.size:
                img2 = img2.resize(img1.size, Image.LANCZOS)

            # 计算差异
            diff = np.abs(np.array(img1).astype(np.float32) - np.array(img2).astype(np.float32))

            # 增强差异显示
            diff = (diff * 3).clip(0, 255).astype(np.uint8)

            return Image.fromarray(diff)

        except Exception:
            return img1.copy()

    def _save_pipeline_debug_results(
        self,
        image_id: str,
        detection_debug: DetectionDebugInfo,
        inpainting_debug: InpaintingDebugInfo,
        bottleneck_stage: str,
        recommendations: List[str],
        error_analysis: Dict[str, Any],
        output_dir: str
    ):
        """保存管道调试结果"""
        try:
            debug_dir = Path(output_dir) / "pipeline_debug"
            debug_dir.mkdir(parents=True, exist_ok=True)

            debug_data = {
                "image_id": image_id,
                "detection_debug": {
                    "detection_time": detection_debug.detection_time,
                    "preprocessing_time": detection_debug.preprocessing_time,
                    "inference_time": detection_debug.inference_time,
                    "postprocessing_time": detection_debug.postprocessing_time,
                    "model_confidence_scores": detection_debug.model_confidence_scores,
                    "detection_boxes": detection_debug.detection_boxes,
                    "mask_quality_score": detection_debug.mask_quality_score,
                    "false_positive_likelihood": detection_debug.false_positive_likelihood
                },
                "inpainting_debug": {
                    "inpainting_time": inpainting_debug.inpainting_time,
                    "preprocessing_time": inpainting_debug.preprocessing_time,
                    "model_inference_time": inpainting_debug.model_inference_time,
                    "postprocessing_time": inpainting_debug.postprocessing_time,
                    "mask_coverage_ratio": inpainting_debug.mask_coverage_ratio,
                    "inpainting_method": inpainting_debug.inpainting_method,
                    "quality_metrics": inpainting_debug.quality_metrics
                },
                "bottleneck_stage": bottleneck_stage,
                "recommendations": recommendations,
                "error_analysis": error_analysis
            }

            with open(debug_dir / f"{image_id}_debug_report.json", 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            logger.error(f"保存管道调试结果失败: {e}")
