# 水印去除桌面客户端 V2

基于 Tauri + React + shadcn/ui + Python 后端的现代化智能水印检测和去除工具。

## 🌟 功能特性

- **智能水印检测**: 基于 YOLO11 的高精度水印检测
- **高质量修复**: 使用 LAMA 模型进行图像修复
- **现代化界面**: 基于 React + shadcn/ui 的美观界面
- **跨平台支持**: 支持 Windows、macOS、Linux
- **批量处理**: 支持多文件批量处理
- **实时预览**: 原图与处理结果对比显示
- **拖拽操作**: 支持文件拖拽添加
- **参数调节**: 可调节检测置信度、修复参数等

## 🏗️ 技术架构

### 前端技术栈
- **React 18+**: 现代化的前端框架
- **TypeScript**: 类型安全的开发体验
- **shadcn/ui**: 现代化的 UI 组件库
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Zustand**: 轻量级状态管理

### 桌面框架
- **Tauri 2.0**: 基于 Rust 的现代桌面应用框架
- **更小的应用体积**: 相比 Electron 更轻量
- **更好的性能**: 原生性能体验
- **更强的安全性**: 内置安全机制

### 后端服务
- **Python 3.8+**: AI 模型的最佳支持语言
- **FastAPI**: 现代化的 Python Web 框架
- **PyTorch**: AI 模型推理框架
- **YOLO11**: 水印检测模型
- **LAMA**: 图像修复模型

## 📋 系统要求

- **操作系统**: Windows 10+, macOS 10.15+, Linux (Ubuntu 20.04+)
- **内存**: 8GB+ RAM (推荐)
- **存储**: 2GB+ 可用空间
- **GPU**: 支持 CUDA (可选，用于加速)

## 🚀 快速开始

### 1. 环境准备

确保已安装以下工具：
- Node.js 18+
- Rust 1.70+
- Python 3.8+
- pnpm

### 2. 克隆项目

```bash
git clone <repository-url>
cd watermark-desktop-client-v2
```

### 3. 安装依赖

```bash
# 安装前端依赖
pnpm install

# 安装 Python 依赖
cd python-backend
pip install -r requirements.txt
cd ..
```

### 4. 准备 AI 模型

将以下模型文件放置到指定位置：
- `python-backend/models/yolo/yolo11x-train28-best.pt` - YOLO11 水印检测模型
- `python-backend/models/lama/big-lama/models/best.ckpt` - LAMA 图像修复模型

### 5. 启动开发环境

```bash
# 启动 Python 后端服务
cd python-backend
python main.py

# 新开终端，启动前端开发服务器
cd ..
pnpm tauri dev
```

## 📖 使用指南

### 基本操作

1. **添加图片**
   - 点击"选择文件"按钮选择图片
   - 或直接拖拽图片文件到应用窗口
   - 支持格式：JPG、PNG、WebP、BMP、TIFF

2. **开始处理**
   - 点击"开始处理"按钮
   - 查看处理进度和状态
   - 在预览面板查看处理结果

3. **保存结果**
   - 在预览面板点击"下载"按钮
   - 选择保存位置和文件名

### 高级设置

- 点击设置按钮调整 AI 参数
- 配置输出选项和性能设置
- 自定义界面主题和语言

## 🔧 开发指南

### 项目结构

```
watermark-desktop-client-v2/
├── src/                    # 前端代码 (React + TypeScript)
│   ├── components/         # React 组件
│   ├── stores/            # 状态管理
│   ├── types/             # 类型定义
│   └── lib/               # 工具库
├── src-tauri/             # Tauri Rust 后端
│   ├── src/               # Rust 源码
│   ├── Cargo.toml         # Rust 依赖
│   └── tauri.conf.json    # Tauri 配置
├── python-backend/        # Python API 服务
│   ├── api/               # API 路由
│   ├── services/          # 业务服务
│   ├── utils/             # 工具模块
│   └── main.py            # 主程序
├── docs/                  # 项目文档
└── tests/                 # 测试文件
```

### 开发命令

```bash
# 前端开发
pnpm dev                   # 启动前端开发服务器
pnpm build                 # 构建前端
pnpm lint                  # 代码检查
pnpm format                # 代码格式化

# Tauri 开发
pnpm tauri dev             # 启动 Tauri 开发模式
pnpm tauri build           # 构建桌面应用

# Python 后端
cd python-backend
python main.py             # 启动后端服务
```

### 测试

```bash
# 前端测试
pnpm test                  # 运行测试
pnpm test:coverage         # 测试覆盖率

# Python 后端测试
cd python-backend
pytest                     # 运行 Python 测试
```

## 📦 构建和部署

### 开发构建

```bash
pnpm tauri build
```

### 生产构建

```bash
# 设置生产环境
export NODE_ENV=production

# 构建应用
pnpm build
pnpm tauri build --release
```

构建产物位于 `src-tauri/target/release/bundle/` 目录。

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Tauri](https://tauri.app/) - 现代桌面应用框架
- [React](https://reactjs.org/) - 用户界面库
- [shadcn/ui](https://ui.shadcn.com/) - UI 组件库
- [FastAPI](https://fastapi.tiangolo.com/) - Python Web 框架
- [PyTorch](https://pytorch.org/) - 机器学习框架

## 📞 支持

如有问题或建议，请：
- 提交 [Issue](../../issues)
- 发送邮件至 <EMAIL>
- 查看 [文档](docs/)

---

**开发团队**: Watermark Team  
**版本**: 0.1.0  
**更新时间**: 2025年8月2日
