#!/usr/bin/env python3
"""
快速修复构建错误
"""

import re
from pathlib import Path

def fix_batch_toolbar():
    """修复 BatchToolbar.tsx 中的未使用变量"""
    file_path = Path("src/components/BatchToolbar.tsx")
    
    if not file_path.exists():
        return
    
    content = file_path.read_text(encoding='utf-8')
    
    # 移除未使用的导入
    content = re.sub(r'  FolderOpen,\n', '', content)
    content = re.sub(r'  Settings,\n', '', content)
    content = re.sub(r"import { Badge } from './ui/badge'\n", '', content)
    content = re.sub(r"import { Separator } from './ui/separator'\n", '', content)
    content = re.sub(r"import { useFileStore } from '../stores/fileStore'\n", '', content)
    
    # 移除未使用的变量声明
    content = re.sub(r'  const \[isPathSelectorOpen, setIsPathSelectorOpen\] = useState\(false\)\n', '', content)
    
    # 注释掉未使用的函数
    content = re.sub(
        r'  const handleSelectPath = async \(\) => \{[\s\S]*?\n  \}',
        '  // const handleSelectPath = async () => {\n  //   // Implementation removed to fix build\n  // }',
        content
    )
    
    content = re.sub(
        r'  const getPathDisplayText = \(\) => \{[\s\S]*?\n  \}',
        '  // const getPathDisplayText = () => {\n  //   // Implementation removed to fix build\n  // }',
        content
    )
    
    file_path.write_text(content, encoding='utf-8')
    print("✅ 修复 BatchToolbar.tsx")

def fix_file_list_item():
    """修复 FileListItem.tsx 中的未使用变量"""
    file_path = Path("src/components/FileListItem.tsx")
    
    if not file_path.exists():
        return
    
    content = file_path.read_text(encoding='utf-8')
    
    # 移除未使用的 onPreview 参数
    content = re.sub(r'  onPreview,\n', '', content)
    
    # 修复 handleCheckboxChange 函数
    content = re.sub(
        r'  const handleCheckboxChange = \(checked: boolean\) => \{',
        '  const handleCheckboxChange = (_checked: boolean) => {',
        content
    )
    
    file_path.write_text(content, encoding='utf-8')
    print("✅ 修复 FileListItem.tsx")

def fix_download_manager():
    """修复 downloadManager.ts 中的导入错误"""
    file_path = Path("src/lib/downloadManager.ts")
    
    if not file_path.exists():
        return
    
    content = file_path.read_text(encoding='utf-8')
    
    # 修复导入
    content = re.sub(r"import { join, basename } from '@tauri-apps/api/path'", "import { join } from '@tauri-apps/api/path'", content)
    content = re.sub(r"import { exists, createDir, copyFile } from '@tauri-apps/plugin-fs'", "import { exists, create, copyFile } from '@tauri-apps/plugin-fs'", content)
    content = re.sub(r"import { open } from '@tauri-apps/plugin-opener'", "// import { open } from '@tauri-apps/plugin-opener'", content)
    
    # 修复函数参数
    content = re.sub(
        r'  private generateFileName\(originalName: string, targetDir: string, overwrite: boolean\): string \{',
        '  private generateFileName(originalName: string, _targetDir: string, overwrite: boolean): string {',
        content
    )
    
    # 替换 createDir 为 create
    content = re.sub(r'createDir\(', 'create(', content)
    
    file_path.write_text(content, encoding='utf-8')
    print("✅ 修复 downloadManager.ts")

def fix_http_client():
    """修复 http-client.ts 中的类型错误"""
    file_path = Path("src/lib/http-client.ts")
    
    if not file_path.exists():
        return
    
    content = file_path.read_text(encoding='utf-8')
    
    # 修复 headers 类型问题
    content = re.sub(
        r'      finalHeaders = \{ \.\.\.options\.headers \}',
        '      finalHeaders = { ...options.headers } as Record<string, string>',
        content
    )
    
    file_path.write_text(content, encoding='utf-8')
    print("✅ 修复 http-client.ts")

def main():
    """主函数"""
    print("🔧 修复构建错误...")
    
    fix_batch_toolbar()
    fix_file_list_item()
    fix_download_manager()
    fix_http_client()
    
    print("✅ 所有错误已修复")

if __name__ == "__main__":
    main()
