@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo.
echo ========================================
echo 🎯 水印去除应用一键构建工具
echo ========================================
echo.

REM 获取脚本目录
set "PROJECT_ROOT=%~dp0"
set "BACKEND_DIR=%PROJECT_ROOT%python-backend"
set "BUILD_SCRIPT=%PROJECT_ROOT%scripts\build-complete.py"

echo 📁 项目根目录: %PROJECT_ROOT%
echo 📁 后端目录: %BACKEND_DIR%
echo.

REM 检查 Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装或不在 PATH 中
    echo 请先安装 Python 3.8+ 并添加到 PATH
    pause
    exit /b 1
)

echo ✅ Python 检查通过
python --version

REM 检查 Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js 未安装或不在 PATH 中
    echo 请先安装 Node.js 并添加到 PATH
    pause
    exit /b 1
)

echo ✅ Node.js 检查通过
node --version

REM 检查 pnpm
pnpm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pnpm 未安装，正在安装...
    npm install -g pnpm
    if errorlevel 1 (
        echo ❌ pnpm 安装失败
        pause
        exit /b 1
    )
)

echo ✅ pnpm 检查通过
pnpm --version

REM 检查 Rust
cargo --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Rust 未安装或不在 PATH 中
    echo 请先安装 Rust 并添加到 PATH
    echo 访问: https://rustup.rs/
    pause
    exit /b 1
)

echo ✅ Rust 检查通过
cargo --version

echo.
echo 🚀 开始构建流程...
echo.

REM 运行完整构建脚本
if exist "%BUILD_SCRIPT%" (
    echo 📋 使用完整构建脚本: %BUILD_SCRIPT%
    python "%BUILD_SCRIPT%"
    set "BUILD_RESULT=!errorlevel!"
) else (
    echo 📋 使用简化构建流程
    
    REM 切换到项目根目录
    cd /d "%PROJECT_ROOT%"
    
    REM 安装前端依赖
    echo 📦 安装前端依赖...
    pnpm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    
    REM 安装后端依赖
    echo 📦 安装后端依赖...
    cd /d "%BACKEND_DIR%"
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
    
    REM 安装 PyInstaller
    echo 📦 安装 PyInstaller...
    python -m pip install pyinstaller
    if errorlevel 1 (
        echo ❌ PyInstaller 安装失败
        pause
        exit /b 1
    )
    
    REM 构建后端
    echo 🔧 构建后端...
    python build_standalone.py
    if errorlevel 1 (
        echo ❌ 后端构建失败
        pause
        exit /b 1
    )
    
    REM 切换回项目根目录
    cd /d "%PROJECT_ROOT%"
    
    REM 构建前端
    echo 🎨 构建前端...
    pnpm build
    if errorlevel 1 (
        echo ❌ 前端构建失败
        pause
        exit /b 1
    )
    
    REM 构建 Tauri 应用
    echo 📦 构建 Tauri 应用...
    pnpm tauri build
    if errorlevel 1 (
        echo ❌ Tauri 构建失败
        pause
        exit /b 1
    )
    
    set "BUILD_RESULT=0"
)

echo.
echo ========================================
if !BUILD_RESULT! equ 0 (
    echo 🎉 构建完成！
    echo.
    echo 📦 安装包位置:
    echo    %PROJECT_ROOT%src-tauri\target\release\bundle\
    echo.
    echo 🔍 查看构建输出:
    if exist "%PROJECT_ROOT%src-tauri\target\release\bundle\msi\" (
        echo    Windows MSI: src-tauri\target\release\bundle\msi\
    )
    if exist "%PROJECT_ROOT%src-tauri\target\release\bundle\nsis\" (
        echo    Windows NSIS: src-tauri\target\release\bundle\nsis\
    )
    if exist "%PROJECT_ROOT%src-tauri\target\release\bundle\deb\" (
        echo    Linux DEB: src-tauri\target\release\bundle\deb\
    )
    if exist "%PROJECT_ROOT%src-tauri\target\release\bundle\appimage\" (
        echo    Linux AppImage: src-tauri\target\release\bundle\appimage\
    )
    if exist "%PROJECT_ROOT%src-tauri\target\release\bundle\dmg\" (
        echo    macOS DMG: src-tauri\target\release\bundle\dmg\
    )
) else (
    echo ❌ 构建失败！
    echo 请检查上面的错误信息
)
echo ========================================

pause
