# 登录功能演示指南

## 快速开始

### 1. 启动应用
```bash
cd watermark-desktop-client-v2
pnpm dev
```

应用将在 http://localhost:5174 启动

### 2. 查看登录界面
打开浏览器访问 http://localhost:5174，您将看到登录界面，包含：

- **应用标题**: "Welcome to DeWatermark"
- **副标题**: "Use your google or email to continue, signing up is free!"
- **浏览器登录按钮**: "Login with browser"
- **邮箱登录表单**: 包含邮箱和密码输入框
- **服务条款链接**: Terms of Service 和 Privacy Policy

### 3. 登录方式

#### 方式一：浏览器登录（推荐）
1. 点击 "Login with browser" 按钮
2. 系统会自动使用默认用户信息登录
3. 登录成功后会显示 Toast 通知
4. 自动跳转到主应用界面

#### 方式二：邮箱登录
1. 在邮箱输入框中输入任意邮箱地址（如：<EMAIL>）
2. 在密码输入框中输入任意密码
3. 点击 "Sign in" 按钮
4. 系统会模拟登录过程（显示加载状态）
5. 登录成功后跳转到主应用

### 4. 主应用界面
登录成功后，您将看到：

- **顶部导航栏**: 显示应用标题和用户信息
- **用户信息**: 在导航栏中央显示用户名或邮箱
- **登出按钮**: 导航栏右侧的登出图标
- **主应用功能**: 完整的水印去除工具界面

### 5. 登出功能
1. 在主应用界面，点击导航栏右侧的登出图标（LogOut）
2. 系统会显示登出成功的 Toast 通知
3. 自动返回到登录界面

## 功能特性演示

### 1. 响应式设计
- 调整浏览器窗口大小，观察界面自适应变化
- 登录卡片会保持居中显示
- 在移动设备上也能正常使用

### 2. 主题支持
- 系统会自动跟随浏览器的深色/浅色主题设置
- 可以通过浏览器开发者工具模拟主题切换

### 3. 表单验证
- 尝试在邮箱或密码为空时点击登录
- 系统会显示错误提示："请输入邮箱和密码"

### 4. 加载状态
- 使用邮箱登录时，观察按钮的加载动画
- 按钮文字会变为 "Signing in..." 并显示旋转图标

### 5. 状态持久化
- 登录成功后，刷新页面
- 系统会保持登录状态，直接显示主应用
- 登出后刷新页面，会返回到登录界面

## 技术细节

### 1. 状态管理
- 使用 Zustand 管理认证状态
- 状态会自动保存到 localStorage
- 支持状态恢复和持久化

### 2. 组件架构
- `App.tsx`: 根据登录状态条件渲染
- `LoginPage.tsx`: 登录界面组件
- `MainApp.tsx`: 主应用组件
- `authStore.ts`: 认证状态管理

### 3. 样式设计
- 使用 Tailwind CSS 构建
- 支持深色/浅色主题
- 现代化的卡片式设计
- 渐变背景和阴影效果

## 测试验证

### 1. 运行自动化测试
```bash
pnpm test auth.test.tsx
```

测试覆盖：
- 初始状态验证
- 登录功能测试
- 登出功能测试
- 错误处理测试

### 2. 手动测试清单
- [ ] 页面加载显示登录界面
- [ ] 浏览器登录功能正常
- [ ] 邮箱登录功能正常
- [ ] 表单验证工作正常
- [ ] 登录成功跳转到主应用
- [ ] 用户信息正确显示
- [ ] 登出功能正常
- [ ] 状态持久化正常
- [ ] 主题切换正常
- [ ] 响应式设计正常

## 开发者工具

### 1. 查看认证状态
打开浏览器开发者工具：
1. 进入 Application/Storage 标签
2. 查看 Local Storage
3. 找到 `auth-storage` 键
4. 观察认证状态的变化

### 2. React DevTools
如果安装了 React DevTools：
1. 查看组件树结构
2. 观察 authStore 状态变化
3. 调试组件渲染过程

### 3. 网络请求
虽然当前是模拟登录，但可以观察：
1. 静态资源加载
2. 组件懒加载
3. 未来真实 API 请求

## 故障排除

### 常见问题
1. **页面空白**: 检查控制台错误信息
2. **样式异常**: 确认 Tailwind CSS 正确加载
3. **状态不保存**: 检查 localStorage 权限
4. **登录无响应**: 查看网络请求和控制台

### 调试步骤
1. 打开浏览器开发者工具
2. 查看 Console 标签的错误信息
3. 检查 Network 标签的请求状态
4. 验证 localStorage 中的数据

## 下一步

体验完登录功能后，您可以：
1. 探索主应用的其他功能
2. 查看技术文档了解实现细节
3. 运行测试验证功能完整性
4. 根据需求定制登录流程

## 反馈

如果在使用过程中遇到问题或有改进建议，请：
1. 检查控制台错误信息
2. 查看相关文档
3. 提交问题报告或功能请求
