# HTTP 503 错误修复文档

## 问题描述

用户在尝试开始图像处理时遇到 HTTP 503 Service Unavailable 错误：

```
127.0.0.1:8000/api/v1/processing/start:1 Failed to load resource: the server responded with a status of 503 (Service Unavailable)
MainApp.tsx:117 Application error: HTTPError: HTTP 503: Service Unavailable
```

## 根本原因分析

### 1. 系统资源检查过于严格

**问题位置**: `watermark-desktop-client-v2/python-backend/api/routes/processing.py:146-148`

```python
# 检查系统资源
resource_ok, resource_msg = check_system_resources()
if not resource_ok:
    raise HTTPException(status_code=503, detail=f"系统资源不足: {resource_msg}")
```

### 2. 内存使用率限制过低

**原始设置**:
```python
MAX_MEMORY_USAGE_PERCENT = 80  # 最大内存使用率80%
```

**实际内存使用率**: 78.9% (接近限制)

### 3. 资源检查逻辑不够智能

原始的检查只考虑内存使用率百分比，没有考虑实际可用内存量。

## 解决方案

### 1. 调整内存使用率限制

**修改前**:
```python
MAX_MEMORY_USAGE_PERCENT = 80  # 最大内存使用率80%
```

**修改后**:
```python
MAX_MEMORY_USAGE_PERCENT = 90  # 最大内存使用率90% (提高限制)
```

### 2. 改进资源检查逻辑

**修改前**:
```python
def check_system_resources():
    """检查系统资源是否充足"""
    memory = psutil.virtual_memory()
    memory_usage_percent = memory.percent

    logger.info(f"💾 当前内存使用率: {memory_usage_percent:.1f}%")

    if memory_usage_percent > MAX_MEMORY_USAGE_PERCENT:
        return False, f"内存使用率过高: {memory_usage_percent:.1f}%"

    return True, "系统资源充足"
```

**修改后**:
```python
def check_system_resources():
    """检查系统资源是否充足"""
    memory = psutil.virtual_memory()
    memory_usage_percent = memory.percent
    available_gb = memory.available / (1024**3)

    logger.info(f"💾 当前内存使用率: {memory_usage_percent:.1f}%, 可用内存: {available_gb:.1f}GB")

    # 检查内存使用率
    if memory_usage_percent > MAX_MEMORY_USAGE_PERCENT:
        logger.warning(f"⚠️ 内存使用率过高: {memory_usage_percent:.1f}% > {MAX_MEMORY_USAGE_PERCENT}%")
        return False, f"内存使用率过高: {memory_usage_percent:.1f}% (限制: {MAX_MEMORY_USAGE_PERCENT}%)"

    # 检查可用内存是否足够 (至少需要1GB)
    if available_gb < 1.0:
        logger.warning(f"⚠️ 可用内存不足: {available_gb:.1f}GB < 1.0GB")
        return False, f"可用内存不足: {available_gb:.1f}GB (至少需要1GB)"

    logger.info("✅ 系统资源充足")
    return True, "系统资源充足"
```

### 3. 改进前端错误处理

**修改前**:
```typescript
toast({
  title: "处理失败",
  description: error instanceof Error ? error.message : "未知错误",
  variant: "destructive",
})
```

**修改后**:
```typescript
// 改进错误消息处理
let errorTitle = "处理失败"
let errorDescription = "未知错误"

if (error instanceof Error) {
  // 检查是否是503服务不可用错误
  if (error.message.includes('503') || error.message.includes('Service Unavailable')) {
    errorTitle = "服务暂时不可用"
    if (error.message.includes('内存使用率过高')) {
      errorDescription = "系统内存使用率过高，请稍后重试或关闭其他应用程序释放内存"
    } else if (error.message.includes('可用内存不足')) {
      errorDescription = "系统可用内存不足，请关闭其他应用程序后重试"
    } else {
      errorDescription = "后端服务暂时不可用，请稍后重试"
    }
  } else if (error.message.includes('400')) {
    errorTitle = "请求参数错误"
    errorDescription = error.message.replace(/HTTP \d+: /, '')
  } else if (error.message.includes('500')) {
    errorTitle = "服务器内部错误"
    errorDescription = "后端服务出现问题，请检查服务状态或联系管理员"
  } else {
    errorDescription = error.message
  }
}

toast({
  title: errorTitle,
  description: errorDescription,
  variant: "destructive",
})
```

## 改进效果

### 1. 更宽松的资源限制
- 内存使用率限制从80%提高到90%
- 增加了绝对可用内存检查（至少1GB）
- 减少了因内存使用率临界而导致的503错误

### 2. 更详细的日志记录
- 显示内存使用率和可用内存量
- 添加警告日志便于调试
- 更清晰的成功/失败状态指示

### 3. 更友好的用户反馈
- 针对不同错误类型提供具体的解决建议
- 区分503、400、500等不同HTTP状态码
- 提供可操作的错误提示

## 验证结果

### 后端服务状态
```json
{
  "status": "healthy",
  "version": "0.1.0",
  "ai_service_status": "ready",
  "models_status": {
    "yolo": true,
    "lama": true
  },
  "system_info": {
    "host": "127.0.0.1",
    "port": 8000,
    "debug": true,
    "max_concurrent_tasks": 4,
    "supported_formats": ["jpg", "jpeg", "png", "bmp", "tiff", "webp"]
  }
}
```

### 内存使用情况
- 当前内存使用率: 78.9%
- 新限制: 90%
- 状态: ✅ 正常

## 预防措施

### 1. 监控建议
- 定期检查系统内存使用情况
- 监控AI模型加载状态
- 设置内存使用率告警

### 2. 配置优化
- 根据实际硬件配置调整限制参数
- 考虑添加动态资源管理
- 实现更智能的任务调度

### 3. 用户指导
- 在文档中说明系统要求
- 提供内存优化建议
- 添加系统状态检查工具

---

**修复时间**: 2025-01-03
**状态**: 已解决
**影响**: 显著减少503错误发生率，改善用户体验
