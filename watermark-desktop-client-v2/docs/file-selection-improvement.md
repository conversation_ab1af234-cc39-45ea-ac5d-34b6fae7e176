# 文件选择交互改进

## 问题描述

原来的 FileDropZone 组件整个区域都可以点击来选择文件，这容易导致意外触发文件选择对话框，用户体验不够友好。

## 解决方案

### 1. 分离点击和拖拽功能

**修改前：**
- 整个 FileDropZone 区域都可以点击选择文件
- 拖拽功能和点击功能混合在同一个区域

**修改后：**
- 拖拽功能：保留在整个 FileDropZone 区域
- 点击功能：仅限于特定的按钮

### 2. 技术实现

#### 隐藏文件输入元素
```tsx
// 修改前：覆盖整个区域的透明 input
<input
  className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
  // ...
/>

// 修改后：完全隐藏的 input，通过 ref 控制
<input
  ref={fileInputRef}
  className="hidden"
  // ...
/>
```

#### 移除容器的点击样式
```tsx
// 修改前：整个容器可点击
className={cn(
  // ...
  !disabled && 'cursor-pointer'
)}

// 修改后：移除点击样式
className={cn(
  // ...
  disabled && 'opacity-50 cursor-not-allowed'
)}
```

#### 添加按钮点击处理
```tsx
const handleButtonClick = useCallback(() => {
  if (!disabled && fileInputRef.current) {
    fileInputRef.current.click()
  }
}, [disabled])

// 完整模式按钮
<Button 
  variant="outline" 
  size="sm" 
  onClick={handleButtonClick}
  className="z-10"
>
  <Upload className="mr-2 h-4 w-4" />
  Select up to {maxFiles} images
</Button>

// 紧凑模式按钮
<Button 
  variant="outline" 
  size="sm" 
  onClick={handleButtonClick}
  className="text-xs h-6 px-2"
>
  点击选择文件
</Button>
```

### 3. 用户体验改进

#### 更精确的交互控制
- **拖拽上传**：整个区域支持拖拽，提供大的拖拽目标
- **点击选择**：仅通过明确的按钮触发，避免意外点击

#### 视觉反馈优化
- 按钮有明确的视觉边界和悬停效果
- 拖拽区域保留拖拽时的视觉反馈
- 移除了容器的点击光标，避免误导

#### 双模式支持
- **完整模式**：大按钮，适合主要的文件选择场景
- **紧凑模式**：小按钮，适合空间受限的场景

### 4. 功能保持

#### 保留的功能
- ✅ 拖拽上传功能完全保留
- ✅ 文件验证逻辑不变
- ✅ 错误处理机制不变
- ✅ 禁用状态处理不变

#### 改进的功能
- ✅ 更精确的点击控制
- ✅ 更好的用户意图识别
- ✅ 减少意外操作

### 5. 测试建议

#### 功能测试
1. **拖拽测试**：验证拖拽文件到区域仍然正常工作
2. **按钮点击测试**：验证按钮点击能正确打开文件选择对话框
3. **区域点击测试**：验证点击非按钮区域不会触发文件选择

#### 交互测试
1. **悬停效果**：验证按钮和拖拽区域的悬停效果
2. **禁用状态**：验证禁用时按钮不可点击
3. **视觉反馈**：验证拖拽时的视觉反馈正常

#### 兼容性测试
1. **不同屏幕尺寸**：测试在不同尺寸下的显示效果
2. **紧凑模式**：测试紧凑模式下的按钮功能
3. **文件类型**：测试不同文件类型的选择和验证

## 预期效果

### 用户体验提升
- 减少意外触发文件选择的情况
- 提供更明确的操作指引
- 保持拖拽上传的便利性

### 界面一致性
- 按钮样式与应用其他部分保持一致
- 交互行为更符合用户预期
- 视觉层次更加清晰

### 可维护性
- 代码逻辑更清晰，拖拽和点击功能分离
- 更容易进行功能扩展和修改
- 更好的组件封装性
