# 水印去除桌面客户端 V2 - UI/UX 设计规范文档

## 🎨 设计系统概览

### 设计理念
基于现有客户端的成功设计，采用现代化的设计语言，确保用户体验的连续性和一致性。设计遵循简洁、直观、高效的原则。

### 设计原则
1. **一致性**: 保持与现有版本的视觉连续性
2. **简洁性**: 减少视觉噪音，突出核心功能
3. **可用性**: 优化用户操作流程和交互体验
4. **现代化**: 采用当前流行的设计趋势
5. **可访问性**: 确保不同用户群体的可用性

## 🎯 色彩系统

### 主色调 (基于现有客户端分析)
```css
/* 主要颜色 */
--primary-50: #f0f9ff
--primary-100: #e0f2fe
--primary-500: #0ea5e9    /* 主品牌色 */
--primary-600: #0284c7
--primary-700: #0369a1

/* 中性色 */
--gray-50: #f8fafc
--gray-100: #f1f5f9
--gray-200: #e2e8f0
--gray-300: #cbd5e1
--gray-400: #94a3b8
--gray-500: #64748b
--gray-600: #475569
--gray-700: #334155
--gray-800: #1e293b
--gray-900: #0f172a

/* 功能色 */
--success: #10b981      /* 成功状态 */
--warning: #f59e0b      /* 警告状态 */
--error: #ef4444        /* 错误状态 */
--info: #3b82f6         /* 信息提示 */
```

### 语义化颜色
```css
/* 状态颜色 */
--status-ready: #64748b       /* 准备就绪 */
--status-processing: #f59e0b  /* 处理中 */
--status-completed: #10b981   /* 已完成 */
--status-failed: #ef4444      /* 失败 */

/* 背景颜色 */
--bg-primary: #ffffff         /* 主背景 */
--bg-secondary: #f8fafc       /* 次要背景 */
--bg-tertiary: #f1f5f9        /* 第三级背景 */
```

## 📝 字体系统

### 字体族
```css
/* 主字体 */
font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;

/* 等宽字体 */
font-family: "JetBrains Mono", "Fira Code", Consolas, monospace;
```

### 字体大小
```css
/* 标题字体 */
--text-xs: 0.75rem      /* 12px */
--text-sm: 0.875rem     /* 14px */
--text-base: 1rem       /* 16px */
--text-lg: 1.125rem     /* 18px */
--text-xl: 1.25rem      /* 20px */
--text-2xl: 1.5rem      /* 24px */
--text-3xl: 1.875rem    /* 30px */

/* 字重 */
--font-normal: 400
--font-medium: 500
--font-semibold: 600
--font-bold: 700
```

## 📐 间距系统

### 基础间距
```css
/* 间距单位 (基于 4px 网格) */
--space-1: 0.25rem      /* 4px */
--space-2: 0.5rem       /* 8px */
--space-3: 0.75rem      /* 12px */
--space-4: 1rem         /* 16px */
--space-5: 1.25rem      /* 20px */
--space-6: 1.5rem       /* 24px */
--space-8: 2rem         /* 32px */
--space-10: 2.5rem      /* 40px */
--space-12: 3rem        /* 48px */
--space-16: 4rem        /* 64px */
```

### 组件间距
- **组件内边距**: 16px (--space-4)
- **组件间距**: 24px (--space-6)
- **区域间距**: 32px (--space-8)
- **页面边距**: 40px (--space-10)

## 🔲 组件设计规范

### 1. 按钮组件

#### 主要按钮 (Primary Button)
```css
.btn-primary {
  background: linear-gradient(135deg, #0ea5e9 0%, #0284c7 100%);
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 600;
  font-size: 14px;
  min-height: 44px;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #0284c7 0%, #0369a1 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(14, 165, 233, 0.3);
}
```

#### 次要按钮 (Secondary Button)
```css
.btn-secondary {
  background: white;
  color: #334155;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 24px;
  font-weight: 500;
  font-size: 14px;
  min-height: 44px;
}

.btn-secondary:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}
```

### 2. 输入框组件

#### 文本输入框
```css
.input {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  color: #0f172a;
  transition: border-color 0.2s ease;
}

.input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}
```

### 3. 卡片组件

#### 基础卡片
```css
.card {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease;
}

.card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

## 🖼️ 界面布局规范

### 1. 主窗口布局

#### 整体结构
```
┌─────────────────────────────────────────────────────────┐
│  Header (高度: 64px)                                     │
├─────────────────────────────────────────────────────────┤
│  Main Content Area                                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│  │  Sidebar    │ │  Content    │ │  Preview    │        │
│  │  (280px)    │ │  (flex-1)   │ │  (400px)    │        │
│  │             │ │             │ │             │        │
│  │             │ │             │ │             │        │
│  └─────────────┘ └─────────────┘ └─────────────┘        │
├─────────────────────────────────────────────────────────┤
│  Footer (高度: 48px)                                     │
└─────────────────────────────────────────────────────────┘
```

#### 响应式断点
```css
/* 桌面端 */
@media (min-width: 1024px) {
  /* 三栏布局 */
}

/* 平板端 */
@media (max-width: 1023px) {
  /* 两栏布局，隐藏侧边栏 */
}

/* 移动端 */
@media (max-width: 768px) {
  /* 单栏布局，堆叠显示 */
}
```

### 2. 拖拽上传区域

#### 设计规范
```css
.drop-zone {
  border: 2px dashed #cbd5e1;
  border-radius: 12px;
  background: #f8fafc;
  padding: 48px 24px;
  text-align: center;
  transition: all 0.2s ease;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.drop-zone.drag-active {
  border-color: #0ea5e9;
  background: #f0f9ff;
  transform: scale(1.02);
}
```

#### 图标设计
- 使用线性图标风格
- 图标大小: 48px
- 颜色: #64748b (中性灰)
- 悬停时颜色: #0ea5e9 (主色)

### 3. 文件列表组件

#### 列表项设计
```css
.file-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  gap: 12px;
}

.file-item:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.file-item.selected {
  background: #f0f9ff;
  border-color: #0ea5e9;
}
```

#### 状态指示器
```css
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-ready { background: #64748b; }
.status-processing { 
  background: #f59e0b; 
  animation: pulse 2s infinite;
}
.status-completed { background: #10b981; }
.status-failed { background: #ef4444; }
```

## 🎭 交互设计规范

### 1. 动画效果

#### 基础动画
```css
/* 淡入淡出 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* 脉冲效果 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}
```

#### 过渡效果
```css
/* 通用过渡 */
.transition {
  transition: all 0.2s ease;
}

/* 悬停效果 */
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### 2. 反馈机制

#### 加载状态
- 使用骨架屏显示加载状态
- 进度条显示处理进度
- 旋转图标表示正在加载

#### 成功反馈
- 绿色勾选图标
- 成功提示消息
- 轻微的动画效果

#### 错误反馈
- 红色警告图标
- 清晰的错误信息
- 重试操作按钮

## 📱 响应式设计

### 1. 断点系统
```css
/* 超小屏幕 */
@media (max-width: 640px) { /* sm */ }

/* 小屏幕 */
@media (max-width: 768px) { /* md */ }

/* 中等屏幕 */
@media (max-width: 1024px) { /* lg */ }

/* 大屏幕 */
@media (max-width: 1280px) { /* xl */ }

/* 超大屏幕 */
@media (min-width: 1281px) { /* 2xl */ }
```

### 2. 适配策略
- **桌面端**: 完整功能，三栏布局
- **平板端**: 简化布局，两栏显示
- **移动端**: 单栏布局，抽屉式导航

## 🔍 可访问性设计

### 1. 颜色对比度
- 文本与背景对比度 ≥ 4.5:1
- 大文本对比度 ≥ 3:1
- 非文本元素对比度 ≥ 3:1

### 2. 键盘导航
- 所有交互元素支持键盘访问
- 清晰的焦点指示器
- 逻辑的 Tab 顺序

### 3. 屏幕阅读器支持
- 语义化 HTML 结构
- 适当的 ARIA 标签
- 图片的替代文本

## 🎨 图标系统

### 1. 图标风格
- 使用 Lucide React 图标库
- 线性风格，2px 描边
- 24px 标准尺寸
- 一致的视觉风格

### 2. 常用图标
```typescript
// 文件操作
import { Upload, Download, File, Folder, Trash2 } from 'lucide-react'

// 状态指示
import { Check, X, AlertCircle, Clock, Play } from 'lucide-react'

// 界面控制
import { Settings, Menu, Search, Filter } from 'lucide-react'
```

## 📋 组件库规范

### 1. shadcn/ui 组件使用
- 按钮: Button 组件
- 输入框: Input 组件
- 对话框: Dialog 组件
- 下拉菜单: DropdownMenu 组件
- 进度条: Progress 组件

### 2. 自定义组件
- FileDropZone: 文件拖拽上传
- FileList: 文件列表显示
- ImagePreview: 图片预览对比
- ProcessingStatus: 处理状态显示

## 🎯 用户体验指导原则

### 1. 操作流程优化
- 减少用户操作步骤
- 提供清晰的操作指引
- 支持批量操作

### 2. 信息架构
- 重要信息优先显示
- 相关功能就近放置
- 减少认知负担

### 3. 错误预防
- 输入验证和提示
- 危险操作确认
- 撤销和恢复功能

---

**文档版本**: v1.0  
**创建日期**: 2025年8月2日  
**更新日期**: 2025年8月2日  
**状态**: 待审核
