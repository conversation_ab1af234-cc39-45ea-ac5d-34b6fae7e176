# 水印去除桌面客户端 V2 - 技术架构设计文档

## 🏗️ 架构概览

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    桌面应用程序                                │
├─────────────────────────────────────────────────────────────┤
│  前端层 (React + shadcn/ui)                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │  UI 组件    │ │  状态管理   │ │  业务逻辑   │            │
│  │  shadcn/ui  │ │  Zustand    │ │  Hooks      │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  桌面框架层 (Tau<PERSON>)                                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │  窗口管理   │ │  文件系统   │ │  系统集成   │            │
│  │  Commands   │ │  API        │ │  通知/托盘  │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
├─────────────────────────────────────────────────────────────┤
│  后端服务层 (Python FastAPI)                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐            │
│  │  AI 服务    │ │  任务管理   │ │  文件处理   │            │
│  │  YOLO+LAMA  │ │  队列系统   │ │  图像处理   │            │
│  └─────────────┘ └─────────────┘ └─────────────┘            │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 技术栈选择

### 前端技术栈
- **React 18+**: 现代化的前端框架，提供组件化开发
- **TypeScript**: 类型安全，提升开发效率和代码质量
- **shadcn/ui**: 现代化的 UI 组件库，基于 Radix UI
- **Tailwind CSS**: 实用优先的 CSS 框架
- **Zustand**: 轻量级状态管理库
- **React Hook Form**: 表单处理
- **React Query**: 数据获取和缓存

### 桌面框架
- **Tauri 2.0**: 基于 Rust 的现代桌面应用框架
- **优势**: 
  - 更小的应用体积
  - 更好的性能
  - 更强的安全性
  - 跨平台支持

### 后端技术栈
- **Python 3.8+**: AI 模型的最佳支持语言
- **FastAPI**: 现代化的 Python Web 框架
- **PyTorch**: AI 模型推理框架
- **PIL/OpenCV**: 图像处理库
- **Uvicorn**: ASGI 服务器

## 🔧 系统架构设计

### 1. 前端架构

#### 组件层次结构
```
App
├── Layout
│   ├── Header
│   ├── Sidebar
│   └── Main
│       ├── DropZone
│       ├── FileList
│       ├── PreviewPanel
│       └── ControlPanel
└── Providers
    ├── ThemeProvider
    ├── StateProvider
    └── QueryProvider
```

#### 状态管理架构
```typescript
// 全局状态结构
interface AppState {
  files: FileItem[]
  processing: ProcessingState
  settings: AppSettings
  ui: UIState
}

// 状态切片
const useFileStore = create<FileState>()
const useProcessingStore = create<ProcessingState>()
const useSettingsStore = create<SettingsState>()
```

#### 组件设计原则
- **原子化设计**: 基于 shadcn/ui 的组件系统
- **可复用性**: 通用组件抽象
- **类型安全**: 完整的 TypeScript 类型定义
- **响应式**: 适配不同屏幕尺寸

### 2. Tauri 架构

#### 命令系统设计
```rust
// Tauri Commands
#[tauri::command]
async fn select_files() -> Result<Vec<String>, String>

#[tauri::command]
async fn start_processing(files: Vec<String>, settings: ProcessingSettings) -> Result<String, String>

#[tauri::command]
async fn get_processing_status(task_id: String) -> Result<ProcessingStatus, String>

#[tauri::command]
async fn save_file(source: String, destination: String) -> Result<(), String>
```

#### 事件系统
```rust
// 事件定义
pub enum AppEvent {
    ProcessingProgress { task_id: String, progress: f32 },
    ProcessingComplete { task_id: String, result: ProcessingResult },
    ProcessingError { task_id: String, error: String },
}
```

#### 文件系统集成
- 文件选择和保存对话框
- 拖拽文件处理
- 文件权限管理
- 临时文件清理

### 3. 后端架构

#### API 设计
```python
# FastAPI 路由结构
/api/v1/
├── /health              # 健康检查
├── /models/status       # 模型状态
├── /processing/
│   ├── /start          # 开始处理
│   ├── /status/{id}    # 查询状态
│   ├── /result/{id}    # 获取结果
│   └── /cancel/{id}    # 取消任务
└── /files/
    ├── /upload         # 文件上传
    └── /download/{id}  # 文件下载
```

#### 服务层设计
```python
# 服务层架构
class AIService:
    def __init__(self):
        self.detector = YOLODetector()
        self.inpainter = LAMAInpainter()
    
    async def process_image(self, image_path: str) -> ProcessingResult:
        # 检测 -> 修复 -> 返回结果
        pass

class TaskManager:
    def __init__(self):
        self.queue = asyncio.Queue()
        self.workers = []
    
    async def submit_task(self, task: ProcessingTask) -> str:
        # 任务提交和管理
        pass
```

## 🔄 通信机制设计

### 1. 前端-Tauri 通信

#### 命令调用模式
```typescript
// 前端调用 Tauri 命令
import { invoke } from '@tauri-apps/api/tauri'

const selectFiles = async (): Promise<string[]> => {
  return await invoke('select_files')
}

const startProcessing = async (files: string[], settings: ProcessingSettings): Promise<string> => {
  return await invoke('start_processing', { files, settings })
}
```

#### 事件监听模式
```typescript
// 前端监听 Tauri 事件
import { listen } from '@tauri-apps/api/event'

listen('processing_progress', (event) => {
  const { task_id, progress } = event.payload
  updateProcessingProgress(task_id, progress)
})
```

### 2. Tauri-Python 通信

#### HTTP API 调用
```rust
// Rust 调用 Python API
use reqwest::Client;

#[tauri::command]
async fn start_processing(files: Vec<String>, settings: ProcessingSettings) -> Result<String, String> {
    let client = Client::new();
    let response = client
        .post("http://localhost:8000/api/v1/processing/start")
        .json(&ProcessingRequest { files, settings })
        .send()
        .await?;
    
    let result: ProcessingResponse = response.json().await?;
    Ok(result.task_id)
}
```

#### WebSocket 实时通信
```rust
// 实时状态更新
use tokio_tungstenite::{connect_async, tungstenite::Message};

async fn listen_processing_updates() {
    let (ws_stream, _) = connect_async("ws://localhost:8000/ws").await.unwrap();
    
    while let Some(msg) = ws_stream.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                let update: ProcessingUpdate = serde_json::from_str(&text).unwrap();
                emit_event("processing_progress", update);
            }
            _ => {}
        }
    }
}
```

## 📊 数据流设计

### 1. 文件处理流程
```
用户选择文件 → 前端验证 → Tauri 文件API → 后端上传 → AI处理 → 结果返回 → 前端显示
```

### 2. 状态同步流程
```
后端状态变更 → WebSocket推送 → Tauri事件 → 前端状态更新 → UI重新渲染
```

### 3. 错误处理流程
```
错误发生 → 错误捕获 → 错误分类 → 用户友好提示 → 恢复建议
```

## 🔒 安全架构

### 1. 前端安全
- CSP (Content Security Policy) 配置
- XSS 防护
- 输入验证和清理

### 2. Tauri 安全
- 命令权限控制
- 文件系统访问限制
- 网络请求白名单

### 3. 后端安全
- API 认证和授权
- 文件上传安全检查
- 资源访问控制

## 📈 性能优化

### 1. 前端性能
- 组件懒加载
- 图片预览优化
- 虚拟滚动
- 状态更新优化

### 2. 桌面应用性能
- 内存管理优化
- 文件操作异步化
- 资源清理机制

### 3. 后端性能
- AI 模型预加载
- 任务队列优化
- 并发处理控制
- 缓存策略

## 🧪 测试架构

### 1. 前端测试
- 单元测试: Jest + React Testing Library
- 组件测试: Storybook
- E2E 测试: Playwright

### 2. Tauri 测试
- 单元测试: Rust 内置测试框架
- 集成测试: Tauri 测试工具

### 3. 后端测试
- 单元测试: pytest
- API 测试: FastAPI TestClient
- 性能测试: locust

## 📦 部署架构

### 1. 开发环境
```bash
# 前端开发服务器
pnpm dev

# Tauri 开发模式
pnpm tauri dev

# Python 后端服务
python -m uvicorn main:app --reload
```

### 2. 生产构建
```bash
# 构建桌面应用
pnpm tauri build

# 打包 Python 服务
pyinstaller --onefile main.py
```

### 3. 分发策略
- Windows: MSI 安装包
- macOS: DMG 镜像文件
- Linux: AppImage/DEB 包

## 🔧 开发工具链

### 1. 代码质量
- ESLint + Prettier (前端)
- Clippy + rustfmt (Rust)
- Black + isort (Python)

### 2. 类型检查
- TypeScript (前端)
- Rust 编译器 (Tauri)
- mypy (Python)

### 3. 构建工具
- Vite (前端构建)
- Cargo (Rust 构建)
- Poetry (Python 依赖管理)

---

**文档版本**: v1.0  
**创建日期**: 2025年8月2日  
**更新日期**: 2025年8月2日  
**状态**: 待审核
