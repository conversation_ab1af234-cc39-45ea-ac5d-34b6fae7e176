// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::process::{Child, Command, Stdio};
use tauri::{Emitter, State, Manager};
use tokio::sync::Mutex;
use uuid::Uuid;

// 应用状态
#[derive(Default)]
struct AppState {
    processing_tasks: Mutex<HashMap<String, ProcessingTask>>,
    backend_process: Mutex<Option<Child>>,
}

// 处理任务结构
#[derive(Debug, Clone, Serialize, Deserialize)]
struct ProcessingTask {
    id: String,
    files: Vec<String>,
    status: TaskStatus,
    progress: f32,
    result: Option<ProcessingResult>,
    error: Option<String>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
enum TaskStatus {
    Pending,
    Processing,
    Completed,
    Failed,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
struct ProcessingResult {
    processed_files: Vec<ProcessedFile>,
    total_time: f64,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
struct ProcessedFile {
    input_path: String,
    output_path: String,
    success: bool,
    error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct ProcessingSettings {
    confidence_threshold: f32,
    enhance_mask: bool,
    use_smart_enhancement: bool,
    context_expansion_ratio: f32,
    output_directory: Option<String>,
    keep_original_name: bool,
}

// Tauri 命令

#[tauri::command]
async fn select_files(app_handle: tauri::AppHandle) -> Result<Vec<String>, String> {
    use tauri_plugin_dialog::DialogExt;
    use std::sync::{Arc, Mutex};
    use tokio::sync::oneshot;

    let (tx, rx) = oneshot::channel();
    let tx = Arc::new(Mutex::new(Some(tx)));

    app_handle
        .dialog()
        .file()
        .add_filter("图片文件", &["jpg", "jpeg", "png", "webp", "bmp", "tiff"])
        .set_title("选择要处理的图片文件")
        .pick_files(move |file_paths| {
            if let Ok(mut sender) = tx.lock() {
                if let Some(tx) = sender.take() {
                    let _ = tx.send(file_paths);
                }
            }
        });

    let files = rx.await.map_err(|e| format!("文件选择失败: {}", e))?;

    match files {
        Some(file_paths) => {
            let paths: Vec<String> = file_paths
                .into_iter()
                .map(|path| path.to_string())
                .collect();

            log::info!("用户选择了 {} 个文件", paths.len());
            for path in &paths {
                log::info!("选择的文件: {}", path);
            }

            Ok(paths)
        }
        None => {
            log::info!("用户取消了文件选择");
            Ok(vec![])
        }
    }
}

#[tauri::command]
async fn start_processing(
    files: Vec<String>,
    _settings: ProcessingSettings,
    state: State<'_, AppState>,
    _app: tauri::AppHandle,
) -> Result<String, String> {
    let task_id = Uuid::new_v4().to_string();
    
    let task = ProcessingTask {
        id: task_id.clone(),
        files: files.clone(),
        status: TaskStatus::Pending,
        progress: 0.0,
        result: None,
        error: None,
    };
    
    // 存储任务
    {
        let mut tasks = state.processing_tasks.lock().await;
        tasks.insert(task_id.clone(), task);
    }
    
    // TODO: 启动异步处理
    // 暂时注释掉异步处理，后续实现
    log::info!("Task {} created with {} files", task_id, files.len());
    
    Ok(task_id)
}

#[tauri::command]
async fn get_processing_status(
    task_id: String,
    state: State<'_, AppState>,
) -> Result<Option<ProcessingTask>, String> {
    let tasks = state.processing_tasks.lock().await;
    Ok(tasks.get(&task_id).cloned())
}

#[tauri::command]
async fn cancel_processing(
    task_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    let mut tasks = state.processing_tasks.lock().await;
    if let Some(task) = tasks.get_mut(&task_id) {
        task.status = TaskStatus::Failed;
        task.error = Some("Cancelled by user".to_string());
        Ok(true)
    } else {
        Ok(false)
    }
}

#[tauri::command]
async fn save_file_dialog(app_handle: tauri::AppHandle, default_name: String) -> Result<Option<String>, String> {
    use tauri_plugin_dialog::DialogExt;
    use std::sync::{Arc, Mutex};
    use tokio::sync::oneshot;

    let (tx, rx) = oneshot::channel();
    let tx = Arc::new(Mutex::new(Some(tx)));

    app_handle
        .dialog()
        .file()
        .add_filter("图片文件", &["jpg", "jpeg", "png", "webp"])
        .set_file_name(&default_name)
        .set_title("保存处理后的图片")
        .save_file(move |file_path| {
            if let Ok(mut sender) = tx.lock() {
                if let Some(tx) = sender.take() {
                    let _ = tx.send(file_path);
                }
            }
        });

    let file_path = rx.await.map_err(|e| format!("保存对话框失败: {}", e))?;

    match file_path {
        Some(path) => {
            let path_str = path.to_string();
            log::info!("用户选择保存路径: {}", path_str);
            Ok(Some(path_str))
        }
        None => {
            log::info!("用户取消了保存");
            Ok(None)
        }
    }
}

#[tauri::command]
async fn save_file_to_path(
    source_path: String,
    destination_path: String,
) -> Result<(), String> {
    std::fs::copy(&source_path, &destination_path)
        .map_err(|e| format!("Failed to save file: {}", e))?;

    Ok(())
}

#[tauri::command]
async fn get_app_version() -> Result<String, String> {
    Ok(env!("CARGO_PKG_VERSION").to_string())
}

#[tauri::command]
async fn get_file_info(file_path: String) -> Result<serde_json::Value, String> {
    use std::fs;
    use image::GenericImageView;

    // 获取文件基本信息
    let metadata = fs::metadata(&file_path)
        .map_err(|e| format!("无法读取文件信息: {}", e))?;

    let file_size = metadata.len();

    // 尝试读取图片尺寸
    let dimensions = match image::open(&file_path) {
        Ok(img) => {
            let (width, height) = img.dimensions();
            Some(serde_json::json!({
                "width": width,
                "height": height
            }))
        },
        Err(_) => None
    };

    Ok(serde_json::json!({
        "size": file_size,
        "dimensions": dimensions
    }))
}

// 异步处理函数 (预留给未来使用)
#[allow(dead_code)]
async fn process_files_async(
    task_id: String,
    files: Vec<String>,
    settings: ProcessingSettings,
    state: &AppState,
    app: tauri::AppHandle,
) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    // 更新任务状态为处理中
    {
        let mut tasks = state.processing_tasks.lock().await;
        if let Some(task) = tasks.get_mut(&task_id) {
            task.status = TaskStatus::Processing;
        }
    }
    
    // 发送状态更新事件
    let _ = app.emit("processing_started", &task_id);
    
    // 调用 Python 后端 API
    let client = reqwest::Client::new();
    let api_url = "http://localhost:8000/api/v1/processing/start";
    
    let request_body = serde_json::json!({
        "files": files,
        "settings": settings
    });
    
    match client.post(api_url).json(&request_body).send().await {
        Ok(response) => {
            if response.status().is_success() {
                // 处理成功响应
                match response.json::<ProcessingResult>().await {
                    Ok(result) => {
                        // 更新任务状态
                        {
                            let mut tasks = state.processing_tasks.lock().await;
                            if let Some(task) = tasks.get_mut(&task_id) {
                                task.status = TaskStatus::Completed;
                                task.progress = 100.0;
                                task.result = Some(result.clone());
                            }
                        }
                        
                        // 发送完成事件
                        let _ = app.emit("processing_completed", (&task_id, &result));
                    }
                    Err(e) => {
                        // JSON 解析错误
                        let error_msg = format!("Failed to parse response: {}", e);
                        update_task_error(&state, &task_id, &error_msg).await;
                        let _ = app.emit("processing_failed", (&task_id, &error_msg));
                    }
                }
            } else {
                // HTTP 错误
                let error_msg = format!("HTTP error: {}", response.status());
                update_task_error(&state, &task_id, &error_msg).await;
                let _ = app.emit("processing_failed", (&task_id, &error_msg));
            }
        }
        Err(e) => {
            // 网络错误
            let error_msg = format!("Network error: {}", e);
            update_task_error(&state, &task_id, &error_msg).await;
            let _ = app.emit("processing_failed", (&task_id, &error_msg));
        }
    }
    
    Ok(())
}

#[allow(dead_code)]
async fn update_task_error(state: &AppState, task_id: &str, error_msg: &str) {
    let mut tasks = state.processing_tasks.lock().await;
    if let Some(task) = tasks.get_mut(task_id) {
        task.status = TaskStatus::Failed;
        task.error = Some(error_msg.to_string());
    }
}

// 检测 Python 环境
fn detect_python() -> Result<String, String> {
    let python_commands = ["python", "python3", "py"];

    for cmd in &python_commands {
        match std::process::Command::new(cmd).arg("--version").output() {
            Ok(output) if output.status.success() => {
                let version = String::from_utf8_lossy(&output.stdout);
                println!("✅ 找到 Python: {} - {}", cmd, version.trim());
                return Ok(cmd.to_string());
            }
            _ => continue,
        }
    }

    Err("未找到 Python 环境".to_string())
}

// 检查并安装 Python 依赖
fn ensure_python_dependencies(python_cmd: &str, backend_path: &std::path::Path) -> Result<(), String> {
    println!("🔍 检查 Python 依赖...");

    let requirements_file = backend_path.join("requirements.txt");
    if !requirements_file.exists() {
        return Err("requirements.txt 文件不存在".to_string());
    }

    // 检查是否已安装关键依赖
    let check_cmd = std::process::Command::new(python_cmd)
        .args(&["-c", "import fastapi, uvicorn, torch; print('Dependencies OK')"])
        .output();

    match check_cmd {
        Ok(output) if output.status.success() => {
            println!("✅ Python 依赖已安装");
            return Ok(());
        }
        _ => {
            println!("📦 正在安装 Python 依赖...");

            let install_result = std::process::Command::new(python_cmd)
                .args(&["-m", "pip", "install", "-r", "requirements.txt"])
                .current_dir(backend_path)
                .output();

            match install_result {
                Ok(output) if output.status.success() => {
                    println!("✅ Python 依赖安装成功");
                    Ok(())
                }
                Ok(output) => {
                    let error = String::from_utf8_lossy(&output.stderr);
                    Err(format!("依赖安装失败: {}", error))
                }
                Err(e) => Err(format!("无法执行依赖安装: {}", e))
            }
        }
    }
}

// 同步版本的后端服务启动函数
fn start_backend_service_sync(app_handle: &tauri::AppHandle) -> Result<Child, String> {
    println!("🚀🚀🚀 start_backend_service_sync 函数被调用！🚀🚀🚀");
    log::info!("🚀 开始启动后端服务 (同步版本)...");

    // 检测 Python 环境
    let python_cmd = detect_python()?;

    // 获取后端路径
    let backend_path = if cfg!(debug_assertions) {
        // 开发模式：从 src-tauri 目录向上一级，然后进入 python-backend
        std::env::current_dir()
            .map_err(|e| format!("无法获取当前目录: {}", e))?
            .parent()
            .ok_or("无法获取父目录")?
            .join("python-backend")
    } else {
        // 生产模式：从资源目录获取
        app_handle
            .path()
            .resource_dir()
            .map_err(|e| format!("无法获取资源目录: {}", e))?
            .join("python-backend")
    };

    println!("📁 后端路径: {:?}", backend_path);

    // 检查 Python 后端目录是否存在
    if !backend_path.exists() {
        return Err(format!("后端服务目录不存在: {:?}", backend_path));
    }

    // 检查 main.py 是否存在
    let main_py = backend_path.join("main.py");
    if !main_py.exists() {
        return Err(format!("后端主文件不存在: {:?}", main_py));
    }

    // 确保依赖已安装
    ensure_python_dependencies(&python_cmd, &backend_path)?;

    // 启动 Python 脚本
    let mut cmd = Command::new(&python_cmd);
    cmd.arg("main.py")
       .current_dir(&backend_path)
       .stdout(Stdio::piped())
       .stderr(Stdio::piped());

    println!("🚀 启动命令: {} main.py", python_cmd);
    let child = cmd.spawn()
        .map_err(|e| format!("启动后端服务失败: {}", e))?;

    println!("✅ 后端启动成功");
    Ok(child)
}

// 异步版本的后端服务管理函数 (保留用于兼容性)
async fn start_backend_service(app_handle: &tauri::AppHandle) -> Result<Child, String> {
    println!("🚀🚀🚀 start_backend_service 函数被调用！🚀🚀🚀");
    log::info!("🚀 开始启动后端服务...");

    // 在开发环境中，使用 Python 脚本
    #[cfg(debug_assertions)]
    {
        // 从 src-tauri 目录向上一级，然后进入 python-backend
        let backend_path = std::env::current_dir()
            .map_err(|e| format!("无法获取当前目录: {}", e))?
            .parent()
            .ok_or("无法获取父目录")?
            .join("python-backend");

        log::info!("🚀 启动后端服务 (开发模式)，路径: {:?}", backend_path);

        // 检查 Python 后端目录是否存在
        if !backend_path.exists() {
            return Err(format!("后端服务目录不存在: {:?}", backend_path));
        }

        // 检查 main.py 是否存在
        let main_py = backend_path.join("main.py");
        if !main_py.exists() {
            return Err(format!("后端主文件不存在: {:?}", main_py));
        }

        // 启动 Python 后端服务
        let mut cmd = Command::new("python");
        cmd.arg("main.py")
            .current_dir(&backend_path)
            .env("PYTHONPATH", &backend_path);

        let child = cmd.spawn()
            .map_err(|e| format!("启动后端服务失败: {}", e))?;

        log::info!("✅ 后端服务已启动 (开发模式)，PID: {}", child.id());

        // 等待一段时间让服务启动
        tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;

        // 检查服务是否正常启动
        match check_backend_health().await {
            Ok(_) => {
                log::info!("✅ 后端服务健康检查通过");
                Ok(child)
            }
            Err(e) => {
                log::error!("❌ 后端服务健康检查失败: {}", e);
                Err(format!("后端服务启动失败: {}", e))
            }
        }
    }

    // 在生产环境中，使用打包的可执行文件
    #[cfg(not(debug_assertions))]
    {
        println!("🏭 生产模式：启动打包的后端可执行文件");

        // 获取应用资源目录
        let resource_dir = app_handle
            .path()
            .resource_dir()
            .map_err(|e| format!("无法获取资源目录: {}", e))?;

        println!("📁 资源目录: {:?}", resource_dir);

        let backend_executable = resource_dir.join("resources").join("watermark-backend");

        log::info!("🚀 启动后端服务 (生产模式)，可执行文件: {:?}", backend_executable);
        log::info!("📁 资源目录: {:?}", resource_dir);

        // 列出资源目录内容进行调试
        if let Ok(entries) = std::fs::read_dir(&resource_dir) {
            log::info!("📂 资源目录内容:");
            for entry in entries {
                if let Ok(entry) = entry {
                    log::info!("  - {:?}", entry.path());
                }
            }
        }

        // 检查可执行文件是否存在
        if !backend_executable.exists() {
            log::error!("❌ 后端可执行文件不存在: {:?}", backend_executable);
            return Err(format!("后端可执行文件不存在: {:?}", backend_executable));
        }

        // 启动独立的后端可执行文件
        let mut cmd = Command::new(&backend_executable);

        // 在 Windows 中隐藏控制台窗口
        #[cfg(windows)]
        {
            use std::os::windows::process::CommandExt;
            cmd.creation_flags(0x08000000); // CREATE_NO_WINDOW
        }

        let child = cmd.spawn()
            .map_err(|e| format!("启动后端服务失败: {}", e))?;

        log::info!("✅ 后端服务已启动 (生产模式)，PID: {}", child.id());

        // 等待一段时间让服务启动
        tokio::time::sleep(tokio::time::Duration::from_secs(5)).await;

        // 检查服务是否正常启动
        match check_backend_health().await {
            Ok(_) => {
                log::info!("✅ 后端服务健康检查通过");
                Ok(child)
            }
            Err(e) => {
                log::error!("❌ 后端服务健康检查失败: {}", e);
                Err(format!("后端服务启动失败: {}", e))
            }
        }
    }
}

async fn check_backend_health() -> Result<(), String> {
    let client = reqwest::Client::new();
    let health_url = "http://localhost:8000/api/v1/health";

    for attempt in 1..=10 {
        match client.get(health_url).send().await {
            Ok(response) => {
                if response.status().is_success() {
                    return Ok(());
                }
            }
            Err(_) => {
                if attempt < 10 {
                    tokio::time::sleep(tokio::time::Duration::from_millis(500)).await;
                    continue;
                }
            }
        }
    }

    Err("后端服务健康检查超时".to_string())
}

async fn stop_backend_service(state: &AppState) {
    let mut backend_process = state.backend_process.lock().await;
    if let Some(mut child) = backend_process.take() {
        log::info!("🛑 正在停止后端服务...");
        match child.kill() {
            Ok(_) => {
                let _ = child.wait();
                log::info!("✅ 后端服务已停止");
            }
            Err(e) => {
                log::error!("❌ 停止后端服务失败: {}", e);
            }
        }
    }
}

fn main() {
    env_logger::init();
    
    tauri::Builder::default()
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_shell::init())
        .manage(AppState::default())
        .invoke_handler(tauri::generate_handler![
            select_files,
            start_processing,
            get_processing_status,
            cancel_processing,
            save_file_dialog,
            save_file_to_path,
            get_app_version,
            get_file_info
        ])
        .setup(|app| {
            // 使用 println! 确保日志一定会输出到控制台
            println!("🚀🚀🚀 TAURI SETUP 函数被调用！🚀🚀🚀");
            log::info!("🎯 Watermark Remover V2 启动中...");

            let app_handle = app.handle().clone();
            let state_handle = app.handle().clone();

            println!("🔄 准备启动后端服务...");

            // 使用同步方式直接启动后端进程，避免异步复杂性
            match start_backend_service_sync(&app_handle) {
                Ok(child) => {
                    println!("✅ 后端服务启动成功！");
                    log::info!("✅ 后端服务启动成功");

                    // 在异步上下文中保存进程句柄
                    tauri::async_runtime::spawn(async move {
                        let state = state_handle.state::<AppState>();
                        let mut backend_process = state.backend_process.lock().await;
                        *backend_process = Some(child);
                    });

                    // 发送服务启动成功事件
                    let _ = app_handle.emit("backend_service_started", ());
                }
                Err(e) => {
                    println!("❌ 后端服务启动失败: {}", e);
                    log::error!("❌ 后端服务启动失败: {}", e);
                    // 发送服务启动失败事件
                    let _ = app_handle.emit("backend_service_failed", e);
                }
            }

            println!("✅ Setup 函数完成");
            Ok(())
        })
        .on_window_event(|window, event| {
            if let tauri::WindowEvent::CloseRequested { .. } = event {
                let app_handle = window.app_handle().clone();
                std::thread::spawn(move || {
                    tauri::async_runtime::block_on(async move {
                        let state = app_handle.state::<AppState>();
                        stop_backend_service(&state).await;
                    });
                });
            }
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
