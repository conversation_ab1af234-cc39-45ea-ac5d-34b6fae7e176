{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "Watermark Remover V2", "version": "0.1.0", "identifier": "com.watermark.remover.v2", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "devUrl": "http://localhost:5173", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Watermark Remover V2", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "transparent": false, "alwaysOnTop": false, "center": true, "skipTaskbar": false}], "security": {"csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost data: blob: http://localhost:8000; style-src 'self' 'unsafe-inline'; font-src 'self' data:; script-src 'self' 'unsafe-eval'; connect-src 'self' http://localhost:8000 ws://localhost:8000"}}, "bundle": {"active": true, "targets": "all", "icon": [], "resources": [], "externalBin": [], "copyright": "© 2024 Watermark Remover", "category": "Utility", "shortDescription": "AI-powered watermark removal tool", "longDescription": "A modern desktop application for intelligent watermark detection and removal using advanced AI models. Features automatic watermark detection and seamless removal with high-quality results.", "publisher": "Watermark Remover Team", "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "entitlements": null}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "nsis": null}, "linux": {"deb": {"depends": []}, "appimage": {"bundleMediaFramework": false}}}, "plugins": {}}