{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "Watermark Remover V2", "version": "0.1.0", "identifier": "com.watermark.remover.v2", "build": {"beforeDevCommand": "pnpm dev", "beforeBuildCommand": "pnpm build", "devUrl": "http://localhost:5173", "frontendDist": "../dist"}, "app": {"windows": [{"title": "Watermark Remover V2", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "fullscreen": false, "decorations": true, "transparent": false, "alwaysOnTop": false, "center": true, "skipTaskbar": false}], "security": {"csp": "default-src 'self'; img-src 'self' asset: https://asset.localhost data: blob: http://localhost:8000; style-src 'self' 'unsafe-inline'; font-src 'self' data:; script-src 'self' 'unsafe-eval'; connect-src 'self' http://localhost:8000 ws://localhost:8000"}}, "bundle": {"active": true, "targets": "all", "icon": [], "resources": ["resources/watermark-backend", "resources/models/**/*"], "externalBin": [], "copyright": "", "category": "Utility", "shortDescription": "Modern watermark removal tool", "longDescription": "A modern desktop application for intelligent watermark detection and removal using AI models.", "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": ""}, "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": ""}}, "plugins": {}}