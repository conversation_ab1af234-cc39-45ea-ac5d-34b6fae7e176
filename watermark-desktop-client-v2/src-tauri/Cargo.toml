[package]
name = "watermark-remover-v2"
version = "0.1.0"
description = "A modern watermark removal desktop application"
authors = ["Watermark Team"]
license = "MIT"
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [], default-features = false }

[dependencies]
tauri = { version = "2.0", features = [
  "image-ico",
  "image-png"
] }
tauri-plugin-dialog = "2.0"
tauri-plugin-fs = "2.0"
tauri-plugin-opener = "2.0"
tauri-plugin-shell = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.12", features = ["json"] }
uuid = { version = "1.0", features = ["v4"] }
log = "0.4"
env_logger = "0.11"
image = "0.24"

[target."cfg(not(any(target_os = \"android\", target_os = \"ios\")))".dependencies]
tauri-plugin-updater = "2.0"

[features]
# This feature is used for production builds or when a dev server is not specified, DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
