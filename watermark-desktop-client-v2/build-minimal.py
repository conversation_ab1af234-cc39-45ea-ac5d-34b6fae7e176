#!/usr/bin/env python3
"""
最小化构建脚本 - 先验证基本功能
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    print("🔧 最小化构建测试")
    print("=" * 40)
    
    project_root = Path(__file__).parent
    print(f"📁 项目根目录: {project_root}")
    
    # 切换到项目目录
    os.chdir(project_root)
    
    try:
        # 1. 构建前端
        print("🎨 构建前端...")
        result = subprocess.run(["pnpm", "build"], check=True)
        print("✅ 前端构建成功")
        
        # 2. 尝试 Tauri 开发模式构建
        print("🚀 尝试 Tauri 开发构建...")
        result = subprocess.run(["pnpm", "tauri", "dev", "--no-watch"], timeout=30)
        print("✅ Tauri 开发模式测试完成")
        
    except subprocess.TimeoutExpired:
        print("✅ Tauri 开发模式启动成功（超时正常）")
    except subprocess.CalledProcessError as e:
        print(f"❌ 构建失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 发生异常: {e}")
        return False
    
    print("🎉 最小化测试完成！")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
