# Dependencies
node_modules/
.pnpm-debug.log*

# Build outputs
dist/
build/
target/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/

# AI Models (too large for git)
# 忽略模型文件但保留 Python 代码
python-backend/models/**/*.pt
python-backend/models/**/*.ckpt
python-backend/models/**/*.pth
python-backend/models/**/*.bin
python-backend/models/**/*.safetensors
python-backend/models/**/*.h5
python-backend/models/**/*.hdf5
python-backend/models/**/*.onnx
python-backend/models/**/*.weights
python-backend/models/**/*.cfg
python-backend/models/**/big-lama/
python-backend/models/**/*.chunks/

# 通用模型文件扩展名
*.pt
*.ckpt
*.pth
*.bin

# 但是要包含 Python 代码文件
!python-backend/models/*.py
!python-backend/models/__init__.py

# Temporary files
temp/
tmp/
uploads/
outputs/
static/

# Coverage reports
coverage/
.nyc_output/
.coverage

# Tauri
src-tauri/target/
src-tauri/Cargo.lock

# Package managers
package-lock.json
yarn.lock
