/**
 * 连接状态管理 Store
 * 管理与后端服务的连接状态，包括 HTTP 和 WebSocket 连接
 */

import { create } from 'zustand'
import { subscribeWithSelector } from 'zustand/middleware'
import { eventBus } from '../lib/event-bus'
import { wsClient, WebSocketStatus } from '../lib/websocket'
import { healthAPI } from '../lib/api'

export interface ConnectionState {
  // HTTP 连接状态
  httpConnected: boolean
  httpLastCheck: number | null
  httpError: string | null

  // WebSocket 连接状态
  wsConnected: boolean
  wsConnectionState: string
  wsError: string | null
  wsReconnectAttempts: number

  // 服务状态
  backendVersion: string | null
  aiServiceStatus: string | null
  modelsStatus: Record<string, boolean>

  // 操作方法
  checkHttpConnection: () => Promise<void>
  connectWebSocket: () => Promise<void>
  disconnectWebSocket: () => void
  resetConnection: () => void
  setHttpConnected: (connected: boolean, error?: string) => void
  setWsConnected: (connected: boolean, error?: string) => void
  updateServiceStatus: (status: any) => void
}

export const useConnectionStore = create<ConnectionState>()(
  subscribeWithSelector((set, get) => ({
    // 初始状态
    httpConnected: false,
    httpLastCheck: null,
    httpError: null,
    wsConnected: false,
    wsConnectionState: 'disconnected',
    wsError: null,
    wsReconnectAttempts: 0,
    backendVersion: null,
    aiServiceStatus: null,
    modelsStatus: {},

    // 检查 HTTP 连接
    checkHttpConnection: async () => {
      try {
        const healthData = await healthAPI.check()
        set({
          httpConnected: true,
          httpLastCheck: Date.now(),
          httpError: null,
          backendVersion: healthData.version,
          aiServiceStatus: healthData.ai_service_status,
          modelsStatus: healthData.models_status,
        })
      } catch (error) {
        set({
          httpConnected: false,
          httpLastCheck: Date.now(),
          httpError: error instanceof Error ? error.message : 'Connection failed',
        })
      }
    },

    // 连接 WebSocket
    connectWebSocket: async () => {
      try {
        wsClient.connect()
        // 状态会通过事件监听器更新
      } catch (error) {
        set({
          wsConnected: false,
          wsConnectionState: 'disconnected',
          wsError: error instanceof Error ? error.message : 'WebSocket connection failed',
        })
      }
    },

    // 断开 WebSocket
    disconnectWebSocket: () => {
      wsClient.disconnect()
      set({
        wsConnected: false,
        wsConnectionState: 'disconnected',
        wsError: null,
      })
    },

    // 重置连接状态
    resetConnection: () => {
      set({
        httpConnected: false,
        httpLastCheck: null,
        httpError: null,
        wsConnected: false,
        wsConnectionState: 'disconnected',
        wsError: null,
        wsReconnectAttempts: 0,
        backendVersion: null,
        aiServiceStatus: null,
        modelsStatus: {},
      })
    },

    // 设置 HTTP 连接状态
    setHttpConnected: (connected: boolean, error?: string) => {
      set({
        httpConnected: connected,
        httpLastCheck: Date.now(),
        httpError: error || null,
      })
    },

    // 设置 WebSocket 连接状态
    setWsConnected: (connected: boolean, error?: string) => {
      set({
        wsConnected: connected,
        wsConnectionState: connected ? 'connected' : 'disconnected',
        wsError: error || null,
      })
    },

    // 更新服务状态
    updateServiceStatus: (status: any) => {
      set({
        backendVersion: status.version || get().backendVersion,
        aiServiceStatus: status.ai_service_status || get().aiServiceStatus,
        modelsStatus: status.models_status || get().modelsStatus,
      })
    },
  }))
)

// 初始化连接管理
export const initializeConnectionManager = () => {
  const store = useConnectionStore.getState()

  // 监听事件总线事件
  eventBus.on('connection:established', () => {
    store.setHttpConnected(true)
  })

  eventBus.on('connection:lost', () => {
    store.setHttpConnected(false, 'Connection lost')
  })

  // 监听 WebSocket 事件
  eventBus.on('websocket:connected', () => {
    store.setWsConnected(true)
  })

  eventBus.on('websocket:status:changed', ({ status }) => {
    const connected = status === WebSocketStatus.CONNECTED
    const error = status === WebSocketStatus.ERROR ? 'WebSocket error' :
                  status === WebSocketStatus.DISCONNECTED ? 'Disconnected' : null
    store.setWsConnected(connected, error || undefined)
  })

  eventBus.on('websocket:error', ({ error }) => {
    store.setWsConnected(false, error)
  })

  // 监听处理相关的 WebSocket 消息
  eventBus.on('processing:update', ({ taskId, update }) => {
    if (update.progress !== undefined) {
      eventBus.emit('processing:progress', { taskId, progress: update.progress })
    }
  })

  // 注意：不要重复发出相同的事件，这会造成无限循环
  // 这些事件应该由 WebSocket 客户端或其他服务直接发出
  // eventBus.on('processing:completed', (data) => {
  //   eventBus.emit('processing:completed', data)  // 移除：造成无限循环
  // })

  // eventBus.on('processing:failed', (data) => {
  //   eventBus.emit('processing:failed', data)  // 移除：造成无限循环
  // })

  // 定期检查 HTTP 连接
  const checkInterval = setInterval(() => {
    store.checkHttpConnection()
  }, 30000) // 每30秒检查一次

  // 初始检查
  store.checkHttpConnection()

  // 设置初始 WebSocket 状态
  const initialWsStatus = wsClient.getStatus()
  store.setWsConnected(initialWsStatus === WebSocketStatus.CONNECTED)

  // 返回清理函数
  return () => {
    clearInterval(checkInterval)
    store.disconnectWebSocket()
  }
}

// 获取连接状态的便捷函数
export const getConnectionStatus = () => {
  const state = useConnectionStore.getState()
  return {
    isOnline: state.httpConnected && state.wsConnected,
    httpConnected: state.httpConnected,
    wsConnected: state.wsConnected,
    hasErrors: !!(state.httpError || state.wsError),
    errors: [state.httpError, state.wsError].filter(Boolean),
  }
}
