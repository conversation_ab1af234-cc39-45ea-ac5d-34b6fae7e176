import { create } from 'zustand'
import { devtools, persist } from 'zustand/middleware'
import { AppSettings, AISettings, UISettings, PerformanceSettings, OutputSettings } from '../types'

interface SettingsState {
  settings: AppSettings
  
  // Actions
  updateAISettings: (settings: Partial<AISettings>) => void
  updateUISettings: (settings: Partial<UISettings>) => void
  updatePerformanceSettings: (settings: Partial<PerformanceSettings>) => void
  updateOutputSettings: (settings: Partial<OutputSettings>) => void
  resetSettings: () => void
  loadSettings: () => Promise<void>
  saveSettings: () => Promise<void>
}

const defaultSettings: AppSettings = {
  ai: {
    confidence_threshold: 0.3,
    enhance_mask: true,
    use_smart_enhancement: true,
    context_expansion_ratio: 0.12,
  },
  ui: {
    theme: 'system',
    language: 'zh-CN',
    showThumbnails: true,
    compactMode: false,
  },
  performance: {
    useGPU: true,
    maxConcurrentTasks: 4,
    memoryLimit: 4096,
  },
  output: {
    defaultPath: '',
    keepOriginalName: true,
    outputFormat: 'same',
    quality: 95,
  },
}

export const useSettingsStore = create<SettingsState>()(
  devtools(
    persist(
      (set, _get) => ({
        settings: defaultSettings,

        updateAISettings: (newSettings: Partial<AISettings>) => {
          set((state) => ({
            settings: {
              ...state.settings,
              ai: { ...state.settings.ai, ...newSettings },
            },
          }))
        },

        updateUISettings: (newSettings: Partial<UISettings>) => {
          set((state) => ({
            settings: {
              ...state.settings,
              ui: { ...state.settings.ui, ...newSettings },
            },
          }))
        },

        updatePerformanceSettings: (newSettings: Partial<PerformanceSettings>) => {
          set((state) => ({
            settings: {
              ...state.settings,
              performance: { ...state.settings.performance, ...newSettings },
            },
          }))
        },

        updateOutputSettings: (newSettings: Partial<OutputSettings>) => {
          set((state) => ({
            settings: {
              ...state.settings,
              output: { ...state.settings.output, ...newSettings },
            },
          }))
        },

        resetSettings: () => {
          set({ settings: defaultSettings })
        },

        loadSettings: async () => {
          try {
            // 这里可以从 Tauri 的文件系统或配置文件加载设置
            // 目前使用 localStorage (通过 persist 中间件)
            console.log('Settings loaded from localStorage')
          } catch (error) {
            console.error('Failed to load settings:', error)
            set({ settings: defaultSettings })
          }
        },

        saveSettings: async () => {
          try {
            // 这里可以保存到 Tauri 的文件系统
            // 目前使用 localStorage (通过 persist 中间件)
            console.log('Settings saved to localStorage')
          } catch (error) {
            console.error('Failed to save settings:', error)
          }
        },
      }),
      {
        name: 'settings-store',
        version: 1,
      }
    ),
    {
      name: 'settings-store',
    }
  )
)
