/**
 * 批量选择状态管理
 * 管理文件的批量选择、下载路径设置等功能
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { FileItem, FileStatus } from '../types'

export interface BatchDownloadProgress {
  fileId: string
  fileName: string
  progress: number // 0-100
  status: 'pending' | 'downloading' | 'completed' | 'failed'
  error?: string
}

export interface BatchSelectionState {
  // 选择状态
  selectedFileIds: Set<string>
  isSelectionMode: boolean
  
  // 下载路径
  defaultDownloadPath: string
  currentDownloadPath: string
  
  // 下载状态
  isDownloading: boolean
  downloadProgress: BatchDownloadProgress[]
  totalProgress: number // 整体进度 0-100
  
  // Actions - 选择管理
  toggleFileSelection: (fileId: string) => void
  selectFiles: (fileIds: string[]) => void
  selectAllCompleted: (files: FileItem[]) => void
  clearSelection: () => void
  setSelectionMode: (enabled: boolean) => void
  
  // Actions - 路径管理
  setDefaultDownloadPath: (path: string) => void
  setCurrentDownloadPath: (path: string) => void
  resetCurrentPath: () => void
  
  // Actions - 下载管理
  startBatchDownload: (files: FileItem[]) => Promise<void>
  updateFileProgress: (fileId: string, progress: number, status?: BatchDownloadProgress['status']) => void
  setFileError: (fileId: string, error: string) => void
  completeBatchDownload: () => void
  cancelBatchDownload: () => void
  
  // Getters
  getSelectedFiles: (files: FileItem[]) => FileItem[]
  getCompletedFiles: (files: FileItem[]) => FileItem[]
  canDownload: (files: FileItem[]) => boolean
}

export const useBatchSelectionStore = create<BatchSelectionState>()(
  persist(
    (set, get) => ({
      // 初始状态
      selectedFileIds: new Set(),
      isSelectionMode: false,
      defaultDownloadPath: '',
      currentDownloadPath: '',
      isDownloading: false,
      downloadProgress: [],
      totalProgress: 0,

      // 选择管理
      toggleFileSelection: (fileId: string) => {
        const state = get()
        const newSelected = new Set(state.selectedFileIds)
        if (newSelected.has(fileId)) {
          newSelected.delete(fileId)
        } else {
          newSelected.add(fileId)
        }

        console.log(`🔄 切换文件选择: ${fileId}, 当前选中: ${newSelected.size} 个`)

        set({
          selectedFileIds: newSelected,
          isSelectionMode: newSelected.size > 0
        })
      },

      selectFiles: (fileIds: string[]) => {
        const state = get()
        const newSelected = new Set(state.selectedFileIds)
        fileIds.forEach(id => newSelected.add(id))

        console.log(`📋 批量选择文件: ${fileIds.length} 个, 总选中: ${newSelected.size} 个`)

        set({
          selectedFileIds: newSelected,
          isSelectionMode: newSelected.size > 0
        })
      },

      selectAllCompleted: (files: FileItem[]) => {
        const completedFiles = files.filter(f =>
          f.status === FileStatus.Completed && f.processedPath
        )
        const completedIds = completedFiles.map(f => f.id)

        const state = get()
        const allSelected = completedIds.every(id => state.selectedFileIds.has(id))

        if (allSelected) {
          // 如果全部已选中，则取消全选
          const newSelected = new Set(state.selectedFileIds)
          completedIds.forEach(id => newSelected.delete(id))

          console.log(`❌ 取消全选: ${completedIds.length} 个文件`)

          set({
            selectedFileIds: newSelected,
            isSelectionMode: newSelected.size > 0
          })
        } else {
          // 否则全选
          const newSelected = new Set([...state.selectedFileIds, ...completedIds])

          console.log(`✅ 全选完成文件: ${completedIds.length} 个`)

          set({
            selectedFileIds: newSelected,
            isSelectionMode: true
          })
        }
      },

      clearSelection: () => {
        console.log(`🧹 清空选择`)
        set({
          selectedFileIds: new Set(),
          isSelectionMode: false
        })
      },

      setSelectionMode: (enabled: boolean) => {
        const state = get()
        set({
          isSelectionMode: enabled,
          selectedFileIds: enabled ? state.selectedFileIds : new Set()
        })
      },

      // 路径管理
      setDefaultDownloadPath: (path: string) => {
        console.log(`📁 设置默认下载路径: ${path}`)
        set({
          defaultDownloadPath: path,
          currentDownloadPath: path
        })
      },

      setCurrentDownloadPath: (path: string) => {
        console.log(`📂 设置当前下载路径: ${path}`)
        set({ currentDownloadPath: path })
      },

      resetCurrentPath: () => {
        set((state) => ({
          currentDownloadPath: state.defaultDownloadPath
        }))
      },

      // 下载管理
      startBatchDownload: async (files: FileItem[]) => {
        const selectedFiles = get().getSelectedFiles(files)
        
        console.log(`🚀 开始批量下载: ${selectedFiles.length} 个文件`)
        
        const progress = selectedFiles.map(file => ({
          fileId: file.id,
          fileName: file.name,
          progress: 0,
          status: 'pending' as const
        }))

        set({
          isDownloading: true,
          downloadProgress: progress,
          totalProgress: 0
        })

        // 实际下载逻辑将在组件中实现
      },

      updateFileProgress: (fileId: string, progress: number, status = 'downloading') => {
        const state = get()
        const newProgress = state.downloadProgress.map(p =>
          p.fileId === fileId ? { ...p, progress, status } : p
        )

        // 计算总进度
        const totalProgress = newProgress.length > 0
          ? newProgress.reduce((sum, p) => sum + p.progress, 0) / newProgress.length
          : 0

        set({
          downloadProgress: newProgress,
          totalProgress: Math.round(totalProgress)
        })
      },

      setFileError: (fileId: string, error: string) => {
        const state = get()
        set({
          downloadProgress: state.downloadProgress.map(p =>
            p.fileId === fileId ? { ...p, status: 'failed' as const, error } : p
          )
        })
      },

      completeBatchDownload: () => {
        console.log(`✅ 批量下载完成`)
        set({
          isDownloading: false,
          downloadProgress: [],
          totalProgress: 0
        })
      },

      cancelBatchDownload: () => {
        console.log(`❌ 取消批量下载`)
        set({
          isDownloading: false,
          downloadProgress: [],
          totalProgress: 0
        })
      },

      // Getters
      getSelectedFiles: (files: FileItem[]) => {
        const { selectedFileIds } = get()
        return files.filter(file => selectedFileIds.has(file.id))
      },

      getCompletedFiles: (files: FileItem[]) => {
        return files.filter(file => 
          file.status === FileStatus.Completed && file.processedPath
        )
      },

      canDownload: (files: FileItem[]) => {
        const { selectedFileIds } = get()
        const selectedFiles = files.filter(file => selectedFileIds.has(file.id))
        return selectedFiles.length > 0 && 
               selectedFiles.every(file => file.status === FileStatus.Completed && file.processedPath)
      }
    }),
    {
      name: 'batch-selection-storage',
      partialize: (state) => ({
        defaultDownloadPath: state.defaultDownloadPath,
        currentDownloadPath: state.currentDownloadPath
      })
    }
  )
)
