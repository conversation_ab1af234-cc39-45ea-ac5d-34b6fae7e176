import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { FileItem, FileStatus } from '../types'

interface FileState {
  files: FileItem[]
  selectedFile: FileItem | null
  // 保存原始 File 对象的映射
  fileObjects: Map<string, File>

  // Actions
  addFiles: (files: File[]) => void
  addFileItems: (fileItems: FileItem[]) => void
  removeFile: (fileId: string) => void
  clearFiles: () => void
  selectFile: (file: FileItem | null) => void
  updateFileStatus: (fileId: string, status: FileStatus, error?: string) => void
  updateFileProgress: (fileId: string, progress: number) => void
  updateFilePath: (fileId: string, path: string) => void
  updateFileProcessedPath: (fileId: string, processedPath: string) => void
  updateFileProcessingResult: (fileId: string, result: {
    processedPath?: string
    detectionCount?: number
    avgConfidence?: number
    processingEndTime?: Date
  }) => void
  setFileTaskId: (fileId: string, taskId: string) => void
  getFileById: (fileId: string) => FileItem | undefined
  getFileObject: (fileId: string) => File | undefined
}

export const useFileStore = create<FileState>()(
  devtools(
    (set, get) => ({
      files: [],
      selectedFile: null,
      fileObjects: new Map<string, File>(),

      addFiles: (files: File[]) => {
        console.log('📁 添加文件到 store:', files.length, '个')

        const newFiles: FileItem[] = files.map((file) => {
          const fileId = crypto.randomUUID()
          const filePath = (file as any).path || file.name

          console.log('  - 文件:', file.name, '路径:', filePath)

          return {
            id: fileId,
            name: file.name,
            path: filePath,
            size: file.size,
            type: file.type,
            status: FileStatus.Ready,
            thumbnail: undefined,
            processedPath: undefined,
            error: undefined,
          }
        })

        set((state) => {
          // 保存原始 File 对象
          const newFileObjects = new Map(state.fileObjects)
          files.forEach((file, index) => {
            newFileObjects.set(newFiles[index].id, file)
          })

          console.log('✅ 文件已添加到 store，总数:', state.files.length + newFiles.length)

          return {
            files: [...state.files, ...newFiles],
            fileObjects: newFileObjects,
          }
        })

        // 如果没有选中文件，自动选中第一个新添加的文件
        const { selectedFile } = get()
        if (!selectedFile && newFiles.length > 0) {
          set({ selectedFile: newFiles[0] })
        }
      },

      addFileItems: (fileItems: FileItem[]) => {
        console.log('📁 直接添加 FileItem 到 store:', fileItems.length, '个')

        set((state) => {
          console.log('✅ FileItem 已添加到 store，总数:', state.files.length + fileItems.length)

          return {
            files: [...state.files, ...fileItems],
            fileObjects: state.fileObjects, // 保持现有的 fileObjects
          }
        })

        // 如果没有选中文件，自动选中第一个新添加的文件
        const { selectedFile } = get()
        if (!selectedFile && fileItems.length > 0) {
          set({ selectedFile: fileItems[0] })
        }
      },

      removeFile: (fileId: string) => {
        set((state) => {
          const newFiles = state.files.filter((file) => file.id !== fileId)
          const newSelectedFile = state.selectedFile?.id === fileId
            ? (newFiles.length > 0 ? newFiles[0] : null)
            : state.selectedFile

          // 移除对应的 File 对象
          const newFileObjects = new Map(state.fileObjects)
          newFileObjects.delete(fileId)

          return {
            files: newFiles,
            selectedFile: newSelectedFile,
            fileObjects: newFileObjects,
          }
        })
      },

      clearFiles: () => {
        set({
          files: [],
          selectedFile: null,
          fileObjects: new Map(),
        })
      },

      selectFile: (file: FileItem | null) => {
        set({ selectedFile: file })
      },

      updateFileStatus: (fileId: string, status: FileStatus, error?: string) => {
        console.log(`📊 Store: 更新文件状态 ${fileId} -> ${status}`)
        set((state) => {
          const updatedFiles = state.files.map((file) => {
            if (file.id === fileId) {
              console.log(`📊 Store: 文件状态已更新 ${file.name} -> ${status}`)
              return { ...file, status, error }
            }
            return file
          })
          return { files: updatedFiles }
        })
      },

      updateFileProgress: (fileId: string, progress: number) => {
        console.log(`📊 Store: 更新文件进度 ${fileId} -> ${progress}%`)
        set((state) => {
          const updatedFiles = state.files.map((file) =>
            file.id === fileId
              ? { ...file, progress }
              : file
          )
          console.log(`📊 Store: 文件进度已更新`, updatedFiles.find(f => f.id === fileId)?.progress)
          return { files: updatedFiles }
        })
      },

      updateFilePath: (fileId: string, path: string) => {
        set((state) => ({
          files: state.files.map((file) =>
            file.id === fileId
              ? { ...file, path }
              : file
          ),
        }))
        console.log(`📁 Store: 文件路径已更新 ${fileId} -> ${path}`)
      },

      updateFileProcessedPath: (fileId: string, processedPath: string) => {
        set((state) => ({
          files: state.files.map((file) =>
            file.id === fileId
              ? { ...file, processedPath }
              : file
          ),
        }))
        console.log(`📁 Store: 文件processedPath已更新 ${fileId} -> ${processedPath}`)
      },

      updateFileProcessingResult: (fileId: string, result: {
        processedPath?: string
        detectionCount?: number
        avgConfidence?: number
        processingEndTime?: Date
      }) => {
        set((state) => ({
          files: state.files.map((file) => {
            if (file.id === fileId) {
              // 只在没有结束时间或明确传入新结束时间时才更新
              const shouldUpdateEndTime = !file.processingEndTime || result.processingEndTime
              const endTime = result.processingEndTime || file.processingEndTime || new Date()

              if (shouldUpdateEndTime) {
                console.log(`⏰ Store: 设置文件结束时间 ${fileId} -> ${endTime.toISOString()}`)
              } else {
                console.log(`🔒 Store: 保持现有结束时间 ${fileId} -> ${file.processingEndTime?.toISOString()}`)
              }

              const updatedFile = {
                ...file,
                ...result,
                // 只在文件还没完成时才更新状态
                status: file.status === FileStatus.Completed ? file.status : FileStatus.Completed,
                progress: file.status === FileStatus.Completed ? file.progress : 100,
                processingEndTime: endTime
              }

              // 计算并记录处理时间
              if (file.processingStartTime && endTime) {
                const duration = (endTime.getTime() - file.processingStartTime.getTime()) / 1000
                console.log(`⏰ Store: 计算处理时间 ${file.name} -> ${duration.toFixed(1)}s`)
                console.log(`⏰ Store: 开始时间: ${file.processingStartTime.toISOString()}`)
                console.log(`⏰ Store: 结束时间: ${endTime.toISOString()}`)
              } else {
                console.log(`⚠️ Store: 文件 ${file.name} 缺少开始时间或结束时间`)
              }

              return updatedFile
            }
            return file
          }),
        }))
      },

      setFileTaskId: (fileId: string, taskId: string) => {
        set((state) => ({
          files: state.files.map((file) => {
            if (file.id === fileId) {
              // 只在没有开始时间时才设置开始时间
              if (!file.processingStartTime) {
                const startTime = new Date()
                console.log(`⏰ Store: 首次设置文件开始时间 ${fileId} -> ${startTime.toISOString()}`)
                console.log(`🔗 Store: 同时设置taskId ${fileId} -> ${taskId}`)
                const updatedFile = {
                  ...file,
                  taskId,
                  status: FileStatus.Processing,
                  progress: 0,
                  processingStartTime: startTime
                }
                console.log(`✅ Store: 文件更新完成，新taskId: ${updatedFile.taskId}`)
                return updatedFile
              } else {
                console.log(`🔗 Store: 只更新任务ID ${fileId} -> ${taskId}，保持现有开始时间: ${file.processingStartTime.toISOString()}`)
                return {
                  ...file,
                  taskId,
                  status: FileStatus.Processing,
                  progress: 0
                  // 保持现有的 processingStartTime
                }
              }
            }
            return file
          }),
        }))
      },

      getFileById: (fileId: string) => {
        return get().files.find((file) => file.id === fileId)
      },

      getFileObject: (fileId: string) => {
        return get().fileObjects.get(fileId)
      },
    }),
    {
      name: 'file-store',
    }
  )
)
