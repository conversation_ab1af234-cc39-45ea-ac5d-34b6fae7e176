import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { ProcessingTask, TaskStatus, ProcessingResult } from '../types'

interface ProcessingState {
  currentTask: ProcessingTask | null
  taskHistory: ProcessingTask[]
  isProcessing: boolean
  
  // Actions
  setCurrentTask: (task: ProcessingTask | null) => void
  updateTaskProgress: (taskId: string, progress: number) => void
  updateTaskStatus: (taskId: string, status: TaskStatus, error?: string) => void
  completeTask: (taskId: string, result: ProcessingResult) => void
  failTask: (taskId: string, error: string) => void
  clearTaskHistory: () => void
  getTaskById: (taskId: string) => ProcessingTask | undefined
}

export const useProcessingStore = create<ProcessingState>()(
  devtools(
    (set, get) => ({
      currentTask: null,
      taskHistory: [],
      isProcessing: false,

      setCurrentTask: (task: ProcessingTask | null) => {
        set({
          currentTask: task,
          isProcessing: task !== null && task.status === TaskStatus.Processing,
        })

        // 如果是新任务，添加到历史记录
        if (task && !get().taskHistory.find(t => t.id === task.id)) {
          set((state) => ({
            taskHistory: [task, ...state.taskHistory].slice(0, 50), // 保留最近50个任务
          }))
        }
      },

      updateTaskProgress: (taskId: string, progress: number) => {
        set((state) => {
          const updateTask = (task: ProcessingTask) =>
            task.id === taskId ? { ...task, progress } : task

          return {
            currentTask: state.currentTask ? updateTask(state.currentTask) : null,
            taskHistory: state.taskHistory.map(updateTask),
          }
        })
      },

      updateTaskStatus: (taskId: string, status: TaskStatus, error?: string) => {
        set((state) => {
          const updateTask = (task: ProcessingTask) =>
            task.id === taskId 
              ? { 
                  ...task, 
                  status, 
                  error,
                  completedAt: status === TaskStatus.Completed || status === TaskStatus.Failed 
                    ? new Date() 
                    : task.completedAt
                } 
              : task

          const updatedCurrentTask = state.currentTask ? updateTask(state.currentTask) : null
          const isProcessing = updatedCurrentTask?.status === TaskStatus.Processing

          return {
            currentTask: updatedCurrentTask,
            taskHistory: state.taskHistory.map(updateTask),
            isProcessing,
          }
        })
      },

      completeTask: (taskId: string, result: ProcessingResult) => {
        set((state) => {
          const updateTask = (task: ProcessingTask) =>
            task.id === taskId 
              ? { 
                  ...task, 
                  status: TaskStatus.Completed,
                  progress: 100,
                  result,
                  completedAt: new Date()
                } 
              : task

          return {
            currentTask: state.currentTask ? updateTask(state.currentTask) : null,
            taskHistory: state.taskHistory.map(updateTask),
            isProcessing: false,
          }
        })
      },

      failTask: (taskId: string, error: string) => {
        set((state) => {
          const updateTask = (task: ProcessingTask) =>
            task.id === taskId 
              ? { 
                  ...task, 
                  status: TaskStatus.Failed,
                  error,
                  completedAt: new Date()
                } 
              : task

          return {
            currentTask: state.currentTask ? updateTask(state.currentTask) : null,
            taskHistory: state.taskHistory.map(updateTask),
            isProcessing: false,
          }
        })
      },

      clearTaskHistory: () => {
        set({ taskHistory: [] })
      },

      getTaskById: (taskId: string) => {
        const { currentTask, taskHistory } = get()
        return currentTask?.id === taskId 
          ? currentTask 
          : taskHistory.find(task => task.id === taskId)
      },
    }),
    {
      name: 'processing-store',
    }
  )
)
