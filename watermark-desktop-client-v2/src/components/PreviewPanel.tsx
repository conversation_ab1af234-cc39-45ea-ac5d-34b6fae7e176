import { useState } from 'react'
import { ZoomIn, ZoomOut, RotateCcw, Download, Eye, EyeOff, Info } from 'lucide-react'
import { Button } from './ui/button'
import { <PERSON>ltip, TooltipContent, TooltipTrigger, TooltipProvider } from './ui/tooltip'
import { useFileStore } from '../stores/fileStore'
import { ImageComparison } from './ImageComparison'
import { message } from '../hooks/use-message'

export function PreviewPanel() {
  const { selectedFile, files, getFileObject } = useFileStore()
  const [zoom, setZoom] = useState(100)
  const [showComparison, setShowComparison] = useState(false)

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 如果没有文件，不渲染 PreviewPanel
  if (files.length === 0) {
    return null
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 200))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25))
  }

  const handleResetZoom = () => {
    setZoom(100)
  }

  const handleDownload = async () => {
    if (!selectedFile?.processedPath) return

    try {
      // 构建下载URL
      const downloadUrl = `http://localhost:8000/${selectedFile.processedPath}`

      // 使用 fetch 获取文件数据，确保能正确下载
      const response = await fetch(downloadUrl)
      if (!response.ok) {
        throw new Error(`下载失败: ${response.status} ${response.statusText}`)
      }

      // 获取文件数据
      const blob = await response.blob()

      // 创建 Blob URL
      const blobUrl = URL.createObjectURL(blob)

      // 创建下载链接
      const link = document.createElement('a')
      link.href = blobUrl
      link.download = `processed_${selectedFile.name}`
      link.style.display = 'none'

      // 添加到 DOM，触发下载，然后清理
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理 Blob URL
      URL.revokeObjectURL(blobUrl)

      console.log('✅ 下载已开始:', selectedFile.processedPath)
      message.success('文件下载已开始', {
        title: '下载成功',
        duration: 3000,
      })
    } catch (error) {
      console.error('❌ 下载失败:', error)
      message.error(error instanceof Error ? error.message : '下载失败，请重试', {
        title: '下载失败',
        duration: 5000,
      })
    }
  }

  const getImageUrl = (file: any) => {
    // 首先尝试从 fileObjects 中获取 File 对象
    const fileObject = getFileObject(file.id)

    if (fileObject) {
      // 在浏览器环境中，为 File 对象创建 URL
      return URL.createObjectURL(fileObject)
    }

    // 如果没有 File 对象，使用路径
    const path = file.path
    if (path.startsWith('http')) {
      return path
    }

    // 在开发环境中，尝试通过后端代理访问本地文件
    try {
      // 对于绝对路径，使用后端的文件服务
      if (path.startsWith('/')) {
        return `http://localhost:8000/api/v1/files/local?path=${encodeURIComponent(path)}`
      }

      // 对于相对路径，使用 Tauri 的 asset 协议
      return `asset://localhost/${path}`
    } catch (error) {
      console.warn('图片路径处理失败:', path, error)
      return path
    }
  }

  const getProcessedImageUrl = (processedPath: string) => {
    // 处理后的图片通过后端服务访问
    return `http://localhost:8000/${processedPath}`
  }

  if (!selectedFile) {
    return (
      <aside className="flex-1 bg-muted/30 border-l border-border flex items-center justify-center">
        <div className="text-center text-muted-foreground">
          <p>选择一个文件以预览</p>
        </div>
      </aside>
    )
  }

  return (
    <TooltipProvider>
      <aside className="flex-1 bg-muted/30 border-l border-border flex flex-col">
        {/* 紧凑的标题栏 - 包含标题、信息图标和工具按钮 */}
        <div className="flex items-center justify-between bg-white p-4 border-b border-border">
          <div className="flex items-center space-x-2">
            <h4 className="font-medium text-foreground">图片预览</h4>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0 text-muted-foreground hover:text-foreground">
                  <Info className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="bottom" className="max-w-xs">
                <div className="space-y-1 text-xs">
                  <div><span className="font-medium">名称:</span> {selectedFile.name}</div>
                  <div><span className="font-medium">大小:</span> {formatFileSize(selectedFile.size)}</div>
                  {selectedFile.dimensions && (
                    <div><span className="font-medium">尺寸:</span> {selectedFile.dimensions.width} × {selectedFile.dimensions.height}</div>
                  )}
                  <div><span className="font-medium">格式:</span> {selectedFile.type}</div>
                  <div><span className="font-medium">状态:</span> {
                    selectedFile.status === 'ready' ? '准备就绪' :
                    selectedFile.status === 'processing' ? `处理中 (${selectedFile.progress || 0}%)` :
                    selectedFile.status === 'completed' ? '已完成' :
                    '失败'
                  }</div>
                  {selectedFile.status === 'completed' && (
                    <>
                      {selectedFile.detectionCount !== undefined && (
                        <div><span className="font-medium">检测到水印:</span> {selectedFile.detectionCount} 个</div>
                      )}
                      {selectedFile.avgConfidence !== undefined && (
                        <div><span className="font-medium">平均置信度:</span> {(selectedFile.avgConfidence * 100).toFixed(1)}%</div>
                      )}
                      {selectedFile.processingStartTime && selectedFile.processingEndTime && (
                        <div><span className="font-medium">处理时间:</span> {((selectedFile.processingEndTime.getTime() - selectedFile.processingStartTime.getTime()) / 1000).toFixed(1)}s</div>
                      )}
                    </>
                  )}
                </div>
              </TooltipContent>
            </Tooltip>
          </div>

          <div className="flex items-center space-x-2">
            {/* 缩放控制组 */}
            <div className="flex items-center space-x-1 bg-muted/50 rounded-md p-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomOut}
                className="h-7 w-7 p-0 hover:bg-background"
              >
                <ZoomOut className="h-4 w-4" />
              </Button>

              <span className="text-sm text-muted-foreground min-w-[3rem] text-center px-1">
                {zoom}%
              </span>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomIn}
                className="h-7 w-7 p-0 hover:bg-background"
              >
                <ZoomIn className="h-4 w-4" />
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetZoom}
                className="h-7 w-7 p-0 hover:bg-background"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>

            {/* 功能按钮组 */}
            <div className="flex items-center space-x-2">
              {/* 对比模式切换 */}
              {selectedFile.processedPath && (
                <Button
                  onClick={() => setShowComparison(!showComparison)}
                  size="sm"
                  variant={showComparison ? "default" : "outline"}
                  className="h-8 px-3 font-medium transition-all duration-200 hover:scale-105"
                >
                  {showComparison ? <EyeOff className="mr-1.5 h-4 w-4" /> : <Eye className="mr-1.5 h-4 w-4" />}
                  {showComparison ? '单独查看' : '对比查看'}
                </Button>
              )}

              {/* 下载按钮 */}
              {selectedFile.processedPath && (
                <Button
                  onClick={handleDownload}
                  size="sm"
                  variant="default"
                  className="h-8 px-3 bg-primary hover:bg-primary/90 text-primary-foreground font-medium transition-all duration-200 hover:scale-105 shadow-sm"
                >
                  <Download className="mr-1.5 h-4 w-4" />
                  下载结果
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* 图片预览区域 - 占用所有剩余空间 */}
        <div className="flex-1 bg-muted rounded-lg overflow-hidden min-h-0 m-4">
          {selectedFile.processedPath && showComparison ? (
            // 对比视图
            <ImageComparison
              beforeImage={getImageUrl(selectedFile)}
              afterImage={getProcessedImageUrl(selectedFile.processedPath)}
              beforeLabel="原图"
              afterLabel="处理后"
              className="w-full h-full"
            />
          ) : selectedFile.processedPath ? (
            // 处理后图片
            <img
              src={getProcessedImageUrl(selectedFile.processedPath)}
              alt="处理后的图片"
              className="w-full h-full object-contain"
              style={{ transform: `scale(${zoom / 100})` }}
            />
          ) : (
            // 原图
            <img
              src={getImageUrl(selectedFile)}
              alt={selectedFile.name}
              className="w-full h-full object-contain"
              style={{ transform: `scale(${zoom / 100})` }}
            />
          )}
        </div>
      </aside>
    </TooltipProvider>
  )
}
