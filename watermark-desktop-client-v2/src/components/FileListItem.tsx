import { FileImage, X, Download, AlertCircle, Clock, CheckCircle2,Stamp } from 'lucide-react'
import { Card, CardContent } from './ui/card'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { Checkbox } from './ui/checkbox'
import { FileItem, FileStatus } from '@/types'
import { useFileStore } from '../stores/fileStore'
import { useBatchSelectionStore } from '../stores/batchSelectionStore'
import { cn } from '../lib/utils'
import { useState, useEffect } from 'react'

interface FileListItemProps {
  file: FileItem
  isSelected?: boolean
  onSelect?: (file: FileItem) => void
  onRemove?: (file: FileItem) => void
  onPreview?: (file: FileItem) => void
  onDownload?: (file: FileItem) => void
}

export function FileListItem({
  file,
  isSelected = false,
  onSelect,
  onRemove,
  onPreview,
  onDownload
}: FileListItemProps) {
  const { getFileObject } = useFileStore()
  const {
    selectedFileIds,
    isSelectionMode,
    toggleFileSelection
  } = useBatchSelectionStore()
  const [thumbnailUrl, setThumbnailUrl] = useState<string | null>(null)

  // 检查当前文件是否被选中
  const isBatchSelected = selectedFileIds.has(file.id)

  // 检查文件是否可以被选择（只有已完成且有处理结果的文件可以选择）
  const canBeSelected = file.status === FileStatus.Completed && file.processedPath

  // 生成缩略图 URL
  useEffect(() => {
    const fileObject = getFileObject(file.id)
    if (fileObject) {
      // 在浏览器环境中，为 File 对象创建 URL
      const url = URL.createObjectURL(fileObject)
      setThumbnailUrl(url)

      // 清理函数
      return () => {
        URL.revokeObjectURL(url)
      }
    } else if (file.path && file.path.startsWith('http')) {
      // 如果是网络路径，直接使用
      setThumbnailUrl(file.path)
    } else if (file.path) {
      // Tauri 环境中的本地文件路径
      setThumbnailUrl(`http://localhost:8000/files/${encodeURIComponent(file.path)}`)
    }
  }, [file.id, file.path, getFileObject])

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  const getStatusConfig = (status: FileStatus) => {
    switch (status) {
      case FileStatus.Ready:
        return {
          icon: Clock,
          label: '准备就绪',
          variant: 'secondary' as const,
          color: 'text-muted-foreground'
        }
      case FileStatus.Processing:
        return {
          icon: Clock,
          label: '处理中',
          variant: 'default' as const,
          color: 'text-yellow-600'
        }
      case FileStatus.Completed:
        return {
          icon: CheckCircle2,
          label: '已完成',
          variant: 'default' as const,
          color: 'text-green-600'
        }
      case FileStatus.Failed:
        return {
          icon: AlertCircle,
          label: '失败',
          variant: 'destructive' as const,
          color: 'text-red-600'
        }
      default:
        return {
          icon: Clock,
          label: '未知',
          variant: 'secondary' as const,
          color: 'text-muted-foreground'
        }
    }
  }

  const statusConfig = getStatusConfig(file.status)
  const StatusIcon = statusConfig.icon

  // 处理复选框点击
  const handleCheckboxChange = (checked: boolean) => {
    if (canBeSelected) {
      toggleFileSelection(file.id)
    }
  }

  // 处理卡片点击
  const handleCardClick = (e: React.MouseEvent) => {
    // 如果点击的是复选框区域，不触发卡片选择
    if ((e.target as HTMLElement).closest('[data-checkbox]')) {
      return
    }
    onSelect?.(file)
  }

  return (
    <Card
      className={cn(
        'transition-all duration-200 hover:shadow-sm cursor-pointer border',
        isSelected
          ? 'border-primary bg-primary/5 dark:bg-primary/10 dark:border-primary'
          : 'border-gray-100 border shadow-none hover:border-border/50 dark:border-border dark:hover:border-border/50',
        isBatchSelected && 'ring-2 ring-primary/20',
        canBeSelected && isSelectionMode && 'hover:ring-1 hover:ring-primary/30'
      )}
      onClick={handleCardClick}
    >
      <CardContent className="p-4">
        <div className="flex items-center space-x-4">
          {/* 批量选择复选框 - 只要文件可以被选择就显示 */}
          {canBeSelected && (
            <div className="flex-shrink-0" data-checkbox>
              <Checkbox
                checked={isBatchSelected}
                onCheckedChange={handleCheckboxChange}
                disabled={!canBeSelected}
                className={cn(
                  'transition-all duration-200',
                  !canBeSelected && 'opacity-50 cursor-not-allowed'
                )}
              />
            </div>
          )}

          {/* 文件缩略图 */}
          <div className="flex-shrink-0">
            <div className="w-12 h-12 bg-muted rounded-lg flex items-center justify-center overflow-hidden">
              {thumbnailUrl ? (
                <img
                  src={thumbnailUrl}
                  alt={file.name}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    // 图片加载失败时显示默认图标
                    const target = e.target as HTMLImageElement
                    target.style.display = 'none'
                    target.nextElementSibling?.classList.remove('hidden')
                  }}
                />
              ) : null}
              <FileImage className={`h-6 w-6 text-muted-foreground ${thumbnailUrl ? 'hidden' : ''}`} />
            </div>
          </div>

          {/* 文件信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2 mb-1">
              <h4 className="text-sm font-medium truncate" title={file.name}>
                {file.name}
              </h4>
              <Badge variant={statusConfig.variant} className="flex-shrink-0">
                <StatusIcon className="h-3 w-3 mr-1" />
                {statusConfig.label}
              </Badge>
            </div>
            
            <div className="flex items-center space-x-4 text-xs text-muted-foreground">
              <span>{formatFileSize(file.size)}</span>
              {file.dimensions && (
                <span>{file.dimensions.width} × {file.dimensions.height}</span>
              )}
              <span className="truncate">{file.type}</span>
            </div>

            {/* 处理进度 */}
            {file.status === FileStatus.Processing && (
              <div className="mt-2">
                <Progress value={file.progress || 0} className="h-1" />
                <div className="flex justify-between items-center mt-1">
                  <span className="text-xs text-muted-foreground">
                    处理中... {file.progress || 0}%
                  </span>
                  {file.processingStartTime && (
                    <span className="text-xs text-muted-foreground">
                      {Math.floor((Date.now() - file.processingStartTime.getTime()) / 1000)}s
                    </span>
                  )}
                </div>
              </div>
            )}

            {/* 处理完成信息 */}
            {file.status === FileStatus.Completed && (
              <div className="mt-2 text-xs text-muted-foreground space-x-4 flex">
                {file.detectionCount !== undefined && (
                  <div className="flex items-center justify-center text-orange-500">  <Stamp className="h-3 w-3 mr-1" /> 水印{file.detectionCount} 个 </div>
                )}
                {file.processingStartTime && file.processingEndTime && (
                  <div className="flex items-center justify-center text-green-500">
                    <Clock className="h-3 w-3 mr-1" /> 耗时
                    {((file.processingEndTime.getTime() - file.processingStartTime.getTime()) / 1000).toFixed(1)}s
                  </div>
                )}
              </div>
            )}

            {/* 错误信息 */}
            {file.status === FileStatus.Failed && file.error && (
              <div className="mt-2 text-xs text-red-600">
                {file.error}
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <div className="flex items-center space-x-1">
            {file.status === FileStatus.Completed && file.processedPath && (
              <>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDownload?.(file)
                  }}
                  className="h-8 w-8 p-0"
                >
                  <Download className="h-4 w-4" />
                </Button>
              </>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation()
                onRemove?.(file)
              }}
              className="h-8 w-8 p-0 text-muted-foreground hover:text-destructive"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
