import { useEffect, useState } from 'react'
import { Wifi, WifiOff, AlertCircle, CheckCircle, Clock } from 'lucide-react'
import { Badge } from './ui/badge'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog'
import { useConnectionStore } from '../stores/connectionStore'
import { cn } from '../lib/utils'

interface ConnectionStatusProps {
  className?: string
  showDetails?: boolean
}

export function ConnectionStatus({ className, showDetails = false }: ConnectionStatusProps) {
  const {
    httpConnected,
    wsConnected,
    httpError,
    wsError,
    backendVersion,
    aiServiceStatus,
    modelsStatus,
    checkHttpConnection,
    connectWebSocket,
  } = useConnectionStore()

  const [isOpen, setIsOpen] = useState(false)

  const isOnline = httpConnected && wsConnected
  const hasErrors = !!(httpError || wsError)

  const getStatusIcon = () => {
    if (isOnline) return CheckCircle
    if (hasErrors) return AlertCircle
    return WifiOff
  }

  const getStatusColor = () => {
    if (isOnline) return 'text-green-600'
    if (hasErrors) return 'text-red-600'
    return 'text-yellow-600'
  }

  const getStatusText = () => {
    if (isOnline) return '已连接'
    if (hasErrors) return '连接错误'
    return '未连接'
  }

  const StatusIcon = getStatusIcon()

  const handleReconnect = async () => {
    await checkHttpConnection()
    if (!wsConnected) {
      await connectWebSocket()
    }
  }

  // 自动重连逻辑
  useEffect(() => {
    if (!isOnline && !hasErrors) {
      const timer = setTimeout(() => {
        handleReconnect()
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [isOnline, hasErrors])

  if (!showDetails) {
    return (
      <div className={cn('flex items-center space-x-2', className)}>
        <StatusIcon className={cn('h-4 w-4', getStatusColor())} />
        <span className={cn('text-sm', getStatusColor())}>{getStatusText()}</span>
      </div>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className={cn('h-8 px-2', className)}>
          <StatusIcon className={cn('h-4 w-4 mr-2', getStatusColor())} />
          <span className={cn('text-sm', getStatusColor())}>{getStatusText()}</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <StatusIcon className={cn('h-5 w-5', getStatusColor())} />
            <span>连接状态</span>
          </DialogTitle>
          <DialogDescription>
            查看与后端服务的连接详情
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* HTTP 连接状态 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Wifi className="h-4 w-4" />
                <span>HTTP 连接</span>
                <Badge variant={httpConnected ? 'default' : 'destructive'}>
                  {httpConnected ? '已连接' : '未连接'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {httpError && (
                <p className="text-sm text-red-600 mb-2">{httpError}</p>
              )}
              {httpConnected && backendVersion && (
                <p className="text-sm text-muted-foreground">
                  后端版本: {backendVersion}
                </p>
              )}
            </CardContent>
          </Card>

          {/* WebSocket 连接状态 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center space-x-2">
                <Wifi className="h-4 w-4" />
                <span>WebSocket 连接</span>
                <Badge variant={wsConnected ? 'default' : 'destructive'}>
                  {wsConnected ? '已连接' : '未连接'}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              {wsError && (
                <p className="text-sm text-red-600 mb-2">{wsError}</p>
              )}
            </CardContent>
          </Card>

          {/* AI 服务状态 */}
          {httpConnected && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>AI 服务状态</span>
                  <Badge variant={aiServiceStatus === 'ready' ? 'default' : 'secondary'}>
                    {aiServiceStatus || '未知'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <div className="space-y-2">
                  {Object.entries(modelsStatus).map(([model, status]) => (
                    <div key={model} className="flex items-center justify-between">
                      <span className="text-sm text-muted-foreground capitalize">
                        {model} 模型
                      </span>
                      <Badge variant={status ? 'default' : 'secondary'} className="text-xs">
                        {status ? '就绪' : '未就绪'}
                      </Badge>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 重连按钮 */}
          {!isOnline && (
            <div className="flex justify-center">
              <Button onClick={handleReconnect} size="sm">
                <Wifi className="h-4 w-4 mr-2" />
                重新连接
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}
