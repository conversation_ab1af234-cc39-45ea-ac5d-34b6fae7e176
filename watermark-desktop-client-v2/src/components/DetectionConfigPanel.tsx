import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON>otateCcw, Save } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { watermarkDetectionService, DetectionOptions } from '../services/WatermarkDetectionService'
import { aiModelService, ModelType } from '../services/AIModelService'
import { eventBus } from '../lib/event-bus'
import { useToast } from '../hooks/use-toast'

interface DetectionConfigPanelProps {
  onOptionsChange?: (options: DetectionOptions) => void
  className?: string
}

export function DetectionConfigPanel({ onOptionsChange, className }: DetectionConfigPanelProps) {
  const [options, setOptions] = useState<DetectionOptions>({})
  const [modelStatus, setModelStatus] = useState<string>('unloaded')
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // 获取默认配置
    const defaultOptions = watermarkDetectionService.getDefaultOptions()
    setOptions(defaultOptions)

    // 获取模型状态
    const yoloModel = aiModelService.getModelInfo(ModelType.YOLO)
    if (yoloModel) {
      setModelStatus(yoloModel.status)
    }

    // 监听模型状态变化
    const unsubscribe = eventBus.on('model:status:changed', ({ modelType, model }) => {
      if (modelType === ModelType.YOLO) {
        setModelStatus(model.status)
      }
    })

    return () => unsubscribe.unsubscribe()
  }, [])

  const handleOptionChange = (key: keyof DetectionOptions, value: any) => {
    const newOptions = { ...options, [key]: value }
    setOptions(newOptions)
    onOptionsChange?.(newOptions)
  }

  const handleSaveAsDefault = () => {
    watermarkDetectionService.updateDefaultOptions(options)
    toast({
      title: "配置已保存",
      description: "检测配置已保存为默认设置",
    })
  }

  const handleResetToDefault = () => {
    const defaultOptions = {
      confidenceThreshold: 0.5,
      nmsThreshold: 0.4,
      maxDetections: 100,
      deviceType: 'auto' as const,
      batchSize: 4
    }
    setOptions(defaultOptions)
    onOptionsChange?.(defaultOptions)
    toast({
      title: "配置已重置",
      description: "检测配置已重置为默认值",
    })
  }

  const handleLoadModel = async () => {
    setIsLoading(true)
    try {
      await aiModelService.loadModel(ModelType.YOLO, {
        deviceType: options.deviceType
      })
      toast({
        title: "模型加载成功",
        description: "YOLO 检测模型已成功加载",
      })
    } catch (error) {
      toast({
        title: "模型加载失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getModelStatusBadge = () => {
    switch (modelStatus) {
      case 'loaded':
        return <Badge variant="default">已加载</Badge>
      case 'loading':
        return <Badge variant="secondary">加载中...</Badge>
      case 'error':
        return <Badge variant="destructive">错误</Badge>
      default:
        return <Badge variant="outline">未加载</Badge>
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>检测配置</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 模型状态 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">YOLO 模型状态</span>
            {getModelStatusBadge()}
          </div>
          {modelStatus !== 'loaded' && (
            <Button
              onClick={handleLoadModel}
              disabled={isLoading || modelStatus === 'loading'}
              size="sm"
              className="w-full"
            >
              {isLoading ? '加载中...' : '加载模型'}
            </Button>
          )}
        </div>

        {/* 检测参数 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">检测参数</h4>
          
          {/* 置信度阈值 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">置信度阈值</label>
              <Badge variant="secondary">{options.confidenceThreshold}</Badge>
            </div>
            <input
              type="range"
              min="0.1"
              max="0.9"
              step="0.05"
              value={options.confidenceThreshold || 0.5}
              onChange={(e) => handleOptionChange('confidenceThreshold', parseFloat(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              较高的值会减少误检，但可能遗漏一些水印
            </p>
          </div>

          {/* NMS 阈值 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">NMS 阈值</label>
              <Badge variant="secondary">{options.nmsThreshold}</Badge>
            </div>
            <input
              type="range"
              min="0.1"
              max="0.8"
              step="0.05"
              value={options.nmsThreshold || 0.4}
              onChange={(e) => handleOptionChange('nmsThreshold', parseFloat(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              用于去除重复检测框，较高的值会保留更多检测框
            </p>
          </div>

          {/* 最大检测数量 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">最大检测数量</label>
              <Badge variant="secondary">{options.maxDetections}</Badge>
            </div>
            <input
              type="range"
              min="10"
              max="500"
              step="10"
              value={options.maxDetections || 100}
              onChange={(e) => handleOptionChange('maxDetections', parseInt(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              单张图片最多检测的水印数量
            </p>
          </div>
        </div>

        {/* 性能参数 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">性能参数</h4>
          
          {/* 设备类型 */}
          <div className="space-y-2">
            <label className="text-sm">计算设备</label>
            <select
              value={options.deviceType || 'auto'}
              onChange={(e) => handleOptionChange('deviceType', e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md text-sm"
            >
              <option value="auto">自动选择</option>
              <option value="cpu">CPU</option>
              <option value="gpu">GPU</option>
            </select>
            <p className="text-xs text-muted-foreground">
              GPU 可以显著提高检测速度
            </p>
          </div>

          {/* 批处理大小 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">批处理大小</label>
              <Badge variant="secondary">{options.batchSize}</Badge>
            </div>
            <input
              type="range"
              min="1"
              max="16"
              step="1"
              value={options.batchSize || 4}
              onChange={(e) => handleOptionChange('batchSize', parseInt(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              同时处理的图片数量，较大的值需要更多内存
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetToDefault}
            className="flex-1"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSaveAsDefault}
            className="flex-1"
          >
            <Save className="h-4 w-4 mr-1" />
            保存为默认
          </Button>
        </div>

        {/* 预设配置 */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">预设配置</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  confidenceThreshold: 0.3,
                  nmsThreshold: 0.4,
                  maxDetections: 200,
                  deviceType: 'auto' as const,
                  batchSize: 4
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              高敏感度
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  confidenceThreshold: 0.7,
                  nmsThreshold: 0.3,
                  maxDetections: 50,
                  deviceType: 'auto' as const,
                  batchSize: 4
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              高精度
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  confidenceThreshold: 0.5,
                  nmsThreshold: 0.4,
                  maxDetections: 100,
                  deviceType: 'gpu' as const,
                  batchSize: 8
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              高性能
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  confidenceThreshold: 0.5,
                  nmsThreshold: 0.4,
                  maxDetections: 100,
                  deviceType: 'cpu' as const,
                  batchSize: 2
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              节能模式
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
