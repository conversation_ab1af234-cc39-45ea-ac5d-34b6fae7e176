import { FileText, <PERSON>ting<PERSON>, History, Info, ChevronLeft, ChevronRight } from 'lucide-react'
import { Button } from './ui/button'
import { useUIStore } from '../stores/uiStore'

export function Sidebar() {
  const { sidebarCollapsed, toggleSidebar } = useUIStore()

  return (
    <aside className={`${sidebarCollapsed ? 'w-16' : 'w-48'} bg-muted/30 border-r border-border flex flex-col transition-all duration-300 ease-in-out`}>
      {/* 折叠按钮 */}
      <div className="flex items-center justify-between p-4 border-b border-border">
        {!sidebarCollapsed && (
          <h2 className="text-sm font-medium text-foreground">功能菜单</h2>
        )}
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSidebar}
          className="h-8 w-8 p-0"
          title={sidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'}
        >
          {sidebarCollapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* 导航菜单 */}
      <nav className="flex-1 p-4">
        <div className="space-y-2">
          <Button
            variant="ghost"
            className={`w-full ${sidebarCollapsed ? 'justify-center px-0' : 'justify-start'}`}
            size="sm"
            title={sidebarCollapsed ? '水印去除' : undefined}
          >
            <FileText className={`h-4 w-4 ${sidebarCollapsed ? '' : 'mr-2'}`} />
            {!sidebarCollapsed && '水印去除'}
          </Button>

          <Button
            variant="ghost"
            className={`w-full ${sidebarCollapsed ? 'justify-center px-0' : 'justify-start'}`}
            size="sm"
            title={sidebarCollapsed ? '图像修复' : undefined}
          >
            <History className={`h-4 w-4 ${sidebarCollapsed ? '' : 'mr-2'}`} />
            {!sidebarCollapsed && '图像修复'}
          </Button>

          <Button
            variant="ghost"
            className={`w-full ${sidebarCollapsed ? 'justify-center px-0' : 'justify-start'}`}
            size="sm"
            title={sidebarCollapsed ? '设置' : undefined}
          >
            <Settings className={`h-4 w-4 ${sidebarCollapsed ? '' : 'mr-2'}`} />
            {!sidebarCollapsed && '设置'}
          </Button>

          <Button
            variant="ghost"
            className={`w-full ${sidebarCollapsed ? 'justify-center px-0' : 'justify-start'}`}
            size="sm"
            title={sidebarCollapsed ? '帮助' : undefined}
          >
            <Info className={`h-4 w-4 ${sidebarCollapsed ? '' : 'mr-2'}`} />
            {!sidebarCollapsed && '帮助'}
          </Button>
        </div>
      </nav>

      {/* 底部信息 */}
      {!sidebarCollapsed && (
        <div className="p-4 border-t border-border">
          <div className="text-xs text-muted-foreground">
            <p>版本 0.1.0</p>
            <p>© 2025 Watermark Team</p>
          </div>
        </div>
      )}
    </aside>
  )
}
