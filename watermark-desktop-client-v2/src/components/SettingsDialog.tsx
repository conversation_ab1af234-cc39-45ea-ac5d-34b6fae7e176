import { useState } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Zap } from 'lucide-react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog'
import { Button } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { ThemeToggle } from './ThemeToggle'
import { useSettingsStore } from '../stores/settingsStore'

interface SettingsDialogProps {
  children?: React.ReactNode
}

export function SettingsDialog({ children }: SettingsDialogProps) {
  const [open, setOpen] = useState(false)
  const { settings, updateAISettings, updatePerformanceSettings, updateOutputSettings } = useSettingsStore()

  const handleAISettingsChange = (key: string, value: any) => {
    updateAISettings({ [key]: value })
  }

  const handlePerformanceSettingsChange = (key: string, value: any) => {
    updatePerformanceSettings({ [key]: value })
  }

  const handleOutputSettingsChange = (key: string, value: any) => {
    updateOutputSettings({ [key]: value })
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children || (
          <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
            <Settings className="h-4 w-4" />
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>应用设置</span>
          </DialogTitle>
          <DialogDescription>
            配置 AI 模型参数、性能选项和输出设置
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* AI 设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-4 w-4" />
                <span>AI 模型设置</span>
              </CardTitle>
              <CardDescription>
                调整水印检测和图像修复的 AI 模型参数
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">检测置信度阈值</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0.1"
                      max="0.9"
                      step="0.1"
                      value={settings.ai.confidence_threshold}
                      onChange={(e) => handleAISettingsChange('confidence_threshold', parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <Badge variant="secondary">{settings.ai.confidence_threshold}</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    较高的值会减少误检，但可能遗漏一些水印
                  </p>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">上下文扩展比例</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="0.05"
                      max="0.3"
                      step="0.01"
                      value={settings.ai.context_expansion_ratio}
                      onChange={(e) => handleAISettingsChange('context_expansion_ratio', parseFloat(e.target.value))}
                      className="flex-1"
                    />
                    <Badge variant="secondary">{settings.ai.context_expansion_ratio}</Badge>
                  </div>
                  <p className="text-xs text-muted-foreground">
                    扩展修复区域以获得更好的上下文信息
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={settings.ai.enhance_mask}
                    onChange={(e) => handleAISettingsChange('enhance_mask', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">增强掩码处理</span>
                </label>

                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={settings.ai.use_smart_enhancement}
                    onChange={(e) => handleAISettingsChange('use_smart_enhancement', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm">智能增强</span>
                </label>
              </div>
            </CardContent>
          </Card>

          {/* 性能设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Cpu className="h-4 w-4" />
                <span>性能设置</span>
              </CardTitle>
              <CardDescription>
                配置处理性能和资源使用选项
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">最大并发任务数</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="1"
                      max="8"
                      step="1"
                      value={settings.performance.maxConcurrentTasks}
                      onChange={(e) => handlePerformanceSettingsChange('maxConcurrentTasks', parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <Badge variant="secondary">{settings.performance.maxConcurrentTasks}</Badge>
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">内存限制 (MB)</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="2048"
                      max="8192"
                      step="512"
                      value={settings.performance.memoryLimit}
                      onChange={(e) => handlePerformanceSettingsChange('memoryLimit', parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <Badge variant="secondary">{settings.performance.memoryLimit}</Badge>
                  </div>
                </div>
              </div>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.performance.useGPU}
                  onChange={(e) => handlePerformanceSettingsChange('useGPU', e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">启用 GPU 加速</span>
                <Badge variant={settings.performance.useGPU ? 'default' : 'secondary'}>
                  {settings.performance.useGPU ? '已启用' : '已禁用'}
                </Badge>
              </label>
            </CardContent>
          </Card>

          {/* 输出设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FolderOpen className="h-4 w-4" />
                <span>输出设置</span>
              </CardTitle>
              <CardDescription>
                配置文件输出路径和格式选项
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">默认输出路径</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={settings.output.defaultPath}
                    onChange={(e) => handleOutputSettingsChange('defaultPath', e.target.value)}
                    placeholder="选择默认输出文件夹"
                    className="flex-1 px-3 py-2 border border-input rounded-md"
                  />
                  <Button variant="outline" size="sm">
                    浏览
                  </Button>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">输出格式</label>
                  <select
                    value={settings.output.outputFormat}
                    onChange={(e) => handleOutputSettingsChange('outputFormat', e.target.value)}
                    className="w-full px-3 py-2 border border-input rounded-md"
                  >
                    <option value="same">保持原格式</option>
                    <option value="jpg">JPEG</option>
                    <option value="png">PNG</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="text-sm font-medium">图片质量</label>
                  <div className="flex items-center space-x-2">
                    <input
                      type="range"
                      min="60"
                      max="100"
                      step="5"
                      value={settings.output.quality}
                      onChange={(e) => handleOutputSettingsChange('quality', parseInt(e.target.value))}
                      className="flex-1"
                    />
                    <Badge variant="secondary">{settings.output.quality}%</Badge>
                  </div>
                </div>
              </div>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={settings.output.keepOriginalName}
                  onChange={(e) => handleOutputSettingsChange('keepOriginalName', e.target.checked)}
                  className="rounded"
                />
                <span className="text-sm">保持原文件名</span>
              </label>
            </CardContent>
          </Card>

          {/* 界面设置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Palette className="h-4 w-4" />
                <span>界面设置</span>
              </CardTitle>
              <CardDescription>
                自定义应用外观和用户体验
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium">主题模式</label>
                  <p className="text-xs text-muted-foreground">选择浅色、深色或跟随系统</p>
                </div>
                <ThemeToggle />
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  )
}
