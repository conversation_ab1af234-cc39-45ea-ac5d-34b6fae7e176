import { useState, useEffect } from 'react'
import { Square, Eye, AlertCircle, CheckCircle, Clock, Paintbrush } from 'lucide-react'
import { Button } from './ui/button'
import { Progress } from './ui/progress'
import { Badge } from './ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { InpaintingResultViewer } from './InpaintingResultViewer'
import { imageInpaintingService, BatchInpaintingProgress } from '../services/ImageInpaintingService'
import { eventBus } from '../lib/event-bus'
import { useToast } from '../hooks/use-toast'

interface InpaintingProgressMonitorProps {
  taskId?: string
  onComplete?: (progress: BatchInpaintingProgress) => void
  onCancel?: (taskId: string) => void
  className?: string
}

export function InpaintingProgressMonitor({
  taskId,
  onComplete,
  onCancel,
  className
}: InpaintingProgressMonitorProps) {
  const [progress, setProgress] = useState<BatchInpaintingProgress | null>(null)
  const [selectedResultIndex, setSelectedResultIndex] = useState<number | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    if (!taskId) return

    // 获取初始进度
    const initialProgress = imageInpaintingService.getInpaintingProgress(taskId)
    if (initialProgress) {
      setProgress(initialProgress)
    }

    // 监听进度更新事件
    const unsubscribe = eventBus.on('inpainting:progress:updated', ({ taskId: eventTaskId, progress: eventProgress }) => {
      if (eventTaskId === taskId) {
        setProgress(eventProgress)
      }
    })

    // 监听完成事件
    const unsubscribeComplete = eventBus.on('inpainting:batch:completed', ({ taskId: eventTaskId, progress: eventProgress }) => {
      if (eventTaskId === taskId) {
        setProgress(eventProgress)
        onComplete?.(eventProgress)
        toast({
          title: "修复完成",
          description: `成功修复 ${eventProgress.results.length} 个文件`,
        })
      }
    })

    // 监听失败事件
    const unsubscribeFailed = eventBus.on('inpainting:batch:failed', ({ taskId: eventTaskId, error }) => {
      if (eventTaskId === taskId) {
        toast({
          title: "修复失败",
          description: error?.message || "修复过程中出现错误",
          variant: "destructive",
        })
      }
    })

    // 监听取消事件
    const unsubscribeCancel = eventBus.on('inpainting:cancelled', ({ taskId: eventTaskId }) => {
      if (eventTaskId === taskId) {
        toast({
          title: "修复已取消",
          description: "修复任务已被用户取消",
        })
      }
    })

    return () => {
      unsubscribe.unsubscribe()
      unsubscribeComplete.unsubscribe()
      unsubscribeFailed.unsubscribe()
      unsubscribeCancel.unsubscribe()
    }
  }, [taskId, onComplete, toast])

  const handleCancel = () => {
    if (taskId && progress && progress.progress < 100) {
      imageInpaintingService.cancelInpainting(taskId)
      onCancel?.(taskId)
    }
  }

  const handleViewResult = (index: number) => {
    setSelectedResultIndex(index)
  }

  const formatTime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)

    if (hours > 0) {
      return `${hours}h ${minutes % 60}m ${seconds % 60}s`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  const getStatusIcon = () => {
    if (!progress) return Clock
    if (progress.progress >= 100) return CheckCircle
    if (progress.errors.length > 0) return AlertCircle
    return Paintbrush
  }

  const getStatusColor = () => {
    if (!progress) return 'text-gray-500'
    if (progress.progress >= 100) return 'text-green-600'
    if (progress.errors.length > 0) return 'text-yellow-600'
    return 'text-blue-600'
  }

  const StatusIcon = getStatusIcon()

  if (!progress) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            <Clock className="h-8 w-8 mx-auto mb-2" />
            <p>等待修复任务开始...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <StatusIcon className={`h-5 w-5 ${getStatusColor()}`} />
              <span>图像修复进度</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">
                {progress.processedFiles}/{progress.totalFiles}
              </Badge>
              {progress.progress < 100 && (
                <Button variant="outline" size="sm" onClick={handleCancel}>
                  <Square className="h-4 w-4 mr-1" />
                  取消
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 进度条 */}
          <div className="space-y-2 mb-4">
            <div className="flex justify-between text-sm">
              <span>总体进度</span>
              <span>{progress.progress}%</span>
            </div>
            <Progress value={progress.progress} className="h-2" />
          </div>

          {/* 当前处理文件 */}
          {progress.currentFile && progress.progress < 100 && (
            <div className="mb-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Paintbrush className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium">正在修复:</span>
              </div>
              <p className="text-sm text-muted-foreground mt-1 truncate">
                {progress.currentFile}
              </p>
            </div>
          )}

          {/* 统计信息 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {progress.results.length}
              </div>
              <div className="text-xs text-muted-foreground">成功修复</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {progress.results.reduce((sum, r) => sum + r.watermarks_removed, 0)}
              </div>
              <div className="text-xs text-muted-foreground">水印移除</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {progress.errors.length}
              </div>
              <div className="text-xs text-muted-foreground">处理失败</div>
            </div>
            <div className="text-center">
              <div className="text-sm font-bold">
                {progress.estimatedTimeRemaining ? formatTime(progress.estimatedTimeRemaining) : '--'}
              </div>
              <div className="text-xs text-muted-foreground">预计剩余</div>
            </div>
          </div>

          {/* 修复结果列表 */}
          {progress.results.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium">修复结果</h4>
              <div className="max-h-48 overflow-y-auto space-y-1">
                {progress.results.map((result, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
                  >
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <Badge variant="default" className="text-xs">
                        已修复
                      </Badge>
                      <span className="text-sm truncate">
                        {result.original_path.split('/').pop()}
                      </span>
                      <Badge variant="secondary" className="text-xs">
                        {result.watermarks_removed} 个水印
                      </Badge>
                      {result.quality_score && (
                        <Badge variant="outline" className="text-xs">
                          质量: {Math.round(result.quality_score * 100)}%
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-xs text-muted-foreground">
                        {result.processing_time.toFixed(0)}ms
                      </span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleViewResult(index)}
                        className="h-6 w-6 p-0"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 错误列表 */}
          {progress.errors.length > 0 && (
            <div className="mt-4 space-y-2">
              <h4 className="font-medium text-red-600">处理错误</h4>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {progress.errors.map((error, index) => (
                  <div key={index} className="p-2 bg-red-50 rounded text-sm">
                    <div className="font-medium text-red-800">
                      {error.file.split('/').pop()}
                    </div>
                    <div className="text-red-600 text-xs">
                      {error.error}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 完成信息 */}
          {progress.progress >= 100 && (
            <div className="mt-4 p-3 bg-green-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">修复完成</span>
              </div>
              <p className="text-sm text-green-700 mt-1">
                总共处理 {progress.totalFiles} 个文件，
                成功 {progress.results.length} 个，
                失败 {progress.errors.length} 个，
                移除水印 {progress.results.reduce((sum, r) => sum + r.watermarks_removed, 0)} 个
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 修复结果详情对话框 */}
      {selectedResultIndex !== null && progress.results[selectedResultIndex] && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg max-w-4xl max-h-[90vh] overflow-auto">
            <div className="p-4 border-b flex items-center justify-between">
              <h3 className="text-lg font-semibold">修复结果详情</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedResultIndex(null)}
              >
                ×
              </Button>
            </div>
            <div className="p-4">
              <InpaintingResultViewer result={progress.results[selectedResultIndex]} />
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
