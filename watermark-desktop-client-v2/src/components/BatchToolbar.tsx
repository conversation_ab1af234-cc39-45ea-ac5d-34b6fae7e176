/**
 * 批量操作工具栏组件
 * 提供批量选择、路径设置、批量下载等功能
 */

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  CheckSquare, 
  Square, 
  Download, 
  FolderOpen, 
  Settings,
  X,
  Loader2
} from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Progress } from './ui/progress'
import { Separator } from './ui/separator'
import { useBatchSelectionStore } from '../stores/batchSelectionStore'
import { useFileStore } from '../stores/fileStore'
import { FileItem } from '../types'

interface BatchToolbarProps {
  files: FileItem[]
  onSelectPath: () => Promise<string | null>
  onDownload: (files: FileItem[], path: string) => Promise<void>
}

export const BatchToolbar: React.FC<BatchToolbarProps> = ({
  files,
  onSelectPath,
  onDownload
}) => {
  const {
    selectedFileIds,
    isSelectionMode,
    currentDownloadPath,
    isDownloading,
    downloadProgress,
    totalProgress,
    selectAllCompleted,
    clearSelection,
    setCurrentDownloadPath,
    getSelectedFiles,
    getCompletedFiles,
    canDownload
  } = useBatchSelectionStore()

  const [isPathSelectorOpen, setIsPathSelectorOpen] = useState(false)

  const completedFiles = getCompletedFiles(files)
  const selectedFiles = getSelectedFiles(files)
  const allCompletedSelected = completedFiles.length > 0 && 
    completedFiles.every(file => selectedFileIds.has(file.id))

  // 处理全选/取消全选
  const handleToggleSelectAll = () => {
    selectAllCompleted(files)
  }

  // 处理路径选择
  const handleSelectPath = async () => {
    setIsPathSelectorOpen(true)
    try {
      const selectedPath = await onSelectPath()
      if (selectedPath) {
        setCurrentDownloadPath(selectedPath)
      }
    } catch (error) {
      console.error('路径选择失败:', error)
    } finally {
      setIsPathSelectorOpen(false)
    }
  }

  // 处理批量下载
  const handleBatchDownload = async () => {
    if (!canDownload(files) || !currentDownloadPath) {
      return
    }

    try {
      await onDownload(selectedFiles, currentDownloadPath)
    } catch (error) {
      console.error('批量下载失败:', error)
    }
  }

  // 获取路径显示文本
  const getPathDisplayText = () => {
    if (!currentDownloadPath) return '请选择保存路径'
    
    // 显示路径的最后两级目录
    const parts = currentDownloadPath.split(/[/\\]/)
    if (parts.length > 2) {
      return `.../${parts.slice(-2).join('/')}`
    }
    return currentDownloadPath
  }

  // 如果没有已完成的文件，不显示工具栏
  if (completedFiles.length === 0) {
    return null
  }

  return (
    <AnimatePresence>
      {isSelectionMode && (
        <motion.div
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
          className="absolute bottom-0 left-0 right-0 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 dark:bg-background/95 dark:supports-[backdrop-filter]:bg-background/80 border-t border-border shadow-lg z-50"
        >
          {/* 与顶部工具栏保持一致的内边距和布局 */}
          <div className="flex items-center justify-between p-4 gap-4">
            {/* 左侧：选择控制 */}
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleSelectAll}
                className="flex items-center gap-2 bg-background hover:bg-muted dark:bg-background dark:hover:bg-muted dark:border-border"
              >
                {allCompletedSelected ? (
                  <CheckSquare className="h-4 w-4" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                {allCompletedSelected ? '取消全选' : '全选'}
              </Button>



            </div>


            {/* 右侧：操作按钮 */}
            <div className="flex items-center gap-2">
              {isDownloading && (
                <div className="flex items-center gap-2 mr-3">
                  <div className="text-xs text-muted-foreground">
                    {totalProgress}%
                  </div>
                  <Progress value={totalProgress} className="w-20 h-2" />
                </div>
              )}

              <Button
                onClick={handleBatchDownload}
                disabled={!canDownload(files) || !currentDownloadPath || isDownloading}
                size="sm"
                className="bg-primary hover:bg-primary/90 flex items-center gap-2"
              >
                {isDownloading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Download className="h-4 w-4" />
                )}
                {isDownloading ? '下载中...' : `下载 ${selectedFiles.length} 个文件`}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={clearSelection}
                disabled={isDownloading}
                className="text-muted-foreground hover:text-foreground dark:border-border dark:hover:bg-muted"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            </div>

          {/* 下载进度详情 */}
          {isDownloading && downloadProgress.length > 0 && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              className="border-t border-border bg-muted/20 dark:bg-muted/10"
            >
              <div className="p-3">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2 max-h-24 overflow-y-auto scrollbar-hide">
                  {downloadProgress.map((progress) => (
                    <div
                      key={progress.fileId}
                      className="flex items-center gap-2 text-xs p-2 rounded bg-background/50 border border-border/50"
                    >
                      <div className="flex-1 min-w-0">
                        <div className="truncate font-medium" title={progress.fileName}>
                          {progress.fileName}
                        </div>
                        <div className="flex items-center gap-2 mt-1">
                          <Progress value={progress.progress} className="flex-1 h-1" />
                          <span className="text-xs text-muted-foreground w-8 text-right">
                            {progress.progress}%
                          </span>
                        </div>
                      </div>
                      <div className="shrink-0">
                        {progress.status === 'completed' && (
                          <CheckSquare className="h-3 w-3 text-green-500" />
                        )}
                        {progress.status === 'failed' && (
                          <X className="h-3 w-3 text-red-500" />
                        )}
                        {progress.status === 'downloading' && (
                          <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </motion.div>
          )}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
