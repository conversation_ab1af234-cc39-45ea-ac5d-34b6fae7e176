import { useState, useEffect } from 'react'
import { Wifi, WifiOff, RotateCcw, Activity } from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { wsClient, WebSocketStatus } from '../lib/websocket'
import { eventBus } from '../lib/event-bus'
import { useToast } from '../hooks/use-toast'

interface WebSocketStatusProps {
  className?: string
  showDetails?: boolean
}

export function WebSocketStatusComponent({ className, showDetails = false }: WebSocketStatusProps) {
  const [status, setStatus] = useState<WebSocketStatus>(wsClient.getStatus())
  const [connectionInfo, setConnectionInfo] = useState<{
    connections: number
    groups: number
    clientId: string
  } | null>(null)
  const { toast } = useToast()

  useEffect(() => {
    // 监听状态变化
    const unsubscribeStatus = eventBus.on('websocket:status:changed', ({ status: newStatus }) => {
      setStatus(newStatus as WebSocketStatus)
    })

    // 监听连接建立
    const unsubscribeConnected = eventBus.on('websocket:connected', ({ clientId }) => {
      toast({
        title: "WebSocket 已连接",
        description: `客户端 ID: ${clientId}`,
      })
      
      // 请求状态信息
      setTimeout(() => {
        wsClient.requestStatus()
      }, 1000)
    })

    // 监听状态信息
    const unsubscribeStatusInfo = eventBus.on('websocket:status', ({ connections, groups, clientId }) => {
      setConnectionInfo({ connections, groups, clientId })
    })

    // 监听错误
    const unsubscribeError = eventBus.on('websocket:error', ({ error }) => {
      toast({
        title: "WebSocket 错误",
        description: error,
        variant: "destructive",
      })
    })

    // 初始状态
    setStatus(wsClient.getStatus())
    
    // 如果已连接，请求状态信息
    if (wsClient.getStatus() === WebSocketStatus.CONNECTED) {
      wsClient.requestStatus()
    }

    return () => {
      unsubscribeStatus.unsubscribe()
      unsubscribeConnected.unsubscribe()
      unsubscribeStatusInfo.unsubscribe()
      unsubscribeError.unsubscribe()
    }
  }, [toast])

  const handleReconnect = () => {
    wsClient.disconnect()
    setTimeout(() => {
      wsClient.connect()
    }, 1000)
  }

  const getStatusIcon = () => {
    switch (status) {
      case WebSocketStatus.CONNECTED:
        return <Wifi className="h-4 w-4 text-green-600" />
      case WebSocketStatus.CONNECTING:
        return <Activity className="h-4 w-4 text-blue-600 animate-pulse" />
      case WebSocketStatus.DISCONNECTED:
      case WebSocketStatus.ERROR:
        return <WifiOff className="h-4 w-4 text-red-600" />
      default:
        return <WifiOff className="h-4 w-4 text-gray-400" />
    }
  }

  const getStatusText = () => {
    switch (status) {
      case WebSocketStatus.CONNECTED:
        return '已连接'
      case WebSocketStatus.CONNECTING:
        return '连接中...'
      case WebSocketStatus.DISCONNECTED:
        return '未连接'
      case WebSocketStatus.ERROR:
        return '连接错误'
      default:
        return '未知状态'
    }
  }

  const getStatusVariant = () => {
    switch (status) {
      case WebSocketStatus.CONNECTED:
        return 'default'
      case WebSocketStatus.CONNECTING:
        return 'secondary'
      case WebSocketStatus.DISCONNECTED:
      case WebSocketStatus.ERROR:
        return 'destructive'
      default:
        return 'outline'
    }
  }

  if (!showDetails) {
    // 简单状态显示
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        {getStatusIcon()}
        <Badge variant={getStatusVariant()}>
          {getStatusText()}
        </Badge>
        {status !== WebSocketStatus.CONNECTED && status !== WebSocketStatus.CONNECTING && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReconnect}
            className="h-6 w-6 p-0"
          >
            <RotateCcw className="h-3 w-3" />
          </Button>
        )}
      </div>
    )
  }

  // 详细状态显示
  return (
    <div className={`space-y-3 ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className="text-sm font-medium">WebSocket 连接</span>
          <Badge variant={getStatusVariant()}>
            {getStatusText()}
          </Badge>
        </div>
        
        {status !== WebSocketStatus.CONNECTED && status !== WebSocketStatus.CONNECTING && (
          <Button
            variant="outline"
            size="sm"
            onClick={handleReconnect}
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重连
          </Button>
        )}
      </div>

      {connectionInfo && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
          <div className="flex justify-between">
            <span className="text-muted-foreground">客户端 ID:</span>
            <span className="font-mono text-xs">
              {connectionInfo.clientId.split('-').pop()}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">活动连接:</span>
            <span className="font-medium">{connectionInfo.connections}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-muted-foreground">活动组:</span>
            <span className="font-medium">{connectionInfo.groups}</span>
          </div>
        </div>
      )}

      <div className="text-xs text-muted-foreground">
        <div>服务器: ws://127.0.0.1:8000/api/v1/ws</div>
        {status === WebSocketStatus.CONNECTED && (
          <div className="text-green-600 mt-1">✓ 实时通信已启用</div>
        )}
        {status === WebSocketStatus.DISCONNECTED && (
          <div className="text-red-600 mt-1">✗ 实时通信不可用</div>
        )}
      </div>
    </div>
  )
}
