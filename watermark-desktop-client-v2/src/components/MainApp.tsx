import { useState, useEffect, useRef } from 'react'
import { listen } from '@tauri-apps/api/event'
import { ThemeProvider } from './ThemeProvider'
import { Toaster } from './ui/toaster'
import { MessageContainer } from './ui/message'
import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { MainContent } from './MainContent'
import { PreviewPanel } from './PreviewPanel'
import { StatusBar } from './StatusBar'
import { useProcessingStore } from '../stores/processingStore'
import { useSettingsStore } from '../stores/settingsStore'
import { useFileStore } from '../stores/fileStore'

import { useMessage } from '../hooks/use-message'
import { initializeConnectionManager } from '../stores/connectionStore'
import { eventBus } from '../lib/event-bus'
import { serviceManager } from '../services/ServiceManager'

// 全局退出处理
let globalShutdownRegistered = false

const registerGlobalShutdown = () => {
  if (globalShutdownRegistered) return

  globalShutdownRegistered = true

  // 监听页面卸载事件
  const handleBeforeUnload = () => {
    console.log('[MainApp] Page unloading, shutting down services...')
    serviceManager.shutdown()
  }

  window.addEventListener('beforeunload', handleBeforeUnload)

  // 如果在 Tauri 环境中，可以添加 Tauri 特定的关闭处理
  if ((window as any).__TAURI__) {
    console.log('[MainApp] Tauri environment detected, shutdown will be handled by beforeunload')
  }
}

export function MainApp() {
  const [isLoading, setIsLoading] = useState(true)
  const { updateTaskProgress } = useProcessingStore()
  const { loadSettings } = useSettingsStore()
  const { files } = useFileStore()

  const [messages, messageAPI] = useMessage()
  const initializationRef = useRef(false)
  const cleanupRef = useRef<(() => void) | null>(null)

  useEffect(() => {
    // 初始化应用
    const initApp = async () => {
      if (initializationRef.current) {
        console.log('[MainApp] Already initialized, skipping...')
        setIsLoading(false)
        return
      }

      try {
        console.log('[MainApp] Starting application initialization...')
        initializationRef.current = true

        // 注册全局退出处理
        registerGlobalShutdown()

        // 初始化服务管理器
        await serviceManager.initialize()

        // 加载设置
        await loadSettings()

        // 初始化连接管理器
        const cleanupConnection = initializeConnectionManager()

        // 监听处理事件（仅在 Tauri 环境中）
        if ((window as any).__TAURI__) {
          try {
            // 监听后端服务状态
            await listen('backend_service_started', () => {
              console.log('✅ 后端服务启动成功')
              messageAPI.success("后端服务已启动", {
                title: "服务就绪",
                duration: 3000,
              })
            })

            await listen('backend_service_failed', (event) => {
              console.error('❌ 后端服务启动失败:', event.payload)
              messageAPI.error(`后端服务启动失败: ${event.payload}`, {
                title: "服务启动失败",
                duration: 8000,
              })
            })

            await listen('processing_started', (event) => {
              console.log('Processing started:', event.payload)
              messageAPI.info("图像处理任务已开始", {
                title: "开始处理",
                duration: 3000,
              })
            })

            await listen('processing_progress', (event) => {
              const { task_id, progress } = event.payload as { task_id: string; progress: number }
              updateTaskProgress(task_id, progress)
            })

            await listen('processing_completed', (event) => {
              console.log('Processing completed:', event.payload)
              messageAPI.success("所有图像已成功处理", {
                title: "处理完成",
                duration: 4000,
              })
            })

            await listen('processing_failed', (event) => {
              console.log('Processing failed:', event.payload)
              messageAPI.error("图像处理过程中出现错误", {
                title: "处理失败",
                duration: 5000,
              })
            })

            console.log('[MainApp] Tauri event listeners registered successfully')
          } catch (error) {
            console.warn('[MainApp] Failed to register Tauri event listeners:', error)
          }
        } else {
          console.log('[MainApp] Running in browser mode, skipping Tauri event listeners')
        }

        // 监听事件总线事件
        eventBus.on('error:occurred', ({ error, context }) => {
          console.error('Application error:', error, context)
          messageAPI.error(error.message || "未知错误", {
            title: "发生错误",
            duration: 5000,
          })
        })

        eventBus.on('connection:lost', () => {
          messageAPI.error("与后端服务的连接已断开", {
            title: "连接丢失",
            duration: 5000,
          })
        })

        eventBus.on('connection:established', () => {
          messageAPI.success("已成功连接到后端服务", {
            title: "连接已建立",
            duration: 3000,
          })
        })

        // 监听服务事件
        eventBus.on('services:initialized', () => {
          messageAPI.success("所有应用服务已成功初始化", {
            title: "服务已启动",
            duration: 3000,
          })
        })

        eventBus.on('service:error', ({ serviceName, error }) => {
          messageAPI.error(`${serviceName} 服务出现错误: ${error}`, {
            title: "服务错误",
            duration: 5000,
          })
        })

        setIsLoading(false)

        // 存储清理函数
        cleanupRef.current = cleanupConnection
      } catch (error) {
        console.error('Failed to initialize app:', error)
        setIsLoading(false)
      }
    }

    initApp()

    // 清理函数 - 只在真正的组件卸载时执行
    return () => {
      console.log('[MainApp] Component unmounting, performing cleanup...')

      // 执行连接清理
      if (cleanupRef.current) {
        cleanupRef.current()
      }

      // 清理事件监听器
      eventBus.clear()

      // 注意：不在这里关闭服务管理器，因为它可能在开发模式下被意外调用
      // 服务管理器应该在应用真正退出时关闭
    }
  }, []) // 移除依赖项，只在组件卸载时执行

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">正在初始化应用...</p>
        </div>
      </div>
    )
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="watermark-ui-theme">
      <div className="flex flex-col h-screen bg-background">
        {/* 顶部标题栏 */}
        <Header />

        {/* 主要内容区域 */}
        <div className="flex flex-1 overflow-hidden">
          {/* 左侧边栏 */}
          <Sidebar />

          {/* 中央内容区域 */}
          <div className="flex flex-1 overflow-hidden">
            {/* 主要内容 */}
            <div className={`flex overflow-hidden transition-all duration-300 ${
              files.length === 0
                ? 'flex-1'
                : files.length > 0 && files.some(f => f.status === 'completed' || f.processedPath)
                  ? 'w-80 min-w-[480px] max-w-md'  // 有处理完成的文件时，MainContent 使用固定宽度
                  : 'flex-1'  // 只有未处理文件时，MainContent 占全部
            }`}>
              <MainContent />
            </div>

            {/* 右侧预览面板 - 只在有文件时显示 */}
            <PreviewPanel />
          </div>
        </div>

        {/* 底部状态栏 */}
        <StatusBar />
      </div>

      {/* 消息提示系统 */}
      <MessageContainer messages={messages} onClose={messageAPI.close} />

      {/* 保留原有的 Toaster 以防兼容性问题 */}
      <Toaster />
    </ThemeProvider>
  )
}
