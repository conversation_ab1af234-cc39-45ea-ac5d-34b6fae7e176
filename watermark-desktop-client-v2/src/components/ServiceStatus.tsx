import { useState, useEffect } from 'react'
import { Activity, AlertCircle, CheckCircle, Clock, RefreshCw } from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from './ui/dialog'
import { serviceManager, ServiceStatus as ServiceStatusType } from '../services/ServiceManager'
import { cn } from '../lib/utils'

interface ServiceStatusProps {
  className?: string
}

export function ServiceStatus({ className }: ServiceStatusProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [services, setServices] = useState<ServiceStatusType[]>([])
  const [stats, setStats] = useState({ total: 0, running: 0, stopped: 0, error: 0, uptime: 0 })

  const updateServiceStatus = () => {
    setServices(serviceManager.getAllServiceStatus())
    setStats(serviceManager.getStats())
  }

  useEffect(() => {
    updateServiceStatus()
    
    // 定期更新状态
    const interval = setInterval(updateServiceStatus, 5000)
    return () => clearInterval(interval)
  }, [])

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return CheckCircle
      case 'error':
        return AlertCircle
      case 'initializing':
        return Clock
      default:
        return Activity
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-600'
      case 'error':
        return 'text-red-600'
      case 'initializing':
        return 'text-yellow-600'
      default:
        return 'text-gray-600'
    }
  }

  const getStatusVariant = (status: string): 'default' | 'secondary' | 'destructive' => {
    switch (status) {
      case 'running':
        return 'default'
      case 'error':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const formatUptime = (ms: number): string => {
    const seconds = Math.floor(ms / 1000)
    const minutes = Math.floor(seconds / 60)
    const hours = Math.floor(minutes / 60)
    
    if (hours > 0) {
      return `${hours}h ${minutes % 60}m`
    } else if (minutes > 0) {
      return `${minutes}m ${seconds % 60}s`
    } else {
      return `${seconds}s`
    }
  }

  const handleRestartService = async (serviceName: string) => {
    try {
      await serviceManager.restartService(serviceName)
      updateServiceStatus()
    } catch (error) {
      console.error(`Failed to restart service ${serviceName}:`, error)
    }
  }

  const overallStatus = stats.error > 0 ? 'error' : stats.running === stats.total ? 'running' : 'partial'
  const StatusIcon = getStatusIcon(overallStatus)

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="ghost" size="sm" className={cn('h-8 px-2', className)}>
          <StatusIcon className={cn('h-4 w-4 mr-2', getStatusColor(overallStatus))} />
          <span className="text-sm">服务</span>
          <Badge variant={getStatusVariant(overallStatus)} className="ml-2 text-xs">
            {stats.running}/{stats.total}
          </Badge>
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>服务状态监控</span>
          </DialogTitle>
          <DialogDescription>
            查看应用服务的运行状态和健康信息
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* 总体统计 */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">总体状态</CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{stats.running}</div>
                  <div className="text-xs text-muted-foreground">运行中</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">{stats.error}</div>
                  <div className="text-xs text-muted-foreground">错误</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-gray-600">{stats.stopped}</div>
                  <div className="text-xs text-muted-foreground">已停止</div>
                </div>
                <div className="text-center">
                  <div className="text-sm font-bold">{formatUptime(stats.uptime)}</div>
                  <div className="text-xs text-muted-foreground">运行时间</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 服务列表 */}
          <div className="space-y-3">
            {services.map((service) => {
              const ServiceIcon = getStatusIcon(service.status)
              
              return (
                <Card key={service.name}>
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <ServiceIcon className={cn('h-5 w-5', getStatusColor(service.status))} />
                        <div>
                          <h4 className="font-medium capitalize">{service.name} 服务</h4>
                          <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                            <Badge variant={getStatusVariant(service.status)} className="text-xs">
                              {service.status === 'running' ? '运行中' :
                               service.status === 'error' ? '错误' :
                               service.status === 'initializing' ? '初始化中' : '已停止'}
                            </Badge>
                            {service.uptime && (
                              <span>运行时间: {formatUptime(service.uptime)}</span>
                            )}
                          </div>
                          {service.lastError && (
                            <div className="text-xs text-red-600 mt-1">
                              错误: {service.lastError}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        {service.status === 'error' && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleRestartService(service.name)}
                            className="h-8"
                          >
                            <RefreshCw className="h-3 w-3 mr-1" />
                            重启
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* 操作按钮 */}
          <div className="flex justify-between pt-4">
            <Button variant="outline" onClick={updateServiceStatus}>
              <RefreshCw className="h-4 w-4 mr-2" />
              刷新状态
            </Button>
            
            <div className="text-xs text-muted-foreground">
              自动刷新间隔: 5秒
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
