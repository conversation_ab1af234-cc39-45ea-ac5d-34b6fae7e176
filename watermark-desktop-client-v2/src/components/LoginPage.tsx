import { Button } from './ui/button'
import { useAuthStore } from '../stores/authStore'
import { useToast } from '../hooks/use-toast'

export function LoginPage() {
  const { login } = useAuthStore()
  const { toast } = useToast()

  const handleLogin = () => {
    login()
    toast({
      title: "登录成功",
      description: "欢迎使用 DeWatermark",
    })
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800 p-4">
      <div className="w-full max-w-md bg-white dark:bg-slate-800 rounded-lg shadow-xl p-8">
        {/* 标题部分 */}
        <div className="text-center space-y-4 mb-8">
          <div className="flex items-center justify-center space-x-2">
            <div className="w-8 h-8 bg-orange-500 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">D</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Welcome to <span className="text-orange-500">DeWatermark</span>
            </h1>
          </div>
          <p className="text-gray-600 dark:text-gray-300 text-base">
            Use your google or email to continue, signing up is free!
          </p>
        </div>

        {/* 登录按钮 */}
        <Button
          onClick={handleLogin}
          className="w-full h-12 text-base font-medium bg-white hover:bg-gray-50 text-gray-700 border border-gray-300 shadow-sm dark:bg-slate-700 dark:hover:bg-slate-600 dark:text-white dark:border-slate-600"
          variant="outline"
        >
          <svg className="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path fill="currentColor" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
          </svg>
          Login with browser
        </Button>

        {/* 服务条款 */}
        <div className="text-center text-xs text-gray-500 dark:text-gray-400 mt-6">
          By continuing up, you agree to our{' '}
          <a href="#" className="text-blue-600 hover:underline dark:text-blue-400">
            Terms of Service
          </a>{' '}
          and our{' '}
          <a href="#" className="text-blue-600 hover:underline dark:text-blue-400">
            Privacy Policy
          </a>
          .
        </div>
      </div>
    </div>
  )
}
