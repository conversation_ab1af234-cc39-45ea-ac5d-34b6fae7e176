import { useState, useRef } from 'react'
import { ZoomIn, ZoomOut, RotateCcw, Download, Eye, EyeOff, ArrowLeftRight } from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { InpaintingResult } from '../services/ImageInpaintingService'

interface InpaintingResultViewerProps {
  result: InpaintingResult
  className?: string
}

export function InpaintingResultViewer({ result, className }: InpaintingResultViewerProps) {
  const [zoom, setZoom] = useState(1)
  const [showComparison, setShowComparison] = useState(true)
  const [showMask, setShowMask] = useState(false)
  const [viewMode, setViewMode] = useState<'side-by-side' | 'overlay'>('side-by-side')
  const originalImageRef = useRef<HTMLImageElement>(null)
  const resultImageRef = useRef<HTMLImageElement>(null)
  const maskImageRef = useRef<HTMLImageElement>(null)

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 5))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.1))
  }

  const handleResetZoom = () => {
    setZoom(1)
  }

  const handleDownload = () => {
    const link = document.createElement('a')
    link.download = `inpainted_${result.original_path.split('/').pop()}`
    link.href = result.output_path
    link.click()
  }

  const toggleComparison = () => {
    setShowComparison(prev => !prev)
  }

  const toggleMask = () => {
    setShowMask(prev => !prev)
  }

  const toggleViewMode = () => {
    setViewMode(prev => prev === 'side-by-side' ? 'overlay' : 'side-by-side')
  }



  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">修复结果</CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant="default">
                {result.watermarks_removed} 个水印已移除
              </Badge>
              {result.quality_score && (
                <Badge variant="secondary">
                  质量: {Math.round(result.quality_score * 100)}%
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 工具栏 */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleResetZoom}>
                <RotateCcw className="h-4 w-4" />
              </Button>
              <span className="text-sm text-muted-foreground">
                {Math.round(zoom * 100)}%
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleComparison}
                className={showComparison ? 'bg-primary text-primary-foreground' : ''}
              >
                <ArrowLeftRight className="h-4 w-4" />
                <span className="ml-1">对比</span>
              </Button>
              
              {result.mask_path && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={toggleMask}
                  className={showMask ? 'bg-primary text-primary-foreground' : ''}
                >
                  {showMask ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                  <span className="ml-1">掩码</span>
                </Button>
              )}

              {showComparison && (
                <Button variant="outline" size="sm" onClick={toggleViewMode}>
                  {viewMode === 'side-by-side' ? '并排' : '叠加'}
                </Button>
              )}

              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4" />
                <span className="ml-1">下载</span>
              </Button>
            </div>
          </div>

          {/* 图像显示区域 */}
          <div className="relative border rounded-lg overflow-hidden bg-gray-50">
            <div 
              className="overflow-auto max-h-96"
              style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}
            >
              {showComparison ? (
                viewMode === 'side-by-side' ? (
                  // 并排对比模式
                  <div className="flex">
                    <div className="flex-1 relative">
                      <img
                        ref={originalImageRef}
                        src={result.original_path}
                        alt="Original"
                        className="block w-full"
                      />
                      <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                        原图
                      </div>
                    </div>
                    <div className="flex-1 relative">
                      <img
                        ref={resultImageRef}
                        src={result.output_path}
                        alt="Inpainted result"
                        className="block w-full"
                      />
                      <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                        修复后
                      </div>
                    </div>
                  </div>
                ) : (
                  // 叠加对比模式
                  <div className="relative">
                    <img
                      ref={originalImageRef}
                      src={result.original_path}
                      alt="Original"
                      className="block w-full"
                    />
                    <img
                      ref={resultImageRef}
                      src={result.output_path}
                      alt="Inpainted result"
                      className="absolute top-0 left-0 w-full opacity-50 hover:opacity-100 transition-opacity cursor-pointer"
                      title="悬停查看修复结果"
                    />
                    <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                      悬停查看修复结果
                    </div>
                  </div>
                )
              ) : (
                // 仅显示修复结果
                <div className="relative">
                  <img
                    ref={resultImageRef}
                    src={result.output_path}
                    alt="Inpainted result"
                    className="block w-full"
                  />
                  <div className="absolute top-2 left-2 bg-black/70 text-white px-2 py-1 rounded text-sm">
                    修复结果
                  </div>
                </div>
              )}

              {/* 掩码叠加 */}
              {showMask && result.mask_path && (
                <div className="absolute top-0 left-0 w-full h-full">
                  <img
                    ref={maskImageRef}
                    src={result.mask_path}
                    alt="Mask"
                    className="w-full h-full opacity-60"
                    style={{ mixBlendMode: 'multiply' }}
                  />
                  <div className="absolute top-2 right-2 bg-red-600 text-white px-2 py-1 rounded text-sm">
                    修复区域
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 修复详情 */}
          <div className="mt-4 space-y-3">
            <h4 className="font-medium">修复详情</h4>
            
            {/* 修复统计 */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-3 bg-gray-50 rounded">
              <div className="text-center">
                <div className="text-lg font-bold text-green-600">
                  {result.watermarks_removed}
                </div>
                <div className="text-xs text-muted-foreground">水印移除</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {result.processing_time.toFixed(0)}ms
                </div>
                <div className="text-xs text-muted-foreground">处理时间</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {result.image_size.width} × {result.image_size.height}
                </div>
                <div className="text-xs text-muted-foreground">图像尺寸</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold">
                  {result.quality_score ? `${Math.round(result.quality_score * 100)}%` : '--'}
                </div>
                <div className="text-xs text-muted-foreground">修复质量</div>
              </div>
            </div>

            {/* 文件信息 */}
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">原始文件:</span>
                <span className="font-mono text-xs">
                  {result.original_path.split('/').pop()}
                </span>
              </div>
              <div className="flex items-center justify-between text-sm">
                <span className="text-muted-foreground">输出文件:</span>
                <span className="font-mono text-xs">
                  {result.output_path.split('/').pop()}
                </span>
              </div>
              {result.mask_path && (
                <div className="flex items-center justify-between text-sm">
                  <span className="text-muted-foreground">掩码文件:</span>
                  <span className="font-mono text-xs">
                    {result.mask_path.split('/').pop()}
                  </span>
                </div>
              )}
            </div>

            {/* 元数据 */}
            {result.metadata && Object.keys(result.metadata).length > 0 && (
              <div className="space-y-2">
                <h5 className="text-sm font-medium">技术参数</h5>
                <div className="grid grid-cols-2 gap-2 text-xs">
                  {Object.entries(result.metadata).map(([key, value]) => (
                    <div key={key} className="flex justify-between">
                      <span className="text-muted-foreground capitalize">
                        {key.replace(/_/g, ' ')}:
                      </span>
                      <span className="font-mono">
                        {typeof value === 'number' ? value.toFixed(3) : String(value)}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
