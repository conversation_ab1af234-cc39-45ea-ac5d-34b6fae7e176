
import { Button } from './ui/button'
import { message } from '../hooks/use-message'

export function MessageTest() {
  const handleSuccess = () => {
    message.success("这是一个成功消息", {
      title: "操作成功",
      duration: 3000,
    })
  }

  const handleError = () => {
    message.error("这是一个错误消息", {
      title: "操作失败",
      duration: 5000,
    })
  }

  const handleWarning = () => {
    message.warning("这是一个警告消息", {
      title: "注意",
      duration: 4000,
    })
  }

  const handleInfo = () => {
    message.info("这是一个信息消息", {
      title: "提示",
      duration: 3000,
    })
  }

  const handleClear = () => {
    message.clear()
  }

  return (
    <div className="p-6 space-y-4">
      <h2 className="text-xl font-semibold">消息系统测试</h2>
      <div className="flex space-x-4">
        <Button onClick={handleSuccess} variant="default">
          成功消息
        </Button>
        <Button onClick={handleError} variant="destructive">
          错误消息
        </Button>
        <Button onClick={handleWarning} variant="outline">
          警告消息
        </Button>
        <Button onClick={handleInfo} variant="secondary">
          信息消息
        </Button>
        <Button onClick={handleClear} variant="ghost">
          清除所有
        </Button>
      </div>
    </div>
  )
}
