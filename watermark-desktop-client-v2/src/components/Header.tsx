import { Info, Minimize2, Square, X, LogOut } from 'lucide-react'
import { Button } from './ui/button'
import { SettingsDialog } from './SettingsDialog'
import { ThemeToggle } from './ThemeToggle'
import { useAuthStore } from '../stores/authStore'
import { useToast } from '../hooks/use-toast'

export function Header() {
  const { logout } = useAuthStore()
  const { toast } = useToast()

  const handleMinimize = () => {
    // TODO: 实现窗口最小化
    console.log('Minimize window')
  }

  const handleMaximize = () => {
    // TODO: 实现窗口最大化
    console.log('Maximize window')
  }

  const handleClose = () => {
    // TODO: 实现窗口关闭
    console.log('Close window')
  }

  const handleAbout = () => {
    // TODO: 打开关于对话框
    console.log('Open about')
  }

  const handleLogout = () => {
    logout()
    toast({
      title: "已登出",
      description: "您已成功登出系统",
    })
  }

  return (
    <header className="flex items-center justify-between h-14 px-4 bg-background border-b border-border">
      {/* 左侧：应用标题和图标 */}
      <div className="flex items-center space-x-3">
        <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
          <span className="text-primary-foreground font-bold text-sm">WR</span>
        </div>
        <div>
          <h1 className="text-lg font-semibold text-foreground">Watermark Remover V2</h1>
          <p className="text-xs text-muted-foreground">智能水印去除工具</p>
        </div>
      </div>

      {/* 中间：状态信息 */}
      <div className="flex items-center space-x-4">
      
      </div>

      {/* 右侧：操作按钮 */}
      <div className="flex items-center space-x-2">
        {/* 主题切换 */}
        <ThemeToggle />

        {/* 设置按钮 */}
        <SettingsDialog />

        {/* 关于按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleAbout}
          className="h-8 w-8 p-0"
        >
          <Info className="h-4 w-4" />
        </Button>

        {/* 登出按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleLogout}
          className="h-8 w-8 p-0"
          title="登出"
        >
          <LogOut className="h-4 w-4" />
        </Button>

        {/* 分隔线 */}
        <div className="w-px h-6 bg-border mx-2" />

        {/* 窗口控制按钮 */}
        <Button
          variant="ghost"
          size="sm"
          onClick={handleMinimize}
          className="h-8 w-8 p-0 hover:bg-muted"
        >
          <Minimize2 className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleMaximize}
          className="h-8 w-8 p-0 hover:bg-muted"
        >
          <Square className="h-4 w-4" />
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleClose}
          className="h-8 w-8 p-0 hover:bg-destructive hover:text-destructive-foreground"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </header>
  )
}
