import { useState, useEffect } from 'react'
import { Play, Square, Trash2, Folder<PERSON><PERSON> } from 'lucide-react'
import { Button } from './ui/button'
import { FileDropZone } from './FileDropZone'
import { FileListItem } from './FileListItem'
import { BatchToolbar } from './BatchToolbar'

import { useFileStore } from '../stores/fileStore'
import { useProcessingStore } from '../stores/processingStore'
import { useBatchSelectionStore } from '../stores/batchSelectionStore'
import { FileStatus, FileItem } from '../types'

import { message } from '../hooks/use-message'
import { tauriAPI } from '../lib/tauri'
import { apiClient, fileAPI } from '../lib/api'
import {
  processSelectedFiles,
  FileProcessingResult
} from '../utils/fileUtils'
import { eventBus } from '../lib/event-bus'
// 暂时使用模拟版本，避免 Tauri API 导入问题
import { downloadManagerMock as downloadManager } from '../lib/downloadManagerMock'
import { pathSelectorMock as pathSelector } from '../lib/pathSelectorMock'
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts'

export function MainContent() {
  const {
    files,
    addFiles,
    addFileItems,
    clearFiles,
    selectedFile,
    selectFile,
    removeFile,
    getFileObject,
    updateFileStatus,
    updateFileProgress,
    updateFilePath,
    updateFileProcessedPath,
    updateFileProcessingResult,
    setFileTaskId,
    getFileById
  } = useFileStore()

  const [isLocalProcessing, setIsLocalProcessing] = useState(false)
  const [currentRequestController, setCurrentRequestController] = useState<AbortController | null>(null)
  const [isStartingProcess, setIsStartingProcess] = useState(false)
  const [webSocketWorking, setWebSocketWorking] = useState(false)
  const [webSocketMessageReceived, setWebSocketMessageReceived] = useState(false)
  const [currentTaskId, setCurrentTaskId] = useState<string | null>(null)
  // 移除缓存相关的状态，使用简单的任务ID匹配方案
  const { isProcessing } = useProcessingStore()

  // 批量选择相关状态
  const {
    setDefaultDownloadPath,
    currentDownloadPath,
    startBatchDownload,
    completeBatchDownload,
    cancelBatchDownload,
    clearSelection,
    selectAllCompleted
  } = useBatchSelectionStore()

  // 键盘快捷键支持
  useKeyboardShortcuts({
    enabled: true,
    onSelectAll: () => selectAllCompleted(files),
    onClearSelection: clearSelection
  })


  // 🧪 全局调试函数
  const debugState = () => {
    console.log('🧪 [调试] 当前状态快照:')
    console.log('🧪 [调试] - currentTaskId:', currentTaskId)
    console.log('🧪 [调试] - files数量:', files.length)
    console.log('🧪 [调试] - webSocketWorking:', webSocketWorking)
  }

  // 暴露到全局，方便在控制台调用
  if (typeof window !== 'undefined') {
    const win = window as any
    win.debugWebSocketState = debugState
    win.testWebSocketCache = () => {
      console.log('🧪 ========== WebSocket缓存机制测试 ==========')
      console.log('🧪 [测试] 当前状态:')
      debugState()
      console.log('🧪 [测试] 请选择文件并开始处理，观察日志输出')
      console.log('🧪 [测试] 关键检查点:')
      console.log('🧪 [测试] 1. 是否看到 "🎯 开始等待API响应，缓存WebSocket消息"')
      console.log('🧪 [测试] 2. 是否看到 "🔍 等待API状态: true"')
      console.log('🧪 [测试] 3. 是否看到 "📦 缓存早期到达的WebSocket消息"')
      console.log('🧪 [测试] 4. 是否看到 "📦 处理 X 条缓存的WebSocket消息"')
      console.log('🧪 ========================================')
    }
  }

  // 初始化默认下载路径
  useEffect(() => {
    const initializeDefaultPath = async () => {
      if (!currentDownloadPath) {
        try {
          const defaultPath = await pathSelector.getDefaultDownloadPath()
          setDefaultDownloadPath(defaultPath)
          console.log(`📁 设置默认下载路径: ${defaultPath}`)
        } catch (error) {
          console.warn('获取默认下载路径失败:', error)
        }
      }
    }

    initializeDefaultPath()
  }, [currentDownloadPath, setDefaultDownloadPath])

  // 监听WebSocket进度更新
  useEffect(() => {
    const handleWebSocketMessage = (data: any) => {
      console.log('📡 收到WebSocket消息:', data)

      if (data.type === 'processing_update') {
        console.log(`📡 处理更新消息 - 任务ID: ${data.task_id}`)

        // 🎯 正确方案：如果当前没有任务ID，直接使用WebSocket消息中的任务ID
        if (!currentTaskId) {
          console.log(`🚀 从WebSocket消息中获取任务ID: ${data.task_id}`)
          setCurrentTaskId(data.task_id)

          // 🔗 同时设置文件的任务ID，确保状态一致
          files.forEach((file) => {
            if (file.status === 'processing') {
              console.log(`🔗 设置文件任务ID: ${file.name?.substring(0, 30)}... -> ${data.task_id}`)
              console.log(`🔍 文件详情: ID=${file.id}, 状态=${file.status}, 当前taskId=${file.taskId}`)
              setFileTaskId(file.id, data.task_id)
              console.log(`✅ 已调用setFileTaskId(${file.id}, ${data.task_id})`)

              // 立即验证Store中的状态
              setTimeout(() => {
                const storeFile = getFileById(file.id)
                console.log(`🔍 Store验证: ${file.name?.substring(0, 30)}... taskId=${storeFile?.taskId || 'undefined'}`)
              }, 10)
            }
          })

          // 继续处理这条消息
        } else if (currentTaskId !== data.task_id) {
          console.log(`🔍 忽略不相关的消息，任务ID不匹配 (当前: ${currentTaskId}, 消息: ${data.task_id})`)
          return
        }

        console.log(`✅ 处理当前任务的消息: ${data.task_id}`)

        // 标记WebSocket正常工作
        setWebSocketWorking(true)
        setWebSocketMessageReceived(true)

        const { stage, progress, message, current_file, total_files, file_name } = data

        console.log(`📡 收到进度更新: ${stage} - ${progress}% - ${message}`)
        console.log(`📋 WebSocket消息详情:`, { stage, progress, message, current_file, total_files, file_name })

    // 显示当前所有文件的状态
    console.log(`📊 当前所有文件状态:`, files.map(f => ({
      name: f.name.substring(0, 20) + '...',
      status: f.status,
      progress: f.progress,
      hasStartTime: !!f.processingStartTime,
      hasEndTime: !!f.processingEndTime
    })))

        // 根据不同阶段更新UI
        switch (stage) {
          case 'started':
            // 处理开始
            message.info(message, {
              title: "开始处理",
              duration: 3000,
            })
            break

          case 'file_started':
            // 文件开始处理，设置真正的开始时间
            if (file_name) {
              console.log(`🔍 查找文件: ${file_name}`)
              console.log(`📋 当前文件列表:`, files.map(f => ({ id: f.id, name: f.name, status: f.status })))

              const fileItem = files.find(f => f.name === file_name)
              if (fileItem) {
                console.log(`✅ 找到文件，设置真正的开始时间: ${fileItem.id}`)

                // 设置文件真正的开始时间（覆盖批量开始时间）
                const realStartTime = new Date()
                console.log(`⏰ 设置文件真正开始时间: ${file_name} -> ${realStartTime.toISOString()}`)

                // 直接更新 store 中的开始时间
                useFileStore.setState(state => ({
                  files: state.files.map(f =>
                    f.id === fileItem.id
                      ? {
                          ...f,
                          status: FileStatus.Processing,
                          processingStartTime: realStartTime,
                          progress: 0
                        }
                      : f
                  )
                }))

                console.log(`🔄 文件开始处理: ${file_name}`)
              } else {
                console.log(`❌ 未找到匹配的文件: ${file_name}`)
                // 尝试模糊匹配（处理文件名可能的差异）
                const fuzzyMatch = files.find(f =>
                  f.name.includes(file_name.split('.')[0]) ||
                  file_name.includes(f.name.split('.')[0])
                )
                if (fuzzyMatch) {
                  console.log(`🔍 模糊匹配成功: ${fuzzyMatch.name}`)
                  const realStartTime = new Date()
                  console.log(`⏰ 模糊匹配设置开始时间: ${fuzzyMatch.name} -> ${realStartTime.toISOString()}`)

                  useFileStore.setState(state => ({
                    files: state.files.map(f =>
                      f.id === fuzzyMatch.id
                        ? {
                            ...f,
                            status: FileStatus.Processing,
                            processingStartTime: realStartTime,
                            progress: 0
                          }
                        : f
                    )
                  }))
                }
              }
            }
            break

          case 'initializing':
          case 'loading':
          case 'detecting':
          case 'inpainting':
          case 'saving':
            // AI处理的各个阶段，更新进度
            console.log(`🔄 更新进度: ${stage} - ${progress}%`)

            if (current_file && total_files && file_name) {
              // 根据文件名找到对应的文件并更新进度
              console.log(`� 查找当前处理文件: ${file_name}`)
              const targetFile = files.find(f => f.name === file_name)

              if (targetFile) {
                console.log(`🎯 更新单个文件进度: ${targetFile.name} -> ${progress}%`)
                updateFileProgress(targetFile.id, progress)
              } else {
                console.log(`❌ 未找到匹配文件: ${file_name}`)
                // 模糊匹配备用
                const fuzzyMatch = files.find(f =>
                  f.name.includes(file_name.split('.')[0]) ||
                  file_name.includes(f.name.split('.')[0])
                )
                if (fuzzyMatch) {
                  console.log(`🔍 模糊匹配成功: ${fuzzyMatch.name}`)
                  updateFileProgress(fuzzyMatch.id, progress)
                }
              }
            } else {
              console.log(`⚠️ 缺少文件信息，跳过进度更新`)
            }
            break

          case 'completed':
            // 单个文件处理完成 - 从消息中提取真实的处理时间
            console.log(`✅ 文件处理完成: ${message}`)

            // 从消息中提取真实的处理时间（如 "处理完成! 耗时: 2.6s"）
            const timeMatch = message.match(/耗时:\s*([\d.]+)s/)
            const realProcessingTime = timeMatch ? parseFloat(timeMatch[1]) : null

            if (file_name) {
              const targetFile = files.find(f => f.name === file_name)
              if (targetFile && targetFile.processingStartTime) {
                // 使用真实的处理时间计算结束时间
                const startTime = targetFile.processingStartTime
                const endTime = realProcessingTime
                  ? new Date(startTime.getTime() + realProcessingTime * 1000)
                  : new Date() // 备用方案

                console.log(`⏰ 使用真实处理时间设置结束时间: ${targetFile.name}`)
                console.log(`⏰ 开始时间: ${startTime.toISOString()}`)
                console.log(`⏰ 真实处理时间: ${realProcessingTime}s`)
                console.log(`⏰ 计算的结束时间: ${endTime.toISOString()}`)
                console.log(`⏰ 验证耗时: ${((endTime.getTime() - startTime.getTime()) / 1000).toFixed(1)}s`)

                // 🔧 修复：更新状态为完成，设置结束时间，但不设置processedPath
                // processedPath 将由轮询逻辑设置，因为WebSocket消息中没有输出路径信息
                updateFileStatus(targetFile.id, FileStatus.Completed)
                updateFileProcessingResult(targetFile.id, {
                  processingEndTime: endTime
                })

                // 标记文件已通过WebSocket完成
                console.log(`✅ WebSocket完成标记: ${targetFile.name} (状态已设置为Completed，等待轮询设置processedPath)`)

                // 🚀 立即触发轮询检查以获取 processedPath
                // 这样用户可以立即看到处理结果，而不需要等待下一次轮询周期
                if (currentTaskId) {
                  console.log(`🔄 立即触发轮询检查以获取 ${targetFile.name} 的 processedPath`)
                  setTimeout(() => {
                    checkSingleFileResult(currentTaskId, targetFile.name, targetFile.path)
                  }, 500) // 延迟500ms，确保后端已经生成了输出文件
                }
              } else {
                console.log(`⚠️ 文件 ${file_name} 缺少开始时间，无法计算真实耗时`)
              }
            }
            break
        }
      } else {
        console.log('📡 收到其他类型消息:', data.type)
      }
    }

    // 添加事件监听器
    const unsubscribe = eventBus.on('websocket:message', handleWebSocketMessage)

    // 返回清理函数
    return () => {
      unsubscribe.unsubscribe()
    }
  }, [files, currentTaskId, setWebSocketWorking, setWebSocketMessageReceived, updateFileStatus, updateFileProgress])

  /**
   * 统一处理文件选择结果
   */
  const handleFileProcessingResult = async (result: FileProcessingResult, source: string) => {
    const { fileItems, errors, warnings } = result

    console.log(`📊 ${source}文件处理结果:`, {
      成功: fileItems.length,
      错误: errors.length,
      警告: warnings.length
    })

    // 添加有效文件到 store
    if (fileItems.length > 0) {
      // 直接添加 FileItem，保持完整的文件信息
      addFileItems(fileItems)

      message.success(`通过${source}成功添加 ${fileItems.length} 个文件`, {
        title: "文件添加成功",
        duration: 3000,
      })
    }

    // 显示错误信息
    if (errors.length > 0) {
      console.error('❌ 文件处理错误:', errors)
      message.warning(`${errors.length} 个文件有问题：${errors[0]}${errors.length > 1 ? ' 等' : ''}`, {
        title: "部分文件无法处理",
        duration: 5000,
      })
    }

    // 显示警告信息
    if (warnings.length > 0) {
      console.warn('⚠️ 文件处理警告:', warnings)
    }

    // 如果没有任何有效文件
    if (fileItems.length === 0 && errors.length > 0) {
      message.error("所选文件都无法处理，请检查文件格式和大小", {
        title: "无法添加文件",
        duration: 5000,
      })
    }
  }

  /**
   * 统一的文件处理函数（拖拽选择）
   */
  const handleFilesSelected = async (newFiles: File[]) => {
    console.log('� 处理拖拽选择的文件:', newFiles.length, '个')

    // 拖拽文件直接使用原有的 addFiles 方法
    addFiles(newFiles)
    message.success(`通过拖拽成功添加 ${newFiles.length} 个文件`, {
      title: "文件添加成功",
      duration: 3000,
    })
  }

  /**
   * 统一的文件处理函数（按钮选择）
   */
  const handleSelectFiles = async () => {
    try {
      console.log('📁 使用文件选择对话框')

      // 检查是否在 Tauri 环境中
      const isTauriEnv = (window as any).__TAURI__ !== undefined

      if (isTauriEnv) {
        // Tauri 环境：使用原生文件选择对话框
        const filePaths = await tauriAPI.file.selectFiles()

        if (filePaths.length > 0) {
          console.log('✅ 用户选择了', filePaths.length, '个文件')
          const result = await processSelectedFiles([], filePaths)
          await handleFileProcessingResult(result, '文件选择')
        } else {
          console.log('ℹ️ 用户取消了文件选择')
        }
      } else {
        // 浏览器环境：使用 HTML input 文件选择
        console.log('🌐 浏览器环境，使用 HTML 文件选择')

        const input = document.createElement('input')
        input.type = 'file'
        input.multiple = true
        input.accept = 'image/jpeg,image/png,image/webp,image/bmp,image/tiff'

        input.onchange = async (event) => {
          const target = event.target as HTMLInputElement
          const files = Array.from(target.files || [])

          if (files.length > 0) {
            console.log('✅ 用户选择了', files.length, '个文件')
            // 直接使用 File 对象
            handleFilesSelected(files)
          } else {
            console.log('ℹ️ 用户取消了文件选择')
          }
        }

        input.click()
      }
    } catch (error) {
      console.error('❌ 文件选择失败:', error)
      message.error(error instanceof Error ? error.message : "文件选择过程中出现错误", {
        title: "选择失败",
        duration: 5000,
      })
    }
  }

  const handleStartProcessing = async () => {
    if (files.length === 0) return

    // 检查是否已经在处理中
    if (isLocalProcessing || isProcessing) {
      message.warning("已有任务在处理中，请等待完成后再试", {
        title: "处理中",
        duration: 3000,
      })
      return
    }

    // 取消之前的请求（如果存在）
    if (currentRequestController) {
      currentRequestController.abort()
    }

    try {
      setIsLocalProcessing(true)
      setIsStartingProcess(true)

      // 立即将所有文件状态更新为"处理中"
      files.forEach(file => {
        // 只更新状态，不设置任务ID，等待真实任务ID
        updateFileStatus(file.id, FileStatus.Processing)
        console.log(`🔄 立即更新文件状态: ${file.name} -> Processing (不设置任务ID)`)
      })

      // 创建新的 AbortController
      const controller = new AbortController()
      setCurrentRequestController(controller)

      console.log('🚀 开始批量处理', files.length, '个文件')

      // 健康检查
      console.log('🔍 检查系统状态...')
      const healthResult = await apiClient.health.check()
      console.log('✅ 系统状态正常:', healthResult.status)

      // 智能环境检测：优先检查文件是否有完整路径
      const hasValidPaths = files.some(file => file.path && file.path !== file.name && file.path.includes('/'))
      const isTauriEnv = (window as any).__TAURI__ !== undefined || hasValidPaths

      console.log('🔍 环境检测结果:')
      console.log('  - __TAURI__ 存在:', (window as any).__TAURI__ !== undefined)
      console.log('  - 文件有完整路径:', hasValidPaths)
      console.log('  - 最终判断:', isTauriEnv ? 'Tauri桌面环境' : '浏览器环境')

      // 获取文件路径和文件ID映射
      const filePaths: string[] = []
      const invalidFiles: string[] = []
      const filePathToIdMap = new Map<string, string>() // 路径到文件ID的映射

      for (const fileItem of files) {
        // 检查文件是否有有效的本地路径（Tauri 环境）
        const hasValidPath = fileItem.path &&
                            fileItem.path !== fileItem.name &&
                            (fileItem.path.includes('/') || fileItem.path.includes('\\')) &&
                            !fileItem.path.startsWith('blob:')

        if (hasValidPath) {
          // 有完整路径：直接使用本地文件路径（Tauri 环境）
          filePaths.push(fileItem.path)
          filePathToIdMap.set(fileItem.path, fileItem.id)
          console.log('📁 使用本地路径:', fileItem.name, '→', fileItem.path)
        } else {
          // 浏览器环境：需要上传文件到后端
          const fileObject = getFileObject(fileItem.id)
          if (fileObject) {
            console.log('📤 上传文件到后端:', fileItem.name)
            try {
              const uploadResult = await fileAPI.uploadFile(fileObject)
              if (uploadResult.files && uploadResult.files.length > 0) {
                // 更新文件项的服务器路径，用于后续处理
                const serverPath = uploadResult.files[0].path
                filePaths.push(serverPath)
                filePathToIdMap.set(serverPath, fileItem.id)

                // 更新 store 中的文件路径（用于任务ID关联）
                updateFilePath(fileItem.id, serverPath)

                console.log('✅ 文件上传成功:', serverPath)
              } else {
                console.error('❌ 上传响应无效:', uploadResult)
                invalidFiles.push(fileItem.name)
              }
            } catch (error) {
              console.error('❌ 文件上传失败:', fileItem.name, error)
              invalidFiles.push(fileItem.name)
            }
          } else {
            console.error('❌ 无法获取文件对象:', fileItem.name)
            invalidFiles.push(fileItem.name)
          }
        }
      }

      // 检查是否有有效文件
      if (filePaths.length === 0) {
        throw new Error('没有有效的文件可以处理')
      }

      if (invalidFiles.length > 0) {
        console.warn('⚠️ 以下文件无法处理:', invalidFiles)
        message.warning(`${invalidFiles.length} 个文件无法处理，将处理其余 ${filePaths.length} 个文件`, {
          title: "部分文件跳过",
          duration: 4000,
        })
      }

      // 开始批量处理
      console.log('🎯 开始批量处理', filePaths.length, '个文件')
      const settings = {
        confidence_threshold: 0.3,
        enhance_mask: true,
        use_smart_enhancement: true,
        context_expansion_ratio: 0.12,
        keep_original_name: true
      }

      console.log('📋 处理配置:', JSON.stringify(settings, null, 2))
      console.log('📂 文件列表:', filePaths)

      // 🎯 最终方案：让WebSocket消息自己设置任务ID，API调用只是触发处理
      console.log('🚀 开始API调用，任务ID将从WebSocket消息中获取')

      // 重置WebSocket状态
      setWebSocketMessageReceived(false)

      const result = await apiClient.processing.start(filePaths, settings)

      console.log('🎉 批量处理任务启动成功!')
      console.log('📋 任务详情:', JSON.stringify(result, null, 2))

      // 🔄 验证任务ID是否已经从WebSocket消息中设置
      if (currentTaskId && currentTaskId === result.task_id) {
        console.log(`✅ 任务ID匹配: ${currentTaskId}`)
      } else {
        console.log(`⚠️ 任务ID不匹配，手动设置: ${currentTaskId} -> ${result.task_id}`)
        setCurrentTaskId(result.task_id)
      }

      // 🔧 强制同步：确保所有文件的taskId都正确设置
      console.log(`🔧 强制同步文件taskId状态`)
      const latestFiles = useFileStore.getState().files
      latestFiles.forEach((file) => {
        if (file.status === 'processing' && file.taskId !== result.task_id) {
          console.log(`🔧 强制同步: ${file.name?.substring(0, 30)}... -> ${result.task_id}`)
          setFileTaskId(file.id, result.task_id)
        }
      })

      console.log(`🔍 设置前文件状态检查:`)
      files.forEach((file, index) => {
        console.log(`  文件 ${index + 1}: ${file.name?.substring(0, 30)}... taskId: ${file.taskId || 'undefined'}`)
      })

      let successCount = 0
      filePaths.forEach((filePath, index) => {
        const fileId = filePathToIdMap.get(filePath)
        if (fileId) {
          const file = files.find(f => f.id === fileId)
          console.log(`🔗 设置文件 ${index + 1}/${filePaths.length}: ${file?.name?.substring(0, 30)}... -> ${result.task_id}`)
          setFileTaskId(fileId, result.task_id)
          successCount++
        } else {
          console.log(`❌ 未找到文件路径对应的文件ID: ${filePath}`)
          console.log('🔍 映射表内容:', Array.from(filePathToIdMap.entries()))
        }
      })

      console.log(`✅ 成功设置 ${successCount}/${filePaths.length} 个文件的任务ID`)

      // 🎯 新方案：不需要缓存机制，直接依赖任务ID匹配
      console.log(`✅ 任务ID已设置，WebSocket消息现在应该能够正确匹配`)

      // 验证任务ID是否正确设置
      setTimeout(() => {
        // 重新获取最新的files状态
        const latestFiles = useFileStore.getState().files

        console.log(`🔍 设置后文件状态验证:`)
        latestFiles.forEach((file, index) => {
          console.log(`  文件 ${index + 1}: ${file.name?.substring(0, 30)}... taskId: ${file.taskId || 'undefined'}`)
          console.log(`    详细信息: ID=${file.id}, 状态=${file.status}, 进度=${file.progress}%`)
        })

        // 额外验证：直接从store获取文件信息
        console.log(`🔍 直接从store验证:`)
        latestFiles.forEach((file, index) => {
          const storeFile = getFileById(file.id)
          console.log(`  Store文件 ${index + 1}: taskId=${storeFile?.taskId || 'undefined'}`)
        })

        // 对比React组件状态和Store状态
        console.log(`🔍 状态对比:`)
        files.forEach((file, index) => {
          const storeFile = latestFiles.find(f => f.id === file.id)
          console.log(`  文件 ${index + 1}: React taskId=${file.taskId || 'undefined'}, Store taskId=${storeFile?.taskId || 'undefined'}`)
        })

        // 🔧 如果发现状态不同步，强制触发重新渲染
        const hasOutOfSyncFiles = files.some((file) => {
          const storeFile = latestFiles.find(f => f.id === file.id)
          return file.taskId !== storeFile?.taskId
        })

        if (hasOutOfSyncFiles) {
          console.log(`🔧 检测到状态不同步，强制触发重新渲染`)
          // 通过更新一个dummy状态来强制重新渲染
          setWebSocketMessageReceived(prev => !prev)
          setTimeout(() => setWebSocketMessageReceived(prev => !prev), 10)
        }
      }, 100)

      message.success(`正在处理 ${filePaths.length} 个文件`, {
        title: "批量处理已启动",
        duration: 3000,
      })

      // 延迟启动轮询（作为备用机制，如果WebSocket不工作）
      setTimeout(() => {
        console.log(`🔍 WebSocket状态检查: webSocketMessageReceived = ${webSocketMessageReceived}`)

        // 🔧 修正逻辑：只有收到WebSocket消息才跳过轮询
        if (!webSocketMessageReceived) {
          console.log('🔄 WebSocket未响应，启动轮询备用机制')
          console.log('🔍 WebSocket状态检查: 任务ID已设置，但未收到匹配的消息')
          pollProcessingStatus(result.task_id, filePaths, filePathToIdMap)
        } else {
          console.log('✅ WebSocket正常工作，跳过轮询')
        }
      }, 5000) // 增加到5秒，给WebSocket更多时间响应

    } catch (error) {
      console.error('Failed to start processing:', error)
      setIsLocalProcessing(false)  // 重置处理状态
      setIsStartingProcess(false)  // 重置启动状态
      setCurrentRequestController(null)  // 清理 controller

      // 检查是否是用户主动取消的请求
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('🚫 请求被用户取消')
        return // 不显示错误提示
      }

      // 改进错误消息处理
      let errorTitle = "处理失败"
      let errorDescription = "未知错误"

      if (error instanceof Error) {
        // 检查是否是503服务不可用错误
        if (error.message.includes('503') || error.message.includes('Service Unavailable')) {
          errorTitle = "服务暂时不可用"
          if (error.message.includes('内存使用率过高')) {
            errorDescription = "系统内存使用率过高，请稍后重试或关闭其他应用程序释放内存"
          } else if (error.message.includes('可用内存不足')) {
            errorDescription = "系统可用内存不足，请关闭其他应用程序后重试"
          } else {
            errorDescription = "后端服务暂时不可用，请稍后重试"
          }
        } else if (error.message.includes('400')) {
          errorTitle = "请求参数错误"
          errorDescription = error.message.replace(/HTTP \d+: /, '')
        } else if (error.message.includes('500')) {
          errorTitle = "服务器内部错误"
          errorDescription = "后端服务出现问题，请检查服务状态或联系管理员"
        } else {
          errorDescription = error.message
        }
      }

      // 使用新的消息系统替代 toast
      message.error(errorDescription, {
        title: errorTitle,
        duration: 6000, // 错误消息显示更长时间
      })
    }
  }

  const pollProcessingStatus = async (taskId: string, filePaths: string[], filePathToIdMap: Map<string, string>) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await apiClient.processing.getStatus(taskId)

        console.log('📊 处理状态更新:', status)

        if (status.status === 'completed') {
          clearInterval(pollInterval)

          console.log('🎉 处理完成！结果详情:', status.result)

          // 更新文件状态
          const result = status.result as any // 临时使用 any 来处理字段名不匹配
          if (result?.processed_files) {
            result.processed_files.forEach((processedFile: any) => {
              // 使用映射表查找文件ID，然后查找文件
              const fileId = filePathToIdMap.get(processedFile.input_path)
              const fileItem = fileId ? files.find(f => f.id === fileId) : null

              if (fileItem) {
                console.log('✅ 更新文件状态:', fileItem.name, '→', processedFile.output_path)

                if (processedFile.success) {
                  // 🔧 修复：无论文件状态如何，都需要设置processedPath（如果还没有的话）
                  if (!fileItem.processedPath) {
                    console.log(`📁 轮询设置processedPath: ${fileItem.name} -> ${processedFile.output_path}`)
                    updateFileProcessedPath(fileItem.id, processedFile.output_path)
                  } else {
                    console.log(`✅ 文件 ${fileItem.name} 已有processedPath: ${fileItem.processedPath}`)
                  }

                  // 如果文件状态还不是完成状态，则更新为完成
                  if (fileItem.status !== FileStatus.Completed) {
                    console.log(`📊 轮询更新文件状态: ${fileItem.name} -> Completed`)
                    updateFileStatus(fileItem.id, FileStatus.Completed)
                  } else {
                    console.log(`✅ 文件 ${fileItem.name} 状态已是Completed，无需更新`)
                  }
                } else {
                  // 处理失败
                  updateFileStatus(fileItem.id, FileStatus.Failed, processedFile.error)
                }
              }
            })
          }

          const successCount = result?.success_count || 0
          const failureCount = result?.failure_count || 0
          const totalTime = result?.total_time || 0

          message.success(`成功处理 ${successCount} 个文件，失败 ${failureCount} 个，耗时 ${totalTime.toFixed(1)}s`, {
            title: "批量处理完成",
            duration: 5000,
          })

          // 显示处理结果详情
          console.log('📋 处理结果统计:')
          console.log('  ✅ 成功:', successCount)
          console.log('  ❌ 失败:', failureCount)
          console.log('  ⏱️ 耗时:', totalTime.toFixed(1), 's')

          // 重置处理状态
          setIsLocalProcessing(false)
          setIsStartingProcess(false)
          setCurrentRequestController(null)

        } else if (status.status === 'failed') {
          clearInterval(pollInterval)
          console.error('❌ 处理失败:', status.error)

          // 更新所有相关文件状态为失败
          filePaths.forEach(filePath => {
            const fileId = filePathToIdMap.get(filePath)
            if (fileId) {
              updateFileStatus(fileId, FileStatus.Failed, status.error)
            }
          })

          message.error(status.error || "处理过程中出现错误", {
            title: "处理失败",
            duration: 6000,
          })

          // 重置处理状态
          setIsLocalProcessing(false)
          setCurrentRequestController(null)
          setCurrentTaskId(null) // 清理当前任务ID
        } else if (status.status === 'processing') {
          console.log('⏳ 处理中... 进度:', status.progress + '%')

          // 更新所有相关文件的进度
          filePaths.forEach(filePath => {
            const fileItem = files.find(f => f.path === filePath)
            if (fileItem && fileItem.status === FileStatus.Processing) {
              updateFileProgress(fileItem.id, status.progress)
            }
          })
        }
      } catch (error) {
        console.error('❌ 获取处理状态失败:', error)
        clearInterval(pollInterval)

        message.error("无法获取处理状态", {
          title: "状态查询失败",
          duration: 4000,
        })
      }
    }, 2000) // 每2秒轮询一次

    // 根据文件数量动态设置超时时间（每个文件预留30秒，最少60秒）
    const timeoutMs = Math.max(60000, filePaths.length * 30000)
    setTimeout(() => {
      clearInterval(pollInterval)
      console.log(`⏰ 轮询超时（${timeoutMs/1000}s），停止状态查询`)
    }, timeoutMs)
  }

  // 🚀 新增：检查单个文件的处理结果
  const checkSingleFileResult = async (taskId: string, fileName: string, filePath: string) => {
    try {
      console.log(`🔍 检查单个文件结果: ${fileName}`)

      // 🔧 修复：在开发环境中使用模拟逻辑，直接设置 processedPath
      if (import.meta.env.DEV) {
        // 查找文件对象
        const fileItem = files.find(f => f.name === fileName)
        if (fileItem && !fileItem.processedPath) {
          // 模拟生成 processedPath
          const mockProcessedPath = `outputs/processed_${fileName}`

          console.log(`📁 立即设置 processedPath (模拟): ${fileName} -> ${mockProcessedPath}`)
          updateFileProcessedPath(fileItem.id, mockProcessedPath)

          // 模拟设置处理结果信息
          updateFileProcessingResult(fileItem.id, {
            detectionCount: Math.floor(Math.random() * 3) + 1, // 1-3个水印
            avgConfidence: 0.8 + Math.random() * 0.2 // 0.8-1.0的置信度
          })

          console.log(`✅ ${fileName} 的处理结果已立即可用 (模拟)`)
        }
        return
      }

      // 生产环境中使用真实的API调用
      const status = await apiClient.processing.getStatus(taskId)

      if (status.status === 'completed' && status.result?.processed_files) {
        // 查找对应文件的处理结果
        const processedFile = status.result.processed_files.find((pf: any) =>
          pf.input_path === filePath
        )

        if (processedFile && processedFile.success && processedFile.output_path) {
          // 查找文件对象
          const fileItem = files.find(f => f.name === fileName)
          if (fileItem && !fileItem.processedPath) {
            console.log(`📁 立即设置 processedPath: ${fileName} -> ${processedFile.output_path}`)
            updateFileProcessedPath(fileItem.id, processedFile.output_path)

            // 如果有其他处理结果信息，也一并更新
            if (processedFile.detection_count !== undefined || processedFile.avg_confidence !== undefined) {
              updateFileProcessingResult(fileItem.id, {
                detectionCount: processedFile.detection_count,
                avgConfidence: processedFile.avg_confidence
              })
            }

            console.log(`✅ ${fileName} 的处理结果已立即可用`)
          }
        }
      }
    } catch (error) {
      console.warn(`⚠️ 检查单个文件结果失败: ${fileName}`, error)
      // 不抛出错误，让正常的轮询机制继续工作
    }
  }

  const handleStopProcessing = () => {
    // TODO: 停止处理
    console.log('Stop processing')
  }

  const handleFileRemove = (file: any) => {
    removeFile(file.id)
  }

  const handleFileSelect = (file: any) => {
    selectFile(file)
  }

  // 批量下载相关处理函数
  const handleSelectDownloadPath = async (): Promise<string | null> => {
    try {
      const selectedPath = await pathSelector.selectFolder({
        title: '选择保存文件夹',
        defaultPath: currentDownloadPath
      })

      if (selectedPath) {
        // 更新默认路径
        setDefaultDownloadPath(selectedPath)
      }

      return selectedPath
    } catch (error) {
      console.error('路径选择失败:', error)
      message.error('无法打开文件夹选择器')
      return null
    }
  }

  const handleBatchDownload = async (files: FileItem[], targetPath: string) => {
    try {
      console.log(`🚀 开始批量下载 ${files.length} 个文件到: ${targetPath}`)

      // 启动批量下载状态
      await startBatchDownload(files)

      // 执行下载
      const result = await downloadManager.downloadFiles(files, {
        targetPath,
        overwrite: false,
        openFolderAfterComplete: true
      })

      // 完成下载
      completeBatchDownload()

      // 显示结果
      if (result.success) {
        message.success(`成功下载 ${result.downloadedFiles.length} 个文件`, {
          title: '批量下载完成',
          duration: 5000
        })

        // 清空选择
        clearSelection()
      } else {
        const successCount = result.downloadedFiles.length
        const failedCount = result.failedFiles.length

        if (successCount > 0) {
          message.warning(
            `部分文件下载成功：${successCount} 个成功，${failedCount} 个失败`,
            {
              title: '批量下载部分完成',
              duration: 6000
            }
          )
        } else {
          message.error('所有文件下载失败', {
            title: '批量下载失败',
            duration: 6000
          })
        }
      }

    } catch (error) {
      console.error('批量下载失败:', error)

      // 取消下载状态
      cancelBatchDownload()

      message.error(
        error instanceof Error ? error.message : '批量下载过程中发生错误',
        {
          title: '批量下载失败',
          duration: 6000
        }
      )
    }
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden">
      {/* 工具栏 - 只在有文件时显示 */}
      {files.length > 0 && (
        <div className="flex items-center justify-between p-4 border-b border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 dark:bg-background/95 dark:supports-[backdrop-filter]:bg-background/80">
          <div className="flex items-center space-x-2">
            <Button onClick={handleSelectFiles} size="sm" variant="outline" className="bg-background hover:bg-muted dark:bg-background dark:hover:bg-muted dark:border-border">
              <FolderOpen className="mr-2 h-4 w-4" />
              选择文件
            </Button>

            <Button
              onClick={clearFiles}
              variant="outline"
              size="sm"
              className="dark:border-border dark:hover:bg-muted"
            >
              <Trash2 className="mr-2 h-4 w-4" />
              清空
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            {!isProcessing ? (
              <Button
                onClick={handleStartProcessing}
                disabled={files.length === 0 || isStartingProcess}
                size="sm"
                className="bg-primary hover:bg-primary/90"
              >
                <Play className="mr-2 h-4 w-4" />
                {isStartingProcess ? '正在启动...' : `开始处理 (${files.length})`}
              </Button>
            ) : (
              <Button
                onClick={handleStopProcessing}
                variant="destructive"
                size="sm"
              >
                <Square className="mr-2 h-4 w-4" />
                停止处理
              </Button>
            )}
          </div>
        </div>
      )}

      {/* 主要内容区域 */}
      <div className="flex-1 relative overflow-hidden">
        {files.length === 0 ? (
          // 无文件状态：全屏拖拽上传区域
          <div className="h-full flex items-center justify-center bg-background dark:bg-background">
            <div className="w-full h-full">
              <FileDropZone
                onFilesSelected={handleFilesSelected}
                disabled={isProcessing}
                className="w-full h-full bg-background dark:bg-background transition-colors duration-200"
              />
            </div>
          </div>
        ) : (
          // 有文件状态：紧凑的文件列表
          <div className="h-full overflow-auto p-4 bg-muted/20 dark:bg-muted/10 scrollbar-hide">
            <div className="space-y-2">
              {files.map((file) => (
                <FileListItem
                  key={file.id}
                  file={file}
                  isSelected={selectedFile?.id === file.id}
                  onSelect={handleFileSelect}
                  onRemove={handleFileRemove}
                />
              ))}
            </div>

         
          </div>
        )}
      </div>

      {/* 批量操作工具栏 */}
      <BatchToolbar
        files={files}
        onSelectPath={handleSelectDownloadPath}
        onDownload={handleBatchDownload}
      />
    </div>
  )
}
