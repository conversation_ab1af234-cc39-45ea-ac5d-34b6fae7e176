import { useState, useRef, useEffect } from 'react'
import { ZoomIn, ZoomOut, RotateCcw, Download, Eye, EyeOff } from 'lucide-react'
import { Button } from './ui/button'
import { Badge } from './ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { DetectionResult, DetectionBox } from '../services/WatermarkDetectionService'

interface DetectionResultViewerProps {
  result: DetectionResult
  className?: string
}

export function DetectionResultViewer({ result, className }: DetectionResultViewerProps) {
  const [zoom, setZoom] = useState(1)
  const [showDetections, setShowDetections] = useState(true)
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const imageRef = useRef<HTMLImageElement>(null)

  useEffect(() => {
    if (result && canvasRef.current && imageRef.current) {
      drawDetections()
    }
  }, [result, zoom, showDetections])

  const drawDetections = () => {
    const canvas = canvasRef.current
    const image = imageRef.current
    if (!canvas || !image || !result) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    // 设置画布尺寸
    canvas.width = image.naturalWidth
    canvas.height = image.naturalHeight

    // 绘制原图
    ctx.drawImage(image, 0, 0)

    // 绘制检测框
    if (showDetections && result.detections.length > 0) {
      result.detections.forEach((detection, index) => {
        drawDetectionBox(ctx, detection, index)
      })
    }
  }

  const drawDetectionBox = (ctx: CanvasRenderingContext2D, detection: DetectionBox, index: number) => {
    const { x, y, width, height, confidence, class_name } = detection

    // 设置样式
    ctx.strokeStyle = getColorForClass(class_name)
    ctx.lineWidth = 3
    ctx.fillStyle = `${getColorForClass(class_name)}20` // 半透明填充

    // 绘制检测框
    ctx.strokeRect(x, y, width, height)
    ctx.fillRect(x, y, width, height)

    // 绘制标签背景
    const label = `${class_name} ${(confidence * 100).toFixed(1)}%`
    ctx.font = '16px Arial'
    const textMetrics = ctx.measureText(label)
    const textHeight = 20
    const padding = 4

    ctx.fillStyle = getColorForClass(class_name)
    ctx.fillRect(
      x,
      y - textHeight - padding,
      textMetrics.width + padding * 2,
      textHeight + padding
    )

    // 绘制标签文字
    ctx.fillStyle = 'white'
    ctx.fillText(label, x + padding, y - padding)

    // 绘制序号
    ctx.fillStyle = getColorForClass(class_name)
    ctx.beginPath()
    ctx.arc(x + width - 15, y + 15, 12, 0, 2 * Math.PI)
    ctx.fill()
    
    ctx.fillStyle = 'white'
    ctx.font = 'bold 12px Arial'
    ctx.textAlign = 'center'
    ctx.fillText((index + 1).toString(), x + width - 15, y + 20)
    ctx.textAlign = 'left'
  }

  const getColorForClass = (className: string): string => {
    const colors = {
      watermark: '#ef4444',
      logo: '#f97316',
      text: '#eab308',
      signature: '#22c55e',
      default: '#6366f1'
    }
    return colors[className as keyof typeof colors] || colors.default
  }

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.2, 5))
  }

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.2, 0.1))
  }

  const handleResetZoom = () => {
    setZoom(1)
  }

  const handleDownload = () => {
    const canvas = canvasRef.current
    if (!canvas) return

    const link = document.createElement('a')
    link.download = `detection_result_${result.image_path.split('/').pop()}`
    link.href = canvas.toDataURL()
    link.click()
  }

  const toggleDetections = () => {
    setShowDetections(prev => !prev)
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">检测结果</CardTitle>
            <div className="flex items-center space-x-2">
              <Badge variant={result.has_watermark ? 'destructive' : 'default'}>
                {result.has_watermark ? '发现水印' : '无水印'}
              </Badge>
              <Badge variant="secondary">
                {result.detection_count} 个检测
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* 工具栏 */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2">
              <Button variant="outline" size="sm" onClick={handleZoomIn}>
                <ZoomIn className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleZoomOut}>
                <ZoomOut className="h-4 w-4" />
              </Button>
              <Button variant="outline" size="sm" onClick={handleResetZoom}>
                <RotateCcw className="h-4 w-4" />
              </Button>
              <span className="text-sm text-muted-foreground">
                {Math.round(zoom * 100)}%
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={toggleDetections}
                className={showDetections ? 'bg-primary text-primary-foreground' : ''}
              >
                {showDetections ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                <span className="ml-1">检测框</span>
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="h-4 w-4" />
                <span className="ml-1">下载</span>
              </Button>
            </div>
          </div>

          {/* 图像显示区域 */}
          <div className="relative border rounded-lg overflow-hidden bg-gray-50">
            <div 
              className="overflow-auto max-h-96"
              style={{ transform: `scale(${zoom})`, transformOrigin: 'top left' }}
            >
              <div className="relative">
                <img
                  ref={imageRef}
                  src={result.image_path}
                  alt="Detection result"
                  className="block max-w-full"
                  onLoad={drawDetections}
                />
                <canvas
                  ref={canvasRef}
                  className="absolute top-0 left-0 pointer-events-none"
                  style={{ width: '100%', height: '100%' }}
                />
              </div>
            </div>
          </div>

          {/* 检测详情 */}
          {result.detections.length > 0 && (
            <div className="mt-4">
              <h4 className="font-medium mb-2">检测详情</h4>
              <div className="space-y-2">
                {result.detections.map((detection, index) => (
                  <div
                    key={index}
                    className="flex items-center justify-between p-2 bg-gray-50 rounded"
                  >
                    <div className="flex items-center space-x-2">
                      <div
                        className="w-4 h-4 rounded"
                        style={{ backgroundColor: getColorForClass(detection.class_name) }}
                      />
                      <span className="font-medium">#{index + 1}</span>
                      <span>{detection.class_name}</span>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-muted-foreground">
                      <span>置信度: {(detection.confidence * 100).toFixed(1)}%</span>
                      <span>
                        位置: ({Math.round(detection.x)}, {Math.round(detection.y)})
                      </span>
                      <span>
                        尺寸: {Math.round(detection.width)} × {Math.round(detection.height)}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 处理信息 */}
          <div className="mt-4 pt-4 border-t">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">图像尺寸:</span>
                <div className="font-medium">
                  {result.image_size.width} × {result.image_size.height}
                </div>
              </div>
              <div>
                <span className="text-muted-foreground">处理时间:</span>
                <div className="font-medium">{result.processing_time.toFixed(0)}ms</div>
              </div>
              <div>
                <span className="text-muted-foreground">置信度阈值:</span>
                <div className="font-medium">{(result.confidence_threshold * 100).toFixed(0)}%</div>
              </div>
              <div>
                <span className="text-muted-foreground">检测数量:</span>
                <div className="font-medium">{result.detection_count}</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
