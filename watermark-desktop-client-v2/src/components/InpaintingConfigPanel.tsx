import { useState, useEffect } from 'react'
import { <PERSON><PERSON><PERSON>, RotateCcw, Save, Paintbrush } from 'lucide-react'
import { Button } from './ui/button'
import { Card, CardContent, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { imageInpaintingService, InpaintingOptions } from '../services/ImageInpaintingService'
import { aiModelService, ModelType } from '../services/AIModelService'
import { eventBus } from '../lib/event-bus'
import { useToast } from '../hooks/use-toast'

interface InpaintingConfigPanelProps {
  onOptionsChange?: (options: InpaintingOptions) => void
  className?: string
}

export function InpaintingConfigPanel({ onOptionsChange, className }: InpaintingConfigPanelProps) {
  const [options, setOptions] = useState<InpaintingOptions>({})
  const [modelStatus, setModelStatus] = useState<string>('unloaded')
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  useEffect(() => {
    // 获取默认配置
    const defaultOptions = imageInpaintingService.getDefaultOptions()
    setOptions(defaultOptions)

    // 获取模型状态
    const lamaModel = aiModelService.getModelInfo(ModelType.LAMA)
    if (lamaModel) {
      setModelStatus(lamaModel.status)
    }

    // 监听模型状态变化
    const unsubscribe = eventBus.on('model:status:changed', ({ modelType, model }) => {
      if (modelType === ModelType.LAMA) {
        setModelStatus(model.status)
      }
    })

    return () => unsubscribe.unsubscribe()
  }, [])

  const handleOptionChange = (key: keyof InpaintingOptions, value: any) => {
    const newOptions = { ...options, [key]: value }
    setOptions(newOptions)
    onOptionsChange?.(newOptions)
  }

  const handleSaveAsDefault = () => {
    imageInpaintingService.updateDefaultOptions(options)
    toast({
      title: "配置已保存",
      description: "修复配置已保存为默认设置",
    })
  }

  const handleResetToDefault = () => {
    const defaultOptions = {
      maskExpansion: 10,
      contextExpansionRatio: 0.1,
      enhanceMask: true,
      useSmartEnhancement: true,
      outputFormat: 'same' as const,
      quality: 95,
      deviceType: 'auto' as const,
      batchSize: 2
    }
    setOptions(defaultOptions)
    onOptionsChange?.(defaultOptions)
    toast({
      title: "配置已重置",
      description: "修复配置已重置为默认值",
    })
  }

  const handleLoadModel = async () => {
    setIsLoading(true)
    try {
      await aiModelService.loadModel(ModelType.LAMA, {
        deviceType: options.deviceType
      })
      toast({
        title: "模型加载成功",
        description: "LaMa 修复模型已成功加载",
      })
    } catch (error) {
      toast({
        title: "模型加载失败",
        description: error instanceof Error ? error.message : "未知错误",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const getModelStatusBadge = () => {
    switch (modelStatus) {
      case 'loaded':
        return <Badge variant="default">已加载</Badge>
      case 'loading':
        return <Badge variant="secondary">加载中...</Badge>
      case 'error':
        return <Badge variant="destructive">错误</Badge>
      default:
        return <Badge variant="outline">未加载</Badge>
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Settings className="h-5 w-5" />
          <span>修复配置</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* 模型状态 */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">LaMa 模型状态</span>
            {getModelStatusBadge()}
          </div>
          {modelStatus !== 'loaded' && (
            <Button
              onClick={handleLoadModel}
              disabled={isLoading || modelStatus === 'loading'}
              size="sm"
              className="w-full"
            >
              {isLoading ? '加载中...' : '加载模型'}
            </Button>
          )}
        </div>

        {/* 修复参数 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">修复参数</h4>
          
          {/* 掩码扩展 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">掩码扩展 (像素)</label>
              <Badge variant="secondary">{options.maskExpansion}</Badge>
            </div>
            <input
              type="range"
              min="0"
              max="50"
              step="1"
              value={options.maskExpansion || 10}
              onChange={(e) => handleOptionChange('maskExpansion', parseInt(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              扩展检测到的水印区域，有助于完全移除水印边缘
            </p>
          </div>

          {/* 上下文扩展比例 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">上下文扩展比例</label>
              <Badge variant="secondary">{options.contextExpansionRatio}</Badge>
            </div>
            <input
              type="range"
              min="0"
              max="0.5"
              step="0.05"
              value={options.contextExpansionRatio || 0.1}
              onChange={(e) => handleOptionChange('contextExpansionRatio', parseFloat(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              扩展修复区域的上下文信息，提高修复质量
            </p>
          </div>

          {/* 增强掩码 */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">增强掩码</label>
              <p className="text-xs text-muted-foreground">
                自动优化掩码边缘，提高修复效果
              </p>
            </div>
            <input
              type="checkbox"
              checked={options.enhanceMask || true}
              onChange={(e) => handleOptionChange('enhanceMask', e.target.checked)}
              className="rounded"
            />
          </div>

          {/* 智能增强 */}
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium">智能增强</label>
              <p className="text-xs text-muted-foreground">
                使用AI算法进一步优化修复结果
              </p>
            </div>
            <input
              type="checkbox"
              checked={options.useSmartEnhancement || true}
              onChange={(e) => handleOptionChange('useSmartEnhancement', e.target.checked)}
              className="rounded"
            />
          </div>
        </div>

        {/* 输出参数 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">输出参数</h4>
          
          {/* 输出格式 */}
          <div className="space-y-2">
            <label className="text-sm">输出格式</label>
            <select
              value={options.outputFormat || 'same'}
              onChange={(e) => handleOptionChange('outputFormat', e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md text-sm"
            >
              <option value="same">与原图相同</option>
              <option value="jpg">JPEG</option>
              <option value="png">PNG</option>
            </select>
          </div>

          {/* 输出质量 */}
          {(options.outputFormat === 'jpg' || options.outputFormat === 'same') && (
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <label className="text-sm">输出质量</label>
                <Badge variant="secondary">{options.quality}%</Badge>
              </div>
              <input
                type="range"
                min="50"
                max="100"
                step="5"
                value={options.quality || 95}
                onChange={(e) => handleOptionChange('quality', parseInt(e.target.value))}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                较高的质量会产生更大的文件
              </p>
            </div>
          )}
        </div>

        {/* 性能参数 */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">性能参数</h4>
          
          {/* 设备类型 */}
          <div className="space-y-2">
            <label className="text-sm">计算设备</label>
            <select
              value={options.deviceType || 'auto'}
              onChange={(e) => handleOptionChange('deviceType', e.target.value)}
              className="w-full px-3 py-2 border border-input rounded-md text-sm"
            >
              <option value="auto">自动选择</option>
              <option value="cpu">CPU</option>
              <option value="gpu">GPU</option>
            </select>
            <p className="text-xs text-muted-foreground">
              GPU 可以显著提高修复速度
            </p>
          </div>

          {/* 批处理大小 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm">批处理大小</label>
              <Badge variant="secondary">{options.batchSize}</Badge>
            </div>
            <input
              type="range"
              min="1"
              max="8"
              step="1"
              value={options.batchSize || 2}
              onChange={(e) => handleOptionChange('batchSize', parseInt(e.target.value))}
              className="w-full"
            />
            <p className="text-xs text-muted-foreground">
              同时处理的图片数量，较大的值需要更多内存
            </p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex space-x-2 pt-4 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={handleResetToDefault}
            className="flex-1"
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSaveAsDefault}
            className="flex-1"
          >
            <Save className="h-4 w-4 mr-1" />
            保存为默认
          </Button>
        </div>

        {/* 预设配置 */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">预设配置</h4>
          <div className="grid grid-cols-2 gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  maskExpansion: 20,
                  contextExpansionRatio: 0.2,
                  enhanceMask: true,
                  useSmartEnhancement: true,
                  outputFormat: 'same' as const,
                  quality: 100,
                  deviceType: 'auto' as const,
                  batchSize: 1
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              <Paintbrush className="h-3 w-3 mr-1" />
              高质量
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  maskExpansion: 5,
                  contextExpansionRatio: 0.05,
                  enhanceMask: false,
                  useSmartEnhancement: false,
                  outputFormat: 'jpg' as const,
                  quality: 85,
                  deviceType: 'gpu' as const,
                  batchSize: 4
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              高速度
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  maskExpansion: 15,
                  contextExpansionRatio: 0.15,
                  enhanceMask: true,
                  useSmartEnhancement: true,
                  outputFormat: 'same' as const,
                  quality: 95,
                  deviceType: 'auto' as const,
                  batchSize: 2
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              平衡模式
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                const preset = {
                  maskExpansion: 8,
                  contextExpansionRatio: 0.08,
                  enhanceMask: true,
                  useSmartEnhancement: false,
                  outputFormat: 'jpg' as const,
                  quality: 80,
                  deviceType: 'cpu' as const,
                  batchSize: 1
                }
                setOptions(preset)
                onOptionsChange?.(preset)
              }}
            >
              节能模式
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
