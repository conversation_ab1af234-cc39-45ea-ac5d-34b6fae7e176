import { useState, useEffect, useCallback } from 'react'
import { createPortal } from 'react-dom'
import { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'
import { cn } from '@/lib/utils'

export type MessageType = 'success' | 'error' | 'warning' | 'info'

export interface MessageItem {
  id: string
  type: MessageType
  title?: string
  content: string
  duration?: number
  closable?: boolean
}

interface MessageProps {
  message: MessageItem
  onClose: (id: string) => void
}

const messageIcons = {
  success: CheckCircle,
  error: AlertCircle,
  warning: AlertTriangle,
  info: Info,
}

const messageStyles = {
  success: 'bg-green-50 border-green-200 text-green-800 dark:bg-green-950/50 dark:border-green-800 dark:text-green-200',
  error: 'bg-red-50 border-red-200 text-red-800 dark:bg-red-950/50 dark:border-red-800 dark:text-red-200',
  warning: 'bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950/50 dark:border-yellow-800 dark:text-yellow-200',
  info: 'bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950/50 dark:border-blue-800 dark:text-blue-200',
}

const iconStyles = {
  success: 'text-green-500 dark:text-green-400',
  error: 'text-red-500 dark:text-red-400',
  warning: 'text-yellow-500 dark:text-yellow-400',
  info: 'text-blue-500 dark:text-blue-400',
}

function Message({ message, onClose }: MessageProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [isLeaving, setIsLeaving] = useState(false)

  const Icon = messageIcons[message.type]

  useEffect(() => {
    // 进入动画
    const timer = setTimeout(() => setIsVisible(true), 10)
    return () => clearTimeout(timer)
  }, [])

  useEffect(() => {
    // 自动关闭
    if (message.duration && message.duration > 0) {
      const timer = setTimeout(() => {
        handleClose()
      }, message.duration)
      return () => clearTimeout(timer)
    }
  }, [message.duration])

  const handleClose = useCallback(() => {
    setIsLeaving(true)
    setTimeout(() => {
      onClose(message.id)
    }, 300) // 等待退出动画完成
  }, [message.id, onClose])

  return (
    <div
      className={cn(
        'flex items-start space-x-3 p-4 mb-3 border rounded-lg shadow-sm backdrop-blur-sm transition-all duration-300 ease-out transform',
        messageStyles[message.type],
        isVisible && !isLeaving
          ? 'translate-y-0 opacity-100 scale-100'
          : 'translate-y-[-20px] opacity-0 scale-95'
      )}
      style={{
        maxWidth: '480px',
        minWidth: '320px',
      }}
    >
      {/* 图标 */}
      <Icon className={cn('h-5 w-5 flex-shrink-0 mt-0.5', iconStyles[message.type])} />
      
      {/* 内容 */}
      <div className="flex-1 min-w-0">
        {message.title && (
          <div className="font-medium text-sm mb-1">{message.title}</div>
        )}
        <div className="text-sm leading-relaxed">{message.content}</div>
      </div>

      {/* 关闭按钮 */}
      {message.closable !== false && (
        <button
          onClick={handleClose}
          className={cn(
            'flex-shrink-0 p-1 rounded-md transition-colors hover:bg-black/5 dark:hover:bg-white/5',
            iconStyles[message.type]
          )}
        >
          <X className="h-4 w-4" />
        </button>
      )}
    </div>
  )
}

interface MessageContainerProps {
  messages: MessageItem[]
  onClose: (id: string) => void
}

export function MessageContainer({ messages, onClose }: MessageContainerProps) {
  if (messages.length === 0) return null

  return createPortal(
    <div className="fixed top-6 left-1/2 transform -translate-x-1/2 z-[9999] pointer-events-none">
      <div className="pointer-events-auto">
        {messages.map((message) => (
          <Message key={message.id} message={message} onClose={onClose} />
        ))}
      </div>
    </div>,
    document.body
  )
}

// 默认配置
export const defaultMessageConfig = {
  duration: 4000, // 4秒
  closable: true,
}
