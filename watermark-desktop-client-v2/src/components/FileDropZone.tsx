import { useState, useCallback, useRef } from 'react'
import { Upload, FileImage, AlertCircle } from 'lucide-react'
import { Card, CardContent } from './ui/card'
import { Button } from './ui/button'
import { cn } from '@/lib/utils'

interface FileDropZoneProps {
  onFilesSelected: (files: File[]) => void
  accept?: string[]
  maxFiles?: number
  maxSize?: number // in bytes
  disabled?: boolean
  className?: string
  compact?: boolean
}

export function FileDropZone({
  onFilesSelected,
  accept = ['image/jpeg', 'image/png', 'image/webp', 'image/bmp', 'image/tiff'],
  maxFiles = 50,
  maxSize = 50 * 1024 * 1024, // 50MB
  disabled = false,
  className,
  compact = false
}: FileDropZoneProps) {
  const [isDragOver, setIsDragOver] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFiles = useCallback((files: File[]): File[] => {
    setError(null)
    
    if (files.length > maxFiles) {
      setError(`最多只能选择 ${maxFiles} 个文件`)
      return []
    }

    const validFiles: File[] = []
    const invalidFiles: string[] = []

    for (const file of files) {
      // 检查文件类型
      if (!accept.includes(file.type)) {
        invalidFiles.push(`${file.name}: 不支持的文件格式`)
        continue
      }

      // 检查文件大小
      if (file.size > maxSize) {
        invalidFiles.push(`${file.name}: 文件过大 (最大 ${Math.round(maxSize / 1024 / 1024)}MB)`)
        continue
      }

      validFiles.push(file)
    }

    if (invalidFiles.length > 0) {
      setError(invalidFiles.join(', '))
    }

    return validFiles
  }, [accept, maxFiles, maxSize])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (!disabled) {
      setIsDragOver(true)
    }
  }, [disabled])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setIsDragOver(false)

    if (disabled) return

    const files = Array.from(e.dataTransfer.files)
    const validFiles = validateFiles(files)
    
    if (validFiles.length > 0) {
      onFilesSelected(validFiles)
    }
  }, [disabled, validateFiles, onFilesSelected])

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (disabled) return

    const files = Array.from(e.target.files || [])
    const validFiles = validateFiles(files)
    
    if (validFiles.length > 0) {
      onFilesSelected(validFiles)
    }

    // 清空 input 值，允许重复选择同一文件
    e.target.value = ''
  }, [disabled, validateFiles, onFilesSelected])

  const handleButtonClick = useCallback(() => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }, [disabled])

  const acceptedFormats = accept.map(type => {
    const ext = type.split('/')[1]
    return ext.toUpperCase()
  }).join(', ')

  return (
    <Card className={cn('transition-all duration-200 rounded-none border-none bg-background dark:bg-background', className)}>
      <CardContent className="p-0 h-full">
        <div
          className={cn(
            'relative border-2 border-dashed border-transparent rounded-none w-full h-full flex items-center justify-center text-center transition-all duration-200',
            compact ? 'p-4' : 'p-8',
            'hover:bg-primary/5 hover:border-primary/20 dark:hover:bg-primary/10 dark:hover:border-primary/30',
            isDragOver && 'bg-primary/10 border-primary/40 dark:bg-primary/20 dark:border-primary/50 scale-[1.02]',
            disabled && 'opacity-50 cursor-not-allowed',
            !isDragOver && 'border-border/20 dark:border-border/30'
          )}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
        >
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={accept.join(',')}
            onChange={handleFileSelect}
            disabled={disabled}
            className="hidden"
          />

          {compact ? (
            // 紧凑模式
            <div className="flex items-center justify-center space-x-3">
              <div className={cn(
                'p-2 rounded-full transition-colors',
                isDragOver
                  ? 'bg-primary text-primary-foreground dark:bg-primary dark:text-primary-foreground'
                  : 'bg-muted text-muted-foreground dark:bg-muted dark:text-muted-foreground'
              )}>
                {isDragOver ? (
                  <FileImage className="h-4 w-4" />
                ) : (
                  <Upload className="h-4 w-4" />
                )}
              </div>
              <div className="text-left">
                <p className="text-sm font-medium text-foreground dark:text-foreground">
                  {isDragOver ? '释放文件到此处' : '拖拽更多图片到此处'}
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleButtonClick}
                  className="text-xs h-6 px-2 bg-background hover:bg-muted dark:bg-background dark:hover:bg-muted dark:border-border"
                >
                  点击选择文件
                </Button>
              </div>
            </div>
          ) : (
            // 完整模式
            <div className="flex flex-col items-center space-y-4">
              <div className={cn(
                'p-4 rounded-full transition-colors',
                isDragOver
                  ? 'bg-primary text-primary-foreground dark:bg-primary dark:text-primary-foreground'
                  : 'bg-muted text-muted-foreground dark:bg-muted dark:text-muted-foreground'
              )}>
                {isDragOver ? (
                  <FileImage className="h-8 w-8" />
                ) : (
                  <Upload className="h-8 w-8" />
                )}
              </div>

              <div className="space-y-2">
              
                <p className="text-base text-muted-foreground dark:text-muted-foreground">
                 Drop your images anywhere on this page or select from your device
                </p>
                <p className="text-xs text-muted-foreground dark:text-muted-foreground">
                  支持格式: {acceptedFormats}
                </p>
       
              </div>

              {!disabled && (
                <Button
                  variant="outline"
                  size="lg"
                  onClick={handleButtonClick}
                  className="z-10 rounded-lg bg-orange-500 text-white hover:bg-orange-600 hover:text-white dark:bg-orange-600 dark:hover:bg-orange-700 dark:border-orange-600 dark:hover:border-orange-700"
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Select up to  {maxFiles}  images
                </Button>
              )}
            </div>
          )}
        </div>

        {error && (
          <div className="p-4 border-t border-border bg-destructive/5 dark:bg-destructive/10">
            <div className="flex items-center space-x-2 text-destructive dark:text-red-400">
              <AlertCircle className="h-4 w-4" />
              <span className="text-sm">{error}</span>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
