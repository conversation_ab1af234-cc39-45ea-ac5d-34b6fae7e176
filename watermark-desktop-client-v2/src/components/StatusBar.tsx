import { useFileStore } from '../stores/fileStore'
import { useProcessingStore } from '../stores/processingStore'
import { ConnectionStatus } from './ConnectionStatus'
import { ServiceStatus } from './ServiceStatus'

export function StatusBar() {
  const { files } = useFileStore()
  const { currentTask, isProcessing } = useProcessingStore()

  const readyCount = files.filter(f => f.status === 'ready').length
  const processingCount = files.filter(f => f.status === 'processing').length
  const completedCount = files.filter(f => f.status === 'completed').length
  const failedCount = files.filter(f => f.status === 'failed').length

  return (
    <footer className="h-8 px-4 bg-muted/30 border-t border-border flex items-center justify-between text-xs text-muted-foreground">
      {/* 左侧：文件统计 */}
      <div className="flex items-center space-x-4">
        <span>总计: {files.length}</span>
        {readyCount > 0 && <span>准备: {readyCount}</span>}
        {processingCount > 0 && <span>处理中: {processingCount}</span>}
        {completedCount > 0 && <span>已完成: {completedCount}</span>}
        {failedCount > 0 && <span>失败: {failedCount}</span>}
      </div>

      {/* 中间：处理进度 */}
      {isProcessing && currentTask && (
        <div className="flex items-center space-x-2">
          <div className="w-32 h-1 bg-muted rounded-full overflow-hidden">
            <div 
              className="h-full bg-primary transition-all duration-300"
              style={{ width: `${currentTask.progress}%` }}
            />
          </div>
          <span>{Math.round(currentTask.progress)}%</span>
        </div>
      )}

      {/* 右侧：系统信息和状态 */}
      <div className="flex items-center space-x-4">
        <ServiceStatus />
        <ConnectionStatus showDetails />
        <span>内存: 2.1GB / 8GB</span>
        <span>GPU: 可用</span>
        <span>版本: 0.1.0</span>
      </div>
    </footer>
  )
}
