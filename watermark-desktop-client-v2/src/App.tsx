import { ThemeProvider } from './components/ThemeProvider'
import { Toaster } from './components/ui/toaster'
import { LoginPage } from './components/LoginPage'
import { MainApp } from './components/MainApp'
import { useAuthStore } from './stores/authStore'

function App() {
  const { isAuthenticated } = useAuthStore()

  // 如果未登录，显示登录页面
  if (!isAuthenticated) {
    return (
      <ThemeProvider defaultTheme="system" storageKey="watermark-ui-theme">
        <LoginPage />
        <Toaster />
      </ThemeProvider>
    )
  }

  // 如果已登录，显示主应用
  return <MainApp />
}

export default App
