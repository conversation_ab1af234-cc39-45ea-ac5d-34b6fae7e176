/**
 * 统一的文件处理工具函数
 * 确保文件选择和拖拽的一致性
 */

import { FileItem, FileStatus } from '../types'

/**
 * 支持的图片格式
 */
export const SUPPORTED_IMAGE_FORMATS = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp',
  'image/bmp',
  'image/tiff',
  'image/gif'
]

/**
 * 支持的文件扩展名
 */
export const SUPPORTED_EXTENSIONS = [
  '.jpg',
  '.jpeg',
  '.png',
  '.webp',
  '.bmp',
  '.tiff',
  '.gif'
]

/**
 * 最大文件大小 (50MB)
 */
export const MAX_FILE_SIZE = 50 * 1024 * 1024

/**
 * 验证文件是否为支持的图片格式
 */
export function validateImageFile(file: File): { valid: boolean; error?: string } {
  // 检查文件类型
  if (!SUPPORTED_IMAGE_FORMATS.includes(file.type)) {
    return {
      valid: false,
      error: `不支持的文件格式: ${file.type}。支持的格式: ${SUPPORTED_IMAGE_FORMATS.join(', ')}`
    }
  }

  // 检查文件大小
  if (file.size > MAX_FILE_SIZE) {
    return {
      valid: false,
      error: `文件过大: ${(file.size / 1024 / 1024).toFixed(1)}MB。最大支持: ${MAX_FILE_SIZE / 1024 / 1024}MB`
    }
  }

  // 检查文件名
  if (!file.name || file.name.trim() === '') {
    return {
      valid: false,
      error: '文件名无效'
    }
  }

  return { valid: true }
}

/**
 * 从文件路径验证是否为支持的图片格式
 */
export function validateImagePath(filePath: string): { valid: boolean; error?: string } {
  const extension = getFileExtension(filePath).toLowerCase()
  
  if (!SUPPORTED_EXTENSIONS.includes(extension)) {
    return {
      valid: false,
      error: `不支持的文件格式: ${extension}。支持的格式: ${SUPPORTED_EXTENSIONS.join(', ')}`
    }
  }

  return { valid: true }
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filePath: string): string {
  const lastDotIndex = filePath.lastIndexOf('.')
  return lastDotIndex !== -1 ? filePath.substring(lastDotIndex) : ''
}

/**
 * 获取文件名（不含路径）
 */
export function getFileName(filePath: string): string {
  return filePath.split(/[/\\]/).pop() || filePath
}

/**
 * 检测运行环境
 */
export function detectEnvironment(): { isTauri: boolean; hasFileAPI: boolean } {
  const isTauri = (window as any).__TAURI__ !== undefined
  const hasFileAPI = 'showOpenFilePicker' in window
  
  return { isTauri, hasFileAPI }
}

/**
 * 从 File 对象创建 FileItem
 */
export function createFileItemFromFile(file: File, customPath?: string): FileItem {
  return {
    id: crypto.randomUUID(),
    name: file.name,
    path: customPath || (file as any).path || file.name,
    size: file.size,
    type: file.type,
    status: FileStatus.Ready,
    thumbnail: undefined,
    processedPath: undefined,
    error: undefined,
  }
}

/**
 * 从文件路径创建 FileItem（用于 Tauri 环境）
 */
export async function createFileItemFromPath(filePath: string): Promise<FileItem> {
  const fileName = getFileName(filePath)
  const extension = getFileExtension(filePath)

  // 根据扩展名推断 MIME 类型
  const mimeType = getMimeTypeFromExtension(extension)

  // 创建基础 FileItem
  const baseItem: FileItem = {
    id: crypto.randomUUID(),
    name: fileName,
    path: filePath,
    size: 0,
    type: mimeType,
    status: FileStatus.Ready,
    thumbnail: undefined,
    processedPath: undefined,
    error: undefined,
  }

  // 尝试获取文件信息（无论环境检测结果如何）
  try {
    console.log('🔍 正在获取文件信息:', fileName)
    const { tauriAPI } = await import('../lib/tauri')
    const fileInfo = await tauriAPI.file.getFileInfo(filePath)

    console.log('📊 文件信息获取成功:', fileName, fileInfo)
    baseItem.size = fileInfo.size
    if (fileInfo.dimensions) {
      baseItem.dimensions = fileInfo.dimensions
    }
  } catch (error) {
    console.warn('⚠️ 无法获取文件信息 (可能是浏览器环境):', fileName, error)
    // 在浏览器环境中，保持默认值
  }

  return baseItem
}

/**
 * 根据文件扩展名获取 MIME 类型
 */
export function getMimeTypeFromExtension(extension: string): string {
  const mimeMap: Record<string, string> = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.webp': 'image/webp',
    '.bmp': 'image/bmp',
    '.tiff': 'image/tiff',
    '.gif': 'image/gif',
  }
  
  return mimeMap[extension.toLowerCase()] || 'image/jpeg'
}

/**
 * 批量验证文件
 */
export function validateFiles(files: File[]): {
  valid: File[]
  invalid: Array<{ file: File; error: string }>
} {
  const valid: File[] = []
  const invalid: Array<{ file: File; error: string }> = []
  
  for (const file of files) {
    const validation = validateImageFile(file)
    if (validation.valid) {
      valid.push(file)
    } else {
      invalid.push({ file, error: validation.error || '未知错误' })
    }
  }
  
  return { valid, invalid }
}

/**
 * 批量验证文件路径
 */
export function validateFilePaths(filePaths: string[]): {
  valid: string[]
  invalid: Array<{ path: string; error: string }>
} {
  const valid: string[] = []
  const invalid: Array<{ path: string; error: string }> = []
  
  for (const path of filePaths) {
    const validation = validateImagePath(path)
    if (validation.valid) {
      valid.push(path)
    } else {
      invalid.push({ path, error: validation.error || '未知错误' })
    }
  }
  
  return { valid, invalid }
}

/**
 * 统一的文件处理结果
 */
export interface FileProcessingResult {
  fileItems: FileItem[]
  errors: string[]
  warnings: string[]
}

/**
 * 统一处理文件选择结果（无论来源）
 */
export async function processSelectedFiles(
  files: File[],
  filePaths?: string[]
): Promise<FileProcessingResult> {
  const fileItems: FileItem[] = []
  const errors: string[] = []
  const warnings: string[] = []

  // 处理 File 对象
  if (files.length > 0) {
    const { valid, invalid } = validateFiles(files)

    // 添加有效文件
    for (const file of valid) {
      const customPath = filePaths ? filePaths[files.indexOf(file)] : undefined
      fileItems.push(createFileItemFromFile(file, customPath))
    }

    // 记录无效文件
    for (const { file, error } of invalid) {
      errors.push(`${file.name}: ${error}`)
    }
  }

  // 处理文件路径（仅 Tauri 环境）
  if (filePaths && filePaths.length > 0 && files.length === 0) {
    const { valid, invalid } = validateFilePaths(filePaths)

    // 添加有效路径（异步获取文件信息）
    for (const path of valid) {
      try {
        const fileItem = await createFileItemFromPath(path)
        fileItems.push(fileItem)
      } catch (error) {
        errors.push(`${getFileName(path)}: 无法获取文件信息`)
      }
    }

    // 记录无效路径
    for (const { path, error } of invalid) {
      errors.push(`${getFileName(path)}: ${error}`)
    }
  }

  return { fileItems, errors, warnings }
}
