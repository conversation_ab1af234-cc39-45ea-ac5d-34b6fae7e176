import { useState, useCallback } from 'react'
import { MessageItem, MessageType, defaultMessageConfig } from '@/components/ui/message'

let messageId = 0

export interface MessageOptions {
  title?: string
  duration?: number
  closable?: boolean
}

export interface MessageAPI {
  success: (content: string, options?: MessageOptions) => string
  error: (content: string, options?: MessageOptions) => string
  warning: (content: string, options?: MessageOptions) => string
  info: (content: string, options?: MessageOptions) => string
  close: (id: string) => void
  clear: () => void
}

// 全局消息状态
let globalMessages: MessageItem[] = []
let globalSetMessages: ((messages: MessageItem[]) => void) | null = null

export function useMessage(): [MessageItem[], MessageAPI] {
  const [messages, setMessages] = useState<MessageItem[]>(globalMessages)

  // 注册全局状态更新函数
  if (!globalSetMessages) {
    globalSetMessages = (newMessages: MessageItem[]) => {
      globalMessages = newMessages
      setMessages(newMessages)
    }
  }

  const createMessage = useCallback((
    type: MessageType,
    content: string,
    options: MessageOptions = {}
  ): string => {
    const id = `message-${++messageId}`
    const message: MessageItem = {
      id,
      type,
      content,
      title: options.title,
      duration: options.duration ?? defaultMessageConfig.duration,
      closable: options.closable ?? defaultMessageConfig.closable,
    }

    const newMessages = [...globalMessages, message]
    globalSetMessages?.(newMessages)
    
    return id
  }, [])

  const closeMessage = useCallback((id: string) => {
    const newMessages = globalMessages.filter(msg => msg.id !== id)
    globalSetMessages?.(newMessages)
  }, [])

  const clearMessages = useCallback(() => {
    globalSetMessages?.([])
  }, [])

  const messageAPI: MessageAPI = {
    success: (content: string, options?: MessageOptions) => 
      createMessage('success', content, options),
    error: (content: string, options?: MessageOptions) => 
      createMessage('error', content, options),
    warning: (content: string, options?: MessageOptions) => 
      createMessage('warning', content, options),
    info: (content: string, options?: MessageOptions) => 
      createMessage('info', content, options),
    close: closeMessage,
    clear: clearMessages,
  }

  return [messages, messageAPI]
}

// 创建全局消息实例
let globalMessageAPI: MessageAPI | null = null

export function createGlobalMessage(): MessageAPI {
  if (!globalMessageAPI) {
    const createMessage = (
      type: MessageType,
      content: string,
      options: MessageOptions = {}
    ): string => {
      const id = `message-${++messageId}`
      const message: MessageItem = {
        id,
        type,
        content,
        title: options.title,
        duration: options.duration ?? defaultMessageConfig.duration,
        closable: options.closable ?? defaultMessageConfig.closable,
      }

      const newMessages = [...globalMessages, message]
      globalMessages = newMessages
      globalSetMessages?.(newMessages)
      
      return id
    }

    const closeMessage = (id: string) => {
      const newMessages = globalMessages.filter(msg => msg.id !== id)
      globalMessages = newMessages
      globalSetMessages?.(newMessages)
    }

    const clearMessages = () => {
      globalMessages = []
      globalSetMessages?.([])
    }

    globalMessageAPI = {
      success: (content: string, options?: MessageOptions) => 
        createMessage('success', content, options),
      error: (content: string, options?: MessageOptions) => 
        createMessage('error', content, options),
      warning: (content: string, options?: MessageOptions) => 
        createMessage('warning', content, options),
      info: (content: string, options?: MessageOptions) => 
        createMessage('info', content, options),
      close: closeMessage,
      clear: clearMessages,
    }
  }

  return globalMessageAPI
}

// 导出全局消息实例
export const message = createGlobalMessage()
