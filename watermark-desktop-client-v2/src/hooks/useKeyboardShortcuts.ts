/**
 * 键盘快捷键 Hook
 * 处理批量选择相关的键盘快捷键
 */

import { useEffect } from 'react'
import { useBatchSelectionStore } from '../stores/batchSelectionStore'
import { useFileStore } from '../stores/fileStore'

export interface KeyboardShortcutsOptions {
  enabled?: boolean
  onSelectAll?: () => void
  onClearSelection?: () => void
  onToggleSelectionMode?: () => void
}

export const useKeyboardShortcuts = (options: KeyboardShortcutsOptions = {}) => {
  const { enabled = true } = options
  const { files } = useFileStore()
  const { 
    selectAllCompleted, 
    clearSelection, 
    setSelectionMode,
    isSelectionMode 
  } = useBatchSelectionStore()

  useEffect(() => {
    if (!enabled) return

    const handleKeyDown = (event: KeyboardEvent) => {
      // 检查是否在输入框中
      const target = event.target as HTMLElement
      if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
        return
      }

      // Ctrl+A - 全选已完成的文件
      if (event.ctrlKey && event.key === 'a') {
        event.preventDefault()
        
        if (options.onSelectAll) {
          options.onSelectAll()
        } else {
          selectAllCompleted(files)
        }
        
        console.log('⌨️ 键盘快捷键: Ctrl+A 全选')
        return
      }

      // Escape - 清空选择或退出选择模式
      if (event.key === 'Escape') {
        event.preventDefault()
        
        if (options.onClearSelection) {
          options.onClearSelection()
        } else {
          clearSelection()
        }
        
        console.log('⌨️ 键盘快捷键: Escape 清空选择')
        return
      }

      // Ctrl+Shift+S - 切换选择模式
      if (event.ctrlKey && event.shiftKey && event.key === 'S') {
        event.preventDefault()
        
        if (options.onToggleSelectionMode) {
          options.onToggleSelectionMode()
        } else {
          setSelectionMode(!isSelectionMode)
        }
        
        console.log('⌨️ 键盘快捷键: Ctrl+Shift+S 切换选择模式')
        return
      }

      // Delete - 删除选中的文件（可选功能）
      if (event.key === 'Delete' && isSelectionMode) {
        // 这里可以添加删除选中文件的逻辑
        console.log('⌨️ 键盘快捷键: Delete 删除选中文件（未实现）')
        return
      }
    }

    // 添加事件监听器
    document.addEventListener('keydown', handleKeyDown)

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [
    enabled,
    files,
    isSelectionMode,
    selectAllCompleted,
    clearSelection,
    setSelectionMode,
    options
  ])

  // 返回快捷键信息，用于显示帮助
  const shortcuts = [
    { key: 'Ctrl+A', description: '全选已完成的文件' },
    { key: 'Escape', description: '清空选择' },
    { key: 'Ctrl+Shift+S', description: '切换选择模式' },
    { key: 'Delete', description: '删除选中文件（未实现）' }
  ]

  return { shortcuts }
}
