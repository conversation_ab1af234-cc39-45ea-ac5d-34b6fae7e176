/**
 * 路径选择器模拟实现
 * 用于开发环境和测试，提供基本的路径选择功能
 */

export interface PathSelectorOptions {
  title?: string
  defaultPath?: string
  multiple?: boolean
}

export class PathSelectorMock {
  /**
   * 选择文件夹 - 模拟实现
   */
  async selectFolder(options: PathSelectorOptions = {}): Promise<string | null> {
    try {
      // 在开发环境中，返回一个模拟的路径
      if (typeof window !== 'undefined') {
        // 浏览器环境 - 使用 HTML5 File API 的替代方案
        const defaultPath = options.defaultPath || await this.getDefaultDownloadPath()
        
        // 模拟用户选择路径的对话框
        const userPath = prompt(`选择保存文件夹 (当前默认: ${defaultPath})`, defaultPath)
        
        if (userPath && userPath.trim()) {
          console.log(`📁 用户选择路径: ${userPath}`)
          return userPath.trim()
        }
      }

      return null
    } catch (error) {
      console.error('文件夹选择失败:', error)
      throw new Error('无法打开文件夹选择器')
    }
  }

  /**
   * 获取默认下载路径 - 模拟实现
   */
  async getDefaultDownloadPath(): Promise<string> {
    try {
      // 在不同环境中返回合适的默认路径
      if (typeof window !== 'undefined') {
        // 浏览器环境
        return '/Downloads' // 模拟路径
      }
      
      // Node.js 环境
      const os = await import('os')
      const path = await import('path')
      return path.join(os.homedir(), 'Downloads')
    } catch (error) {
      console.error('获取默认路径失败:', error)
      return '/Downloads' // 备用路径
    }
  }

  /**
   * 获取推荐的保存路径列表 - 模拟实现
   */
  async getRecommendedPaths(): Promise<{ name: string; path: string }[]> {
    const paths: { name: string; path: string }[] = []

    try {
      // 模拟常用路径
      paths.push(
        { name: '下载', path: '/Downloads' },
        { name: '桌面', path: '/Desktop' },
        { name: '文档', path: '/Documents' },
        { name: '图片', path: '/Pictures' }
      )
    } catch (error) {
      console.error('获取推荐路径失败:', error)
    }

    return paths
  }

  /**
   * 验证路径是否有效 - 模拟实现
   */
  async validatePath(path: string): Promise<{ valid: boolean; error?: string }> {
    try {
      if (!path) {
        return { valid: false, error: '路径不能为空' }
      }

      // 简单的路径格式验证
      if (path.length < 2) {
        return { valid: false, error: '路径格式无效' }
      }

      // 在模拟环境中，假设所有路径都有效
      return { valid: true }
    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : '路径验证失败' 
      }
    }
  }

  /**
   * 格式化路径显示
   */
  formatPathForDisplay(path: string, maxLength = 50): string {
    if (!path) return ''
    
    if (path.length <= maxLength) {
      return path
    }

    // 显示开头和结尾，中间用省略号
    const start = path.substring(0, Math.floor(maxLength / 2) - 2)
    const end = path.substring(path.length - Math.floor(maxLength / 2) + 2)
    
    return `${start}...${end}`
  }

  /**
   * 获取路径的父目录
   */
  getParentPath(path: string): string {
    const parts = path.split(/[/\\]/)
    parts.pop()
    return parts.join('/')
  }

  /**
   * 获取路径的最后一级目录名
   */
  getDirectoryName(path: string): string {
    const parts = path.split(/[/\\]/)
    return parts[parts.length - 1] || path
  }
}

// 创建单例实例
export const pathSelectorMock = new PathSelectorMock()
