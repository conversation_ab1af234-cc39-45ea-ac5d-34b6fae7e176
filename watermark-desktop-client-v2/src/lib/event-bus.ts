/**
 * 事件总线系统
 * 提供应用内组件间的事件通信机制
 */

export type EventHandler<T = any> = (data: T) => void

export interface EventSubscription {
  unsubscribe: () => void
}

export class EventBus {
  private events: Map<string, Set<EventHandler>> = new Map()
  private onceEvents: Map<string, Set<EventHandler>> = new Map()

  /**
   * 订阅事件
   */
  on<T = any>(event: string, handler: EventHandler<T>): EventSubscription {
    if (!this.events.has(event)) {
      this.events.set(event, new Set())
    }
    
    this.events.get(event)!.add(handler)

    return {
      unsubscribe: () => this.off(event, handler)
    }
  }

  /**
   * 订阅事件（只触发一次）
   */
  once<T = any>(event: string, handler: EventHandler<T>): EventSubscription {
    if (!this.onceEvents.has(event)) {
      this.onceEvents.set(event, new Set())
    }
    
    this.onceEvents.get(event)!.add(handler)

    return {
      unsubscribe: () => this.offOnce(event, handler)
    }
  }

  /**
   * 取消订阅事件
   */
  off<T = any>(event: string, handler?: EventHandler<T>): void {
    if (!this.events.has(event)) return

    if (handler) {
      this.events.get(event)!.delete(handler)
    } else {
      this.events.get(event)!.clear()
    }

    // 如果没有监听器了，删除事件
    if (this.events.get(event)!.size === 0) {
      this.events.delete(event)
    }
  }

  /**
   * 取消订阅一次性事件
   */
  offOnce<T = any>(event: string, handler?: EventHandler<T>): void {
    if (!this.onceEvents.has(event)) return

    if (handler) {
      this.onceEvents.get(event)!.delete(handler)
    } else {
      this.onceEvents.get(event)!.clear()
    }

    // 如果没有监听器了，删除事件
    if (this.onceEvents.get(event)!.size === 0) {
      this.onceEvents.delete(event)
    }
  }

  /**
   * 发布事件
   */
  emit<T = any>(event: string, data?: T): void {
    // 触发普通事件监听器
    const handlers = this.events.get(event)
    if (handlers) {
      handlers.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error)
        }
      })
    }

    // 触发一次性事件监听器
    const onceHandlers = this.onceEvents.get(event)
    if (onceHandlers) {
      const handlersArray = Array.from(onceHandlers)
      onceHandlers.clear()
      this.onceEvents.delete(event)

      handlersArray.forEach(handler => {
        try {
          handler(data)
        } catch (error) {
          console.error(`Error in once event handler for ${event}:`, error)
        }
      })
    }
  }

  /**
   * 清除所有事件监听器
   */
  clear(): void {
    this.events.clear()
    this.onceEvents.clear()
  }

  /**
   * 获取事件监听器数量
   */
  listenerCount(event: string): number {
    const normalCount = this.events.get(event)?.size || 0
    const onceCount = this.onceEvents.get(event)?.size || 0
    return normalCount + onceCount
  }

  /**
   * 获取所有事件名称
   */
  eventNames(): string[] {
    const normalEvents = Array.from(this.events.keys())
    const onceEvents = Array.from(this.onceEvents.keys())
    return [...new Set([...normalEvents, ...onceEvents])]
  }

  /**
   * 检查是否有指定事件的监听器
   */
  hasListeners(event: string): boolean {
    return this.listenerCount(event) > 0
  }
}

// 应用事件类型定义
export interface AppEvents {
  // 文件相关事件
  'file:added': { files: File[] }
  'file:removed': { fileId: string }
  'file:selected': { fileId: string }
  'file:cleared': void
  'file:upload:started': { files: File[] }
  'file:upload:completed': { files: File[]; result: any }
  'file:upload:failed': { files: File[]; error: any }
  'file:download:completed': { filePath: string }
  'file:download:failed': { filePath: string; error: any }
  'file:deleted': { filePath: string }
  'file:cleanup:completed': void

  // 处理相关事件
  'processing:started': { taskId: string }
  'processing:progress': { taskId: string; progress: number }
  'processing:completed': { taskId: string; result: any }
  'processing:failed': { taskId: string; error: string }
  'processing:cancelled': { taskId: string }

  // 设置相关事件
  'settings:changed': { section: string; settings: any }
  'theme:changed': { theme: string }

  // 配置相关事件
  'config:saved': { config: any }
  'config:updated': { section: string; updates: any }
  'config:reset': void
  'config:section:reset': { section: string }
  'config:imported': { config: any }

  // 日志相关事件
  'log:entry': any
  'log:cleared': void
  'log:config:updated': { config: any }

  // 通知相关事件
  'notification:show': { type: string; title: string; message: string }
  'notification:hide': { id: string }

  // 连接相关事件
  'connection:established': void
  'connection:lost': void
  'connection:restored': void

  // 错误相关事件
  'error:occurred': { error: Error; context?: string }
  'error:resolved': { errorId: string }

  // 服务相关事件
  'services:initialized': undefined
  'services:shutdown': undefined
  'service:started': { serviceName: string }
  'service:stopped': { serviceName: string }
  'service:initialized': { serviceName: string }
  'service:error': { serviceName: string; error: string }

  // AI 模型相关事件
  'model:load:requested': { modelType: string; options?: any }
  'model:unload:requested': { modelType: string }
  'model:loaded': { modelType: string; model: any }
  'model:unloaded': { modelType: string; model: any }
  'model:status:changed': { modelType: string; model: any }

  // 水印检测相关事件
  'detection:start:requested': { files: string[]; options?: any }
  'detection:cancel:requested': { taskId: string }
  'detection:completed': { imagePath: string; result: any; processingTime: number }
  'detection:batch:started': { taskId: string; progress: any }
  'detection:batch:completed': { taskId: string; progress: any }
  'detection:batch:failed': { taskId: string; error: any }
  'detection:cancelled': { taskId: string }
  'detection:progress:updated': { taskId: string; progress: any }

  // 图像修复相关事件
  'inpainting:start:requested': { tasks: any[]; options?: any }
  'inpainting:cancel:requested': { taskId: string }
  'inpainting:completed': { imagePath: string; result: any; processingTime: number }
  'inpainting:batch:started': { taskId: string; progress: any }
  'inpainting:batch:completed': { taskId: string; progress: any }
  'inpainting:batch:failed': { taskId: string; error: any }
  'inpainting:cancelled': { taskId: string }
  'inpainting:progress:updated': { taskId: string; progress: any }

  // AI 工作流相关事件
  'workflow:start:requested': { files: string[]; options?: any }
  'workflow:cancel:requested': { workflowId: string }
  'workflow:started': { workflowId: string; progress: any }
  'workflow:completed': { workflowId: string; result: any }
  'workflow:failed': { workflowId: string; error: any }
  'workflow:cancelled': { workflowId: string }
  'workflow:progress:updated': { workflowId: string; progress: any }

  // 批量处理相关事件
  'batch:task:add': { task: any }
  'batch:task:cancel': { taskId: string }
  'batch:task:pause': { taskId: string }
  'batch:task:resume': { taskId: string }
  'batch:queue:clear': void
  'batch:limits:update': { limits: any }
  'batch:task:added': { task: any }
  'batch:queue:updated': { queue: any }
  'batch:task:cancelled': { taskId: string; task: any }
  'batch:task:paused': { taskId: string; task: any }
  'batch:task:resumed': { taskId: string; task: any }
  'batch:queue:cleared': { cancelledTasks: any[] }
  'batch:limits:updated': { limits: any }
  'batch:task:started': { task: any }
  'batch:task:completed': { task: any }
  'batch:task:failed': { task: any }
  'batch:task:retry': { task: any }
  'batch:stats:updated': { stats: any }

  // WebSocket 相关事件
  'websocket:message': any
  'websocket:connected': { clientId: string }
  'websocket:status:changed': { status: string; oldStatus: string; clientId?: string }
  'websocket:group:joined': { group: string }
  'websocket:group:left': { group: string }
  'websocket:status': { connections: number; groups: number; clientId: string }
  'websocket:error': { error: string }
  'processing:update': { taskId: string; update: any }
  'system:notification': { notification: any }
  'system:error': { error: any }
}

// 类型安全的事件总线
export class TypedEventBus<T extends Record<string, any> = AppEvents> {
  private eventBus = new EventBus()

  on<K extends keyof T>(event: K, handler: EventHandler<T[K]>): EventSubscription {
    return this.eventBus.on(event as string, handler)
  }

  once<K extends keyof T>(event: K, handler: EventHandler<T[K]>): EventSubscription {
    return this.eventBus.once(event as string, handler)
  }

  off<K extends keyof T>(event: K, handler?: EventHandler<T[K]>): void {
    this.eventBus.off(event as string, handler)
  }

  emit<K extends keyof T>(event: K, data: T[K]): void {
    this.eventBus.emit(event as string, data)
  }

  clear(): void {
    this.eventBus.clear()
  }

  listenerCount<K extends keyof T>(event: K): number {
    return this.eventBus.listenerCount(event as string)
  }

  eventNames(): (keyof T)[] {
    return this.eventBus.eventNames() as (keyof T)[]
  }

  hasListeners<K extends keyof T>(event: K): boolean {
    return this.eventBus.hasListeners(event as string)
  }
}

// 创建全局事件总线实例
export const eventBus = new TypedEventBus<AppEvents>()

export default eventBus
