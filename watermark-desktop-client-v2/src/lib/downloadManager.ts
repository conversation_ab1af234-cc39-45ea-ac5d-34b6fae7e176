/**
 * 批量下载管理器
 * 处理文件的批量下载、进度跟踪、错误处理等功能
 */

import { invoke } from '@tauri-apps/api/core'
import { join, basename } from '@tauri-apps/api/path'
import { exists, createDir, copyFile } from '@tauri-apps/plugin-fs'
import { open } from '@tauri-apps/plugin-opener'
import { FileItem } from '../types'
import { useBatchSelectionStore } from '../stores/batchSelectionStore'

export interface DownloadOptions {
  targetPath: string
  overwrite?: boolean
  openFolderAfterComplete?: boolean
}

export interface DownloadResult {
  success: boolean
  downloadedFiles: string[]
  failedFiles: { file: FileItem; error: string }[]
  totalFiles: number
}

export class DownloadManager {
  private abortController: AbortController | null = null

  /**
   * 批量下载文件
   */
  async downloadFiles(
    files: FileItem[],
    options: DownloadOptions
  ): Promise<DownloadResult> {
    const { updateFileProgress, setFileError } = useBatchSelectionStore.getState()
    
    this.abortController = new AbortController()
    const downloadedFiles: string[] = []
    const failedFiles: { file: FileItem; error: string }[] = []

    console.log(`🚀 开始批量下载 ${files.length} 个文件到: ${options.targetPath}`)

    try {
      // 确保目标目录存在
      await this.ensureDirectoryExists(options.targetPath)

      // 并发下载文件（限制并发数为3）
      const concurrency = 3
      const chunks = this.chunkArray(files, concurrency)

      for (const chunk of chunks) {
        if (this.abortController.signal.aborted) {
          break
        }

        await Promise.all(
          chunk.map(async (file) => {
            try {
              if (this.abortController?.signal.aborted) {
                return
              }

              updateFileProgress(file.id, 0, 'downloading')
              
              const downloadedPath = await this.downloadSingleFile(
                file,
                options.targetPath,
                options.overwrite,
                (progress) => {
                  updateFileProgress(file.id, progress, 'downloading')
                }
              )

              updateFileProgress(file.id, 100, 'completed')
              downloadedFiles.push(downloadedPath)
              
              console.log(`✅ 文件下载完成: ${file.name} -> ${downloadedPath}`)
              
            } catch (error) {
              const errorMessage = error instanceof Error ? error.message : '下载失败'
              console.error(`❌ 文件下载失败: ${file.name}`, error)
              
              setFileError(file.id, errorMessage)
              failedFiles.push({ file, error: errorMessage })
            }
          })
        )
      }

      // 下载完成后打开文件夹
      if (options.openFolderAfterComplete && downloadedFiles.length > 0) {
        await this.openFolder(options.targetPath)
      }

      const result: DownloadResult = {
        success: failedFiles.length === 0,
        downloadedFiles,
        failedFiles,
        totalFiles: files.length
      }

      console.log(`📊 批量下载完成: 成功 ${downloadedFiles.length}/${files.length} 个文件`)
      
      return result

    } catch (error) {
      console.error('批量下载过程中发生错误:', error)
      throw error
    }
  }

  /**
   * 下载单个文件
   */
  private async downloadSingleFile(
    file: FileItem,
    targetDir: string,
    overwrite = false,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    if (!file.processedPath) {
      throw new Error('文件没有处理结果路径')
    }

    // 生成目标文件路径
    const fileName = this.generateFileName(file.name, targetDir, overwrite)
    const targetPath = await join(targetDir, fileName)

    // 检查文件是否已存在
    if (!overwrite && await exists(targetPath)) {
      throw new Error('文件已存在')
    }

    try {
      // 模拟进度更新
      onProgress?.(10)

      // 获取源文件的完整路径
      const sourcePath = await this.resolveSourcePath(file.processedPath)
      
      onProgress?.(30)

      // 检查源文件是否存在
      if (!await exists(sourcePath)) {
        throw new Error('源文件不存在')
      }

      onProgress?.(50)

      // 复制文件
      await copyFile(sourcePath, targetPath)
      
      onProgress?.(90)

      // 验证复制是否成功
      if (!await exists(targetPath)) {
        throw new Error('文件复制失败')
      }

      onProgress?.(100)

      return targetPath

    } catch (error) {
      console.error(`文件复制失败: ${file.name}`, error)
      throw error
    }
  }

  /**
   * 解析源文件路径
   */
  private async resolveSourcePath(processedPath: string): Promise<string> {
    // 如果是相对路径，需要转换为绝对路径
    if (!processedPath.startsWith('/') && !processedPath.match(/^[A-Za-z]:/)) {
      // 假设处理后的文件在后端的输出目录中
      // 这里需要根据实际的后端配置来调整
      const backendOutputDir = await invoke<string>('get_output_directory')
      return await join(backendOutputDir, processedPath)
    }
    
    return processedPath
  }

  /**
   * 生成唯一的文件名
   */
  private generateFileName(originalName: string, targetDir: string, overwrite: boolean): string {
    if (overwrite) {
      return originalName
    }

    // 如果不覆盖，生成唯一文件名
    const nameWithoutExt = originalName.replace(/\.[^/.]+$/, '')
    const ext = originalName.match(/\.[^/.]+$/)?.[0] || ''
    
    // 添加时间戳确保唯一性
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, -5)
    return `${nameWithoutExt}_${timestamp}${ext}`
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      if (!await exists(dirPath)) {
        await createDir(dirPath, { recursive: true })
        console.log(`📁 创建目录: ${dirPath}`)
      }
    } catch (error) {
      console.error('创建目录失败:', error)
      throw new Error(`无法创建目录: ${dirPath}`)
    }
  }

  /**
   * 打开文件夹
   */
  private async openFolder(folderPath: string): Promise<void> {
    try {
      await open(folderPath)
      console.log(`📂 打开文件夹: ${folderPath}`)
    } catch (error) {
      console.warn('打开文件夹失败:', error)
      // 不抛出错误，因为这不是关键功能
    }
  }

  /**
   * 将数组分块
   */
  private chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize))
    }
    return chunks
  }

  /**
   * 取消下载
   */
  cancel(): void {
    if (this.abortController) {
      this.abortController.abort()
      console.log('❌ 批量下载已取消')
    }
  }

  /**
   * 检查是否正在下载
   */
  isDownloading(): boolean {
    return this.abortController !== null && !this.abortController.signal.aborted
  }
}

// 创建单例实例
export const downloadManager = new DownloadManager()
