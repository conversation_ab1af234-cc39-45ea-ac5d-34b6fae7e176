/**
 * WebSocket 客户端
 * 提供实时通信功能
 */

import { eventBus } from './event-bus'
import { logService } from '../services/LogService'

export enum WebSocketStatus {
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  DISCONNECTED = 'disconnected',
  ERROR = 'error'
}

export interface WebSocketMessage {
  type: string
  [key: string]: any
}

export interface WebSocketConfig {
  url: string
  reconnectInterval: number
  maxReconnectAttempts: number
  heartbeatInterval: number
  clientId?: string
}

export class WebSocketClient {
  private ws: WebSocket | null = null
  private config: WebSocketConfig
  private status: WebSocketStatus = WebSocketStatus.DISCONNECTED
  private reconnectAttempts = 0
  private heartbeatTimer?: NodeJS.Timeout
  private reconnectTimer?: NodeJS.Timeout
  private messageQueue: WebSocketMessage[] = []

  constructor(config: Partial<WebSocketConfig> = {}) {
    this.config = {
      url: 'ws://127.0.0.1:8000/api/v1/ws',
      reconnectInterval: 5000,  // 增加重连间隔到5秒
      maxReconnectAttempts: 10, // 增加最大重连次数到10次
      heartbeatInterval: 30000,
      clientId: this.generateClientId(),
      ...config
    }
  }

  /**
   * 连接 WebSocket
   */
  connect(): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      logService.warn('WebSocket is already connected', 'websocket')
      return
    }

    this.setStatus(WebSocketStatus.CONNECTING)
    
    const wsUrl = `${this.config.url}/${this.config.clientId}`
    logService.info(`Connecting to WebSocket: ${wsUrl}`, 'websocket')

    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventListeners()
    } catch (error) {
      logService.error('Failed to create WebSocket connection', 'websocket', {}, error as Error)
      this.setStatus(WebSocketStatus.ERROR)
      this.scheduleReconnect()
    }
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.clearTimers()
    this.reconnectAttempts = 0
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
    
    this.setStatus(WebSocketStatus.DISCONNECTED)
    logService.info('WebSocket disconnected', 'websocket')
  }

  /**
   * 发送消息
   */
  send(message: WebSocketMessage): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message))
        logService.debug('WebSocket message sent', 'websocket', { message })
      } catch (error) {
        logService.error('Failed to send WebSocket message', 'websocket', { message }, error as Error)
      }
    } else {
      // 如果连接未建立，将消息加入队列
      this.messageQueue.push(message)
      logService.warn('WebSocket not connected, message queued', 'websocket', { message })
      
      // 尝试重新连接
      if (this.status === WebSocketStatus.DISCONNECTED) {
        this.connect()
      }
    }
  }

  /**
   * 获取连接状态
   */
  getStatus(): WebSocketStatus {
    return this.status
  }

  /**
   * 获取客户端ID
   */
  getClientId(): string {
    return this.config.clientId!
  }

  /**
   * 加入组
   */
  joinGroup(groupName: string): void {
    this.send({
      type: 'join_group',
      group: groupName
    })
  }

  /**
   * 离开组
   */
  leaveGroup(groupName: string): void {
    this.send({
      type: 'leave_group',
      group: groupName
    })
  }

  /**
   * 获取状态信息
   */
  requestStatus(): void {
    this.send({
      type: 'get_status'
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    if (!this.ws) return

    this.ws.onopen = () => {
      logService.info('WebSocket connected successfully', 'websocket')
      this.setStatus(WebSocketStatus.CONNECTED)
      this.reconnectAttempts = 0
      this.startHeartbeat()
      this.processMessageQueue()
    }

    this.ws.onclose = (event) => {
      logService.info('WebSocket connection closed', 'websocket', {
        code: event.code,
        reason: event.reason,
        wasClean: event.wasClean
      })
      
      this.clearTimers()
      this.setStatus(WebSocketStatus.DISCONNECTED)
      
      // 如果不是正常关闭，尝试重连
      if (event.code !== 1000) {
        this.scheduleReconnect()
      }
    }

    this.ws.onerror = (error) => {
      logService.error('WebSocket error occurred', 'websocket', {}, error as any)
      this.setStatus(WebSocketStatus.ERROR)
    }

    this.ws.onmessage = (event) => {
      try {
        const message: WebSocketMessage = JSON.parse(event.data)
        this.handleMessage(message)
      } catch (error) {
        logService.error('Failed to parse WebSocket message', 'websocket', { data: event.data }, error as Error)
      }
    }
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(message: WebSocketMessage): void {
    logService.debug('WebSocket message received', 'websocket', { message })

    // 发布到事件总线 - 直接传递消息内容
    eventBus.emit('websocket:message', message)

    // 处理特定类型的消息
    switch (message.type) {
      case 'connection_established':
        eventBus.emit('websocket:connected', { clientId: message.client_id })
        break

      case 'pong':
        // 心跳响应
        break

      case 'processing_update':
        eventBus.emit('processing:update', {
          taskId: message.task_id,
          update: message
        })
        break

      case 'system_notification':
        eventBus.emit('system:notification', { notification: message })
        break

      case 'error_notification':
        eventBus.emit('system:error', { error: message })
        break

      case 'group_joined':
        eventBus.emit('websocket:group:joined', { group: message.group })
        break

      case 'group_left':
        eventBus.emit('websocket:group:left', { group: message.group })
        break

      case 'status':
        eventBus.emit('websocket:status', {
          connections: message.connections,
          groups: message.groups,
          clientId: message.client_id
        })
        break

      case 'error':
        logService.error('WebSocket server error', 'websocket', { message: message.message })
        eventBus.emit('websocket:error', { error: message.message })
        break

      default:
        logService.warn('Unknown WebSocket message type', 'websocket', { type: message.type, message })
    }
  }

  /**
   * 设置连接状态
   */
  private setStatus(status: WebSocketStatus): void {
    if (this.status !== status) {
      const oldStatus = this.status
      this.status = status
      
      logService.info('WebSocket status changed', 'websocket', {
        from: oldStatus,
        to: status
      })
      
      eventBus.emit('websocket:status:changed', {
        status,
        oldStatus,
        clientId: this.config.clientId
      })
    }
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.clearHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.send({
          type: 'ping',
          timestamp: Date.now()
        })
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 清除心跳定时器
   */
  private clearHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = undefined
    }
  }

  /**
   * 安排重连
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.maxReconnectAttempts) {
      logService.error('Max reconnect attempts reached', 'websocket', {
        attempts: this.reconnectAttempts,
        maxAttempts: this.config.maxReconnectAttempts
      })
      return
    }

    this.reconnectAttempts++
    
    logService.info('Scheduling WebSocket reconnect', 'websocket', {
      attempt: this.reconnectAttempts,
      maxAttempts: this.config.maxReconnectAttempts,
      delay: this.config.reconnectInterval
    })

    this.reconnectTimer = setTimeout(() => {
      this.connect()
    }, this.config.reconnectInterval)
  }

  /**
   * 清除定时器
   */
  private clearTimers(): void {
    this.clearHeartbeat()
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = undefined
    }
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      if (message) {
        this.send(message)
      }
    }
  }

  /**
   * 生成客户端ID
   */
  private generateClientId(): string {
    return `client-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

// 创建全局 WebSocket 客户端实例
export const wsClient = new WebSocketClient()

// 自动连接
wsClient.connect()

export default wsClient
