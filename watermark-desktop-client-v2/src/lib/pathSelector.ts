/**
 * 路径选择器工具
 * 使用 Tauri API 进行文件夹选择
 */

import { open } from '@tauri-apps/plugin-dialog'
import { homeDir, downloadDir, documentDir } from '@tauri-apps/api/path'
import { exists } from '@tauri-apps/plugin-fs'

export interface PathSelectorOptions {
  title?: string
  defaultPath?: string
  multiple?: boolean
}

export class PathSelector {
  /**
   * 选择文件夹
   */
  async selectFolder(options: PathSelectorOptions = {}): Promise<string | null> {
    try {
      const defaultPath = options.defaultPath || await this.getDefaultDownloadPath()
      
      const selected = await open({
        directory: true,
        multiple: false,
        title: options.title || '选择保存文件夹',
        defaultPath
      })

      if (typeof selected === 'string') {
        console.log(`📁 用户选择路径: ${selected}`)
        return selected
      }

      return null
    } catch (error) {
      console.error('文件夹选择失败:', error)
      throw new Error('无法打开文件夹选择器')
    }
  }

  /**
   * 获取默认下载路径
   */
  async getDefaultDownloadPath(): Promise<string> {
    try {
      // 优先使用下载文件夹
      const downloadPath = await downloadDir()
      if (downloadPath && await exists(downloadPath)) {
        return downloadPath
      }

      // 备选：文档文件夹
      const documentsPath = await documentDir()
      if (documentsPath && await exists(documentsPath)) {
        return documentsPath
      }

      // 最后备选：用户主目录
      const homePath = await homeDir()
      if (homePath) {
        return homePath
      }

      throw new Error('无法确定默认路径')
    } catch (error) {
      console.error('获取默认路径失败:', error)
      throw error
    }
  }

  /**
   * 获取推荐的保存路径列表
   */
  async getRecommendedPaths(): Promise<{ name: string; path: string }[]> {
    const paths: { name: string; path: string }[] = []

    try {
      // 下载文件夹
      const downloadPath = await downloadDir()
      if (downloadPath && await exists(downloadPath)) {
        paths.push({ name: '下载', path: downloadPath })
      }

      // 文档文件夹
      const documentsPath = await documentDir()
      if (documentsPath && await exists(documentsPath)) {
        paths.push({ name: '文档', path: documentsPath })
      }

      // 桌面文件夹
      const homePath = await homeDir()
      if (homePath) {
        const desktopPath = `${homePath}/Desktop`
        if (await exists(desktopPath)) {
          paths.push({ name: '桌面', path: desktopPath })
        }
      }

      // 用户主目录
      if (homePath) {
        paths.push({ name: '主目录', path: homePath })
      }

    } catch (error) {
      console.error('获取推荐路径失败:', error)
    }

    return paths
  }

  /**
   * 验证路径是否有效
   */
  async validatePath(path: string): Promise<{ valid: boolean; error?: string }> {
    try {
      if (!path) {
        return { valid: false, error: '路径不能为空' }
      }

      const pathExists = await exists(path)
      if (!pathExists) {
        return { valid: false, error: '路径不存在' }
      }

      // 这里可以添加更多验证逻辑，比如检查写入权限等
      
      return { valid: true }
    } catch (error) {
      return { 
        valid: false, 
        error: error instanceof Error ? error.message : '路径验证失败' 
      }
    }
  }

  /**
   * 格式化路径显示
   */
  formatPathForDisplay(path: string, maxLength = 50): string {
    if (!path) return ''
    
    if (path.length <= maxLength) {
      return path
    }

    // 显示开头和结尾，中间用省略号
    const start = path.substring(0, Math.floor(maxLength / 2) - 2)
    const end = path.substring(path.length - Math.floor(maxLength / 2) + 2)
    
    return `${start}...${end}`
  }

  /**
   * 获取路径的父目录
   */
  getParentPath(path: string): string {
    const parts = path.split(/[/\\]/)
    parts.pop()
    return parts.join('/')
  }

  /**
   * 获取路径的最后一级目录名
   */
  getDirectoryName(path: string): string {
    const parts = path.split(/[/\\]/)
    return parts[parts.length - 1] || path
  }
}

// 创建单例实例
export const pathSelector = new PathSelector()
