/**
 * HTTP 客户端服务
 * 提供统一的 HTTP 请求接口，支持重试、超时、错误处理等功能
 */

export interface RequestConfig {
  timeout?: number
  retries?: number
  retryDelay?: number
  headers?: Record<string, string>
}

export interface RequestOptions extends RequestInit {
  timeout?: number
  retries?: number
  retryDelay?: number
}

export class HTTPError extends Error {
  constructor(
    message: string,
    public status?: number,
    public response?: Response
  ) {
    super(message)
    this.name = 'HTTPError'
  }
}

export class HTTPClient {
  private baseURL: string
  private defaultConfig: RequestConfig

  constructor(baseURL: string, config: RequestConfig = {}) {
    this.baseURL = baseURL.replace(/\/$/, '')
    this.defaultConfig = {
      timeout: 30000,
      retries: 3,
      retryDelay: 1000,
      headers: {
        'Content-Type': 'application/json',
      },
      ...config,
    }
  }

  private async delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  private async fetchWithTimeout(
    url: string,
    options: RequestInit,
    timeout: number
  ): Promise<Response> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal,
      })
      clearTimeout(timeoutId)
      return response
    } catch (error) {
      clearTimeout(timeoutId)
      throw error
    }
  }

  private async makeRequest(
    endpoint: string,
    options: RequestOptions = {}
  ): Promise<Response> {
    const url = `${this.baseURL}${endpoint}`
    const config = { ...this.defaultConfig, ...options }

    // 处理 headers：如果 options.headers 已经存在，直接使用它
    // 这样可以避免重新合并默认头部，保持调用方的头部设置
    let finalHeaders: Record<string, string>

    if (options.headers) {
      // 如果调用方已经提供了 headers，直接使用（调用方已经处理了合并逻辑）
      finalHeaders = { ...options.headers }
    } else {
      // 如果没有提供 headers，使用默认头部
      finalHeaders = { ...this.defaultConfig.headers }
    }

    // 过滤掉 undefined 和 null 值的 headers
    const filteredHeaders: Record<string, string> = {}
    Object.entries(finalHeaders).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        filteredHeaders[key] = value as string
      }
    })

    const requestOptions: RequestInit = {
      ...options,
      headers: filteredHeaders,
    }

    let lastError: Error | null = null
    const maxRetries = config.retries || 0

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const response = await this.fetchWithTimeout(
          url,
          requestOptions,
          config.timeout || 30000
        )

        if (!response.ok) {
          throw new HTTPError(
            `HTTP ${response.status}: ${response.statusText}`,
            response.status,
            response
          )
        }

        return response
      } catch (error) {
        lastError = error as Error
        
        // 如果是最后一次尝试，或者是非网络错误，直接抛出
        if (attempt === maxRetries || !(error instanceof TypeError)) {
          break
        }

        // 等待后重试
        if (config.retryDelay && attempt < maxRetries) {
          await this.delay(config.retryDelay * (attempt + 1))
        }
      }
    }

    throw lastError
  }

  async get<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const response = await this.makeRequest(endpoint, {
      ...options,
      method: 'GET',
    })
    return response.json()
  }

  async post<T = any>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<T> {
    // 处理不同类型的数据
    let body: any
    let headers: Record<string, string> = {}

    if (data instanceof FormData) {
      // FormData：让浏览器自动设置 Content-Type
      body = data
      // 对于 FormData，不设置任何 Content-Type 头部
      // 浏览器会自动设置为 multipart/form-data 并包含正确的 boundary

      // 只合并非 Content-Type 的头部
      Object.entries(this.defaultConfig.headers || {}).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'content-type' && value !== undefined) {
          headers[key] = value as string
        }
      })

      // 合并 options.headers，但排除 Content-Type
      if (options.headers) {
        Object.entries(options.headers).forEach(([key, value]) => {
          if (key.toLowerCase() !== 'content-type' && value !== undefined) {
            headers[key] = value as string
          }
        })
      }
    } else {
      // 非 FormData：使用默认头部
      headers = { ...this.defaultConfig.headers }

      // 合并 options.headers，过滤掉 undefined 值
      if (options.headers) {
        Object.entries(options.headers).forEach(([key, value]) => {
          if (value !== undefined) {
            headers[key] = value as string
          }
        })
      }

      if (data) {
        // JSON 数据
        body = JSON.stringify(data)
        headers['Content-Type'] = 'application/json'
      } else {
        body = undefined
      }
    }

    const response = await this.makeRequest(endpoint, {
      ...options,
      method: 'POST',
      headers,
      body,
    })
    return response.json()
  }

  async put<T = any>(
    endpoint: string,
    data?: any,
    options: RequestOptions = {}
  ): Promise<T> {
    // 处理不同类型的数据
    let body: any
    let headers: Record<string, string> = {}

    if (data instanceof FormData) {
      // FormData：让浏览器自动设置 Content-Type
      body = data
      // 对于 FormData，不设置任何 Content-Type 头部
      // 浏览器会自动设置为 multipart/form-data 并包含正确的 boundary

      // 只合并非 Content-Type 的头部
      Object.entries(this.defaultConfig.headers || {}).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'content-type' && value !== undefined) {
          headers[key] = value as string
        }
      })

      // 合并 options.headers，但排除 Content-Type
      if (options.headers) {
        Object.entries(options.headers).forEach(([key, value]) => {
          if (key.toLowerCase() !== 'content-type' && value !== undefined) {
            headers[key] = value as string
          }
        })
      }
    } else {
      // 非 FormData：使用默认头部
      headers = { ...this.defaultConfig.headers }

      // 合并 options.headers，过滤掉 undefined 值
      if (options.headers) {
        Object.entries(options.headers).forEach(([key, value]) => {
          if (value !== undefined) {
            headers[key] = value as string
          }
        })
      }

      if (data) {
        // JSON 数据
        body = JSON.stringify(data)
        headers['Content-Type'] = 'application/json'
      } else {
        body = undefined
      }
    }

    const response = await this.makeRequest(endpoint, {
      ...options,
      method: 'PUT',
      headers,
      body,
    })
    return response.json()
  }

  async delete<T = any>(endpoint: string, options: RequestOptions = {}): Promise<T> {
    const response = await this.makeRequest(endpoint, {
      ...options,
      method: 'DELETE',
    })
    return response.json()
  }

  async upload<T = any>(
    endpoint: string,
    formData: FormData,
    options: RequestOptions = {}
  ): Promise<T> {
    const { headers, ...restOptions } = options

    // 构建头部，排除 Content-Type
    const uploadHeaders: Record<string, string> = {}

    // 合并非 Content-Type 的默认头部
    Object.entries(this.defaultConfig.headers || {}).forEach(([key, value]) => {
      if (key.toLowerCase() !== 'content-type' && value !== undefined) {
        uploadHeaders[key] = value as string
      }
    })

    // 合并 options.headers，但排除 Content-Type
    if (headers) {
      Object.entries(headers).forEach(([key, value]) => {
        if (key.toLowerCase() !== 'content-type' && value !== undefined) {
          uploadHeaders[key] = value as string
        }
      })
    }

    const response = await this.makeRequest(endpoint, {
      ...restOptions,
      method: 'POST',
      body: formData,
      headers: uploadHeaders,
    })
    return response.json()
  }

  async download(endpoint: string, options: RequestOptions = {}): Promise<Blob> {
    const response = await this.makeRequest(endpoint, {
      ...options,
      method: 'GET',
    })
    return response.blob()
  }

  // 健康检查
  async healthCheck(): Promise<boolean> {
    try {
      await this.get('/health', { timeout: 5000, retries: 1 })
      return true
    } catch {
      return false
    }
  }

  // 设置默认头部
  setDefaultHeader(key: string, value: string): void {
    this.defaultConfig.headers = {
      ...this.defaultConfig.headers,
      [key]: value,
    }
  }

  // 移除默认头部
  removeDefaultHeader(key: string): void {
    if (this.defaultConfig.headers) {
      delete this.defaultConfig.headers[key]
    }
  }
}

// 创建默认的 HTTP 客户端实例
export const httpClient = new HTTPClient('http://127.0.0.1:8000/api/v1', {
  timeout: 30000,
  retries: 3,
  retryDelay: 1000,
})

export default httpClient
