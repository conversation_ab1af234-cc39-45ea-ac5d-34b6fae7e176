/**
 * Tauri API 封装模块
 * 提供与 Tauri 后端通信的统一接口
 */

import { invoke } from '@tauri-apps/api/core'
import { listen } from '@tauri-apps/api/event'
import { ProcessingSettings, ProcessingTask } from '../types'

// 文件操作相关 API
export const fileAPI = {
  /**
   * 选择文件
   */
  async selectFiles(): Promise<string[]> {
    try {
      return await invoke('select_files')
    } catch (error) {
      console.error('Failed to select files:', error)
      return []
    }
  },

  /**
   * 保存文件对话框
   */
  async saveFileDialog(defaultName: string): Promise<string | null> {
    try {
      return await invoke('save_file_dialog', { defaultName })
    } catch (error) {
      console.error('Failed to open save dialog:', error)
      return null
    }
  },

  /**
   * 保存文件到指定路径
   */
  async saveFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      await invoke('save_file_to_path', { sourcePath, destinationPath })
    } catch (error) {
      console.error('Failed to save file:', error)
      throw error
    }
  },

  /**
   * 获取文件信息（大小、尺寸等）
   */
  async getFileInfo(filePath: string): Promise<{
    size: number
    dimensions?: { width: number; height: number }
  }> {
    try {
      const result = await invoke('get_file_info', { filePath })
      return result as any
    } catch (error) {
      console.error('Failed to get file info:', error)
      throw error
    }
  }
}

// 处理任务相关 API
export const processingAPI = {
  /**
   * 开始处理任务
   */
  async startProcessing(files: string[], settings: ProcessingSettings): Promise<string> {
    try {
      return await invoke('start_processing', { files, settings })
    } catch (error) {
      console.error('Failed to start processing:', error)
      throw error
    }
  },

  /**
   * 获取处理状态
   */
  async getProcessingStatus(taskId: string): Promise<ProcessingTask | null> {
    try {
      return await invoke('get_processing_status', { taskId })
    } catch (error) {
      console.error('Failed to get processing status:', error)
      return null
    }
  },

  /**
   * 取消处理任务
   */
  async cancelProcessing(taskId: string): Promise<boolean> {
    try {
      return await invoke('cancel_processing', { taskId })
    } catch (error) {
      console.error('Failed to cancel processing:', error)
      return false
    }
  }
}

// 应用信息相关 API
export const appAPI = {
  /**
   * 获取应用版本
   */
  async getVersion(): Promise<string> {
    try {
      return await invoke('get_app_version')
    } catch (error) {
      console.error('Failed to get app version:', error)
      return 'Unknown'
    }
  }
}

// 事件监听器类型
export type EventCallback<T = any> = (payload: T) => void

// 事件监听相关 API
export const eventAPI = {
  /**
   * 监听处理开始事件
   */
  async onProcessingStarted(callback: EventCallback<string>) {
    return await listen('processing_started', (event) => {
      callback(event.payload as string)
    })
  },

  /**
   * 监听处理进度事件
   */
  async onProcessingProgress(callback: EventCallback<{ task_id: string; progress: number }>) {
    return await listen('processing_progress', (event) => {
      callback(event.payload as { task_id: string; progress: number })
    })
  },

  /**
   * 监听处理完成事件
   */
  async onProcessingCompleted(callback: EventCallback<{ task_id: string; result: any }>) {
    return await listen('processing_completed', (event) => {
      callback(event.payload as { task_id: string; result: any })
    })
  },

  /**
   * 监听处理失败事件
   */
  async onProcessingFailed(callback: EventCallback<{ task_id: string; error: string }>) {
    return await listen('processing_failed', (event) => {
      callback(event.payload as { task_id: string; error: string })
    })
  }
}

// 统一的 Tauri API 导出
export const tauriAPI = {
  file: fileAPI,
  processing: processingAPI,
  app: appAPI,
  event: eventAPI
}

export default tauriAPI
