/**
 * Python 后端 API 客户端
 * 提供与 FastAPI 后端通信的接口
 */

import { ProcessingSettings, ProcessingResult } from '../types'
import { httpClient, HTTPError } from './http-client'
import { eventBus } from './event-bus'

// 重新导出 HTTPError 作为 APIError
export class APIError extends HTTPError {
  constructor(message: string, status?: number, response?: Response) {
    super(message, status, response)
    this.name = 'APIError'
  }
}

// 健康检查 API
export const healthAPI = {
  /**
   * 检查服务健康状态
   */
  async check() {
    try {
      const result = await httpClient.get<{
        status: string
        version: string
        ai_service_status: string
        models_status: Record<string, boolean>
        system_info: Record<string, any>
      }>('/health')

      eventBus.emit('connection:established', undefined)
      return result
    } catch (error) {
      eventBus.emit('connection:lost', undefined)
      throw new APIError(
        error instanceof Error ? error.message : 'Health check failed'
      )
    }
  },

  /**
   * 检查模型状态
   */
  async checkModels() {
    return httpClient.get<{
      yolo: { path: string; exists: boolean; status: string }
      lama: { path: string; exists: boolean; status: string }
    }>('/models/status')
  }
}

// 文件管理 API
export const fileAPI = {
  /**
   * 上传单个文件
   */
  async uploadFile(file: File) {
    const formData = new FormData()
    formData.append('files', file) // 注意：后端期望的字段名是 'files'

    try {
      const result = await httpClient.post<{
        message: string
        files: Array<{
          name: string
          path: string
          size: number
          type: string
        }>
      }>('/files/upload', formData, {
        headers: {
          // 让浏览器自动设置 Content-Type 为 multipart/form-data
        }
      })

      return result
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('File upload failed'),
        context: 'file upload'
      })
      throw error
    }
  },

  /**
   * 上传多个文件
   */
  async upload(files: File[]) {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    try {
      const result = await httpClient.upload<{
        message: string
        files: Array<{
          name: string
          path: string
          size: number
          type: string
        }>
      }>('/files/upload', formData)

      eventBus.emit('file:added', { files })
      return result
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Upload failed'),
        context: 'file upload'
      })
      throw error
    }
  },

  /**
   * 获取文件信息
   */
  async getInfo(filePath: string) {
    return httpClient.get<{
      name: string
      path: string
      size: number
      type: string
      exists: boolean
    }>(`/files/info?file_path=${encodeURIComponent(filePath)}`)
  },

  /**
   * 下载文件
   */
  async download(filePath: string): Promise<Blob> {
    return httpClient.download(`/files/download?file_path=${encodeURIComponent(filePath)}`)
  },

  /**
   * 删除文件
   */
  async delete(filePath: string) {
    const result = await httpClient.delete<{ message: string }>('/files/delete', {
      body: JSON.stringify({ file_path: filePath })
    })

    eventBus.emit('file:removed', { fileId: filePath })
    return result
  },

  /**
   * 清理临时文件
   */
  async cleanup() {
    return httpClient.post<{ message: string }>('/files/cleanup')
  }
}

// 图像处理 API
export const processingAPI = {
  /**
   * 开始处理图像
   */
  async start(files: string[], settings: ProcessingSettings) {
    try {
      // 对于多文件处理，增加超时时间
      const timeout = Math.max(60000, files.length * 30000) // 至少60秒，每个文件额外30秒

      const result = await httpClient.post<{
        task_id: string
        status: string
        message: string
      }>('/processing/start', { files, settings }, { timeout })

      eventBus.emit('processing:started', { taskId: result.task_id })
      return result
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Processing start failed'),
        context: 'processing start'
      })
      throw error
    }
  },

  /**
   * 获取处理状态
   */
  async getStatus(taskId: string) {
    const result = await httpClient.get<{
      task_id: string
      status: string
      progress: number
      result?: ProcessingResult
      error?: string
    }>(`/processing/status/${taskId}`)

    // 发布进度事件
    if (result.progress !== undefined) {
      eventBus.emit('processing:progress', { taskId, progress: result.progress })
    }

    // 发布完成或失败事件
    if (result.status === 'completed' && result.result) {
      eventBus.emit('processing:completed', { taskId, result: result.result })
    } else if (result.status === 'failed' && result.error) {
      eventBus.emit('processing:failed', { taskId, error: result.error })
    }

    return result
  },

  /**
   * 取消处理任务
   */
  async cancel(taskId: string) {
    const result = await httpClient.post<{ message: string }>(`/processing/cancel/${taskId}`)
    eventBus.emit('processing:cancelled', { taskId })
    return result
  }
}

// 统一的 API 客户端导出
export const apiClient = {
  health: healthAPI,
  file: fileAPI,
  processing: processingAPI
}

export default apiClient

// APIError 已在上面导出，无需重复导出
