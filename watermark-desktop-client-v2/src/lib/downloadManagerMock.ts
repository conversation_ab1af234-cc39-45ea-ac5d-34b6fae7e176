/**
 * 下载管理器模拟实现
 * 用于开发环境和测试，提供基本的下载功能模拟
 */

import { FileItem } from '../types'
import { useBatchSelectionStore } from '../stores/batchSelectionStore'

export interface DownloadOptions {
  targetPath: string
  overwrite?: boolean
  openFolderAfterComplete?: boolean
}

export interface DownloadResult {
  success: boolean
  downloadedFiles: string[]
  failedFiles: { file: FileItem; error: string }[]
  totalFiles: number
}

export class DownloadManagerMock {
  private abortController: AbortController | null = null

  /**
   * 批量下载文件 - 模拟实现
   */
  async downloadFiles(
    files: FileItem[],
    options: DownloadOptions
  ): Promise<DownloadResult> {
    const { updateFileProgress, setFileError } = useBatchSelectionStore.getState()
    
    this.abortController = new AbortController()
    const downloadedFiles: string[] = []
    const failedFiles: { file: FileItem; error: string }[] = []

    console.log(`🚀 模拟批量下载 ${files.length} 个文件到: ${options.targetPath}`)

    try {
      // 模拟下载过程
      for (const file of files) {
        if (this.abortController.signal.aborted) {
          break
        }

        try {
          updateFileProgress(file.id, 0, 'downloading')
          
          // 模拟下载进度
          await this.simulateDownload(file, options.targetPath, (progress) => {
            updateFileProgress(file.id, progress, 'downloading')
          })

          updateFileProgress(file.id, 100, 'completed')
          
          const downloadedPath = `${options.targetPath}/${file.name}`
          downloadedFiles.push(downloadedPath)
          
          console.log(`✅ 模拟文件下载完成: ${file.name} -> ${downloadedPath}`)
          
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : '下载失败'
          console.error(`❌ 模拟文件下载失败: ${file.name}`, error)
          
          setFileError(file.id, errorMessage)
          failedFiles.push({ file, error: errorMessage })
        }
      }

      // 模拟打开文件夹
      if (options.openFolderAfterComplete && downloadedFiles.length > 0) {
        await this.openFolder(options.targetPath)
      }

      const result: DownloadResult = {
        success: failedFiles.length === 0,
        downloadedFiles,
        failedFiles,
        totalFiles: files.length
      }

      console.log(`📊 模拟批量下载完成: 成功 ${downloadedFiles.length}/${files.length} 个文件`)
      
      return result

    } catch (error) {
      console.error('模拟批量下载过程中发生错误:', error)
      throw error
    }
  }

  /**
   * 模拟单个文件下载
   */
  private async simulateDownload(
    file: FileItem,
    targetDir: string,
    onProgress?: (progress: number) => void
  ): Promise<string> {
    if (!file.processedPath) {
      throw new Error('文件没有处理结果路径')
    }

    // 模拟下载进度
    const progressSteps = [10, 30, 50, 70, 90, 100]
    
    for (const progress of progressSteps) {
      if (this.abortController?.signal.aborted) {
        throw new Error('下载已取消')
      }

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300))
      
      onProgress?.(progress)
    }

    // 模拟生成目标文件路径
    const targetPath = `${targetDir}/${file.name}`

    // 模拟随机失败（10% 概率）
    if (Math.random() < 0.1) {
      throw new Error('模拟网络错误')
    }

    return targetPath
  }

  /**
   * 模拟打开文件夹
   */
  private async openFolder(folderPath: string): Promise<void> {
    try {
      console.log(`📂 模拟打开文件夹: ${folderPath}`)
      
      // 在浏览器环境中，可以显示一个提示
      if (typeof window !== 'undefined') {
        // 可以在这里添加一个 toast 提示或者模态框
        console.log(`文件已保存到: ${folderPath}`)
      }
    } catch (error) {
      console.warn('模拟打开文件夹失败:', error)
      // 不抛出错误，因为这不是关键功能
    }
  }

  /**
   * 取消下载
   */
  cancel(): void {
    if (this.abortController) {
      this.abortController.abort()
      console.log('❌ 模拟批量下载已取消')
    }
  }

  /**
   * 检查是否正在下载
   */
  isDownloading(): boolean {
    return this.abortController !== null && !this.abortController.signal.aborted
  }
}

// 创建单例实例
export const downloadManagerMock = new DownloadManagerMock()
