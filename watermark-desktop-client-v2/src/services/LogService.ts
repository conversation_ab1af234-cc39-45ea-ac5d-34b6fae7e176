/**
 * 日志服务系统
 * 提供结构化日志记录、分级管理、持久化等功能
 */

import { eventBus } from '../lib/event-bus'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  id: string
  timestamp: number
  level: LogLevel
  message: string
  context?: string
  data?: any
  stack?: string
  userId?: string
  sessionId?: string
}

export interface LogFilter {
  level?: LogLevel
  context?: string
  startTime?: number
  endTime?: number
  limit?: number
}

export interface LogServiceConfig {
  level: LogLevel
  maxEntries: number
  enableConsole: boolean
  enablePersistence: boolean
  enableRemoteLogging: boolean
  contexts: string[]
}

export class LogService {
  private logs: LogEntry[] = []
  private config: LogServiceConfig
  private sessionId: string
  private readonly storageKey = 'watermark-app-logs'

  constructor(config: Partial<LogServiceConfig> = {}) {
    this.config = {
      level: LogLevel.INFO,
      maxEntries: 1000,
      enableConsole: true,
      enablePersistence: true,
      enableRemoteLogging: false,
      contexts: [],
      ...config
    }

    this.sessionId = this.generateSessionId()
    this.loadPersistedLogs()
    this.setupEventListeners()
  }

  /**
   * 初始化日志服务
   */
  async initialize(): Promise<void> {
    try {
      // 清理旧日志（如果日志过多）
      if (this.logs.length > this.config.maxEntries) {
        this.logs = this.logs.slice(-this.config.maxEntries)
      }

      this.info('LogService initialized', 'log_service')
      eventBus.emit('service:initialized', { serviceName: 'log' })
    } catch (error) {
      console.error('Failed to initialize LogService:', error)
      eventBus.emit('service:error', {
        serviceName: 'log',
        error: error instanceof Error ? error.message : 'Initialization failed'
      })
      throw error
    }
  }

  /**
   * 记录调试日志
   */
  debug(message: string, context?: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, context, data)
  }

  /**
   * 记录信息日志
   */
  info(message: string, context?: string, data?: any): void {
    this.log(LogLevel.INFO, message, context, data)
  }

  /**
   * 记录警告日志
   */
  warn(message: string, context?: string, data?: any): void {
    this.log(LogLevel.WARN, message, context, data)
  }

  /**
   * 记录错误日志
   */
  error(message: string, context?: string, data?: any, error?: Error): void {
    this.log(LogLevel.ERROR, message, context, data, error?.stack)
  }

  /**
   * 记录致命错误日志
   */
  fatal(message: string, context?: string, data?: any, error?: Error): void {
    this.log(LogLevel.FATAL, message, context, data, error?.stack)
  }

  /**
   * 核心日志记录方法
   */
  private log(level: LogLevel, message: string, context?: string, data?: any, stack?: string): void {
    // 检查日志级别
    if (level < this.config.level) {
      return
    }

    // 检查上下文过滤
    if (this.config.contexts.length > 0 && context && !this.config.contexts.includes(context)) {
      return
    }

    const entry: LogEntry = {
      id: this.generateLogId(),
      timestamp: Date.now(),
      level,
      message,
      context,
      data,
      stack,
      sessionId: this.sessionId
    }

    // 添加到内存日志
    this.logs.push(entry)

    // 限制日志数量
    if (this.logs.length > this.config.maxEntries) {
      this.logs = this.logs.slice(-this.config.maxEntries)
    }

    // 控制台输出
    if (this.config.enableConsole) {
      this.logToConsole(entry)
    }

    // 持久化
    if (this.config.enablePersistence) {
      this.persistLogs()
    }

    // 远程日志
    if (this.config.enableRemoteLogging) {
      this.sendToRemote(entry)
    }

    // 发布日志事件
    eventBus.emit('log:entry', entry)
  }

  /**
   * 输出到控制台
   */
  private logToConsole(entry: LogEntry): void {
    const timestamp = new Date(entry.timestamp).toISOString()
    const levelName = LogLevel[entry.level]
    const prefix = `[${timestamp}] [${levelName}]${entry.context ? ` [${entry.context}]` : ''}`
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(prefix, entry.message, entry.data)
        break
      case LogLevel.INFO:
        console.info(prefix, entry.message, entry.data)
        break
      case LogLevel.WARN:
        console.warn(prefix, entry.message, entry.data)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(prefix, entry.message, entry.data)
        if (entry.stack) {
          console.error('Stack trace:', entry.stack)
        }
        break
    }
  }

  /**
   * 持久化日志
   */
  private persistLogs(): void {
    try {
      // 只保存最近的日志到 localStorage
      const recentLogs = this.logs.slice(-100)
      localStorage.setItem(this.storageKey, JSON.stringify(recentLogs))
    } catch (error) {
      console.error('Failed to persist logs:', error)
    }
  }

  /**
   * 加载持久化的日志
   */
  private loadPersistedLogs(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const persistedLogs = JSON.parse(stored) as LogEntry[]
        this.logs = persistedLogs
      }
    } catch (error) {
      console.error('Failed to load persisted logs:', error)
    }
  }

  /**
   * 发送到远程日志服务
   */
  private sendToRemote(_entry: LogEntry): void {
    // TODO: 实现远程日志发送
    // 可以发送到 Sentry、LogRocket 等服务
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    // 监听应用错误
    eventBus.on('error:occurred', ({ error, context }) => {
      this.error(error.message, context || 'application', { error: error.name }, error)
    })

    // 监听连接状态变化
    eventBus.on('connection:lost', () => {
      this.warn('Connection lost', 'network')
    })

    eventBus.on('connection:established', () => {
      this.info('Connection established', 'network')
    })

    // 监听处理事件
    eventBus.on('processing:started', ({ taskId }) => {
      this.info('Processing started', 'processing', { taskId })
    })

    eventBus.on('processing:completed', ({ taskId }) => {
      this.info('Processing completed', 'processing', { taskId })
    })

    eventBus.on('processing:failed', ({ taskId, error }) => {
      this.error('Processing failed', 'processing', { taskId, error })
    })
  }

  /**
   * 获取日志
   */
  getLogs(filter?: LogFilter): LogEntry[] {
    let filteredLogs = [...this.logs]

    if (filter) {
      if (filter.level !== undefined) {
        filteredLogs = filteredLogs.filter(log => log.level >= filter.level!)
      }
      
      if (filter.context) {
        filteredLogs = filteredLogs.filter(log => log.context === filter.context)
      }
      
      if (filter.startTime) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.startTime!)
      }
      
      if (filter.endTime) {
        filteredLogs = filteredLogs.filter(log => log.timestamp <= filter.endTime!)
      }
      
      if (filter.limit) {
        filteredLogs = filteredLogs.slice(-filter.limit)
      }
    }

    return filteredLogs
  }

  /**
   * 清除日志
   */
  clearLogs(): void {
    this.logs = []
    localStorage.removeItem(this.storageKey)
    eventBus.emit('log:cleared', undefined)
  }

  /**
   * 导出日志
   */
  exportLogs(format: 'json' | 'csv' = 'json'): string {
    if (format === 'json') {
      return JSON.stringify(this.logs, null, 2)
    } else {
      // CSV 格式
      const headers = ['timestamp', 'level', 'message', 'context', 'sessionId']
      const rows = this.logs.map(log => [
        new Date(log.timestamp).toISOString(),
        LogLevel[log.level],
        log.message,
        log.context || '',
        log.sessionId
      ])
      
      return [headers, ...rows].map(row => row.join(',')).join('\n')
    }
  }

  /**
   * 更新配置
   */
  updateConfig(updates: Partial<LogServiceConfig>): void {
    this.config = { ...this.config, ...updates }
    eventBus.emit('log:config:updated', { config: this.config })
  }

  /**
   * 获取配置
   */
  getConfig(): LogServiceConfig {
    return { ...this.config }
  }

  /**
   * 获取统计信息
   */
  getStats(): { total: number, byLevel: Record<string, number>, byContext: Record<string, number> } {
    const byLevel: Record<string, number> = {}
    const byContext: Record<string, number> = {}

    this.logs.forEach(log => {
      const levelName = LogLevel[log.level]
      byLevel[levelName] = (byLevel[levelName] || 0) + 1
      
      const context = log.context || 'unknown'
      byContext[context] = (byContext[context] || 0) + 1
    })

    return {
      total: this.logs.length,
      byLevel,
      byContext
    }
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

// 创建全局日志服务实例
export const logService = new LogService({
  level: LogLevel.INFO,
  enableConsole: true,
  enablePersistence: true
})

export default logService
