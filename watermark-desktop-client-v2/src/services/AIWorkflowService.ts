/**
 * AI 处理工作流服务
 * 统一协调水印检测和图像修复的完整工作流
 */

import { eventBus } from '../lib/event-bus'
import { logService } from './LogService'
import { watermarkDetectionService, DetectionResult } from './WatermarkDetectionService'
import { imageInpaintingService, InpaintingResult, InpaintingTask } from './ImageInpaintingService'

export enum WorkflowStep {
  DETECTION = 'detection',
  INPAINTING = 'inpainting',
  COMPLETED = 'completed'
}

export enum WorkflowStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface WorkflowOptions {
  // 检测选项
  detectionOptions?: {
    confidenceThreshold?: number
    nmsThreshold?: number
    maxDetections?: number
  }
  
  // 修复选项
  inpaintingOptions?: {
    maskExpansion?: number
    contextExpansionRatio?: number
    enhanceMask?: boolean
    useSmartEnhancement?: boolean
    outputFormat?: 'same' | 'jpg' | 'png'
    quality?: number
  }

  // 工作流选项
  skipDetection?: boolean
  skipInpainting?: boolean
  deviceType?: 'cpu' | 'gpu' | 'auto'
  batchSize?: number
}

export interface WorkflowProgress {
  workflowId: string
  status: WorkflowStatus
  currentStep: WorkflowStep
  totalFiles: number
  processedFiles: number
  currentFile: string
  progress: number
  
  // 步骤进度
  detectionProgress: number
  inpaintingProgress: number
  
  // 结果
  detectionResults: DetectionResult[]
  inpaintingResults: InpaintingResult[]
  
  // 统计
  filesWithWatermarksDetected: number
  watermarksRemoved: number
  
  // 错误
  errors: Array<{ file: string; step: WorkflowStep; error: string }>
  
  // 时间
  startTime: number
  estimatedTimeRemaining?: number
}

export interface WorkflowResult {
  workflowId: string
  status: WorkflowStatus
  totalFiles: number
  successfulFiles: number
  failedFiles: number
  detectionResults: DetectionResult[]
  inpaintingResults: InpaintingResult[]
  processingTime: number
  errors: Array<{ file: string; step: WorkflowStep; error: string }>
}

export class AIWorkflowService {
  private activeWorkflows: Map<string, WorkflowProgress> = new Map()
  private defaultOptions: WorkflowOptions = {
    detectionOptions: {
      confidenceThreshold: 0.5,
      nmsThreshold: 0.4,
      maxDetections: 100
    },
    inpaintingOptions: {
      maskExpansion: 10,
      contextExpansionRatio: 0.1,
      enhanceMask: true,
      useSmartEnhancement: true,
      outputFormat: 'same',
      quality: 95
    },
    skipDetection: false,
    skipInpainting: false,
    deviceType: 'auto',
    batchSize: 2
  }

  constructor() {
    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    eventBus.on('workflow:start:requested', ({ files, options }) => {
      this.startWorkflow(files, options)
    })

    eventBus.on('workflow:cancel:requested', ({ workflowId }) => {
      this.cancelWorkflow(workflowId)
    })

    // 监听检测和修复事件
    eventBus.on('detection:batch:completed', ({ taskId, progress }) => {
      this.handleDetectionCompleted(taskId, progress)
    })

    eventBus.on('inpainting:batch:completed', ({ taskId, progress }) => {
      this.handleInpaintingCompleted(taskId, progress)
    })
  }

  /**
   * 开始完整的工作流
   */
  async startWorkflow(
    imagePaths: string[],
    options: WorkflowOptions = {}
  ): Promise<string> {
    const workflowId = this.generateWorkflowId()
    const finalOptions = { ...this.defaultOptions, ...options }

    // 初始化工作流进度
    const progress: WorkflowProgress = {
      workflowId,
      status: WorkflowStatus.PENDING,
      currentStep: finalOptions.skipDetection ? WorkflowStep.INPAINTING : WorkflowStep.DETECTION,
      totalFiles: imagePaths.length,
      processedFiles: 0,
      currentFile: '',
      progress: 0,
      detectionProgress: 0,
      inpaintingProgress: 0,
      detectionResults: [],
      inpaintingResults: [],
      filesWithWatermarksDetected: 0,
      watermarksRemoved: 0,
      errors: [],
      startTime: Date.now()
    }

    this.activeWorkflows.set(workflowId, progress)

    logService.info(
      `Starting AI workflow for ${imagePaths.length} images`,
      'ai_workflow',
      { workflowId, options: finalOptions }
    )

    // 发布工作流开始事件
    eventBus.emit('workflow:started', { workflowId, progress })

    // 异步执行工作流
    this.executeWorkflow(workflowId, imagePaths, finalOptions)

    return workflowId
  }

  /**
   * 执行工作流
   */
  private async executeWorkflow(
    workflowId: string,
    imagePaths: string[],
    options: WorkflowOptions
  ): Promise<void> {
    const progress = this.activeWorkflows.get(workflowId)
    if (!progress) return

    try {
      progress.status = WorkflowStatus.RUNNING
      this.updateProgress(workflowId)

      // 步骤1: 水印检测
      if (!options.skipDetection) {
        await this.executeDetectionStep(workflowId, imagePaths, options)
        
        // 检查是否被取消
        if (!this.activeWorkflows.has(workflowId)) return
      }

      // 步骤2: 图像修复
      if (!options.skipInpainting) {
        await this.executeInpaintingStep(workflowId, options)
        
        // 检查是否被取消
        if (!this.activeWorkflows.has(workflowId)) return
      }

      // 完成工作流
      progress.status = WorkflowStatus.COMPLETED
      progress.currentStep = WorkflowStep.COMPLETED
      progress.progress = 100
      this.updateProgress(workflowId)

      const result = this.createWorkflowResult(workflowId)
      
      logService.info(
        `AI workflow completed for ${workflowId}`,
        'ai_workflow',
        {
          totalFiles: result.totalFiles,
          successfulFiles: result.successfulFiles,
          failedFiles: result.failedFiles,
          processingTime: result.processingTime
        }
      )

      eventBus.emit('workflow:completed', { workflowId, result })

    } catch (error) {
      progress.status = WorkflowStatus.FAILED
      this.updateProgress(workflowId)

      logService.error(
        `AI workflow failed for ${workflowId}`,
        'ai_workflow',
        { workflowId },
        error instanceof Error ? error : new Error(String(error))
      )

      eventBus.emit('workflow:failed', { workflowId, error })

    } finally {
      // 清理工作流
      setTimeout(() => {
        this.activeWorkflows.delete(workflowId)
      }, 300000) // 5分钟后清理
    }
  }

  /**
   * 执行检测步骤
   */
  private async executeDetectionStep(
    workflowId: string,
    imagePaths: string[],
    options: WorkflowOptions
  ): Promise<void> {
    const progress = this.activeWorkflows.get(workflowId)
    if (!progress) return

    progress.currentStep = WorkflowStep.DETECTION
    this.updateProgress(workflowId)

    logService.info(`Starting detection step for workflow ${workflowId}`, 'ai_workflow')

    // 启动批量检测
    const detectionTaskId = await watermarkDetectionService.detectWatermarksInBatch(
      imagePaths,
      {
        ...options.detectionOptions,
        deviceType: options.deviceType,
        batchSize: options.batchSize
      }
    )

    // 存储检测任务ID以便后续处理
    progress.currentFile = `Detection Task: ${detectionTaskId}`
    this.updateProgress(workflowId)

    // 等待检测完成
    return new Promise((resolve, reject) => {
      const checkDetection = () => {
        const detectionProgress = watermarkDetectionService.getDetectionProgress(detectionTaskId)
        if (!detectionProgress) {
          reject(new Error('Detection task not found'))
          return
        }

        // 更新检测进度
        progress.detectionProgress = detectionProgress.progress
        progress.currentFile = detectionProgress.currentFile
        this.updateProgress(workflowId)

        if (detectionProgress.progress >= 100) {
          // 检测完成，保存结果
          progress.detectionResults = detectionProgress.results
          progress.filesWithWatermarksDetected = detectionProgress.results.filter(r => r.has_watermark).length
          
          // 添加错误
          detectionProgress.errors.forEach(error => {
            progress.errors.push({
              file: error.file,
              step: WorkflowStep.DETECTION,
              error: error.error
            })
          })

          resolve()
        } else {
          setTimeout(checkDetection, 1000) // 每秒检查一次
        }
      }

      checkDetection()
    })
  }

  /**
   * 执行修复步骤
   */
  private async executeInpaintingStep(
    workflowId: string,
    options: WorkflowOptions
  ): Promise<void> {
    const progress = this.activeWorkflows.get(workflowId)
    if (!progress) return

    progress.currentStep = WorkflowStep.INPAINTING
    this.updateProgress(workflowId)

    logService.info(`Starting inpainting step for workflow ${workflowId}`, 'ai_workflow')

    // 创建修复任务
    const inpaintingTasks: InpaintingTask[] = imageInpaintingService.createTasksFromDetection(
      progress.detectionResults
    )

    if (inpaintingTasks.length === 0) {
      logService.info(`No watermarks detected, skipping inpainting for workflow ${workflowId}`, 'ai_workflow')
      return
    }

    // 启动批量修复
    const inpaintingTaskId = await imageInpaintingService.inpaintImagesInBatch(
      inpaintingTasks,
      {
        ...options.inpaintingOptions,
        deviceType: options.deviceType,
        batchSize: options.batchSize
      }
    )

    // 存储修复任务ID
    progress.currentFile = `Inpainting Task: ${inpaintingTaskId}`
    this.updateProgress(workflowId)

    // 等待修复完成
    return new Promise((resolve, reject) => {
      const checkInpainting = () => {
        const inpaintingProgress = imageInpaintingService.getInpaintingProgress(inpaintingTaskId)
        if (!inpaintingProgress) {
          reject(new Error('Inpainting task not found'))
          return
        }

        // 更新修复进度
        progress.inpaintingProgress = inpaintingProgress.progress
        progress.currentFile = inpaintingProgress.currentFile
        this.updateProgress(workflowId)

        if (inpaintingProgress.progress >= 100) {
          // 修复完成，保存结果
          progress.inpaintingResults = inpaintingProgress.results
          progress.watermarksRemoved = inpaintingProgress.results.reduce(
            (sum, result) => sum + result.watermarks_removed, 0
          )
          
          // 添加错误
          inpaintingProgress.errors.forEach(error => {
            progress.errors.push({
              file: error.file,
              step: WorkflowStep.INPAINTING,
              error: error.error
            })
          })

          resolve()
        } else {
          setTimeout(checkInpainting, 1000) // 每秒检查一次
        }
      }

      checkInpainting()
    })
  }

  /**
   * 处理检测完成事件
   */
  private handleDetectionCompleted(taskId: string, _detectionProgress: any): void {
    // 这里可以添加额外的检测完成处理逻辑
    logService.info(`Detection task ${taskId} completed`, 'ai_workflow')
  }

  /**
   * 处理修复完成事件
   */
  private handleInpaintingCompleted(taskId: string, _inpaintingProgress: any): void {
    // 这里可以添加额外的修复完成处理逻辑
    logService.info(`Inpainting task ${taskId} completed`, 'ai_workflow')
  }

  /**
   * 取消工作流
   */
  async cancelWorkflow(workflowId: string): Promise<void> {
    const progress = this.activeWorkflows.get(workflowId)
    if (!progress) {
      logService.warn(`Workflow ${workflowId} not found`, 'ai_workflow')
      return
    }

    try {
      progress.status = WorkflowStatus.CANCELLED
      this.updateProgress(workflowId)

      // 取消相关的检测和修复任务
      // 这里需要实现具体的取消逻辑

      this.activeWorkflows.delete(workflowId)

      logService.info(`Workflow ${workflowId} cancelled`, 'ai_workflow')
      eventBus.emit('workflow:cancelled', { workflowId })

    } catch (error) {
      logService.error(
        `Failed to cancel workflow ${workflowId}`,
        'ai_workflow',
        { workflowId },
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * 获取工作流进度
   */
  getWorkflowProgress(workflowId: string): WorkflowProgress | undefined {
    return this.activeWorkflows.get(workflowId)
  }

  /**
   * 获取所有活动工作流
   */
  getActiveWorkflows(): WorkflowProgress[] {
    return Array.from(this.activeWorkflows.values())
  }

  /**
   * 更新工作流进度
   */
  private updateProgress(workflowId: string): void {
    const progress = this.activeWorkflows.get(workflowId)
    if (!progress) return

    // 计算总体进度
    if (progress.currentStep === WorkflowStep.DETECTION) {
      progress.progress = Math.round(progress.detectionProgress * 0.5)
    } else if (progress.currentStep === WorkflowStep.INPAINTING) {
      progress.progress = Math.round(50 + progress.inpaintingProgress * 0.5)
    }

    // 估算剩余时间
    if (progress.progress > 0) {
      const elapsedTime = Date.now() - progress.startTime
      const estimatedTotalTime = (elapsedTime / progress.progress) * 100
      progress.estimatedTimeRemaining = Math.round(estimatedTotalTime - elapsedTime)
    }

    // 发布进度更新事件
    eventBus.emit('workflow:progress:updated', { workflowId, progress })
  }

  /**
   * 创建工作流结果
   */
  private createWorkflowResult(workflowId: string): WorkflowResult {
    const progress = this.activeWorkflows.get(workflowId)!
    
    return {
      workflowId,
      status: progress.status,
      totalFiles: progress.totalFiles,
      successfulFiles: progress.inpaintingResults.length,
      failedFiles: progress.errors.length,
      detectionResults: progress.detectionResults,
      inpaintingResults: progress.inpaintingResults,
      processingTime: Date.now() - progress.startTime,
      errors: progress.errors
    }
  }

  /**
   * 生成工作流ID
   */
  private generateWorkflowId(): string {
    return `workflow-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取工作流统计信息
   */
  getStats(): {
    activeWorkflows: number
    totalWorkflows: number
    averageProcessingTime: number
    successRate: number
    totalWatermarksRemoved: number
  } {
    const workflows = Array.from(this.activeWorkflows.values())
    const completedWorkflows = workflows.filter(w => w.status === WorkflowStatus.COMPLETED)
    
    let totalProcessingTime = 0
    let totalWatermarksRemoved = 0
    
    completedWorkflows.forEach(workflow => {
      totalProcessingTime += Date.now() - workflow.startTime
      totalWatermarksRemoved += workflow.watermarksRemoved
    })

    return {
      activeWorkflows: workflows.filter(w => w.status === WorkflowStatus.RUNNING).length,
      totalWorkflows: workflows.length,
      averageProcessingTime: completedWorkflows.length > 0 ? totalProcessingTime / completedWorkflows.length : 0,
      successRate: workflows.length > 0 ? (completedWorkflows.length / workflows.length) * 100 : 0,
      totalWatermarksRemoved
    }
  }

  /**
   * 更新默认选项
   */
  updateDefaultOptions(options: Partial<WorkflowOptions>): void {
    this.defaultOptions = { ...this.defaultOptions, ...options }
    logService.info('Workflow default options updated', 'ai_workflow', this.defaultOptions)
  }

  /**
   * 获取默认选项
   */
  getDefaultOptions(): WorkflowOptions {
    return { ...this.defaultOptions }
  }
}

// 创建全局 AI 工作流服务实例
export const aiWorkflowService = new AIWorkflowService()

export default aiWorkflowService
