/**
 * 配置服务管理器
 * 提供配置的持久化、验证、迁移等功能
 */

import { eventBus } from '../lib/event-bus'

export interface ConfigSchema {
  version: string
  app: {
    language: string
    theme: string
    autoSave: boolean
    checkUpdates: boolean
  }
  ai: {
    confidenceThreshold: number
    contextExpansionRatio: number
    enhanceMask: boolean
    useSmartEnhancement: boolean
  }
  performance: {
    maxConcurrentTasks: number
    memoryLimit: number
    useGPU: boolean
    enableHardwareAcceleration: boolean
  }
  output: {
    defaultPath: string
    outputFormat: string
    quality: number
    keepOriginalName: boolean
    addTimestamp: boolean
  }
  ui: {
    showPreview: boolean
    autoHideCompleted: boolean
    confirmDelete: boolean
    showNotifications: boolean
  }
}

export interface ConfigValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
  migratedConfig?: Partial<ConfigSchema>
}

export class ConfigService {
  private config: ConfigSchema
  private readonly storageKey = 'watermark-app-config'
  private readonly currentVersion = '1.0.0'
  private isInitialized = false

  constructor() {
    this.config = this.getDefaultConfig()
    this.loadConfig()
  }

  /**
   * 初始化配置服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 确保配置已加载
      this.loadConfig()

      this.isInitialized = true
      eventBus.emit('service:initialized', { serviceName: 'config' })
    } catch (error) {
      eventBus.emit('service:error', {
        serviceName: 'config',
        error: error instanceof Error ? error.message : 'Initialization failed'
      })
      throw error
    }
  }

  /**
   * 获取默认配置
   */
  private getDefaultConfig(): ConfigSchema {
    return {
      version: this.currentVersion,
      app: {
        language: 'zh-CN',
        theme: 'system',
        autoSave: true,
        checkUpdates: true
      },
      ai: {
        confidenceThreshold: 0.5,
        contextExpansionRatio: 0.1,
        enhanceMask: true,
        useSmartEnhancement: true
      },
      performance: {
        maxConcurrentTasks: 3,
        memoryLimit: 4096,
        useGPU: true,
        enableHardwareAcceleration: true
      },
      output: {
        defaultPath: '',
        outputFormat: 'same',
        quality: 95,
        keepOriginalName: true,
        addTimestamp: false
      },
      ui: {
        showPreview: true,
        autoHideCompleted: false,
        confirmDelete: true,
        showNotifications: true
      }
    }
  }

  /**
   * 加载配置
   */
  private loadConfig(): void {
    try {
      const stored = localStorage.getItem(this.storageKey)
      if (stored) {
        const parsedConfig = JSON.parse(stored)
        const validationResult = this.validateConfig(parsedConfig)
        
        if (validationResult.isValid) {
          this.config = { ...this.config, ...parsedConfig }
        } else {
          console.warn('Invalid config found, using defaults:', validationResult.errors)
          if (validationResult.migratedConfig) {
            this.config = { ...this.config, ...validationResult.migratedConfig }
            this.saveConfig()
          }
        }
      }
    } catch (error) {
      console.error('Failed to load config:', error)
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Failed to load config'),
        context: 'config loading'
      })
    }
  }

  /**
   * 保存配置
   */
  private saveConfig(): void {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.config))
      eventBus.emit('config:saved', { config: this.config })
    } catch (error) {
      console.error('Failed to save config:', error)
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Failed to save config'),
        context: 'config saving'
      })
    }
  }

  /**
   * 验证配置
   */
  private validateConfig(config: any): ConfigValidationResult {
    const errors: string[] = []
    const warnings: string[] = []
    let migratedConfig: Partial<ConfigSchema> | undefined

    // 检查版本
    if (!config.version) {
      warnings.push('Missing config version, assuming latest')
      migratedConfig = { ...config, version: this.currentVersion }
    } else if (config.version !== this.currentVersion) {
      // 执行配置迁移
      migratedConfig = this.migrateConfig(config)
      warnings.push(`Config migrated from version ${config.version} to ${this.currentVersion}`)
    }

    // 验证 AI 配置
    if (config.ai) {
      if (typeof config.ai.confidenceThreshold !== 'number' || 
          config.ai.confidenceThreshold < 0 || 
          config.ai.confidenceThreshold > 1) {
        errors.push('Invalid AI confidence threshold')
      }
      if (typeof config.ai.contextExpansionRatio !== 'number' || 
          config.ai.contextExpansionRatio < 0 || 
          config.ai.contextExpansionRatio > 1) {
        errors.push('Invalid AI context expansion ratio')
      }
    }

    // 验证性能配置
    if (config.performance) {
      if (typeof config.performance.maxConcurrentTasks !== 'number' || 
          config.performance.maxConcurrentTasks < 1 || 
          config.performance.maxConcurrentTasks > 10) {
        errors.push('Invalid max concurrent tasks')
      }
      if (typeof config.performance.memoryLimit !== 'number' || 
          config.performance.memoryLimit < 1024) {
        errors.push('Invalid memory limit')
      }
    }

    // 验证输出配置
    if (config.output) {
      if (typeof config.output.quality !== 'number' || 
          config.output.quality < 1 || 
          config.output.quality > 100) {
        errors.push('Invalid output quality')
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      migratedConfig
    }
  }

  /**
   * 迁移配置
   */
  private migrateConfig(oldConfig: any): ConfigSchema {
    // 这里可以实现不同版本间的配置迁移逻辑
    const defaultConfig = this.getDefaultConfig()
    
    // 简单的合并策略，保留有效的旧配置
    return {
      ...defaultConfig,
      ...oldConfig,
      version: this.currentVersion
    }
  }

  /**
   * 获取完整配置
   */
  getConfig(): ConfigSchema {
    return { ...this.config }
  }

  /**
   * 获取配置的某个部分
   */
  getSection<K extends keyof ConfigSchema>(section: K): ConfigSchema[K] {
    return JSON.parse(JSON.stringify(this.config[section]))
  }

  /**
   * 更新配置的某个部分
   */
  updateSection<K extends keyof ConfigSchema>(
    section: K,
    updates: Partial<ConfigSchema[K]>
  ): void {
    this.config[section] = Object.assign({}, this.config[section], updates)
    this.saveConfig()
    eventBus.emit('config:updated', { section, updates })
  }

  /**
   * 更新完整配置
   */
  updateConfig(updates: Partial<ConfigSchema>): void {
    this.config = { ...this.config, ...updates }
    this.saveConfig()
    eventBus.emit('config:updated', { section: 'all', updates })
  }

  /**
   * 重置配置到默认值
   */
  resetConfig(): void {
    this.config = this.getDefaultConfig()
    this.saveConfig()
    eventBus.emit('config:reset', undefined)
  }

  /**
   * 重置某个部分到默认值
   */
  resetSection<K extends keyof ConfigSchema>(section: K): void {
    const defaultConfig = this.getDefaultConfig()
    this.config[section] = defaultConfig[section]
    this.saveConfig()
    eventBus.emit('config:section:reset', { section })
  }

  /**
   * 导出配置
   */
  exportConfig(): string {
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 导入配置
   */
  importConfig(configJson: string): void {
    try {
      const importedConfig = JSON.parse(configJson)
      const validationResult = this.validateConfig(importedConfig)
      
      if (validationResult.isValid) {
        this.config = importedConfig
        this.saveConfig()
        eventBus.emit('config:imported', { config: this.config })
      } else {
        throw new Error(`Invalid config: ${validationResult.errors.join(', ')}`)
      }
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Failed to import config'),
        context: 'config import'
      })
      throw error
    }
  }

  /**
   * 获取配置版本
   */
  getVersion(): string {
    return this.config.version
  }

  /**
   * 检查是否需要迁移
   */
  needsMigration(): boolean {
    return this.config.version !== this.currentVersion
  }
}

// 创建全局配置服务实例
export const configService = new ConfigService()

export default configService
