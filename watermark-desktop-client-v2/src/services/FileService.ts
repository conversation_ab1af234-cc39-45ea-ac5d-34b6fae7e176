/**
 * 文件服务管理器
 * 提供文件上传、下载、管理等功能
 */

import { fileAPI } from '../lib/api'
import { eventBus } from '../lib/event-bus'
import { tauriAPI } from '../lib/tauri'

export interface FileValidationResult {
  isValid: boolean
  errors: string[]
  warnings: string[]
}

export interface FileUploadProgress {
  fileId: string
  fileName: string
  progress: number
  status: 'pending' | 'uploading' | 'completed' | 'failed'
  error?: string
}

export interface FileDownloadOptions {
  saveAs?: boolean
  defaultPath?: string
  showProgress?: boolean
}

export class FileService {
  private uploadQueue: Map<string, FileUploadProgress> = new Map()
  private supportedFormats = new Set([
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/webp',
    'image/bmp',
    'image/tiff',
    'image/tif'
  ])
  private maxFileSize = 50 * 1024 * 1024 // 50MB
  private maxFiles = 100
  private isInitialized = false

  /**
   * 初始化文件服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 清理上传队列
      this.uploadQueue.clear()

      this.isInitialized = true
      eventBus.emit('service:initialized', { serviceName: 'file' })
    } catch (error) {
      eventBus.emit('service:error', {
        serviceName: 'file',
        error: error instanceof Error ? error.message : 'Initialization failed'
      })
      throw error
    }
  }

  /**
   * 验证文件
   */
  validateFile(file: File): FileValidationResult {
    const errors: string[] = []
    const warnings: string[] = []

    // 检查文件类型
    if (!this.supportedFormats.has(file.type.toLowerCase())) {
      errors.push(`不支持的文件格式: ${file.type}`)
    }

    // 检查文件大小
    if (file.size > this.maxFileSize) {
      errors.push(`文件过大: ${this.formatFileSize(file.size)} (最大 ${this.formatFileSize(this.maxFileSize)})`)
    }

    // 检查文件名
    if (file.name.length > 255) {
      errors.push('文件名过长')
    }

    // 警告检查
    if (file.size > 10 * 1024 * 1024) { // 10MB
      warnings.push('文件较大，处理可能需要更长时间')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * 批量验证文件
   */
  validateFiles(files: File[]): { valid: File[], invalid: Array<{ file: File, result: FileValidationResult }> } {
    const valid: File[] = []
    const invalid: Array<{ file: File, result: FileValidationResult }> = []

    // 检查文件数量
    if (files.length > this.maxFiles) {
      throw new Error(`最多只能选择 ${this.maxFiles} 个文件`)
    }

    for (const file of files) {
      const result = this.validateFile(file)
      if (result.isValid) {
        valid.push(file)
      } else {
        invalid.push({ file, result })
      }
    }

    return { valid, invalid }
  }

  /**
   * 上传文件
   */
  async uploadFiles(files: File[]): Promise<void> {
    const { valid, invalid } = this.validateFiles(files)

    // 报告无效文件
    if (invalid.length > 0) {
      const errorMessages = invalid.map(({ file, result }) => 
        `${file.name}: ${result.errors.join(', ')}`
      ).join('\n')
      
      eventBus.emit('error:occurred', {
        error: new Error(`以下文件无效:\n${errorMessages}`),
        context: 'file validation'
      })
    }

    // 上传有效文件
    if (valid.length > 0) {
      try {
        // 初始化上传进度
        valid.forEach(file => {
          const fileId = this.generateFileId(file)
          this.uploadQueue.set(fileId, {
            fileId,
            fileName: file.name,
            progress: 0,
            status: 'pending'
          })
        })

        // 发布上传开始事件
        eventBus.emit('file:upload:started', { files: valid })

        // 执行上传
        const result = await fileAPI.upload(valid)

        // 更新上传状态
        valid.forEach(file => {
          const fileId = this.generateFileId(file)
          this.uploadQueue.set(fileId, {
            fileId,
            fileName: file.name,
            progress: 100,
            status: 'completed'
          })
        })

        // 发布上传完成事件
        eventBus.emit('file:upload:completed', { files: valid, result })

      } catch (error) {
        // 更新失败状态
        valid.forEach(file => {
          const fileId = this.generateFileId(file)
          this.uploadQueue.set(fileId, {
            fileId,
            fileName: file.name,
            progress: 0,
            status: 'failed',
            error: error instanceof Error ? error.message : 'Upload failed'
          })
        })

        // 发布上传失败事件
        eventBus.emit('file:upload:failed', { files: valid, error })
        throw error
      }
    }
  }

  /**
   * 下载文件
   */
  async downloadFile(filePath: string, options: FileDownloadOptions = {}): Promise<void> {
    try {
      if (options.saveAs) {
        // 使用 Tauri 保存对话框
        const fileName = filePath.split('/').pop() || 'download'
        const savePath = await tauriAPI.file.saveFileDialog(fileName)
        
        if (savePath) {
          // 下载并保存文件
          await fileAPI.download(filePath)
          // TODO: 实现 Tauri 文件保存
          console.log('Save file to:', savePath)
        }
      } else {
        // 直接下载到浏览器
        const blob = await fileAPI.download(filePath)
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = filePath.split('/').pop() || 'download'
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      eventBus.emit('file:download:completed', { filePath })
    } catch (error) {
      eventBus.emit('file:download:failed', { filePath, error })
      throw error
    }
  }

  /**
   * 获取文件信息
   */
  async getFileInfo(filePath: string) {
    try {
      return await fileAPI.getInfo(filePath)
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Failed to get file info'),
        context: 'file info'
      })
      throw error
    }
  }

  /**
   * 删除文件
   */
  async deleteFile(filePath: string): Promise<void> {
    try {
      await fileAPI.delete(filePath)
      eventBus.emit('file:deleted', { filePath })
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Failed to delete file'),
        context: 'file deletion'
      })
      throw error
    }
  }

  /**
   * 清理临时文件
   */
  async cleanupTempFiles(): Promise<void> {
    try {
      await fileAPI.cleanup()
      eventBus.emit('file:cleanup:completed', undefined)
    } catch (error) {
      eventBus.emit('error:occurred', {
        error: error instanceof Error ? error : new Error('Failed to cleanup files'),
        context: 'file cleanup'
      })
      throw error
    }
  }

  /**
   * 获取上传进度
   */
  getUploadProgress(fileId: string): FileUploadProgress | null {
    return this.uploadQueue.get(fileId) || null
  }

  /**
   * 获取所有上传进度
   */
  getAllUploadProgress(): FileUploadProgress[] {
    return Array.from(this.uploadQueue.values())
  }

  /**
   * 清除上传队列
   */
  clearUploadQueue(): void {
    this.uploadQueue.clear()
  }

  /**
   * 格式化文件大小
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
  }

  /**
   * 生成文件ID
   */
  private generateFileId(file: File): string {
    return `${file.name}-${file.size}-${file.lastModified}`
  }

  /**
   * 获取支持的文件格式
   */
  getSupportedFormats(): string[] {
    return Array.from(this.supportedFormats)
  }

  /**
   * 获取最大文件大小
   */
  getMaxFileSize(): number {
    return this.maxFileSize
  }

  /**
   * 获取最大文件数量
   */
  getMaxFiles(): number {
    return this.maxFiles
  }
}

// 创建全局文件服务实例
export const fileService = new FileService()

export default fileService
