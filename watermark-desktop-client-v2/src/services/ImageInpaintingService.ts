/**
 * 图像修复服务
 * 基于 LaMa 模型实现图像修复功能
 */

import { eventBus } from '../lib/event-bus'
import { logService } from './LogService'
import { errorService, ErrorType } from './ErrorService'
import { aiModelService, ModelType } from './AIModelService'
import { httpClient } from '../lib/http-client'
import { DetectionResult } from './WatermarkDetectionService'

export interface InpaintingOptions {
  maskExpansion?: number
  contextExpansionRatio?: number
  enhanceMask?: boolean
  useSmartEnhancement?: boolean
  outputFormat?: 'same' | 'jpg' | 'png'
  quality?: number
  deviceType?: 'cpu' | 'gpu' | 'auto'
  batchSize?: number
}

export interface InpaintingResult {
  original_path: string
  output_path: string
  mask_path?: string
  processing_time: number
  image_size: {
    width: number
    height: number
  }
  watermarks_removed: number
  quality_score?: number
  metadata?: Record<string, any>
}

export interface BatchInpaintingProgress {
  taskId: string
  totalFiles: number
  processedFiles: number
  currentFile: string
  progress: number
  results: InpaintingResult[]
  errors: Array<{ file: string; error: string }>
  startTime: number
  estimatedTimeRemaining?: number
}

export interface InpaintingTask {
  imagePath: string
  detectionResult?: DetectionResult
  customMask?: string
  options?: InpaintingOptions
}

export class ImageInpaintingService {
  private activeTasks: Map<string, BatchInpaintingProgress> = new Map()
  private defaultOptions: InpaintingOptions = {
    maskExpansion: 10,
    contextExpansionRatio: 0.1,
    enhanceMask: true,
    useSmartEnhancement: true,
    outputFormat: 'same',
    quality: 95,
    deviceType: 'auto',
    batchSize: 2
  }

  constructor() {
    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    eventBus.on('inpainting:start:requested', ({ tasks, options }) => {
      this.inpaintImagesInBatch(tasks, options)
    })

    eventBus.on('inpainting:cancel:requested', ({ taskId }) => {
      this.cancelInpainting(taskId)
    })
  }

  /**
   * 修复单个图像
   */
  async inpaintImage(
    imagePath: string,
    detectionResult?: DetectionResult,
    customMask?: string,
    options: InpaintingOptions = {}
  ): Promise<InpaintingResult> {
    const finalOptions = { ...this.defaultOptions, ...options }

    try {
      // 确保 LaMa 模型已加载
      if (!aiModelService.isModelLoaded(ModelType.LAMA)) {
        logService.info('Loading LaMa model for inpainting', 'image_inpainting')
        await aiModelService.loadModel(ModelType.LAMA, {
          deviceType: finalOptions.deviceType
        })
      }

      const startTime = Date.now()
      logService.info(`Starting image inpainting for ${imagePath}`, 'image_inpainting', finalOptions)

      // 准备请求数据
      const requestData: any = {
        image_path: imagePath,
        mask_expansion: finalOptions.maskExpansion,
        context_expansion_ratio: finalOptions.contextExpansionRatio,
        enhance_mask: finalOptions.enhanceMask,
        use_smart_enhancement: finalOptions.useSmartEnhancement,
        output_format: finalOptions.outputFormat,
        quality: finalOptions.quality,
        device_type: finalOptions.deviceType
      }

      // 添加检测结果或自定义掩码
      if (customMask) {
        requestData.custom_mask = customMask
      } else if (detectionResult) {
        requestData.detection_result = detectionResult
      } else {
        throw new Error('Either detection result or custom mask must be provided')
      }

      // 调用后端修复 API
      const response = await httpClient.post<InpaintingResult>('/inpainting/inpaint', requestData, {
        timeout: 300000 // 5分钟超时
      })

      const processingTime = Date.now() - startTime

      // 更新模型性能指标
      aiModelService.updateInferenceMetrics(ModelType.LAMA, processingTime)

      logService.info(
        `Image inpainting completed for ${imagePath}`,
        'image_inpainting',
        {
          outputPath: response.output_path,
          watermarksRemoved: response.watermarks_removed,
          processingTime
        }
      )

      // 发布修复完成事件
      eventBus.emit('inpainting:completed', {
        imagePath,
        result: response,
        processingTime
      })

      return response

    } catch (error) {
      const errorMessage = `Image inpainting failed for ${imagePath}: ${error instanceof Error ? error.message : String(error)}`
      logService.error(errorMessage, 'image_inpainting', { imagePath, options })

      await errorService.handleError(
        new Error(errorMessage),
        ErrorType.PROCESSING,
        'image_inpainting'
      )

      throw error
    }
  }

  /**
   * 批量修复图像
   */
  async inpaintImagesInBatch(
    tasks: InpaintingTask[],
    globalOptions: InpaintingOptions = {}
  ): Promise<string> {
    const taskId = this.generateTaskId()
    const finalOptions = { ...this.defaultOptions, ...globalOptions }

    // 初始化任务进度
    const progress: BatchInpaintingProgress = {
      taskId,
      totalFiles: tasks.length,
      processedFiles: 0,
      currentFile: '',
      progress: 0,
      results: [],
      errors: [],
      startTime: Date.now()
    }

    this.activeTasks.set(taskId, progress)

    logService.info(
      `Starting batch image inpainting for ${tasks.length} images`,
      'image_inpainting',
      { taskId, options: finalOptions }
    )

    // 发布批量修复开始事件
    eventBus.emit('inpainting:batch:started', { taskId, progress })

    // 异步执行批量修复
    this.performBatchInpainting(taskId, tasks, finalOptions)

    return taskId
  }

  /**
   * 执行批量修复
   */
  private async performBatchInpainting(
    taskId: string,
    tasks: InpaintingTask[],
    globalOptions: InpaintingOptions
  ): Promise<void> {
    const progress = this.activeTasks.get(taskId)
    if (!progress) return

    try {
      // 确保 LaMa 模型已加载
      if (!aiModelService.isModelLoaded(ModelType.LAMA)) {
        await aiModelService.loadModel(ModelType.LAMA, {
          deviceType: globalOptions.deviceType
        })
      }

      // 分批处理
      const batchSize = globalOptions.batchSize || 2
      for (let i = 0; i < tasks.length; i += batchSize) {
        // 检查任务是否被取消
        if (!this.activeTasks.has(taskId)) {
          logService.info(`Batch inpainting task ${taskId} was cancelled`, 'image_inpainting')
          return
        }

        const batch = tasks.slice(i, i + batchSize)
        const batchPromises = batch.map(async (task) => {
          try {
            progress.currentFile = task.imagePath
            this.updateProgress(taskId)

            const taskOptions = { ...globalOptions, ...task.options }
            const result = await this.inpaintImage(
              task.imagePath,
              task.detectionResult,
              task.customMask,
              taskOptions
            )

            progress.results.push(result)
            progress.processedFiles++

          } catch (error) {
            progress.errors.push({
              file: task.imagePath,
              error: error instanceof Error ? error.message : String(error)
            })
            progress.processedFiles++
          }

          this.updateProgress(taskId)
        })

        await Promise.allSettled(batchPromises)
      }

      // 完成批量修复
      progress.progress = 100
      this.updateProgress(taskId)

      logService.info(
        `Batch image inpainting completed for task ${taskId}`,
        'image_inpainting',
        {
          totalFiles: progress.totalFiles,
          successCount: progress.results.length,
          errorCount: progress.errors.length,
          processingTime: Date.now() - progress.startTime
        }
      )

      eventBus.emit('inpainting:batch:completed', { taskId, progress })

    } catch (error) {
      logService.error(
        `Batch image inpainting failed for task ${taskId}`,
        'image_inpainting',
        { taskId },
        error instanceof Error ? error : new Error(String(error))
      )

      eventBus.emit('inpainting:batch:failed', { taskId, error })

    } finally {
      // 清理任务
      setTimeout(() => {
        this.activeTasks.delete(taskId)
      }, 60000) // 1分钟后清理
    }
  }

  /**
   * 取消修复任务
   */
  async cancelInpainting(taskId: string): Promise<void> {
    const progress = this.activeTasks.get(taskId)
    if (!progress) {
      logService.warn(`Inpainting task ${taskId} not found`, 'image_inpainting')
      return
    }

    try {
      // 调用后端取消 API
      await httpClient.post('/inpainting/cancel', { task_id: taskId })

      // 移除任务
      this.activeTasks.delete(taskId)

      logService.info(`Inpainting task ${taskId} cancelled`, 'image_inpainting')
      eventBus.emit('inpainting:cancelled', { taskId })

    } catch (error) {
      logService.error(
        `Failed to cancel inpainting task ${taskId}`,
        'image_inpainting',
        { taskId },
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * 从检测结果创建修复任务
   */
  createTasksFromDetection(detectionResults: DetectionResult[]): InpaintingTask[] {
    return detectionResults
      .filter(result => result.has_watermark && result.detections.length > 0)
      .map(result => ({
        imagePath: result.image_path,
        detectionResult: result
      }))
  }

  /**
   * 获取修复任务进度
   */
  getInpaintingProgress(taskId: string): BatchInpaintingProgress | undefined {
    return this.activeTasks.get(taskId)
  }

  /**
   * 获取所有活动任务
   */
  getActiveTasks(): BatchInpaintingProgress[] {
    return Array.from(this.activeTasks.values())
  }

  /**
   * 更新任务进度
   */
  private updateProgress(taskId: string): void {
    const progress = this.activeTasks.get(taskId)
    if (!progress) return

    // 计算进度百分比
    progress.progress = Math.round((progress.processedFiles / progress.totalFiles) * 100)

    // 估算剩余时间
    if (progress.processedFiles > 0) {
      const elapsedTime = Date.now() - progress.startTime
      const avgTimePerFile = elapsedTime / progress.processedFiles
      const remainingFiles = progress.totalFiles - progress.processedFiles
      progress.estimatedTimeRemaining = Math.round(avgTimePerFile * remainingFiles)
    }

    // 发布进度更新事件
    eventBus.emit('inpainting:progress:updated', { taskId, progress })
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `inpainting-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取修复统计信息
   */
  getStats(): {
    activeTasks: number
    totalInpaintings: number
    averageProcessingTime: number
    successRate: number
    totalWatermarksRemoved: number
  } {
    const tasks = Array.from(this.activeTasks.values())
    const totalInpaintings = tasks.reduce((sum, task) => sum + task.results.length, 0)
    const totalErrors = tasks.reduce((sum, task) => sum + task.errors.length, 0)
    const totalProcessed = totalInpaintings + totalErrors

    let totalProcessingTime = 0
    let totalWatermarksRemoved = 0
    let inpaintingCount = 0

    tasks.forEach(task => {
      task.results.forEach(result => {
        totalProcessingTime += result.processing_time
        totalWatermarksRemoved += result.watermarks_removed
        inpaintingCount++
      })
    })

    return {
      activeTasks: tasks.length,
      totalInpaintings,
      averageProcessingTime: inpaintingCount > 0 ? totalProcessingTime / inpaintingCount : 0,
      successRate: totalProcessed > 0 ? (totalInpaintings / totalProcessed) * 100 : 0,
      totalWatermarksRemoved
    }
  }

  /**
   * 更新默认选项
   */
  updateDefaultOptions(options: Partial<InpaintingOptions>): void {
    this.defaultOptions = { ...this.defaultOptions, ...options }
    logService.info('Inpainting default options updated', 'image_inpainting', this.defaultOptions)
  }

  /**
   * 获取默认选项
   */
  getDefaultOptions(): InpaintingOptions {
    return { ...this.defaultOptions }
  }
}

// 创建全局图像修复服务实例
export const imageInpaintingService = new ImageInpaintingService()

export default imageInpaintingService
