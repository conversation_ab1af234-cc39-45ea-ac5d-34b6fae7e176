/**
 * AI 模型管理服务
 * 统一管理 YOLO 检测模型和 LaMa 修复模型的加载、卸载和状态
 */

import { eventBus } from '../lib/event-bus'
import { logService } from './LogService'
import { errorService, ErrorType } from './ErrorService'
import { httpClient } from '../lib/http-client'

export enum ModelType {
  YOLO = 'yolo',
  LAMA = 'lama'
}

export enum ModelStatus {
  UNLOADED = 'unloaded',
  LOADING = 'loading',
  LOADED = 'loaded',
  ERROR = 'error'
}

export interface ModelInfo {
  type: ModelType
  name: string
  version: string
  path: string
  size: number
  status: ModelStatus
  loadedAt?: number
  error?: string
  metadata?: Record<string, any>
}

export interface ModelLoadOptions {
  force?: boolean
  timeout?: number
  deviceType?: 'cpu' | 'gpu' | 'auto'
}

export interface ModelPerformanceMetrics {
  loadTime: number
  memoryUsage: number
  inferenceTime: number
  throughput: number
}

export class AIModelService {
  private models: Map<ModelType, ModelInfo> = new Map()
  private loadingPromises: Map<ModelType, Promise<void>> = new Map()
  private performanceMetrics: Map<ModelType, ModelPerformanceMetrics> = new Map()

  constructor() {
    this.initializeModels()
    this.setupEventListeners()
  }

  /**
   * 初始化模型信息
   */
  private initializeModels(): void {
    // YOLO 水印检测模型
    this.models.set(ModelType.YOLO, {
      type: ModelType.YOLO,
      name: 'YOLO Watermark Detection',
      version: '1.0.0',
      path: '/models/yolo_watermark.pt',
      size: 0,
      status: ModelStatus.UNLOADED
    })

    // LaMa 图像修复模型
    this.models.set(ModelType.LAMA, {
      type: ModelType.LAMA,
      name: 'LaMa Image Inpainting',
      version: '1.0.0',
      path: '/models/lama_model.pth',
      size: 0,
      status: ModelStatus.UNLOADED
    })
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    eventBus.on('model:load:requested', ({ modelType, options }) => {
      this.loadModel(modelType as ModelType, options)
    })

    eventBus.on('model:unload:requested', ({ modelType }) => {
      this.unloadModel(modelType as ModelType)
    })
  }

  /**
   * 加载模型
   */
  async loadModel(modelType: ModelType, options: ModelLoadOptions = {}): Promise<void> {
    const model = this.models.get(modelType)
    if (!model) {
      throw new Error(`Unknown model type: ${modelType}`)
    }

    // 如果模型已经加载且不强制重新加载
    if (model.status === ModelStatus.LOADED && !options.force) {
      logService.info(`Model ${modelType} is already loaded`, 'ai_model')
      return
    }

    // 如果正在加载，返回现有的 Promise
    const existingPromise = this.loadingPromises.get(modelType)
    if (existingPromise) {
      return existingPromise
    }

    // 创建加载 Promise
    const loadPromise = this.performModelLoad(modelType, options)
    this.loadingPromises.set(modelType, loadPromise)

    try {
      await loadPromise
    } finally {
      this.loadingPromises.delete(modelType)
    }
  }

  /**
   * 执行模型加载
   */
  private async performModelLoad(modelType: ModelType, options: ModelLoadOptions): Promise<void> {
    const model = this.models.get(modelType)!
    const startTime = Date.now()

    try {
      // 更新状态为加载中
      model.status = ModelStatus.LOADING
      model.error = undefined
      this.updateModelStatus(model)

      logService.info(`Loading model ${modelType}`, 'ai_model', { options })

      // 调用后端 API 加载模型
      const response = await httpClient.post('/models/load', {
        model_type: modelType,
        device_type: options.deviceType || 'auto',
        force: options.force || false
      }, {
        timeout: options.timeout || 120000 // 2分钟超时
      })

      // 更新模型信息
      model.status = ModelStatus.LOADED
      model.loadedAt = Date.now()
      model.size = response.model_size || 0
      model.metadata = response.metadata || {}

      // 记录性能指标
      const loadTime = Date.now() - startTime
      this.performanceMetrics.set(modelType, {
        loadTime,
        memoryUsage: response.memory_usage || 0,
        inferenceTime: 0,
        throughput: 0
      })

      this.updateModelStatus(model)
      logService.info(`Model ${modelType} loaded successfully`, 'ai_model', {
        loadTime,
        size: model.size
      })

      eventBus.emit('model:loaded', { modelType, model })

    } catch (error) {
      model.status = ModelStatus.ERROR
      model.error = error instanceof Error ? error.message : String(error)
      this.updateModelStatus(model)

      const errorMessage = `Failed to load model ${modelType}: ${model.error}`
      logService.error(errorMessage, 'ai_model', { modelType, options })
      
      await errorService.handleError(
        new Error(errorMessage),
        ErrorType.PROCESSING,
        'model_loading'
      )

      throw error
    }
  }

  /**
   * 卸载模型
   */
  async unloadModel(modelType: ModelType): Promise<void> {
    const model = this.models.get(modelType)
    if (!model) {
      throw new Error(`Unknown model type: ${modelType}`)
    }

    if (model.status !== ModelStatus.LOADED) {
      logService.warn(`Model ${modelType} is not loaded`, 'ai_model')
      return
    }

    try {
      logService.info(`Unloading model ${modelType}`, 'ai_model')

      // 调用后端 API 卸载模型
      await httpClient.post('/models/unload', {
        model_type: modelType
      })

      // 更新状态
      model.status = ModelStatus.UNLOADED
      model.loadedAt = undefined
      model.error = undefined
      this.updateModelStatus(model)

      // 清除性能指标
      this.performanceMetrics.delete(modelType)

      logService.info(`Model ${modelType} unloaded successfully`, 'ai_model')
      eventBus.emit('model:unloaded', { modelType, model })

    } catch (error) {
      const errorMessage = `Failed to unload model ${modelType}: ${error instanceof Error ? error.message : String(error)}`
      logService.error(errorMessage, 'ai_model', { modelType })
      
      await errorService.handleError(
        new Error(errorMessage),
        ErrorType.PROCESSING,
        'model_unloading'
      )

      throw error
    }
  }

  /**
   * 获取模型信息
   */
  getModelInfo(modelType: ModelType): ModelInfo | undefined {
    return this.models.get(modelType)
  }

  /**
   * 获取所有模型信息
   */
  getAllModels(): ModelInfo[] {
    return Array.from(this.models.values())
  }

  /**
   * 检查模型是否已加载
   */
  isModelLoaded(modelType: ModelType): boolean {
    const model = this.models.get(modelType)
    return model?.status === ModelStatus.LOADED
  }

  /**
   * 获取模型性能指标
   */
  getPerformanceMetrics(modelType: ModelType): ModelPerformanceMetrics | undefined {
    return this.performanceMetrics.get(modelType)
  }

  /**
   * 更新推理性能指标
   */
  updateInferenceMetrics(modelType: ModelType, inferenceTime: number): void {
    const metrics = this.performanceMetrics.get(modelType)
    if (metrics) {
      metrics.inferenceTime = inferenceTime
      metrics.throughput = 1000 / inferenceTime // 每秒处理数
    }
  }

  /**
   * 检查所有模型状态
   */
  async checkModelsStatus(): Promise<void> {
    try {
      const response = await httpClient.get('/models/status')
      
      for (const [modelType, model] of this.models.entries()) {
        const backendStatus = response[modelType]
        if (backendStatus) {
          // 同步后端状态
          if (backendStatus.loaded && model.status !== ModelStatus.LOADED) {
            model.status = ModelStatus.LOADED
            model.loadedAt = Date.now()
          } else if (!backendStatus.loaded && model.status === ModelStatus.LOADED) {
            model.status = ModelStatus.UNLOADED
            model.loadedAt = undefined
          }
          
          model.size = backendStatus.size || model.size
          model.metadata = backendStatus.metadata || model.metadata
          
          this.updateModelStatus(model)
        }
      }
    } catch (error) {
      logService.error('Failed to check models status', 'ai_model', undefined, error instanceof Error ? error : new Error(String(error)))
    }
  }

  /**
   * 预热所有模型
   */
  async warmupModels(): Promise<void> {
    logService.info('Starting models warmup', 'ai_model')
    
    const loadPromises = Array.from(this.models.keys()).map(modelType =>
      this.loadModel(modelType, { deviceType: 'auto' }).catch(error => {
        logService.warn(`Failed to warmup model ${modelType}`, 'ai_model', { error: error.message })
      })
    )

    await Promise.allSettled(loadPromises)
    logService.info('Models warmup completed', 'ai_model')
  }

  /**
   * 更新模型状态并发布事件
   */
  private updateModelStatus(model: ModelInfo): void {
    eventBus.emit('model:status:changed', { modelType: model.type, model })
  }

  /**
   * 获取模型统计信息
   */
  getStats(): {
    total: number
    loaded: number
    loading: number
    error: number
    totalSize: number
  } {
    const models = Array.from(this.models.values())
    
    return {
      total: models.length,
      loaded: models.filter(m => m.status === ModelStatus.LOADED).length,
      loading: models.filter(m => m.status === ModelStatus.LOADING).length,
      error: models.filter(m => m.status === ModelStatus.ERROR).length,
      totalSize: models.reduce((sum, m) => sum + (m.size || 0), 0)
    }
  }
}

// 创建全局 AI 模型服务实例
export const aiModelService = new AIModelService()

export default aiModelService
