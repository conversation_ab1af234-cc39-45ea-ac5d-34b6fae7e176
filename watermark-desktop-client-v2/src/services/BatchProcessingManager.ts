/**
 * 批量处理队列管理器
 * 智能管理批量任务的执行、优先级和资源分配
 */

import { eventBus } from '../lib/event-bus'
import { logService } from './LogService'
import { errorService, ErrorType } from './ErrorService'

export enum TaskPriority {
  LOW = 1,
  NORMAL = 2,
  HIGH = 3,
  URGENT = 4
}

export enum TaskStatus {
  PENDING = 'pending',
  RUNNING = 'running',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled',
  PAUSED = 'paused'
}

export interface BatchTask {
  id: string
  type: 'detection' | 'inpainting' | 'workflow'
  priority: TaskPriority
  status: TaskStatus
  data: any
  options?: any
  createdAt: number
  startedAt?: number
  completedAt?: number
  progress: number
  estimatedDuration?: number
  actualDuration?: number
  retryCount: number
  maxRetries: number
  error?: string
  dependencies?: string[]
  resourceRequirements: {
    memory: number // MB
    gpu: boolean
    cpu: number // cores
  }
}

export interface QueueStats {
  totalTasks: number
  pendingTasks: number
  runningTasks: number
  completedTasks: number
  failedTasks: number
  averageProcessingTime: number
  throughput: number // tasks per minute
  memoryUsage: number
  cpuUsage: number
}

export interface ProcessingLimits {
  maxConcurrentTasks: number
  maxMemoryUsage: number // MB
  maxCpuUsage: number // percentage
  maxQueueSize: number
  taskTimeout: number // ms
}

export class BatchProcessingManager {
  private queue: BatchTask[] = []
  private runningTasks: Map<string, BatchTask> = new Map()
  private completedTasks: Map<string, BatchTask> = new Map()
  private processingLimits: ProcessingLimits = {
    maxConcurrentTasks: 3,
    maxMemoryUsage: 4096, // 4GB
    maxCpuUsage: 80,
    maxQueueSize: 100,
    taskTimeout: 300000 // 5 minutes
  }
  private isProcessing = false
  private processingInterval?: NodeJS.Timeout
  private statsInterval?: NodeJS.Timeout

  constructor() {
    this.setupEventListeners()
    this.startProcessing()
    this.startStatsCollection()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    eventBus.on('batch:task:add', ({ task }) => {
      this.addTask(task)
    })

    eventBus.on('batch:task:cancel', ({ taskId }) => {
      this.cancelTask(taskId)
    })

    eventBus.on('batch:task:pause', ({ taskId }) => {
      this.pauseTask(taskId)
    })

    eventBus.on('batch:task:resume', ({ taskId }) => {
      this.resumeTask(taskId)
    })

    eventBus.on('batch:queue:clear', () => {
      this.clearQueue()
    })

    eventBus.on('batch:limits:update', ({ limits }) => {
      this.updateLimits(limits)
    })
  }

  /**
   * 添加任务到队列
   */
  addTask(task: Omit<BatchTask, 'id' | 'createdAt' | 'progress' | 'retryCount'>): string {
    if (this.queue.length >= this.processingLimits.maxQueueSize) {
      throw new Error('Queue is full')
    }

    const batchTask: BatchTask = {
      ...task,
      id: this.generateTaskId(),
      createdAt: Date.now(),
      progress: 0,
      retryCount: 0,
      maxRetries: task.maxRetries || 3
    }

    // 按优先级插入队列
    this.insertTaskByPriority(batchTask)

    logService.info(`Task ${batchTask.id} added to queue`, 'batch_processing', {
      type: batchTask.type,
      priority: batchTask.priority
    })

    eventBus.emit('batch:task:added', { task: batchTask })
    eventBus.emit('batch:queue:updated', { queue: this.getQueueSnapshot() })

    return batchTask.id
  }

  /**
   * 按优先级插入任务
   */
  private insertTaskByPriority(task: BatchTask): void {
    let insertIndex = this.queue.length

    for (let i = 0; i < this.queue.length; i++) {
      if (this.queue[i].priority < task.priority) {
        insertIndex = i
        break
      }
    }

    this.queue.splice(insertIndex, 0, task)
  }

  /**
   * 取消任务
   */
  cancelTask(taskId: string): boolean {
    // 从队列中移除
    const queueIndex = this.queue.findIndex(task => task.id === taskId)
    if (queueIndex !== -1) {
      const task = this.queue[queueIndex]
      task.status = TaskStatus.CANCELLED
      this.queue.splice(queueIndex, 1)
      this.completedTasks.set(taskId, task)

      logService.info(`Task ${taskId} cancelled from queue`, 'batch_processing')
      eventBus.emit('batch:task:cancelled', { taskId, task })
      return true
    }

    // 取消正在运行的任务
    const runningTask = this.runningTasks.get(taskId)
    if (runningTask) {
      runningTask.status = TaskStatus.CANCELLED
      runningTask.completedAt = Date.now()
      runningTask.actualDuration = runningTask.completedAt - (runningTask.startedAt || runningTask.createdAt)

      this.runningTasks.delete(taskId)
      this.completedTasks.set(taskId, runningTask)

      logService.info(`Running task ${taskId} cancelled`, 'batch_processing')
      eventBus.emit('batch:task:cancelled', { taskId, task: runningTask })
      return true
    }

    return false
  }

  /**
   * 暂停任务
   */
  pauseTask(taskId: string): boolean {
    const task = this.runningTasks.get(taskId)
    if (task && task.status === TaskStatus.RUNNING) {
      task.status = TaskStatus.PAUSED
      logService.info(`Task ${taskId} paused`, 'batch_processing')
      eventBus.emit('batch:task:paused', { taskId, task })
      return true
    }
    return false
  }

  /**
   * 恢复任务
   */
  resumeTask(taskId: string): boolean {
    const task = this.runningTasks.get(taskId)
    if (task && task.status === TaskStatus.PAUSED) {
      task.status = TaskStatus.RUNNING
      logService.info(`Task ${taskId} resumed`, 'batch_processing')
      eventBus.emit('batch:task:resumed', { taskId, task })
      return true
    }
    return false
  }

  /**
   * 清空队列
   */
  clearQueue(): void {
    const cancelledTasks = this.queue.map(task => {
      task.status = TaskStatus.CANCELLED
      return task
    })

    cancelledTasks.forEach(task => {
      this.completedTasks.set(task.id, task)
    })

    this.queue = []

    logService.info(`Queue cleared, ${cancelledTasks.length} tasks cancelled`, 'batch_processing')
    eventBus.emit('batch:queue:cleared', { cancelledTasks })
    eventBus.emit('batch:queue:updated', { queue: this.getQueueSnapshot() })
  }

  /**
   * 更新处理限制
   */
  updateLimits(limits: Partial<ProcessingLimits>): void {
    this.processingLimits = { ...this.processingLimits, ...limits }
    logService.info('Processing limits updated', 'batch_processing', this.processingLimits)
    eventBus.emit('batch:limits:updated', { limits: this.processingLimits })
  }

  /**
   * 开始处理队列
   */
  private startProcessing(): void {
    if (this.isProcessing) return

    this.isProcessing = true
    this.processingInterval = setInterval(() => {
      this.processQueue()
    }, 1000) // 每秒检查一次

    logService.info('Batch processing started', 'batch_processing')
  }

  /**
   * 停止处理队列
   */
  stopProcessing(): void {
    if (!this.isProcessing) return

    this.isProcessing = false
    if (this.processingInterval) {
      clearInterval(this.processingInterval)
      this.processingInterval = undefined
    }

    logService.info('Batch processing stopped', 'batch_processing')
  }

  /**
   * 处理队列
   */
  private processQueue(): void {
    if (this.queue.length === 0) return

    // 检查资源限制
    if (!this.canStartNewTask()) return

    // 获取下一个可执行的任务
    const nextTask = this.getNextExecutableTask()
    if (!nextTask) return

    // 从队列中移除并开始执行
    const taskIndex = this.queue.findIndex(task => task.id === nextTask.id)
    if (taskIndex !== -1) {
      this.queue.splice(taskIndex, 1)
      this.startTask(nextTask)
    }
  }

  /**
   * 检查是否可以开始新任务
   */
  private canStartNewTask(): boolean {
    const currentStats = this.getCurrentStats()
    
    return (
      this.runningTasks.size < this.processingLimits.maxConcurrentTasks &&
      currentStats.memoryUsage < this.processingLimits.maxMemoryUsage &&
      currentStats.cpuUsage < this.processingLimits.maxCpuUsage
    )
  }

  /**
   * 获取下一个可执行的任务
   */
  private getNextExecutableTask(): BatchTask | null {
    for (const task of this.queue) {
      if (task.status === TaskStatus.PENDING && this.areDependenciesMet(task)) {
        return task
      }
    }
    return null
  }

  /**
   * 检查任务依赖是否满足
   */
  private areDependenciesMet(task: BatchTask): boolean {
    if (!task.dependencies || task.dependencies.length === 0) {
      return true
    }

    return task.dependencies.every(depId => {
      const depTask = this.completedTasks.get(depId)
      return depTask && depTask.status === TaskStatus.COMPLETED
    })
  }

  /**
   * 开始执行任务
   */
  private async startTask(task: BatchTask): Promise<void> {
    task.status = TaskStatus.RUNNING
    task.startedAt = Date.now()
    this.runningTasks.set(task.id, task)

    logService.info(`Starting task ${task.id}`, 'batch_processing', {
      type: task.type,
      priority: task.priority
    })

    eventBus.emit('batch:task:started', { task })

    try {
      // 执行任务
      await this.executeTask(task)
      
      // 任务完成
      task.status = TaskStatus.COMPLETED
      task.completedAt = Date.now()
      task.actualDuration = task.completedAt - (task.startedAt || task.createdAt)
      task.progress = 100

      this.runningTasks.delete(task.id)
      this.completedTasks.set(task.id, task)

      logService.info(`Task ${task.id} completed`, 'batch_processing', {
        duration: task.actualDuration
      })

      eventBus.emit('batch:task:completed', { task })

    } catch (error) {
      // 任务失败
      task.error = error instanceof Error ? error.message : String(error)
      task.retryCount++

      if (task.retryCount < task.maxRetries) {
        // 重试任务
        task.status = TaskStatus.PENDING
        this.insertTaskByPriority(task)
        this.runningTasks.delete(task.id)

        logService.warn(`Task ${task.id} failed, retrying (${task.retryCount}/${task.maxRetries})`, 'batch_processing')
        eventBus.emit('batch:task:retry', { task })

      } else {
        // 任务最终失败
        task.status = TaskStatus.FAILED
        task.completedAt = Date.now()
        task.actualDuration = task.completedAt - (task.startedAt || task.createdAt)

        this.runningTasks.delete(task.id)
        this.completedTasks.set(task.id, task)

        logService.error(`Task ${task.id} failed permanently`, 'batch_processing', { error: task.error })
        eventBus.emit('batch:task:failed', { task })

        await errorService.handleError(
          new Error(`Batch task failed: ${task.error}`),
          ErrorType.PROCESSING,
          'batch_processing'
        )
      }
    }

    eventBus.emit('batch:queue:updated', { queue: this.getQueueSnapshot() })
  }

  /**
   * 执行具体任务
   */
  private async executeTask(task: BatchTask): Promise<void> {
    // 这里会根据任务类型调用相应的服务
    switch (task.type) {
      case 'detection':
        eventBus.emit('detection:start:requested', task.data)
        break
      case 'inpainting':
        eventBus.emit('inpainting:start:requested', task.data)
        break
      case 'workflow':
        eventBus.emit('workflow:start:requested', task.data)
        break
      default:
        throw new Error(`Unknown task type: ${task.type}`)
    }

    // 模拟任务执行时间
    await new Promise(resolve => setTimeout(resolve, 1000))
  }

  /**
   * 获取当前统计信息
   */
  private getCurrentStats(): QueueStats {
    const now = Date.now()
    const completedTasksArray = Array.from(this.completedTasks.values())
    const recentTasks = completedTasksArray.filter(task => 
      task.completedAt && (now - task.completedAt) < 60000 // 最近1分钟
    )

    const totalProcessingTime = completedTasksArray.reduce((sum, task) => 
      sum + (task.actualDuration || 0), 0
    )

    return {
      totalTasks: this.queue.length + this.runningTasks.size + this.completedTasks.size,
      pendingTasks: this.queue.length,
      runningTasks: this.runningTasks.size,
      completedTasks: completedTasksArray.filter(task => task.status === TaskStatus.COMPLETED).length,
      failedTasks: completedTasksArray.filter(task => task.status === TaskStatus.FAILED).length,
      averageProcessingTime: completedTasksArray.length > 0 ? totalProcessingTime / completedTasksArray.length : 0,
      throughput: recentTasks.length, // tasks per minute
      memoryUsage: this.estimateMemoryUsage(),
      cpuUsage: this.estimateCpuUsage()
    }
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(): number {
    return Array.from(this.runningTasks.values()).reduce((sum, task) => 
      sum + task.resourceRequirements.memory, 0
    )
  }

  /**
   * 估算CPU使用量
   */
  private estimateCpuUsage(): number {
    return Array.from(this.runningTasks.values()).reduce((sum, task) => 
      sum + (task.resourceRequirements.cpu * 25), 0 // 假设每个核心25%使用率
    )
  }

  /**
   * 开始统计信息收集
   */
  private startStatsCollection(): void {
    this.statsInterval = setInterval(() => {
      const stats = this.getCurrentStats()
      eventBus.emit('batch:stats:updated', { stats })
    }, 5000) // 每5秒更新一次统计信息
  }

  /**
   * 获取队列快照
   */
  getQueueSnapshot(): {
    pending: BatchTask[]
    running: BatchTask[]
    completed: BatchTask[]
    stats: QueueStats
  } {
    return {
      pending: [...this.queue],
      running: Array.from(this.runningTasks.values()),
      completed: Array.from(this.completedTasks.values()),
      stats: this.getCurrentStats()
    }
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `batch-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取处理限制
   */
  getProcessingLimits(): ProcessingLimits {
    return { ...this.processingLimits }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopProcessing()
    
    if (this.statsInterval) {
      clearInterval(this.statsInterval)
      this.statsInterval = undefined
    }

    this.queue = []
    this.runningTasks.clear()
    this.completedTasks.clear()

    logService.info('Batch processing manager destroyed', 'batch_processing')
  }
}

// 创建全局批量处理管理器实例
export const batchProcessingManager = new BatchProcessingManager()

export default batchProcessingManager
