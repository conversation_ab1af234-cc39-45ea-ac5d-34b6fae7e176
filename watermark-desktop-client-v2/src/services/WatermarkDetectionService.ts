/**
 * 水印检测服务
 * 基于 YOLO 模型实现水印检测功能
 */

import { eventBus } from '../lib/event-bus'
import { logService } from './LogService'
import { errorService, ErrorType } from './ErrorService'
import { aiModelService, ModelType } from './AIModelService'
import { httpClient } from '../lib/http-client'

export interface DetectionBox {
  x: number
  y: number
  width: number
  height: number
  confidence: number
  class_id: number
  class_name: string
}

export interface DetectionResult {
  image_path: string
  image_size: {
    width: number
    height: number
  }
  detections: DetectionBox[]
  detection_count: number
  processing_time: number
  confidence_threshold: number
  has_watermark: boolean
}

export interface DetectionOptions {
  confidenceThreshold?: number
  nmsThreshold?: number
  maxDetections?: number
  deviceType?: 'cpu' | 'gpu' | 'auto'
  batchSize?: number
}

export interface BatchDetectionProgress {
  taskId: string
  totalFiles: number
  processedFiles: number
  currentFile: string
  progress: number
  results: DetectionResult[]
  errors: Array<{ file: string; error: string }>
  startTime: number
  estimatedTimeRemaining?: number
}

export class WatermarkDetectionService {
  private activeTasks: Map<string, BatchDetectionProgress> = new Map()
  private defaultOptions: DetectionOptions = {
    confidenceThreshold: 0.5,
    nmsThreshold: 0.4,
    maxDetections: 100,
    deviceType: 'auto',
    batchSize: 4
  }

  constructor() {
    this.setupEventListeners()
  }

  /**
   * 设置事件监听器
   */
  private setupEventListeners(): void {
    eventBus.on('detection:start:requested', ({ files, options }) => {
      this.detectWatermarksInBatch(files, options)
    })

    eventBus.on('detection:cancel:requested', ({ taskId }) => {
      this.cancelDetection(taskId)
    })
  }

  /**
   * 检测单个图像中的水印
   */
  async detectWatermark(
    imagePath: string,
    options: DetectionOptions = {}
  ): Promise<DetectionResult> {
    const finalOptions = { ...this.defaultOptions, ...options }

    try {
      // 确保 YOLO 模型已加载
      if (!aiModelService.isModelLoaded(ModelType.YOLO)) {
        logService.info('Loading YOLO model for detection', 'watermark_detection')
        await aiModelService.loadModel(ModelType.YOLO, {
          deviceType: finalOptions.deviceType
        })
      }

      const startTime = Date.now()
      logService.info(`Starting watermark detection for ${imagePath}`, 'watermark_detection', finalOptions)

      // 调用后端检测 API
      const response = await httpClient.post<DetectionResult>('/detection/detect', {
        image_path: imagePath,
        confidence_threshold: finalOptions.confidenceThreshold,
        nms_threshold: finalOptions.nmsThreshold,
        max_detections: finalOptions.maxDetections,
        device_type: finalOptions.deviceType
      })

      const processingTime = Date.now() - startTime

      // 更新模型性能指标
      aiModelService.updateInferenceMetrics(ModelType.YOLO, processingTime)

      logService.info(
        `Watermark detection completed for ${imagePath}`,
        'watermark_detection',
        {
          detectionCount: response.detection_count,
          hasWatermark: response.has_watermark,
          processingTime
        }
      )

      // 发布检测完成事件
      eventBus.emit('detection:completed', {
        imagePath,
        result: response,
        processingTime
      })

      return response

    } catch (error) {
      const errorMessage = `Watermark detection failed for ${imagePath}: ${error instanceof Error ? error.message : String(error)}`
      logService.error(errorMessage, 'watermark_detection', { imagePath, options })

      await errorService.handleError(
        new Error(errorMessage),
        ErrorType.PROCESSING,
        'watermark_detection'
      )

      throw error
    }
  }

  /**
   * 批量检测水印
   */
  async detectWatermarksInBatch(
    imagePaths: string[],
    options: DetectionOptions = {}
  ): Promise<string> {
    const taskId = this.generateTaskId()
    const finalOptions = { ...this.defaultOptions, ...options }

    // 初始化任务进度
    const progress: BatchDetectionProgress = {
      taskId,
      totalFiles: imagePaths.length,
      processedFiles: 0,
      currentFile: '',
      progress: 0,
      results: [],
      errors: [],
      startTime: Date.now()
    }

    this.activeTasks.set(taskId, progress)

    logService.info(
      `Starting batch watermark detection for ${imagePaths.length} images`,
      'watermark_detection',
      { taskId, options: finalOptions }
    )

    // 发布批量检测开始事件
    eventBus.emit('detection:batch:started', { taskId, progress })

    // 异步执行批量检测
    this.performBatchDetection(taskId, imagePaths, finalOptions)

    return taskId
  }

  /**
   * 执行批量检测
   */
  private async performBatchDetection(
    taskId: string,
    imagePaths: string[],
    options: DetectionOptions
  ): Promise<void> {
    const progress = this.activeTasks.get(taskId)
    if (!progress) return

    try {
      // 确保 YOLO 模型已加载
      if (!aiModelService.isModelLoaded(ModelType.YOLO)) {
        await aiModelService.loadModel(ModelType.YOLO, {
          deviceType: options.deviceType
        })
      }

      // 分批处理
      const batchSize = options.batchSize || 4
      for (let i = 0; i < imagePaths.length; i += batchSize) {
        // 检查任务是否被取消
        if (!this.activeTasks.has(taskId)) {
          logService.info(`Batch detection task ${taskId} was cancelled`, 'watermark_detection')
          return
        }

        const batch = imagePaths.slice(i, i + batchSize)
        const batchPromises = batch.map(async (imagePath) => {
          try {
            progress.currentFile = imagePath
            this.updateProgress(taskId)

            const result = await this.detectWatermark(imagePath, options)
            progress.results.push(result)
            progress.processedFiles++

          } catch (error) {
            progress.errors.push({
              file: imagePath,
              error: error instanceof Error ? error.message : String(error)
            })
            progress.processedFiles++
          }

          this.updateProgress(taskId)
        })

        await Promise.allSettled(batchPromises)
      }

      // 完成批量检测
      progress.progress = 100
      this.updateProgress(taskId)

      logService.info(
        `Batch watermark detection completed for task ${taskId}`,
        'watermark_detection',
        {
          totalFiles: progress.totalFiles,
          successCount: progress.results.length,
          errorCount: progress.errors.length,
          processingTime: Date.now() - progress.startTime
        }
      )

      eventBus.emit('detection:batch:completed', { taskId, progress })

    } catch (error) {
      logService.error(
        `Batch watermark detection failed for task ${taskId}`,
        'watermark_detection',
        { taskId },
        error instanceof Error ? error : new Error(String(error))
      )

      eventBus.emit('detection:batch:failed', { taskId, error })

    } finally {
      // 清理任务
      setTimeout(() => {
        this.activeTasks.delete(taskId)
      }, 60000) // 1分钟后清理
    }
  }

  /**
   * 取消检测任务
   */
  async cancelDetection(taskId: string): Promise<void> {
    const progress = this.activeTasks.get(taskId)
    if (!progress) {
      logService.warn(`Detection task ${taskId} not found`, 'watermark_detection')
      return
    }

    try {
      // 调用后端取消 API
      await httpClient.post('/detection/cancel', { task_id: taskId })

      // 移除任务
      this.activeTasks.delete(taskId)

      logService.info(`Detection task ${taskId} cancelled`, 'watermark_detection')
      eventBus.emit('detection:cancelled', { taskId })

    } catch (error) {
      logService.error(
        `Failed to cancel detection task ${taskId}`,
        'watermark_detection',
        { taskId },
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * 获取检测任务进度
   */
  getDetectionProgress(taskId: string): BatchDetectionProgress | undefined {
    return this.activeTasks.get(taskId)
  }

  /**
   * 获取所有活动任务
   */
  getActiveTasks(): BatchDetectionProgress[] {
    return Array.from(this.activeTasks.values())
  }

  /**
   * 更新任务进度
   */
  private updateProgress(taskId: string): void {
    const progress = this.activeTasks.get(taskId)
    if (!progress) return

    // 计算进度百分比
    progress.progress = Math.round((progress.processedFiles / progress.totalFiles) * 100)

    // 估算剩余时间
    if (progress.processedFiles > 0) {
      const elapsedTime = Date.now() - progress.startTime
      const avgTimePerFile = elapsedTime / progress.processedFiles
      const remainingFiles = progress.totalFiles - progress.processedFiles
      progress.estimatedTimeRemaining = Math.round(avgTimePerFile * remainingFiles)
    }

    // 发布进度更新事件
    eventBus.emit('detection:progress:updated', { taskId, progress })
  }

  /**
   * 生成任务ID
   */
  private generateTaskId(): string {
    return `detection-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 获取检测统计信息
   */
  getStats(): {
    activeTasks: number
    totalDetections: number
    averageProcessingTime: number
    successRate: number
  } {
    const tasks = Array.from(this.activeTasks.values())
    const totalDetections = tasks.reduce((sum, task) => sum + task.results.length, 0)
    const totalErrors = tasks.reduce((sum, task) => sum + task.errors.length, 0)
    const totalProcessed = totalDetections + totalErrors

    let totalProcessingTime = 0
    let detectionCount = 0

    tasks.forEach(task => {
      task.results.forEach(result => {
        totalProcessingTime += result.processing_time
        detectionCount++
      })
    })

    return {
      activeTasks: tasks.length,
      totalDetections,
      averageProcessingTime: detectionCount > 0 ? totalProcessingTime / detectionCount : 0,
      successRate: totalProcessed > 0 ? (totalDetections / totalProcessed) * 100 : 0
    }
  }

  /**
   * 更新默认选项
   */
  updateDefaultOptions(options: Partial<DetectionOptions>): void {
    this.defaultOptions = { ...this.defaultOptions, ...options }
    logService.info('Detection default options updated', 'watermark_detection', this.defaultOptions)
  }

  /**
   * 获取默认选项
   */
  getDefaultOptions(): DetectionOptions {
    return { ...this.defaultOptions }
  }
}

// 创建全局水印检测服务实例
export const watermarkDetectionService = new WatermarkDetectionService()

export default watermarkDetectionService
