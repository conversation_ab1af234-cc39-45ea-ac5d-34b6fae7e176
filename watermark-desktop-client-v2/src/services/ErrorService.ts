/**
 * 错误处理服务
 * 提供统一的错误处理、分类、恢复策略等功能
 */

import { eventBus } from '../lib/event-bus'
import { logService } from './LogService'

export enum ErrorType {
  NETWORK = 'network',
  VALIDATION = 'validation',
  PROCESSING = 'processing',
  FILE_SYSTEM = 'file_system',
  AUTHENTICATION = 'authentication',
  PERMISSION = 'permission',
  CONFIGURATION = 'configuration',
  UNKNOWN = 'unknown'
}

export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

export interface ErrorInfo {
  id: string
  type: ErrorType
  severity: ErrorSeverity
  message: string
  originalError: Error
  context?: string
  timestamp: number
  userId?: string
  sessionId?: string
  metadata?: Record<string, any>
  resolved?: boolean
  resolvedAt?: number
  recoveryActions?: string[]
}

export interface ErrorRecoveryStrategy {
  type: ErrorType
  handler: (error: ErrorInfo) => Promise<boolean>
  maxRetries?: number
  retryDelay?: number
}

export class ErrorService {
  private errors: Map<string, ErrorInfo> = new Map()
  private recoveryStrategies: Map<ErrorType, ErrorRecoveryStrategy> = new Map()
  private retryAttempts: Map<string, number> = new Map()
  private isInitialized = false

  constructor() {
    this.setupDefaultRecoveryStrategies()
    this.setupGlobalErrorHandlers()
  }

  /**
   * 初始化错误服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      // 清理旧错误记录
      this.errors.clear()
      this.retryAttempts.clear()

      this.isInitialized = true
      eventBus.emit('service:initialized', { serviceName: 'error' })
    } catch (error) {
      console.error('Failed to initialize ErrorService:', error)
      eventBus.emit('service:error', {
        serviceName: 'error',
        error: error instanceof Error ? error.message : 'Initialization failed'
      })
      throw error
    }
  }

  /**
   * 处理错误
   */
  async handleError(
    error: Error,
    type: ErrorType = ErrorType.UNKNOWN,
    context?: string,
    metadata?: Record<string, any>
  ): Promise<ErrorInfo> {
    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      type,
      severity: this.determineSeverity(error, type),
      message: error.message,
      originalError: error,
      context,
      timestamp: Date.now(),
      metadata,
      resolved: false,
      recoveryActions: this.getRecoveryActions(type)
    }

    // 存储错误信息
    this.errors.set(errorInfo.id, errorInfo)

    // 记录日志
    logService.error(
      `${type.toUpperCase()}: ${error.message}`,
      context,
      { errorId: errorInfo.id, metadata },
      error
    )

    // 发布错误事件
    eventBus.emit('error:occurred', { error, context })

    // 尝试自动恢复
    await this.attemptRecovery(errorInfo)

    return errorInfo
  }

  /**
   * 尝试错误恢复
   */
  private async attemptRecovery(errorInfo: ErrorInfo): Promise<boolean> {
    const strategy = this.recoveryStrategies.get(errorInfo.type)
    if (!strategy) {
      return false
    }

    const currentAttempts = this.retryAttempts.get(errorInfo.id) || 0
    const maxRetries = strategy.maxRetries || 3

    if (currentAttempts >= maxRetries) {
      logService.warn(`Max retry attempts reached for error ${errorInfo.id}`)
      return false
    }

    try {
      // 延迟重试
      if (strategy.retryDelay && currentAttempts > 0) {
        await this.delay(strategy.retryDelay * Math.pow(2, currentAttempts))
      }

      // 执行恢复策略
      const recovered = await strategy.handler(errorInfo)
      
      if (recovered) {
        this.markAsResolved(errorInfo.id)
        logService.info(`Error ${errorInfo.id} recovered successfully`)
        return true
      } else {
        // 增加重试次数
        this.retryAttempts.set(errorInfo.id, currentAttempts + 1)
        logService.warn(`Recovery attempt ${currentAttempts + 1} failed for error ${errorInfo.id}`)
        return false
      }
    } catch (recoveryError) {
      logService.error(
        `Recovery strategy failed for error ${errorInfo.id}`,
        'error_recovery',
        { originalError: errorInfo.message },
        recoveryError instanceof Error ? recoveryError : new Error(String(recoveryError))
      )
      return false
    }
  }

  /**
   * 标记错误为已解决
   */
  markAsResolved(errorId: string): void {
    const errorInfo = this.errors.get(errorId)
    if (errorInfo) {
      errorInfo.resolved = true
      errorInfo.resolvedAt = Date.now()
      this.retryAttempts.delete(errorId)
      eventBus.emit('error:resolved', { errorId })
    }
  }

  /**
   * 确定错误严重程度
   */
  private determineSeverity(error: Error, type: ErrorType): ErrorSeverity {
    // 根据错误类型和内容确定严重程度
    switch (type) {
      case ErrorType.NETWORK:
        return error.message.includes('timeout') ? ErrorSeverity.MEDIUM : ErrorSeverity.LOW
      case ErrorType.PROCESSING:
        return ErrorSeverity.HIGH
      case ErrorType.FILE_SYSTEM:
        return error.message.includes('permission') ? ErrorSeverity.HIGH : ErrorSeverity.MEDIUM
      case ErrorType.AUTHENTICATION:
      case ErrorType.PERMISSION:
        return ErrorSeverity.HIGH
      case ErrorType.CONFIGURATION:
        return ErrorSeverity.MEDIUM
      default:
        return ErrorSeverity.LOW
    }
  }

  /**
   * 获取恢复操作建议
   */
  private getRecoveryActions(type: ErrorType): string[] {
    switch (type) {
      case ErrorType.NETWORK:
        return ['检查网络连接', '重试请求', '切换到离线模式']
      case ErrorType.FILE_SYSTEM:
        return ['检查文件权限', '检查磁盘空间', '重新选择文件路径']
      case ErrorType.PROCESSING:
        return ['降低处理质量', '减少并发任务', '重启处理服务']
      case ErrorType.AUTHENTICATION:
        return ['重新登录', '检查凭据', '联系管理员']
      case ErrorType.CONFIGURATION:
        return ['重置配置', '检查配置文件', '恢复默认设置']
      default:
        return ['重试操作', '重启应用', '联系技术支持']
    }
  }

  /**
   * 设置默认恢复策略
   */
  private setupDefaultRecoveryStrategies(): void {
    // 网络错误恢复策略
    this.recoveryStrategies.set(ErrorType.NETWORK, {
      type: ErrorType.NETWORK,
      maxRetries: 3,
      retryDelay: 1000,
      handler: async (_errorInfo) => {
        // 简单的网络重试策略
        try {
          // 这里可以实现具体的网络恢复逻辑
          return false // 暂时返回 false，实际应该实现重试逻辑
        } catch {
          return false
        }
      }
    })

    // 文件系统错误恢复策略
    this.recoveryStrategies.set(ErrorType.FILE_SYSTEM, {
      type: ErrorType.FILE_SYSTEM,
      maxRetries: 2,
      retryDelay: 500,
      handler: async (_errorInfo) => {
        // 文件系统错误恢复逻辑
        return false
      }
    })

    // 配置错误恢复策略
    this.recoveryStrategies.set(ErrorType.CONFIGURATION, {
      type: ErrorType.CONFIGURATION,
      maxRetries: 1,
      handler: async (_errorInfo) => {
        // 配置错误恢复逻辑，例如重置到默认配置
        try {
          eventBus.emit('config:reset', undefined)
          return true
        } catch {
          return false
        }
      }
    })
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    // 捕获未处理的 Promise 拒绝
    window.addEventListener('unhandledrejection', (event) => {
      this.handleError(
        new Error(event.reason),
        ErrorType.UNKNOWN,
        'unhandled_promise_rejection'
      )
    })

    // 捕获全局错误
    window.addEventListener('error', (event) => {
      this.handleError(
        event.error || new Error(event.message),
        ErrorType.UNKNOWN,
        'global_error',
        {
          filename: event.filename,
          lineno: event.lineno,
          colno: event.colno
        }
      )
    })
  }

  /**
   * 注册自定义恢复策略
   */
  registerRecoveryStrategy(strategy: ErrorRecoveryStrategy): void {
    this.recoveryStrategies.set(strategy.type, strategy)
  }

  /**
   * 获取错误信息
   */
  getError(errorId: string): ErrorInfo | undefined {
    return this.errors.get(errorId)
  }

  /**
   * 获取所有错误
   */
  getAllErrors(): ErrorInfo[] {
    return Array.from(this.errors.values())
  }

  /**
   * 获取未解决的错误
   */
  getUnresolvedErrors(): ErrorInfo[] {
    return Array.from(this.errors.values()).filter(error => !error.resolved)
  }

  /**
   * 清除已解决的错误
   */
  clearResolvedErrors(): void {
    for (const [id, error] of this.errors.entries()) {
      if (error.resolved) {
        this.errors.delete(id)
      }
    }
  }

  /**
   * 清除所有错误
   */
  clearAllErrors(): void {
    this.errors.clear()
    this.retryAttempts.clear()
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number
    unresolved: number
    byType: Record<string, number>
    bySeverity: Record<string, number>
  } {
    const errors = Array.from(this.errors.values())
    const byType: Record<string, number> = {}
    const bySeverity: Record<string, number> = {}

    errors.forEach(error => {
      byType[error.type] = (byType[error.type] || 0) + 1
      bySeverity[error.severity] = (bySeverity[error.severity] || 0) + 1
    })

    return {
      total: errors.length,
      unresolved: errors.filter(e => !e.resolved).length,
      byType,
      bySeverity
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }
}

// 创建全局错误服务实例
export const errorService = new ErrorService()

export default errorService
