/**
 * 服务管理器
 * 统一管理和协调所有应用服务
 */

import { fileService, FileService } from './FileService'
import { configService, ConfigService } from './ConfigService'
import { logService, LogService } from './LogService'
import { errorService, ErrorService } from './ErrorService'
import { aiModelService, AIModelService } from './AIModelService'
import { watermarkDetectionService, WatermarkDetectionService } from './WatermarkDetectionService'
import { imageInpaintingService, ImageInpaintingService } from './ImageInpaintingService'
import { aiWorkflowService, AIWorkflowService } from './AIWorkflowService'
import { batchProcessingManager, BatchProcessingManager } from './BatchProcessingManager'
import { eventBus } from '../lib/event-bus'

export interface ServiceStatus {
  name: string
  status: 'initializing' | 'running' | 'stopped' | 'error'
  lastError?: string
  startTime?: number
  uptime?: number
}

export interface ServiceManagerConfig {
  autoStart: boolean
  enableHealthCheck: boolean
  healthCheckInterval: number
}

export class ServiceManager {
  private services: Map<string, any> = new Map()
  private serviceStatus: Map<string, ServiceStatus> = new Map()
  private config: ServiceManagerConfig
  private healthCheckTimer?: number
  private isInitialized = false

  constructor(config: Partial<ServiceManagerConfig> = {}) {
    this.config = {
      autoStart: true,
      enableHealthCheck: true,
      healthCheckInterval: 30000, // 30 seconds
      ...config
    }

    this.registerServices()
  }

  /**
   * 注册所有服务
   */
  private registerServices(): void {
    this.registerService('file', fileService)
    this.registerService('config', configService)
    this.registerService('log', logService)
    this.registerService('error', errorService)
    this.registerService('aiModel', aiModelService)
    this.registerService('watermarkDetection', watermarkDetectionService)
    this.registerService('imageInpainting', imageInpaintingService)
    this.registerService('aiWorkflow', aiWorkflowService)
    this.registerService('batchProcessing', batchProcessingManager)
  }

  /**
   * 注册服务
   */
  registerService(name: string, service: any): void {
    this.services.set(name, service)
    this.serviceStatus.set(name, {
      name,
      status: 'stopped'  // 初始状态为 stopped，等待初始化
    })

    console.log(`[ServiceManager] Service registered: ${name}`)
  }

  /**
   * 初始化所有服务
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.warn('[ServiceManager] Already initialized, skipping')
      return
    }

    console.log('[ServiceManager] Starting initialization...')

    try {
      // 按依赖顺序初始化服务
      console.log('[ServiceManager] Initializing core services...')
      await this.initializeService('log')
      await this.initializeService('error')
      await this.initializeService('config')
      await this.initializeService('file')

      // 现在可以安全使用 logService
      logService.info('Core services initialized, continuing with AI services', 'service_manager')

      await this.initializeService('aiModel')
      await this.initializeService('watermarkDetection')
      await this.initializeService('imageInpainting')
      await this.initializeService('aiWorkflow')
      await this.initializeService('batchProcessing')

      this.isInitialized = true

      // 启动健康检查
      if (this.config.enableHealthCheck) {
        this.startHealthCheck()
      }

      // 发布初始化完成事件
      eventBus.emit('services:initialized', undefined)
      logService.info('ServiceManager initialized successfully', 'service_manager')
      console.log('[ServiceManager] All services initialized successfully')

    } catch (error) {
      console.error('[ServiceManager] Failed to initialize:', error)

      // 只有在 logService 已初始化后才使用它
      if (this.serviceStatus.get('log')?.status === 'running') {
        logService.error(
          'Failed to initialize ServiceManager',
          'service_manager',
          undefined,
          error instanceof Error ? error : new Error(String(error))
        )
      }
      throw error
    }
  }

  /**
   * 初始化单个服务
   */
  private async initializeService(name: string): Promise<void> {
    const service = this.services.get(name)
    const status = this.serviceStatus.get(name)

    if (!service || !status) {
      const error = new Error(`Service not found: ${name}`)
      console.error(`[ServiceManager] Service not found: ${name}`)
      throw error
    }

    try {
      status.status = 'initializing'
      status.startTime = Date.now()

      // 如果服务有初始化方法，调用它
      if (typeof service.initialize === 'function') {
        await service.initialize()
      }

      status.status = 'running'
      logService.info(`Service initialized: ${name}`, 'service_manager')

    } catch (error) {
      status.status = 'error'
      status.lastError = error instanceof Error ? error.message : String(error)

      console.error(`[ServiceManager] Failed to initialize service: ${name}`, error)
      logService.error(
        `Failed to initialize service: ${name}`,
        'service_manager',
        { serviceName: name },
        error instanceof Error ? error : new Error(String(error))
      )

      throw error
    }
  }

  /**
   * 停止所有服务
   */
  async shutdown(): Promise<void> {
    logService.info('Shutting down ServiceManager', 'service_manager')

    // 停止健康检查
    this.stopHealthCheck()

    // 按相反顺序停止服务
    const serviceNames = Array.from(this.services.keys()).reverse()
    
    for (const name of serviceNames) {
      await this.stopService(name)
    }

    this.isInitialized = false
    eventBus.emit('services:shutdown', undefined)
    logService.info('ServiceManager shutdown completed', 'service_manager')
  }

  /**
   * 停止单个服务
   */
  private async stopService(name: string): Promise<void> {
    const service = this.services.get(name)
    const status = this.serviceStatus.get(name)

    if (!service || !status) {
      return
    }

    try {
      // 如果服务有停止方法，调用它
      if (typeof service.shutdown === 'function') {
        await service.shutdown()
      }

      status.status = 'stopped'
      logService.info(`Service stopped: ${name}`, 'service_manager')

    } catch (error) {
      status.status = 'error'
      status.lastError = error instanceof Error ? error.message : String(error)
      
      logService.error(
        `Failed to stop service: ${name}`,
        'service_manager',
        { serviceName: name },
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * 重启服务
   */
  async restartService(name: string): Promise<void> {
    logService.info(`Restarting service: ${name}`, 'service_manager')
    
    await this.stopService(name)
    await this.initializeService(name)
    
    logService.info(`Service restarted: ${name}`, 'service_manager')
  }

  /**
   * 获取服务
   */
  getService<T = any>(name: string): T | undefined {
    return this.services.get(name)
  }

  /**
   * 获取服务状态
   */
  getServiceStatus(name: string): ServiceStatus | undefined {
    const status = this.serviceStatus.get(name)
    if (status && status.startTime) {
      status.uptime = Date.now() - status.startTime
    }
    return status
  }

  /**
   * 获取所有服务状态
   */
  getAllServiceStatus(): ServiceStatus[] {
    return Array.from(this.serviceStatus.values()).map(status => {
      if (status.startTime) {
        status.uptime = Date.now() - status.startTime
      }
      return status
    })
  }

  /**
   * 检查服务健康状态
   */
  private async checkServiceHealth(): Promise<void> {
    for (const [name, service] of this.services.entries()) {
      const status = this.serviceStatus.get(name)
      if (!status) continue

      try {
        // 如果服务有健康检查方法，调用它
        if (typeof service.healthCheck === 'function') {
          const isHealthy = await service.healthCheck()
          if (!isHealthy && status.status === 'running') {
            status.status = 'error'
            status.lastError = 'Health check failed'
            logService.warn(`Service health check failed: ${name}`, 'service_manager')
          }
        }
      } catch (error) {
        if (status.status === 'running') {
          status.status = 'error'
          status.lastError = error instanceof Error ? error.message : String(error)
          logService.error(
            `Service health check error: ${name}`,
            'service_manager',
            undefined,
            error instanceof Error ? error : new Error(String(error))
          )
        }
      }
    }
  }

  /**
   * 启动健康检查
   */
  private startHealthCheck(): void {
    if (this.healthCheckTimer) {
      return
    }

    this.healthCheckTimer = window.setInterval(() => {
      this.checkServiceHealth()
    }, this.config.healthCheckInterval)

    logService.info('Health check started', 'service_manager')
  }

  /**
   * 停止健康检查
   */
  private stopHealthCheck(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer)
      this.healthCheckTimer = undefined
      logService.info('Health check stopped', 'service_manager')
    }
  }

  /**
   * 获取服务统计信息
   */
  getStats(): {
    total: number
    running: number
    stopped: number
    error: number
    uptime: number
  } {
    const statuses = Array.from(this.serviceStatus.values())
    const now = Date.now()
    
    return {
      total: statuses.length,
      running: statuses.filter(s => s.status === 'running').length,
      stopped: statuses.filter(s => s.status === 'stopped').length,
      error: statuses.filter(s => s.status === 'error').length,
      uptime: this.isInitialized ? now - (Math.min(...statuses.map(s => s.startTime || now))) : 0
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized && Array.from(this.serviceStatus.values())
      .every(status => status.status === 'running')
  }
}

// 创建全局服务管理器实例
export const serviceManager = new ServiceManager()

// 便捷的服务访问器
export const services = {
  file: () => serviceManager.getService<FileService>('file'),
  config: () => serviceManager.getService<ConfigService>('config'),
  log: () => serviceManager.getService<LogService>('log'),
  error: () => serviceManager.getService<ErrorService>('error'),
  aiModel: () => serviceManager.getService<AIModelService>('aiModel'),
  watermarkDetection: () => serviceManager.getService<WatermarkDetectionService>('watermarkDetection'),
  imageInpainting: () => serviceManager.getService<ImageInpaintingService>('imageInpainting'),
  aiWorkflow: () => serviceManager.getService<AIWorkflowService>('aiWorkflow'),
  batchProcessing: () => serviceManager.getService<BatchProcessingManager>('batchProcessing')
}

export default serviceManager
