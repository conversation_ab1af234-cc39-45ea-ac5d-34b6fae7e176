import '@testing-library/jest-dom'
import { vi, beforeEach } from 'vitest'

// Mock Tauri API
const mockInvoke = vi.fn()
const mockListen = vi.fn()

vi.mock('@tauri-apps/api/core', () => ({
  invoke: mockInvoke
}))

vi.mock('@tauri-apps/api/event', () => ({
  listen: mockListen
}))

// Global test utilities
declare global {
  var mockTauriInvoke: typeof mockInvoke
  var mockTauriListen: typeof mockListen
}

globalThis.mockTauriInvoke = mockInvoke
globalThis.mockTauriListen = mockListen

// Reset mocks before each test
beforeEach(() => {
  mockInvoke.mockClear()
  mockListen.mockClear()
})
