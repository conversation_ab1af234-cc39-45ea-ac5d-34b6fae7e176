// 文件相关类型
export interface FileItem {
  id: string
  name: string
  path: string
  size: number
  type: string
  dimensions?: {
    width: number
    height: number
  }
  thumbnail?: string
  status: FileStatus
  processedPath?: string
  error?: string
  progress?: number // 处理进度 0-100
  taskId?: string // 关联的任务ID
  processingStartTime?: Date // 处理开始时间
  processingEndTime?: Date // 处理结束时间
  detectionCount?: number // 检测到的水印数量
  avgConfidence?: number // 平均置信度
}

export enum FileStatus {
  Ready = 'ready',
  Processing = 'processing',
  Completed = 'completed',
  Failed = 'failed'
}

// 处理任务相关类型
export interface ProcessingTask {
  id: string
  files: string[]
  status: TaskStatus
  progress: number
  result?: ProcessingResult
  error?: string
  createdAt: Date
  completedAt?: Date
}

export enum TaskStatus {
  Pending = 'pending',
  Processing = 'processing',
  Completed = 'completed',
  Failed = 'failed'
}

export interface ProcessingResult {
  processedFiles: ProcessedFile[]
  totalTime: number
  successCount: number
  failureCount: number
}

export interface ProcessedFile {
  inputPath: string
  outputPath: string
  success: boolean
  error?: string
  processingTime?: number
}

// 设置相关类型
export interface AppSettings {
  ai: AISettings
  ui: UISettings
  performance: PerformanceSettings
  output: OutputSettings
}

export interface AISettings {
  confidence_threshold: number
  enhance_mask: boolean
  use_smart_enhancement: boolean
  context_expansion_ratio: number
}

export interface UISettings {
  theme: 'light' | 'dark' | 'system'
  language: string
  showThumbnails: boolean
  compactMode: boolean
}

export interface PerformanceSettings {
  useGPU: boolean
  maxConcurrentTasks: number
  memoryLimit: number
}

export interface OutputSettings {
  defaultPath: string
  keepOriginalName: boolean
  outputFormat: 'same' | 'jpg' | 'png'
  quality: number
}

// API 相关类型
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface ProcessingRequest {
  files: string[]
  settings: ProcessingSettings
}

export interface ProcessingSettings {
  confidence_threshold: number
  enhance_mask: boolean
  use_smart_enhancement: boolean
  context_expansion_ratio: number
  output_directory?: string
  keep_original_name: boolean
}

// UI 组件相关类型
export interface DropZoneProps {
  onFilesSelected: (files: File[]) => void
  accept?: string[]
  maxFiles?: number
  disabled?: boolean
}

export interface FileListProps {
  files: FileItem[]
  selectedFile?: FileItem
  onFileSelect: (file: FileItem) => void
  onFileRemove: (file: FileItem) => void
  onClearAll: () => void
}

export interface PreviewPanelProps {
  selectedFile?: FileItem
  showComparison?: boolean
}

// 事件相关类型
export interface ProcessingEvent {
  taskId: string
  type: 'started' | 'progress' | 'completed' | 'failed'
  data?: any
}

export interface FileDropEvent {
  files: File[]
  position: { x: number; y: number }
}

// 错误相关类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: Date
}

export enum ErrorCode {
  FileNotFound = 'FILE_NOT_FOUND',
  InvalidFormat = 'INVALID_FORMAT',
  ProcessingFailed = 'PROCESSING_FAILED',
  NetworkError = 'NETWORK_ERROR',
  PermissionDenied = 'PERMISSION_DENIED',
  InsufficientMemory = 'INSUFFICIENT_MEMORY'
}

// 通知相关类型
export interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  duration?: number
  actions?: NotificationAction[]
}

export interface NotificationAction {
  label: string
  action: () => void
}

// 统计相关类型
export interface ProcessingStats {
  totalFiles: number
  processedFiles: number
  successfulFiles: number
  failedFiles: number
  totalTime: number
  averageTime: number
}

// 应用状态类型
export interface AppState {
  isInitialized: boolean
  isProcessing: boolean
  currentTask?: ProcessingTask
  selectedFile?: FileItem
  notifications: Notification[]
  errors: AppError[]
}
