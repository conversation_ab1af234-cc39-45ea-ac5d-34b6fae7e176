"""
工具模块包
包含日志配置、错误处理等工具函数
"""

from .logging_config import (
    setup_logging,
    get_logger,
    log_performance,
    LoggingContext,
    setup_request_logging,
    init_default_logging
)

from .error_handling import (
    ErrorCode,
    WatermarkRemovalError,
    ModelError,
    FileError,
    TaskError,
    ProcessingError,
    create_error_response,
    setup_error_handlers,
    safe_execute,
    safe_execute_async,
    validate_file_size,
    validate_image_format
)

from .performance import (
    MemoryInfo,
    GPUMemoryInfo,
    PerformanceMonitor,
    MemoryManager,
    ModelCache,
    memory_monitor,
    performance_monitor,
    memory_manager,
    model_cache,
    log_system_info,
    get_performance_stats
)

__all__ = [
    # 日志相关
    'setup_logging',
    'get_logger',
    'log_performance',
    'LoggingContext',
    'setup_request_logging',
    'init_default_logging',

    # 错误处理相关
    'ErrorCode',
    'WatermarkRemovalError',
    'ModelError',
    'FileError',
    'TaskError',
    'ProcessingError',
    'create_error_response',
    'setup_error_handlers',
    'safe_execute',
    'safe_execute_async',
    'validate_file_size',
    'validate_image_format',

    # 性能监控相关
    'MemoryInfo',
    'GPUMemoryInfo',
    'PerformanceMonitor',
    'MemoryManager',
    'ModelCache',
    'memory_monitor',
    'performance_monitor',
    'memory_manager',
    'model_cache',
    'log_system_info',
    'get_performance_stats'
]
