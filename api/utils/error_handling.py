"""
错误处理模块
提供统一的错误处理和异常管理
"""

import logging
import traceback
from typing import Optional, Dict, Any
from fastapi import HTTPException, Request
from fastapi.responses import JSONResponse
from enum import Enum


class ErrorCode(Enum):
    """错误代码枚举"""
    
    # 通用错误
    INTERNAL_ERROR = "INTERNAL_ERROR"
    INVALID_REQUEST = "INVALID_REQUEST"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    
    # 文件相关错误
    FILE_TOO_LARGE = "FILE_TOO_LARGE"
    INVALID_FILE_FORMAT = "INVALID_FILE_FORMAT"
    FILE_DOWNLOAD_FAILED = "FILE_DOWNLOAD_FAILED"
    FILE_PROCESSING_FAILED = "FILE_PROCESSING_FAILED"
    
    # 模型相关错误
    MODEL_NOT_LOADED = "MODEL_NOT_LOADED"
    MODEL_LOADING_FAILED = "MODEL_LOADING_FAILED"
    INFERENCE_FAILED = "INFERENCE_FAILED"
    
    # 任务相关错误
    TASK_NOT_FOUND = "TASK_NOT_FOUND"
    TASK_CREATION_FAILED = "TASK_CREATION_FAILED"
    TASK_PROCESSING_FAILED = "TASK_PROCESSING_FAILED"
    
    # 水印处理错误
    WATERMARK_DETECTION_FAILED = "WATERMARK_DETECTION_FAILED"
    WATERMARK_REMOVAL_FAILED = "WATERMARK_REMOVAL_FAILED"
    MASK_PROCESSING_FAILED = "MASK_PROCESSING_FAILED"


class WatermarkRemovalError(Exception):
    """水印去除相关异常基类"""
    
    def __init__(
        self, 
        message: str, 
        error_code: ErrorCode = ErrorCode.INTERNAL_ERROR,
        details: Optional[Dict[str, Any]] = None
    ):
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        super().__init__(self.message)


class ModelError(WatermarkRemovalError):
    """模型相关错误"""
    pass


class FileError(WatermarkRemovalError):
    """文件相关错误"""
    pass


class TaskError(WatermarkRemovalError):
    """任务相关错误"""
    pass


class ProcessingError(WatermarkRemovalError):
    """处理相关错误"""
    pass


def create_error_response(
    error: Exception,
    status_code: int = 500,
    include_traceback: bool = False
) -> JSONResponse:
    """
    创建统一的错误响应
    
    Args:
        error: 异常对象
        status_code: HTTP状态码
        include_traceback: 是否包含堆栈跟踪
        
    Returns:
        JSON错误响应
    """
    logger = logging.getLogger(__name__)
    
    # 基础错误信息
    error_data = {
        "success": False,
        "error": {
            "type": type(error).__name__,
            "message": str(error)
        }
    }
    
    # 如果是自定义错误，添加错误代码和详细信息
    if isinstance(error, WatermarkRemovalError):
        error_data["error"]["code"] = error.error_code.value
        if error.details:
            error_data["error"]["details"] = error.details
    
    # 添加堆栈跟踪（仅在调试模式下）
    if include_traceback:
        error_data["error"]["traceback"] = traceback.format_exc()
    
    # 记录错误日志
    logger.error(
        f"错误响应: {error_data['error']['type']} - {error_data['error']['message']}",
        exc_info=True
    )
    
    return JSONResponse(
        status_code=status_code,
        content=error_data
    )


def setup_error_handlers(app):
    """
    设置全局错误处理器
    
    Args:
        app: FastAPI应用实例
    """
    
    @app.exception_handler(WatermarkRemovalError)
    async def watermark_error_handler(request: Request, exc: WatermarkRemovalError):
        """处理水印去除相关错误"""
        status_code = 400
        
        # 根据错误类型确定状态码
        if isinstance(exc, ModelError):
            status_code = 503  # Service Unavailable
        elif isinstance(exc, FileError):
            status_code = 400  # Bad Request
        elif isinstance(exc, TaskError):
            if exc.error_code == ErrorCode.TASK_NOT_FOUND:
                status_code = 404  # Not Found
            else:
                status_code = 400  # Bad Request
        elif isinstance(exc, ProcessingError):
            status_code = 422  # Unprocessable Entity
        
        return create_error_response(exc, status_code)
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """处理HTTP异常"""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "success": False,
                "error": {
                    "type": "HTTPException",
                    "message": exc.detail,
                    "status_code": exc.status_code
                }
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """处理通用异常"""
        logger = logging.getLogger(__name__)
        logger.error(f"未处理的异常: {type(exc).__name__} - {str(exc)}", exc_info=True)
        
        return create_error_response(
            exc, 
            status_code=500,
            include_traceback=False  # 生产环境不暴露堆栈跟踪
        )


def safe_execute(func, *args, error_message: str = "操作失败", **kwargs):
    """
    安全执行函数，捕获异常并转换为自定义错误
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        error_message: 错误消息
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果
        
    Raises:
        WatermarkRemovalError: 执行失败时抛出
    """
    try:
        return func(*args, **kwargs)
    except WatermarkRemovalError:
        # 重新抛出自定义错误
        raise
    except Exception as e:
        # 转换为自定义错误
        raise WatermarkRemovalError(
            message=f"{error_message}: {str(e)}",
            error_code=ErrorCode.INTERNAL_ERROR,
            details={"original_error": str(e)}
        ) from e


async def safe_execute_async(func, *args, error_message: str = "异步操作失败", **kwargs):
    """
    安全执行异步函数
    
    Args:
        func: 要执行的异步函数
        *args: 函数参数
        error_message: 错误消息
        **kwargs: 函数关键字参数
        
    Returns:
        函数执行结果
        
    Raises:
        WatermarkRemovalError: 执行失败时抛出
    """
    try:
        return await func(*args, **kwargs)
    except WatermarkRemovalError:
        # 重新抛出自定义错误
        raise
    except Exception as e:
        # 转换为自定义错误
        raise WatermarkRemovalError(
            message=f"{error_message}: {str(e)}",
            error_code=ErrorCode.INTERNAL_ERROR,
            details={"original_error": str(e)}
        ) from e


def validate_file_size(file_size: int, max_size: int):
    """
    验证文件大小
    
    Args:
        file_size: 文件大小（字节）
        max_size: 最大允许大小（字节）
        
    Raises:
        FileError: 文件过大时抛出
    """
    if file_size > max_size:
        raise FileError(
            message=f"文件大小 {file_size / 1024 / 1024:.1f}MB 超过限制 {max_size / 1024 / 1024:.1f}MB",
            error_code=ErrorCode.FILE_TOO_LARGE,
            details={
                "file_size": file_size,
                "max_size": max_size
            }
        )


def validate_image_format(file_extension: str, allowed_extensions: set):
    """
    验证图像格式
    
    Args:
        file_extension: 文件扩展名
        allowed_extensions: 允许的扩展名集合
        
    Raises:
        FileError: 格式不支持时抛出
    """
    if file_extension.lower() not in allowed_extensions:
        raise FileError(
            message=f"不支持的文件格式: {file_extension}",
            error_code=ErrorCode.INVALID_FILE_FORMAT,
            details={
                "file_extension": file_extension,
                "allowed_extensions": list(allowed_extensions)
            }
        )
