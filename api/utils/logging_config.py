"""
日志配置模块
提供统一的日志配置和管理
"""

import logging
import logging.handlers
import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional


class ColoredFormatter(logging.Formatter):
    """彩色日志格式化器"""
    
    # 颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.COLORS['RESET']}"
        
        return super().format(record)


def setup_logging(
    log_level: str = "INFO",
    log_dir: Optional[str] = None,
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    enable_console: bool = True,
    enable_file: bool = True
) -> logging.Logger:
    """
    设置日志配置
    
    Args:
        log_level: 日志级别
        log_dir: 日志文件目录
        max_file_size: 单个日志文件最大大小
        backup_count: 备份文件数量
        enable_console: 是否启用控制台输出
        enable_file: 是否启用文件输出
        
    Returns:
        配置好的logger
    """
    # 创建根logger
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 日志格式
    detailed_format = (
        "%(asctime)s - %(name)s - %(levelname)s - "
        "%(filename)s:%(lineno)d - %(funcName)s() - %(message)s"
    )
    simple_format = "%(asctime)s - %(levelname)s - %(message)s"
    
    # 控制台处理器
    if enable_console:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(getattr(logging, log_level.upper()))
        
        # 使用彩色格式化器
        console_formatter = ColoredFormatter(simple_format)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if enable_file:
        if log_dir is None:
            log_dir = "logs"
        
        # 创建日志目录
        Path(log_dir).mkdir(exist_ok=True)
        
        # 应用日志文件
        app_log_file = Path(log_dir) / "app.log"
        app_handler = logging.handlers.RotatingFileHandler(
            app_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        app_handler.setLevel(logging.INFO)
        app_formatter = logging.Formatter(detailed_format)
        app_handler.setFormatter(app_formatter)
        logger.addHandler(app_handler)
        
        # 错误日志文件
        error_log_file = Path(log_dir) / "error.log"
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_formatter = logging.Formatter(detailed_format)
        error_handler.setFormatter(error_formatter)
        logger.addHandler(error_handler)
        
        # 性能日志文件
        perf_log_file = Path(log_dir) / "performance.log"
        perf_handler = logging.handlers.RotatingFileHandler(
            perf_log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        perf_handler.setLevel(logging.INFO)
        perf_formatter = logging.Formatter(
            "%(asctime)s - PERF - %(message)s"
        )
        perf_handler.setFormatter(perf_formatter)
        
        # 创建性能logger
        perf_logger = logging.getLogger('performance')
        perf_logger.addHandler(perf_handler)
        perf_logger.setLevel(logging.INFO)
        perf_logger.propagate = False
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """获取指定名称的logger"""
    return logging.getLogger(name)


def log_performance(operation: str, duration: float, **kwargs):
    """记录性能日志"""
    perf_logger = logging.getLogger('performance')
    
    extra_info = ""
    if kwargs:
        extra_info = " - " + " - ".join([f"{k}: {v}" for k, v in kwargs.items()])
    
    perf_logger.info(f"{operation} - Duration: {duration:.3f}s{extra_info}")


class LoggingContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: logging.Logger, operation: str, level: int = logging.INFO):
        self.logger = logger
        self.operation = operation
        self.level = level
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        self.logger.log(self.level, f"开始 {self.operation}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = (datetime.now() - self.start_time).total_seconds()
        
        if exc_type is None:
            self.logger.log(self.level, f"完成 {self.operation} - 耗时: {duration:.3f}s")
            log_performance(self.operation, duration)
        else:
            self.logger.error(f"失败 {self.operation} - 耗时: {duration:.3f}s - 错误: {exc_val}")


def setup_request_logging():
    """设置请求日志中间件"""
    import time
    from fastapi import Request, Response
    from starlette.middleware.base import BaseHTTPMiddleware
    
    class RequestLoggingMiddleware(BaseHTTPMiddleware):
        async def dispatch(self, request: Request, call_next):
            start_time = time.time()
            
            # 记录请求开始
            logger = get_logger("request")
            logger.info(f"请求开始: {request.method} {request.url}")
            
            try:
                response = await call_next(request)
                
                # 记录请求完成
                duration = time.time() - start_time
                logger.info(
                    f"请求完成: {request.method} {request.url} - "
                    f"状态码: {response.status_code} - 耗时: {duration:.3f}s"
                )
                
                # 记录性能
                log_performance(
                    f"HTTP {request.method} {request.url.path}",
                    duration,
                    status_code=response.status_code,
                    client_ip=request.client.host if request.client else "unknown"
                )
                
                return response
                
            except Exception as e:
                duration = time.time() - start_time
                logger.error(
                    f"请求失败: {request.method} {request.url} - "
                    f"错误: {str(e)} - 耗时: {duration:.3f}s"
                )
                raise
    
    return RequestLoggingMiddleware


# 默认日志配置
def init_default_logging():
    """初始化默认日志配置"""
    log_level = os.getenv("LOG_LEVEL", "INFO")
    log_dir = os.getenv("LOG_DIR", "logs")
    
    return setup_logging(
        log_level=log_level,
        log_dir=log_dir,
        enable_console=True,
        enable_file=True
    )
