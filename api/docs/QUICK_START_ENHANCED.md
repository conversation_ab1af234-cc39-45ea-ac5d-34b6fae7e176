# 🚀 增强功能快速开始指南

本指南将帮助您快速上手水印检测和移除系统的新增强功能。

## 📋 前提条件

确保您已经完成基本系统的安装和配置：

```bash
# 安装依赖
pip install -r requirements.txt

# 确保模型文件已下载
python -c "from models import WatermarkDetector; WatermarkDetector()"
```

## 🎯 5分钟快速体验

### 1. 准备测试图像

将您的测试图像放在项目目录中，例如 `test_image.jpg`

### 2. 运行完整分析

```bash
python examples/enhanced_watermark_analysis.py --image test_image.jpg --output ./quick_results
```

### 3. 查看结果

分析完成后，您将在 `./quick_results` 目录中看到：

```
quick_results/
├── visualization/          # 可视化结果
│   ├── detection_boxes.jpg
│   ├── mask_overlay.jpg
│   ├── detection_summary.jpg
│   └── side_by_side.jpg
├── validation/             # 验证结果
│   └── single_*/
│       ├── original.jpg
│       ├── restored.jpg
│       ├── comparison.jpg
│       └── metrics.json
└── debugging/              # 调试结果
    ├── detection_debug/
    ├── inpainting_debug/
    └── pipeline_debug/
```

## 🔍 功能详解

### 可视化功能

#### 基本使用
```python
from models import WatermarkRemovalPipeline

# 初始化管道
pipeline = WatermarkRemovalPipeline()

# 创建可视化
result = pipeline.create_detection_visualization(
    "your_image.jpg",
    confidence_threshold=0.5,
    save_path="./visualizations"
)

print(f"检测到 {result['watermark_count']} 个水印")
print(f"平均置信度: {result['average_confidence']:.3f}")
```

#### 高级可视化
```python
from models import WatermarkVisualization

visualizer = WatermarkVisualization()

# 自定义检测框
detection_image = visualizer.draw_detection_boxes(
    image, 
    detection_info,
    show_confidence=True,
    box_thickness=5
)

# 自定义掩码叠加
mask_image = visualizer.create_mask_overlay(
    image, 
    mask,
    alpha=0.6,
    mask_color=(0, 255, 0)  # 绿色掩码
)
```

### 验证功能

#### 单图像验证
```python
from models import LamaValidationFramework, WatermarkDetector, LamaInpainter

# 初始化组件
detector = WatermarkDetector()
inpainter = LamaInpainter()
validator = LamaValidationFramework(detector, inpainter)

# 执行验证
result = validator.validate_single_watermark_removal(
    "test_image.jpg",
    confidence_threshold=0.5,
    save_results=True,
    output_dir="./validation_results"
)

if result.success:
    print(f"SSIM分数: {result.metrics.ssim_score:.3f}")
    print(f"PSNR分数: {result.metrics.psnr_score:.3f}")
    print(f"处理时间: {result.metrics.processing_time:.3f}秒")
else:
    print(f"验证失败: {result.error_message}")
```

#### 批量验证
```python
# 准备图像列表
image_paths = ["img1.jpg", "img2.jpg", "img3.jpg"]

# 批量验证
batch_result = validator.run_batch_validation(
    image_paths,
    confidence_threshold=0.5,
    save_results=True,
    output_dir="./batch_results"
)

# 查看统计结果
print(f"成功: {batch_result['successful_validations']}")
print(f"失败: {batch_result['failed_validations']}")
print(f"平均SSIM: {batch_result['average_metrics'].ssim_score:.3f}")
```

### 调试功能

#### 完整管道调试
```python
from models import WatermarkDebuggingTool

# 初始化调试工具
debugger = WatermarkDebuggingTool(detector, inpainter)

# 执行调试
debug_result = debugger.debug_full_pipeline(
    "test_image.jpg",
    confidence_threshold=0.5,
    save_debug_results=True,
    output_dir="./debug_results"
)

# 查看调试信息
print(f"检测时间: {debug_result.detection_debug.detection_time:.3f}秒")
print(f"修复时间: {debug_result.inpainting_debug.inpainting_time:.3f}秒")
print(f"性能瓶颈: {debug_result.bottleneck_stage}")

# 查看建议
for recommendation in debug_result.recommendations:
    print(f"建议: {recommendation}")
```

#### 分阶段调试
```python
# 只调试检测阶段
detection_debug = debugger.debug_detection_stage(
    "test_image.jpg",
    save_debug_images=True,
    output_dir="./detection_debug"
)

print(f"掩码质量: {detection_debug.mask_quality_score:.3f}")
print(f"假阳性风险: {detection_debug.false_positive_likelihood:.3f}")

# 只调试修复阶段（需要先获取掩码）
mask, _, _ = detector.detect("test_image.jpg")
inpainting_debug = debugger.debug_inpainting_stage(
    "test_image.jpg",
    mask,
    save_debug_images=True,
    output_dir="./inpainting_debug"
)

print(f"修复方法: {inpainting_debug.inpainting_method}")
print(f"掩码覆盖率: {inpainting_debug.mask_coverage_ratio:.3f}")
```

## 🎛️ 常用参数配置

### 检测参数
```python
# 调整检测敏感度
result = pipeline.create_detection_visualization(
    "image.jpg",
    confidence_threshold=0.3,  # 降低阈值，检测更多可能的水印
    save_path="./sensitive_detection"
)

# 严格检测
result = pipeline.create_detection_visualization(
    "image.jpg",
    confidence_threshold=0.8,  # 提高阈值，只检测高置信度水印
    save_path="./strict_detection"
)
```

### 可视化参数
```python
# 高对比度可视化
visualizer = WatermarkVisualization(font_size=30)
result = visualizer.draw_detection_boxes(
    image,
    detection_info,
    show_confidence=True,
    box_thickness=8
)

# 柔和掩码叠加
mask_overlay = visualizer.create_mask_overlay(
    image,
    mask,
    alpha=0.3,  # 更透明
    mask_color=(255, 255, 0)  # 黄色
)
```

## 📊 结果解读

### 验证指标含义

- **SSIM (0-1)**: 结构相似性，越接近1越好
- **PSNR (dB)**: 峰值信噪比，数值越大越好
- **MSE/MAE**: 误差指标，数值越小越好
- **水印移除完整性 (0-1)**: 水印移除的彻底程度
- **视觉质量分数 (0-1)**: 修复区域的自然度

### 调试信息解读

#### 性能瓶颈
- `detection`: 检测阶段耗时较长
- `inpainting`: 修复阶段耗时较长
- `balanced`: 两阶段耗时相当

#### 常见建议
- "检测可能存在假阳性": 降低置信度阈值或检查图像质量
- "修复质量较低": 检查掩码准确性或调整修复参数
- "处理时间过长": 考虑使用GPU加速或优化图像大小

## 🔧 故障排除

### 常见问题

#### 1. 内存不足
```python
# 处理大图像时，先调整大小
from PIL import Image

image = Image.open("large_image.jpg")
if max(image.size) > 1024:
    image.thumbnail((1024, 1024), Image.LANCZOS)
    image.save("resized_image.jpg")
```

#### 2. 字体加载失败
```python
# 手动指定字体路径
visualizer = WatermarkVisualization()
# 系统会自动回退到默认字体
```

#### 3. 模型加载失败
```python
# 检查模型是否正确下载
try:
    detector = WatermarkDetector()
    print("检测器加载成功")
except Exception as e:
    print(f"检测器加载失败: {e}")
```

### 性能优化

#### 1. 批量处理优化
```python
# 分批处理大量图像
def process_in_batches(image_paths, batch_size=10):
    for i in range(0, len(image_paths), batch_size):
        batch = image_paths[i:i+batch_size]
        result = validator.run_batch_validation(batch)
        yield result
```

#### 2. 内存管理
```python
import gc

# 处理完每个图像后清理内存
for image_path in image_paths:
    result = validator.validate_single_watermark_removal(image_path)
    # 处理结果...
    gc.collect()  # 强制垃圾回收
```

## 📝 下一步

1. **深入学习**: 阅读 [增强功能文档](ENHANCED_FEATURES.md) 了解更多细节
2. **自定义开发**: 基于提供的API开发自己的分析工具
3. **性能调优**: 根据调试结果优化您的处理流程
4. **批量处理**: 使用批量验证功能评估大量图像

## 💡 提示

- 首次运行可能需要下载模型文件，请耐心等待
- 建议在GPU环境下运行以获得更好的性能
- 保存的结果文件可以用于后续分析和比较
- 定期清理输出目录以节省磁盘空间
