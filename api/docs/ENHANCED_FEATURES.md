# 🔍 增强功能文档

本文档介绍水印检测和移除系统的增强功能，包括可视化、验证和调试工具。

## 📋 目录

- [可视化功能](#可视化功能)
- [验证框架](#验证框架)
- [调试工具](#调试工具)
- [使用示例](#使用示例)
- [API参考](#api参考)

## 🎨 可视化功能

### 概述

新的可视化系统提供了全面的水印检测结果展示功能，帮助用户直观地理解检测过程和结果。

### 主要特性

#### 1. 检测框可视化
- 在原图上绘制水印检测框
- 显示每个检测框的置信度分数
- 支持多个水印的不同颜色标识
- 可自定义框线粗细和颜色

#### 2. 掩码叠加可视化
- 将检测掩码以半透明方式叠加在原图上
- 支持自定义透明度和掩码颜色
- 清晰显示水印区域的精确位置

#### 3. 并排对比
- 创建原图与处理结果的并排对比图
- 支持自定义标题和布局
- 便于评估处理效果

#### 4. 检测摘要
- 综合展示检测框、掩码和统计信息
- 包含检测数量、置信度统计、处理时间等
- 一图了解完整检测结果

### 使用方法

```python
from models import WatermarkVisualization, WatermarkRemovalPipeline

# 初始化
pipeline = WatermarkRemovalPipeline()
visualizer = WatermarkVisualization()

# 创建检测可视化
vis_result = pipeline.create_detection_visualization(
    image_path="input.jpg",
    confidence_threshold=0.5,
    save_path="./visualizations"
)

# 手动创建特定可视化
detection_boxes = visualizer.draw_detection_boxes(
    image, detection_info, show_confidence=True
)
mask_overlay = visualizer.create_mask_overlay(
    image, mask, alpha=0.4
)
```

## 🔬 验证框架

### 概述

LAMA模型验证框架提供了全面的性能评估工具，用于测试和验证水印移除的准确性和质量。

### 主要特性

#### 1. 单个水印验证
- 测试单个水印的检测和移除效果
- 计算多种质量指标（SSIM、PSNR、MSE、MAE）
- 评估水印移除完整性和视觉质量

#### 2. 多个水印验证
- 专门测试多水印场景下的处理能力
- 验证模型在复杂情况下的表现
- 分析不同水印之间的相互影响

#### 3. 批量验证
- 支持大量图像的批量处理和评估
- 生成统计报告和平均性能指标
- 便于模型性能的全面评估

#### 4. 详细指标计算
- **SSIM**: 结构相似性指数
- **PSNR**: 峰值信噪比
- **MSE/MAE**: 均方误差/平均绝对误差
- **水印移除完整性**: 评估水印移除的彻底程度
- **视觉质量分数**: 评估修复区域的视觉自然度

### 使用方法

```python
from models import LamaValidationFramework

# 初始化验证框架
validator = LamaValidationFramework(detector, inpainter, visualizer)

# 单个水印验证
result = validator.validate_single_watermark_removal(
    image_path="test_image.jpg",
    confidence_threshold=0.5,
    save_results=True,
    output_dir="./validation_results"
)

# 批量验证
batch_result = validator.run_batch_validation(
    image_paths=["img1.jpg", "img2.jpg", "img3.jpg"],
    confidence_threshold=0.5,
    save_results=True,
    output_dir="./batch_validation"
)
```

## 🛠️ 调试工具

### 概述

调试工具提供了深入的性能分析和问题诊断功能，帮助识别和解决检测与修复过程中的问题。

### 主要特性

#### 1. 检测阶段调试
- 详细的时间分析（预处理、推理、后处理）
- 掩码质量评估
- 假阳性风险分析
- 检测参数优化建议

#### 2. 修复阶段调试
- 修复时间分析
- 质量指标计算（颜色一致性、纹理一致性、边界平滑度）
- 掩码覆盖率分析
- 修复方法识别

#### 3. 完整管道调试
- 端到端性能分析
- 瓶颈识别（检测 vs 修复）
- 自动生成优化建议
- 错误分析和分类

#### 4. 可视化调试结果
- 保存调试过程中的中间图像
- 生成差异图像和对比图
- 创建详细的调试报告

### 使用方法

```python
from models import WatermarkDebuggingTool

# 初始化调试工具
debugger = WatermarkDebuggingTool(detector, inpainter, visualizer, validator)

# 完整管道调试
debug_result = debugger.debug_full_pipeline(
    image_path="test_image.jpg",
    confidence_threshold=0.5,
    save_debug_results=True,
    output_dir="./debug_results"
)

# 单独调试检测阶段
detection_debug = debugger.debug_detection_stage(
    image_path="test_image.jpg",
    save_debug_images=True,
    output_dir="./detection_debug"
)
```

## 📝 使用示例

### 完整分析流程

```python
import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent.parent))

from models import (
    WatermarkRemovalPipeline, WatermarkVisualization,
    LamaValidationFramework, WatermarkDebuggingTool
)

# 1. 初始化所有组件
pipeline = WatermarkRemovalPipeline()
visualizer = WatermarkVisualization()
validator = LamaValidationFramework(
    pipeline.detector, pipeline.inpainter, visualizer
)
debugger = WatermarkDebuggingTool(
    pipeline.detector, pipeline.inpainter, visualizer, validator
)

# 2. 创建可视化
vis_result = pipeline.create_detection_visualization(
    "input_image.jpg",
    save_path="./analysis/visualization"
)

# 3. 执行验证
validation_result = validator.validate_single_watermark_removal(
    "input_image.jpg",
    save_results=True,
    output_dir="./analysis/validation"
)

# 4. 运行调试
debug_result = debugger.debug_full_pipeline(
    "input_image.jpg",
    save_debug_results=True,
    output_dir="./analysis/debugging"
)

# 5. 分析结果
print(f"检测到水印数量: {vis_result['watermark_count']}")
print(f"验证成功: {validation_result.success}")
print(f"SSIM分数: {validation_result.metrics.ssim_score:.3f}")
print(f"性能瓶颈: {debug_result.bottleneck_stage}")
```

### 批量处理示例

```python
# 批量验证多个图像
image_paths = ["img1.jpg", "img2.jpg", "img3.jpg"]

batch_result = validator.run_batch_validation(
    image_paths,
    confidence_threshold=0.5,
    save_results=True,
    output_dir="./batch_analysis"
)

print(f"成功处理: {batch_result['successful_validations']}/{batch_result['total_images']}")
print(f"平均SSIM: {batch_result['average_metrics'].ssim_score:.3f}")
```

## 📚 API参考

### WatermarkVisualization

#### 主要方法

- `draw_detection_boxes(image, detection_info, show_confidence=True, box_thickness=3)`
- `create_mask_overlay(image, mask, alpha=0.5, mask_color=(255,0,0))`
- `create_side_by_side_comparison(original, processed, titles=("原图", "处理后"))`
- `create_detection_summary(image, detection_info, mask, processing_time=None)`

### LamaValidationFramework

#### 主要方法

- `validate_single_watermark_removal(image, reference_image=None, confidence_threshold=0.5, save_results=False, output_dir=None)`
- `validate_multiple_watermark_removal(image, reference_image=None, confidence_threshold=0.5, save_results=False, output_dir=None)`
- `run_batch_validation(image_paths, confidence_threshold=0.5, save_results=False, output_dir=None)`

### WatermarkDebuggingTool

#### 主要方法

- `debug_detection_stage(image, confidence_threshold=0.5, save_debug_images=False, output_dir=None)`
- `debug_inpainting_stage(image, mask, save_debug_images=False, output_dir=None)`
- `debug_full_pipeline(image, confidence_threshold=0.5, save_debug_results=False, output_dir=None)`

### 数据结构

#### ValidationMetrics
```python
@dataclass
class ValidationMetrics:
    ssim_score: float
    psnr_score: float
    mse_score: float
    mae_score: float
    processing_time: float
    watermark_removal_completeness: float
    visual_quality_score: float
```

#### ValidationResult
```python
@dataclass
class ValidationResult:
    image_id: str
    watermark_count: int
    detection_confidence: float
    metrics: ValidationMetrics
    success: bool
    error_message: Optional[str] = None
```

## 🚀 快速开始

1. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **运行示例**
   ```bash
   python examples/enhanced_watermark_analysis.py --image test_image.jpg --output ./results
   ```

3. **查看结果**
   - 可视化结果: `./results/visualization/`
   - 验证结果: `./results/validation/`
   - 调试结果: `./results/debugging/`

## 📊 输出文件说明

### 可视化输出
- `detection_boxes.jpg`: 带检测框的图像
- `mask_overlay.jpg`: 掩码叠加图像
- `detection_summary.jpg`: 检测摘要图像
- `side_by_side.jpg`: 并排对比图像

### 验证输出
- `original.jpg`: 原始图像
- `restored.jpg`: 修复后图像
- `mask.png`: 检测掩码
- `comparison.jpg`: 对比图像
- `metrics.json`: 详细指标数据

### 调试输出
- `detection_debug/`: 检测阶段调试图像
- `inpainting_debug/`: 修复阶段调试图像
- `pipeline_debug/`: 管道调试报告
- `*_debug_report.json`: 详细调试数据

## 🔧 配置选项

### 可视化配置
- `font_size`: 文字大小
- `box_thickness`: 检测框粗细
- `alpha`: 掩码透明度
- `mask_color`: 掩码颜色

### 验证配置
- `confidence_threshold`: 检测置信度阈值
- `save_results`: 是否保存结果
- `output_dir`: 输出目录

### 调试配置
- `save_debug_images`: 是否保存调试图像
- `save_debug_results`: 是否保存调试结果
- `output_dir`: 调试输出目录

## ⚠️ 注意事项

### 性能考虑
- 可视化功能会增加一定的处理时间
- 批量验证可能需要较长时间，建议分批处理
- 调试功能会生成大量中间文件，注意磁盘空间

### 内存使用
- 处理大图像时注意内存使用
- 批量处理时建议控制并发数量
- 及时清理不需要的中间结果

### 兼容性
- 需要Python 3.8+
- 某些功能需要GPU支持
- 字体加载可能因系统而异

## 🤝 贡献

欢迎提交问题报告和功能请求！请查看项目的贡献指南了解更多信息。
