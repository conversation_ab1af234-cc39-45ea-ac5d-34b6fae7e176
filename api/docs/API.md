# API 文档

## 概述

水印去除系统提供了完整的RESTful API接口，支持同步/异步处理、批量操作和系统监控。

## 基础信息

- **Base URL**: `http://localhost:8000`
- **Content-Type**: `application/json`
- **认证**: 暂无（可根据需要添加）

## 核心接口

### 1. 同步水印去除

立即处理单张图片并返回结果。

**请求**
```http
POST /remove-watermark-sync
Content-Type: application/json

{
    "image_url": "https://example.com/watermarked_image.jpg",
    "confidence_threshold": 0.5,
    "enhance_mask": true
}
```

**参数说明**
- `image_url` (string, 必需): 图片URL地址
- `confidence_threshold` (float, 可选): 水印检测置信度阈值，范围0.0-1.0，默认0.3
- `enhance_mask` (boolean, 可选): 是否增强检测掩码，默认true

**响应**
```json
{
    "success": true,
    "message": "水印去除完成",
    "task_id": "sync",
    "result_url": "/outputs/nowatermark_20240129_143022_abc12345.jpg",
    "processing_time": 2.45,
    "detection_confidence": 0.85
}
```

**状态码**
- `200`: 处理成功
- `400`: 请求参数错误
- `422`: 图片处理失败
- `500`: 服务器内部错误

### 2. 异步水印去除

提交处理任务，返回任务ID用于后续查询。

**请求**
```http
POST /remove-watermark
Content-Type: application/json

{
    "image_url": "https://example.com/watermarked_image.jpg",
    "confidence_threshold": 0.5,
    "enhance_mask": true
}
```

**响应**
```json
{
    "success": true,
    "message": "任务已提交，正在处理中...",
    "task_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

### 3. 任务状态查询

查询异步任务的处理状态。

**请求**
```http
GET /task/{task_id}
```

**响应**
```json
{
    "task_id": "550e8400-e29b-41d4-a716-446655440000",
    "status": "completed",
    "progress": 100,
    "message": "水印去除完成",
    "result_url": "/outputs/nowatermark_20240129_143022_abc12345.jpg",
    "created_at": "2024-01-29T14:30:22.123456",
    "completed_at": "2024-01-29T14:30:25.678901"
}
```

**状态说明**
- `pending`: 等待处理
- `processing`: 正在处理
- `completed`: 处理完成
- `failed`: 处理失败

### 4. 删除任务

删除任务记录。

**请求**
```http
DELETE /task/{task_id}
```

**响应**
```json
{
    "message": "任务已删除"
}
```

## 批量处理接口

### 1. 批量水印去除

同时处理多张图片。

**请求**
```http
POST /remove-watermark-batch
Content-Type: application/json

{
    "image_urls": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg",
        "https://example.com/image3.jpg"
    ],
    "confidence_threshold": 0.5,
    "enhance_mask": true
}
```

**响应**
```json
{
    "success": true,
    "message": "批量任务已提交，共 3 张图片",
    "batch_id": "batch_550e8400-e29b-41d4-a716-446655440000",
    "task_ids": [
        "batch_550e8400-e29b-41d4-a716-446655440000_0",
        "batch_550e8400-e29b-41d4-a716-446655440000_1",
        "batch_550e8400-e29b-41d4-a716-446655440000_2"
    ]
}
```

### 2. 批量状态查询

查询批量任务的整体状态。

**请求**
```http
GET /batch/{batch_id}
```

**响应**
```json
{
    "batch_id": "batch_550e8400-e29b-41d4-a716-446655440000",
    "status": "processing",
    "progress": 66,
    "total_tasks": 3,
    "completed_tasks": 2,
    "failed_tasks": 0,
    "processing_tasks": 1,
    "tasks": {
        "batch_550e8400-e29b-41d4-a716-446655440000_0": {
            "task_id": "batch_550e8400-e29b-41d4-a716-446655440000_0",
            "status": "completed",
            "progress": 100,
            "message": "水印去除完成",
            "result_url": "/outputs/nowatermark_20240129_143022_abc12345.jpg",
            "created_at": "2024-01-29T14:30:22.123456",
            "completed_at": "2024-01-29T14:30:25.678901"
        }
    }
}
```

## 监控接口

### 1. 健康检查

检查服务状态和基本信息。

**请求**
```http
GET /health
```

**响应**
```json
{
    "status": "healthy",
    "timestamp": "2024-01-29T14:30:22.123456",
    "device": "cuda",
    "active_tasks": 2,
    "memory": {
        "system_usage_percent": 45.2,
        "gpu_usage_percent": 67.8,
        "needs_cleanup": false
    },
    "model_cache_size": 1
}
```

### 2. 性能统计

获取详细的性能统计信息。

**请求**
```http
GET /performance
```

**响应**
```json
{
    "memory": {
        "system_memory": {
            "total_gb": 16.0,
            "used_gb": 7.2,
            "available_gb": 8.8,
            "usage_percent": 45.2
        },
        "gpu_memory": {
            "total_gb": 8.0,
            "used_gb": 5.4,
            "free_gb": 2.6,
            "usage_percent": 67.8
        },
        "needs_cleanup": false
    },
    "model_cache_size": 1,
    "performance_metrics": {
        "watermark_detection": {
            "count": 15,
            "average": 1.23,
            "recent_average": 1.18
        },
        "image_inpainting": {
            "count": 12,
            "average": 3.45,
            "recent_average": 3.21
        }
    }
}
```

### 3. 内存清理

手动触发内存清理。

**请求**
```http
POST /performance/cleanup
```

**响应**
```json
{
    "success": true,
    "message": "内存清理完成",
    "timestamp": "2024-01-29T14:30:22.123456"
}
```

## 错误处理

### 错误响应格式

```json
{
    "success": false,
    "error": {
        "type": "FileError",
        "code": "FILE_TOO_LARGE",
        "message": "文件大小 52.1MB 超过限制 50.0MB",
        "details": {
            "file_size": 54658048,
            "max_size": 52428800
        }
    }
}
```

### 常见错误代码

- `INVALID_REQUEST`: 请求参数无效
- `FILE_TOO_LARGE`: 文件过大
- `INVALID_FILE_FORMAT`: 文件格式不支持
- `FILE_DOWNLOAD_FAILED`: 文件下载失败
- `MODEL_NOT_LOADED`: 模型未加载
- `WATERMARK_DETECTION_FAILED`: 水印检测失败
- `WATERMARK_REMOVAL_FAILED`: 水印去除失败
- `TASK_NOT_FOUND`: 任务不存在

## 使用示例

### Python客户端

```python
import requests

# 同步处理
response = requests.post("http://localhost:8000/remove-watermark-sync", json={
    "image_url": "https://example.com/watermarked_image.jpg",
    "confidence_threshold": 0.5,
    "enhance_mask": True
})

result = response.json()
if result["success"]:
    print(f"处理完成: {result['result_url']}")
    print(f"处理时间: {result['processing_time']:.2f}秒")
else:
    print(f"处理失败: {result['error']['message']}")
```

### JavaScript客户端

```javascript
// 异步处理
const response = await fetch('http://localhost:8000/remove-watermark', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json',
    },
    body: JSON.stringify({
        image_url: 'https://example.com/watermarked_image.jpg',
        confidence_threshold: 0.5,
        enhance_mask: true
    })
});

const result = await response.json();
if (result.success) {
    console.log(`任务已提交: ${result.task_id}`);
    
    // 轮询任务状态
    const checkStatus = async () => {
        const statusResponse = await fetch(`http://localhost:8000/task/${result.task_id}`);
        const status = await statusResponse.json();
        
        if (status.status === 'completed') {
            console.log(`处理完成: ${status.result_url}`);
        } else if (status.status === 'failed') {
            console.log(`处理失败: ${status.message}`);
        } else {
            setTimeout(checkStatus, 2000); // 2秒后再次检查
        }
    };
    
    checkStatus();
}
```

## 限制说明

- **文件大小**: 最大50MB
- **支持格式**: JPG, JPEG, PNG, WebP
- **并发限制**: 建议不超过10个并发请求
- **超时时间**: 单个任务最长处理时间5分钟
- **文件保留**: 结果文件保留24小时后自动清理
