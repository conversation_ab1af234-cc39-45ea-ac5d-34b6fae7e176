# 水印去除桌面客户端 - 软件架构设计文档

## 1. 设计概述

### 1.1 设计目标
基于现有的水印去除系统，设计一个使用 PySide6 + PyInstaller 的跨平台桌面客户端应用，提供原生桌面体验、离线处理能力和优化的性能表现。采用纯Python技术栈，确保开发效率和维护便利性。

### 1.2 设计原则
- **模块化设计**: 高内聚低耦合的模块划分
- **跨平台兼容**: 统一的代码基础，平台特定优化
- **性能优先**: 内存和CPU使用优化
- **用户体验**: 直观易用的界面设计
- **可扩展性**: 支持功能插件和模型更新
- **安全性**: 本地处理，数据隐私保护
- **简化部署**: 单一可执行文件，简化分发

### 1.3 技术选型理由

#### PySide6框架选择
- **优势**: 成熟稳定、功能丰富、跨平台支持好
- **适用性**: 原生桌面应用开发，性能优秀
- **生态**: Qt生态系统完善，文档齐全
- **开发效率**: Python语言简洁，开发速度快

#### PyInstaller打包选择
- **兼容性**: 支持复杂Python应用打包
- **便利性**: 一键打包，支持多种模式
- **成熟度**: 广泛使用，社区支持好
- **维护性**: 简化部署流程，用户体验好

## 2. 整体架构设计

### 2.1 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                PySide6 桌面客户端应用                        │
├─────────────────────────────────────────────────────────────┤
│                   表示层 (View)                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   主窗口    │ │  批处理窗口  │ │  设置对话框  │ │ 历史窗口 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              PySide6 Widget 组件层                      │ │
│  │    QMainWindow, QDialog, QWidget, QThread等             │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  控制层 (Presenter)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 主窗口控制器 │ │ 任务控制器  │ │ 设置控制器  │ │文件控制器│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Qt 信号槽机制                            │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                   业务层 (Model)                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  任务管理   │ │  文件管理   │ │  配置管理   │ │ 数据存储 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                业务逻辑服务层                           │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                  AI 处理层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ 水印检测器  │ │ 图像修复器  │ │ 掩码处理器  │ │ 工具库  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  AI 模型层                              │ │
│  │         YOLOv11 检测模型    LAMA 修复模型               │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    系统层                                   │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  文件系统   │ │   GPU加速   │ │  系统通知   │ │ 系统集成 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 架构分层说明

#### 2.2.1 表示层 (View)
- **技术栈**: PySide6 + Qt Designer
- **职责**: 用户界面展示、用户交互处理、界面状态管理
- **组件**: 主窗口、批处理窗口、设置对话框、历史记录窗口
- **特性**: 响应式设计、主题支持、国际化

#### 2.2.2 控制层 (Presenter)
- **技术栈**: Python + Qt信号槽机制
- **职责**: 界面逻辑控制、事件处理、数据绑定
- **模块**: 主窗口控制器、任务控制器、设置控制器、文件控制器
- **特性**: 松耦合设计、异步处理支持

#### 2.2.3 业务层 (Model)
- **技术栈**: Python + SQLite
- **职责**: 业务逻辑处理、数据管理、任务调度
- **模块**: 任务管理器、文件管理器、配置管理器、数据存储
- **特性**: 事务处理、数据持久化、缓存机制

#### 2.2.4 AI处理层
- **技术栈**: Python + PyTorch + Ultralytics YOLO + LAMA + PIL
- **核心模型**:
  - **水印检测**: YOLOv11 (yolo11x-train28-best.pt)
  - **图像修复**: LAMA (big-lama/models/best.ckpt)
- **职责**: AI模型推理、图像处理、水印检测与修复
- **复用**: 现有的WatermarkRemovalPipeline完整实现
- **优化**: 多线程处理、GPU加速、内存管理、模型缓存

#### 2.2.5 系统层
- **职责**: 操作系统接口、硬件资源访问、系统集成
- **功能**: 文件系统访问、GPU加速、系统通知、系统托盘

## 3. 技术栈详细设计

### 3.1 PySide6 GUI技术栈

#### 3.1.1 核心框架配置
```python
# 技术栈配置
TECH_STACK = {
    "gui_framework": "PySide6 6.5+",
    "language": "Python 3.8+",
    "ui_design": "Qt Designer + .ui files",
    "styling": "Qt Style Sheets (QSS)",
    "threading": "QThread + QThreadPool",
    "signals": "Qt Signals & Slots",
    "resources": "Qt Resource System",
    "packaging": "PyInstaller"
}
```

#### 3.1.2 应用状态管理设计
```python
# 全局状态管理类
from dataclasses import dataclass
from typing import Optional, List
from enum import Enum

class AppState(Enum):
    IDLE = "idle"
    PROCESSING = "processing"
    BATCH_PROCESSING = "batch_processing"
    SETTINGS = "settings"

@dataclass
class ApplicationState:
    # 应用配置
    settings: dict = None

    # 处理任务
    current_task: Optional['Task'] = None
    task_queue: List['Task'] = None
    task_history: List['TaskHistory'] = None

    # UI状态
    app_state: AppState = AppState.IDLE
    is_processing: bool = False
    progress: float = 0.0
    status_message: str = ""

    def __post_init__(self):
        if self.task_queue is None:
            self.task_queue = []
        if self.task_history is None:
            self.task_history = []
        if self.settings is None:
            self.settings = self._default_settings()

    def _default_settings(self) -> dict:
        return {
            "theme": "system",
            "language": "zh_CN",
            "output_path": "./outputs",
            "gpu_enabled": True,
            "confidence_threshold": 0.5,
            "max_concurrent_tasks": 4
        }
```

### 3.2 Python业务逻辑技术栈

#### 3.2.1 核心模块结构
```python
# 主要模块结构
src/
├── models/                 # 数据模型
│   ├── task_model.py      # 任务数据模型
│   ├── config_model.py    # 配置数据模型
│   └── ai_models/         # AI模型封装
├── services/              # 业务服务
│   ├── file_service.py    # 文件管理服务
│   ├── task_service.py    # 任务调度服务
│   ├── config_service.py  # 配置管理服务
│   └── ai_service.py      # AI处理服务
├── presenters/            # 控制器
│   ├── main_presenter.py  # 主窗口控制器
│   ├── task_presenter.py  # 任务控制器
│   └── settings_presenter.py # 设置控制器
└── utils/                 # 工具类
    ├── logger.py          # 日志工具
    ├── thread_utils.py    # 线程工具
    └── image_utils.py     # 图像工具
```

#### 3.2.2 AI处理模块设计
```python
# AI处理服务封装 - 基于实际的WatermarkRemovalPipeline
from pathlib import Path
from typing import Optional, Union, Dict, Any, Callable
from PIL import Image
import torch
import logging

# 导入实际的模型实现
from models.watermark_removal_pipeline import WatermarkRemovalPipeline

class WatermarkProcessor:
    """水印处理器 - 封装实际的AI模型"""

    def __init__(self, models_dir: Path = None):
        """
        初始化水印处理器

        Args:
            models_dir: 模型文件目录，包含:
                - yolo/yolo11x-train28-best.pt (YOLO11水印检测模型)
                - lama/big-lama/models/best.ckpt (LAMA图像修复模型)
        """
        self.models_dir = models_dir or Path("models")
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.pipeline = None
        self.is_loaded = False

        # 模型路径配置
        self.detector_model_path = self.models_dir / "yolo" / "yolo11x-train28-best.pt"
        self.lama_model_path = self.models_dir / "lama" / "big-lama" / "models" / "best.ckpt"

        logging.info(f"水印处理器初始化 - 设备: {self.device}")

    def load_models(self) -> bool:
        """加载AI模型"""
        try:
            logging.info("正在加载水印检测和修复模型...")

            # 初始化完整的水印去除管道
            self.pipeline = WatermarkRemovalPipeline(
                detector_model_path=str(self.detector_model_path) if self.detector_model_path.exists() else None,
                inpainter_model_path=str(self.lama_model_path) if self.lama_model_path.exists() else None,
                device=str(self.device)
            )

            self.is_loaded = True
            logging.info("✅ AI模型加载成功")
            return True

        except Exception as e:
            logging.error(f"❌ AI模型加载失败: {e}")
            self.is_loaded = False
            return False

    def process_image(
        self,
        input_path: Path,
        output_path: Path,
        confidence_threshold: float = 0.3,
        enhance_mask: bool = True,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> Dict[str, Any]:
        """
        处理图像去除水印

        Args:
            input_path: 输入图像路径
            output_path: 输出图像路径
            confidence_threshold: 水印检测置信度阈值
            enhance_mask: 是否增强检测掩码
            use_smart_enhancement: 是否使用智能掩码增强
            context_expansion_ratio: 上下文扩展比例
            progress_callback: 进度回调函数

        Returns:
            处理结果字典
        """
        if not self.is_loaded:
            if not self.load_models():
                return {"success": False, "error": "模型加载失败"}

        try:
            if progress_callback:
                progress_callback(0.1)

            # 使用实际的水印去除管道
            result = self.pipeline.remove_watermark(
                image=input_path,
                confidence_threshold=confidence_threshold,
                enhance_mask=enhance_mask,
                use_smart_enhancement=use_smart_enhancement,
                context_expansion_ratio=context_expansion_ratio,
                return_intermediate=True
            )

            if progress_callback:
                progress_callback(0.8)

            # 保存结果图像
            if isinstance(result, dict) and 'result_image' in result:
                result['result_image'].save(str(output_path), 'JPEG', quality=95)

                if progress_callback:
                    progress_callback(1.0)

                return {
                    "success": True,
                    "output_path": str(output_path),
                    "processing_time": result.get('processing_time', 0),
                    "detection_time": result.get('detection_time', 0),
                    "inpainting_time": result.get('inpainting_time', 0),
                    "watermark_count": len(result.get('detection_info', [])),
                    "detection_confidence": result.get('confidence', 0),
                    "watermark_detected": result.get('watermark_detected', False)
                }
            else:
                # 简单结果处理
                result.save(str(output_path), 'JPEG', quality=95)

                if progress_callback:
                    progress_callback(1.0)

                return {
                    "success": True,
                    "output_path": str(output_path),
                    "processing_time": 0,
                    "watermark_detected": False
                }

        except Exception as e:
            logging.error(f"图像处理失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
```

### 3.3 数据存储设计

#### 3.3.1 SQLite数据库设计
```python
# 数据库模型定义
import sqlite3
from pathlib import Path
from datetime import datetime
from typing import Optional, List
import json

class DatabaseManager:
    """数据库管理器"""

    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.executescript("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    uuid TEXT UNIQUE NOT NULL,
                    input_path TEXT NOT NULL,
                    output_path TEXT,
                    status TEXT NOT NULL, -- 'pending', 'processing', 'completed', 'failed'
                    parameters TEXT, -- JSON格式的处理参数
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME,
                    error_message TEXT
                );

                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS presets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    parameters TEXT NOT NULL, -- JSON格式
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
                CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
            """)
```

#### 3.3.2 配置管理设计
```python
# 配置管理器
import json
from pathlib import Path
from typing import Any, Dict

class ConfigManager:
    """配置管理器"""

    DEFAULT_CONFIG = {
        "app": {
            "name": "WatermarkRemover",
            "version": "1.0.0",
            "language": "zh_CN",
            "theme": "system"
        },
        "processing": {
            "max_concurrent_tasks": 4,
            "gpu_acceleration": True,
            "memory_limit_gb": 2,
            "confidence_threshold": 0.5
        },
        "ui": {
            "window_size": [1200, 800],
            "window_position": [100, 100],
            "remember_window_state": True
        },
        "paths": {
            "models_dir": "./models",
            "temp_dir": "./temp",
            "output_dir": "./outputs",
            "config_dir": "./config"
        }
    }

    def __init__(self, config_path: Path):
        self.config_path = config_path
        self.config = self.load_config()

    def load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                return self._merge_config(self.DEFAULT_CONFIG, config)
            except Exception:
                pass
        return self.DEFAULT_CONFIG.copy()

    def save_config(self):
        """保存配置"""
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        with open(self.config_path, 'w', encoding='utf-8') as f:
            json.dump(self.config, f, indent=2, ensure_ascii=False)

    def get(self, key: str, default=None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value

    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        config[keys[-1]] = value
        self.save_config()
```

## 4. 模块设计

### 4.1 文件管理模块

#### 4.1.1 功能设计
```python
from pathlib import Path
from typing import List, Optional, Set
import shutil
import tempfile
from dataclasses import dataclass

@dataclass
class FileInfo:
    path: Path
    size: int
    format: str
    width: int
    height: int
    is_valid: bool

class FileManager:
    """文件管理器"""

    SUPPORTED_FORMATS = {'.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff'}
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB

    def __init__(self, temp_dir: Path, output_dir: Path):
        self.temp_dir = temp_dir
        self.output_dir = output_dir
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)

    def validate_file(self, file_path: Path) -> FileInfo:
        """验证文件"""
        try:
            if not file_path.exists():
                return FileInfo(file_path, 0, "", 0, 0, False)

            size = file_path.stat().st_size
            if size > self.MAX_FILE_SIZE:
                return FileInfo(file_path, size, "", 0, 0, False)

            suffix = file_path.suffix.lower()
            if suffix not in self.SUPPORTED_FORMATS:
                return FileInfo(file_path, size, suffix, 0, 0, False)

            # 获取图像尺寸
            from PIL import Image
            with Image.open(file_path) as img:
                width, height = img.size

            return FileInfo(file_path, size, suffix, width, height, True)

        except Exception:
            return FileInfo(file_path, 0, "", 0, 0, False)

    def create_temp_file(self, extension: str) -> Path:
        """创建临时文件"""
        temp_file = tempfile.NamedTemporaryFile(
            suffix=extension,
            dir=self.temp_dir,
            delete=False
        )
        temp_file.close()
        return Path(temp_file.name)

    def cleanup_temp_files(self):
        """清理临时文件"""
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            self.temp_dir.mkdir(parents=True, exist_ok=True)

    def generate_output_path(self, input_path: Path, suffix: str = "_processed") -> Path:
        """生成输出路径"""
        stem = input_path.stem
        extension = input_path.suffix
        output_name = f"{stem}{suffix}{extension}"
        return self.output_dir / output_name

    def scan_directory(self, directory: Path, recursive: bool = True) -> List[Path]:
        """扫描目录中的图片文件"""
        files = []
        pattern = "**/*" if recursive else "*"

        for file_path in directory.glob(pattern):
            if file_path.is_file() and file_path.suffix.lower() in self.SUPPORTED_FORMATS:
                files.append(file_path)

        return files
```

#### 4.1.2 文件类型支持
- **输入格式**: JPG, PNG, WebP, BMP, TIFF
- **输出格式**: JPG, PNG (可配置)
- **大小限制**: 50MB (可配置)
- **批处理**: 支持文件夹递归扫描

### 4.2 任务调度模块

#### 4.2.1 任务管理设计
```python
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Optional, Dict, Any, List
from queue import Queue
import threading
import uuid

class TaskStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

@dataclass
class ProcessingParams:
    confidence_threshold: float = 0.5
    enhance_mask: bool = True
    mask_dilation_size: int = 5
    mask_blur_size: int = 3
    context_expansion_ratio: float = 0.1
    use_gpu: bool = True

@dataclass
class Task:
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    input_path: Path = None
    output_path: Optional[Path] = None
    parameters: ProcessingParams = field(default_factory=ProcessingParams)
    status: TaskStatus = TaskStatus.PENDING
    progress: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error: Optional[str] = None
    result: Optional[Dict[str, Any]] = None

class TaskScheduler:
    """任务调度器"""

    def __init__(self, max_concurrent: int = 4):
        self.max_concurrent = max_concurrent
        self.task_queue = Queue()
        self.active_tasks: Dict[str, Task] = {}
        self.completed_tasks: List[Task] = []
        self.worker_threads: List[threading.Thread] = []
        self.is_running = False
        self.lock = threading.Lock()

        # 回调函数
        self.on_task_started = None
        self.on_task_progress = None
        self.on_task_completed = None
        self.on_task_failed = None

    def start(self):
        """启动调度器"""
        self.is_running = True
        for i in range(self.max_concurrent):
            worker = threading.Thread(target=self._worker, daemon=True)
            worker.start()
            self.worker_threads.append(worker)

    def stop(self):
        """停止调度器"""
        self.is_running = False
        # 等待所有工作线程结束
        for worker in self.worker_threads:
            worker.join(timeout=5.0)

    def submit_task(self, task: Task) -> str:
        """提交任务"""
        self.task_queue.put(task)
        return task.id

    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        with self.lock:
            if task_id in self.active_tasks:
                task = self.active_tasks[task_id]
                task.status = TaskStatus.CANCELLED
                return True
        return False

    def get_task_status(self, task_id: str) -> Optional[Task]:
        """获取任务状态"""
        with self.lock:
            if task_id in self.active_tasks:
                return self.active_tasks[task_id]

            for task in self.completed_tasks:
                if task.id == task_id:
                    return task
        return None

    def _worker(self):
        """工作线程"""
        from .ai_service import WatermarkProcessor

        processor = WatermarkProcessor()

        while self.is_running:
            try:
                task = self.task_queue.get(timeout=1.0)
                self._process_task(task, processor)
            except:
                continue

    def _process_task(self, task: Task, processor):
        """处理任务"""
        try:
            with self.lock:
                self.active_tasks[task.id] = task
                task.status = TaskStatus.PROCESSING
                task.started_at = datetime.now()

            if self.on_task_started:
                self.on_task_started(task)

            # 处理进度回调
            def progress_callback(progress: float):
                task.progress = progress
                if self.on_task_progress:
                    self.on_task_progress(task)

            # 执行处理
            result = processor.process_image(
                task.input_path,
                task.output_path,
                task.parameters.__dict__,
                progress_callback
            )

            with self.lock:
                if task.status != TaskStatus.CANCELLED:
                    if result.get("success", False):
                        task.status = TaskStatus.COMPLETED
                        task.result = result
                        task.completed_at = datetime.now()
                        if self.on_task_completed:
                            self.on_task_completed(task)
                    else:
                        task.status = TaskStatus.FAILED
                        task.error = result.get("error", "Unknown error")
                        if self.on_task_failed:
                            self.on_task_failed(task)

                # 移动到完成列表
                if task.id in self.active_tasks:
                    del self.active_tasks[task.id]
                self.completed_tasks.append(task)

        except Exception as e:
            with self.lock:
                task.status = TaskStatus.FAILED
                task.error = str(e)
                if task.id in self.active_tasks:
                    del self.active_tasks[task.id]
                self.completed_tasks.append(task)

                if self.on_task_failed:
                    self.on_task_failed(task)
```

#### 4.2.2 并发处理设计
- **线程池**: 基于Python threading的多线程处理
- **并发控制**: 可配置的最大并发任务数
- **队列管理**: 使用Queue进行任务队列管理
- **取消机制**: 支持任务取消和清理
- **回调机制**: 支持任务状态变化回调

### 4.3 GUI控制器模块

#### 4.3.1 主窗口控制器设计
```python
from PySide6.QtWidgets import QMainWindow, QWidget, QHBoxLayout, QVBoxLayout
from PySide6.QtCore import QThread, Signal, QObject
from typing import Optional

class MainWindowPresenter(QObject):
    """主窗口控制器"""

    # 信号定义
    task_started = Signal(str)  # task_id
    task_progress = Signal(str, float)  # task_id, progress
    task_completed = Signal(str, dict)  # task_id, result
    task_failed = Signal(str, str)  # task_id, error

    def __init__(self, view: 'MainWindow', model: 'ApplicationModel'):
        super().__init__()
        self.view = view
        self.model = model
        self.task_scheduler = None
        self.setup_connections()

    def setup_connections(self):
        """设置信号槽连接"""
        # 连接视图信号
        self.view.files_dropped.connect(self.handle_files_dropped)
        self.view.start_processing.connect(self.handle_start_processing)
        self.view.cancel_task.connect(self.handle_cancel_task)

        # 连接模型信号
        self.task_started.connect(self.view.on_task_started)
        self.task_progress.connect(self.view.on_task_progress)
        self.task_completed.connect(self.view.on_task_completed)
        self.task_failed.connect(self.view.on_task_failed)

    def handle_files_dropped(self, file_paths: list):
        """处理文件拖拽"""
        for file_path in file_paths:
            file_info = self.model.file_manager.validate_file(Path(file_path))
            if file_info.is_valid:
                task = Task(
                    input_path=file_info.path,
                    output_path=self.model.file_manager.generate_output_path(file_info.path)
                )
                self.model.add_task(task)
                self.view.add_task_to_list(task)

    def handle_start_processing(self):
        """开始处理"""
        if not self.task_scheduler:
            self.task_scheduler = TaskScheduler()
            self.task_scheduler.on_task_started = self.task_started.emit
            self.task_scheduler.on_task_progress = self.task_progress.emit
            self.task_scheduler.on_task_completed = self.task_completed.emit
            self.task_scheduler.on_task_failed = self.task_failed.emit
            self.task_scheduler.start()

        # 提交待处理任务
        for task in self.model.get_pending_tasks():
            self.task_scheduler.submit_task(task)

    def handle_cancel_task(self, task_id: str):
        """取消任务"""
        if self.task_scheduler:
            self.task_scheduler.cancel_task(task_id)
```

#### 4.3.2 异步处理设计
```python
from PySide6.QtCore import QThread, Signal
from pathlib import Path

class ProcessingWorker(QThread):
    """处理工作线程"""

    progress_updated = Signal(float)
    processing_completed = Signal(dict)
    processing_failed = Signal(str)

    def __init__(self, task: Task, processor):
        super().__init__()
        self.task = task
        self.processor = processor
        self.is_cancelled = False

    def run(self):
        """执行处理"""
        try:
            def progress_callback(progress: float):
                if not self.is_cancelled:
                    self.progress_updated.emit(progress)

            result = self.processor.process_image(
                self.task.input_path,
                self.task.output_path,
                self.task.parameters.__dict__,
                progress_callback
            )

            if not self.is_cancelled:
                if result.get("success", False):
                    self.processing_completed.emit(result)
                else:
                    self.processing_failed.emit(result.get("error", "Unknown error"))

        except Exception as e:
            if not self.is_cancelled:
                self.processing_failed.emit(str(e))

    def cancel(self):
        """取消处理"""
        self.is_cancelled = True
        self.quit()
        self.wait()
```

## 5. PySide6用户界面设计

### 5.1 主窗口设计

#### 5.1.1 主窗口布局
```python
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout,
    QSplitter, QMenuBar, QStatusBar, QToolBar
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QAction, QIcon

class MainWindow(QMainWindow):
    """主窗口"""

    # 信号定义
    files_dropped = Signal(list)
    start_processing = Signal()
    cancel_task = Signal(str)

    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.setup_menu()
        self.setup_toolbar()
        self.setup_statusbar()

    def setup_ui(self):
        """设置UI布局"""
        self.setWindowTitle("水印去除工具")
        self.setMinimumSize(1200, 800)

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 水平分割器
        main_layout = QHBoxLayout(central_widget)
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧：任务列表面板
        self.task_list_widget = TaskListWidget()
        self.task_list_widget.setMinimumWidth(400)
        self.task_list_widget.setMaximumWidth(600)
        splitter.addWidget(self.task_list_widget)

        # 右侧：内容面板（状态/结果显示）
        self.content_widget = ContentWidget()
        splitter.addWidget(self.content_widget)

        # 设置分割器比例
        splitter.setStretchFactor(0, 1)  # 左侧
        splitter.setStretchFactor(1, 2)  # 右侧

        # 连接信号
        self.task_list_widget.files_dropped.connect(self.files_dropped)
        self.task_list_widget.start_processing.connect(self.start_processing)
        self.task_list_widget.cancel_task.connect(self.cancel_task)

    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        open_action = QAction("打开文件(&O)", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_files)
        file_menu.addAction(open_action)

        open_folder_action = QAction("打开文件夹(&D)", self)
        open_folder_action.setShortcut("Ctrl+Shift+O")
        open_folder_action.triggered.connect(self.open_folder)
        file_menu.addAction(open_folder_action)

        file_menu.addSeparator()

        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 设置菜单
        settings_menu = menubar.addMenu("设置(&S)")

        preferences_action = QAction("首选项(&P)", self)
        preferences_action.triggered.connect(self.show_preferences)
        settings_menu.addAction(preferences_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_toolbar(self):
        """设置工具栏"""
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)

        # 添加文件按钮
        add_files_action = QAction("添加文件", self)
        add_files_action.setIcon(QIcon(":/icons/add_files.png"))
        add_files_action.triggered.connect(self.open_files)
        toolbar.addAction(add_files_action)

        # 开始处理按钮
        start_action = QAction("开始处理", self)
        start_action.setIcon(QIcon(":/icons/start.png"))
        start_action.triggered.connect(self.start_processing)
        toolbar.addAction(start_action)

        toolbar.addSeparator()

        # 设置按钮
        settings_action = QAction("设置", self)
        settings_action.setIcon(QIcon(":/icons/settings.png"))
        settings_action.triggered.connect(self.show_preferences)
        toolbar.addAction(settings_action)

    def setup_statusbar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 状态信息
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)

        # 任务统计
        self.task_stats_label = QLabel("总计: 0 | 完成: 0 | 失败: 0")
        self.status_bar.addPermanentWidget(self.task_stats_label)

        # GPU状态
        self.gpu_status_label = QLabel("GPU: 未启用")
        self.status_bar.addPermanentWidget(self.gpu_status_label)
```

#### 5.1.2 界面布局图
```
┌─────────────────────────────────────────────────────────┐
│                      菜单栏                              │
│  文件(F)  设置(S)  帮助(H)                               │
├─────────────────────────────────────────────────────────┤
│                      工具栏                              │
│  [添加文件] [开始处理] | [设置]                           │
├─────────────────────────────────────────────────────────┤
│  ┌─────────────────────┐  ┌─────────────────────────────┐ │
│  │                     │  │                             │ │
│  │   任务列表面板       │  │       内容显示面板           │ │
│  │                     │  │                             │ │
│  │  ┌─────────────────┐ │  │  ┌─────────────────────────┐ │ │
│  │  │ 拖拽上传区域    │ │  │  │                         │ │ │
│  │  │ + 添加图片文件  │ │  │  │     处理状态信息         │ │ │
│  │  └─────────────────┘ │  │  │                         │ │ │
│  │                     │  │  │   当前任务: 3/10        │ │ │
│  │  ┌─────────────────┐ │  │  │   进度: 65%            │ │ │
│  │  │ 📷 image1.jpg   │ │  │  │   预计剩余: 2分钟       │ │ │
│  │  │ ✅ 已完成       │ │  │  │                         │ │ │
│  │  │ [查看结果]      │ │  │  │                         │ │ │
│  │  └─────────────────┘ │  │  │     或显示              │ │ │
│  │                     │  │  │                         │ │ │
│  │  ┌─────────────────┐ │  │  │  ┌─────────────────────┐ │ │
│  │  │ 📷 image2.jpg   │ │  │  │  │                     │ │ │
│  │  │ 🔄 处理中...    │ │  │  │  │    结果对比显示      │ │ │
│  │  │ [取消]          │ │  │  │  │                     │ │ │
│  │  └─────────────────┘ │  │  │  │ ┌─────────┐ ┌─────┐ │ │ │
│  │                     │  │  │  │ │ 原始图片 │ │处理后│ │ │ │
│  │  [开始批量处理]     │  │  │  │ └─────────┘ └─────┘ │ │ │
│  │  [清空列表]         │  │  │  │                     │ │ │
│  │                     │  │  │  │ [保存] [重新处理]   │ │ │
│  │                     │  │  │  └─────────────────────┘ │ │
│  └─────────────────────┘  └─────────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│ 状态栏: 总计 10 张图片 | 已完成 3 张 | 失败 0 张 | GPU已启用 │
└─────────────────────────────────────────────────────────┘
```

#### 5.1.2 主窗口组件设计
```python
# views/main_window.py
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QSplitter,
    QStackedWidget, QLabel
)
from PySide6.QtCore import Qt, Signal
from .task_list_widget import TaskListWidget
from .content_widget import ContentWidget

class MainWindow(QMainWindow):
    """主窗口"""

    # 信号定义
    files_dropped = Signal(list)
    start_processing = Signal()
    cancel_task = Signal(str)
    task_selected = Signal(str)
    view_result = Signal(str)

    def __init__(self):
        super().__init__()
        self.selected_task_id = None
        self.view_mode = 'status'  # 'status' 或 'result'
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("水印去除工具")
        self.setMinimumSize(1200, 800)

        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 水平分割器
        main_layout = QHBoxLayout(central_widget)
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)

        # 左侧：任务列表组件
        self.task_list_widget = TaskListWidget()
        self.task_list_widget.setMinimumWidth(400)
        self.task_list_widget.setMaximumWidth(600)
        splitter.addWidget(self.task_list_widget)

        # 右侧：内容显示组件
        self.content_widget = ContentWidget()
        splitter.addWidget(self.content_widget)

        # 设置分割器比例
        splitter.setStretchFactor(0, 1)  # 左侧
        splitter.setStretchFactor(1, 2)  # 右侧

    def setup_connections(self):
        """设置信号槽连接"""
        # 任务列表信号
        self.task_list_widget.files_dropped.connect(self.files_dropped)
        self.task_list_widget.start_processing.connect(self.start_processing)
        self.task_list_widget.cancel_task.connect(self.cancel_task)
        self.task_list_widget.task_selected.connect(self.on_task_selected)
        self.task_list_widget.view_result.connect(self.on_view_result)

        # 内容组件信号
        self.content_widget.back_to_status.connect(self.on_back_to_status)

    def on_task_selected(self, task_id: str):
        """处理任务选择"""
        self.selected_task_id = task_id
        self.task_selected.emit(task_id)

        if self.view_mode == 'status':
            self.content_widget.show_status_view(task_id)

    def on_view_result(self, task_id: str):
        """查看任务结果"""
        self.selected_task_id = task_id
        self.view_mode = 'result'
        self.view_result.emit(task_id)
        self.content_widget.show_result_view(task_id)

    def on_back_to_status(self):
        """返回状态视图"""
        self.view_mode = 'status'
        if self.selected_task_id:
            self.content_widget.show_status_view(self.selected_task_id)
        else:
            self.content_widget.show_default_view()
```

#### 5.1.3 任务列表组件
```python
# views/task_list_widget.py
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QFrame, QProgressBar, QFileDialog
)
from PySide6.QtCore import Qt, Signal, QMimeData
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QPixmap, QIcon
from typing import List, Optional
from pathlib import Path

class TaskListWidget(QWidget):
    """任务列表组件"""

    # 信号定义
    files_dropped = Signal(list)
    start_processing = Signal()
    cancel_task = Signal(str)
    task_selected = Signal(str)
    view_result = Signal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.selected_task_id = None
        self.setup_ui()
        self.setup_connections()

        # 启用拖拽
        self.setAcceptDrops(True)

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 拖拽上传区域
        self.drop_zone = self.create_drop_zone()
        layout.addWidget(self.drop_zone)

        # 任务列表
        self.task_list = QListWidget()
        self.task_list.setSelectionMode(QListWidget.SingleSelection)
        layout.addWidget(self.task_list)

        # 批量操作区域
        self.batch_controls = self.create_batch_controls()
        layout.addWidget(self.batch_controls)

    def create_drop_zone(self) -> QFrame:
        """创建拖拽上传区域"""
        frame = QFrame()
        frame.setFrameStyle(QFrame.Box)
        frame.setStyleSheet("""
            QFrame {
                border: 2px dashed #ccc;
                border-radius: 8px;
                background-color: #f9f9f9;
                min-height: 120px;
            }
            QFrame:hover {
                border-color: #007acc;
                background-color: #f0f8ff;
            }
        """)

        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)

        # 图标
        icon_label = QLabel("📁")
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setStyleSheet("font-size: 32px; color: #666;")
        layout.addWidget(icon_label)

        # 主要文本
        main_text = QLabel("拖拽图片到此处")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setStyleSheet("font-size: 14px; color: #333; font-weight: bold;")
        layout.addWidget(main_text)

        # 或者点击选择按钮
        select_button = QPushButton("或点击选择文件")
        select_button.setStyleSheet("""
            QPushButton {
                background: none;
                border: none;
                color: #007acc;
                text-decoration: underline;
                font-size: 12px;
            }
            QPushButton:hover {
                color: #005a9e;
            }
        """)
        select_button.clicked.connect(self.select_files)
        layout.addWidget(select_button)

        # 支持格式说明
        format_text = QLabel("支持 JPG、PNG、WebP 格式，最大 50MB")
        format_text.setAlignment(Qt.AlignCenter)
        format_text.setStyleSheet("font-size: 10px; color: #666;")
        layout.addWidget(format_text)

        return frame

    def create_batch_controls(self) -> QFrame:
        """创建批量操作控制区域"""
        frame = QFrame()
        frame.setStyleSheet("background-color: #f5f5f5; border-top: 1px solid #ddd;")

        layout = QHBoxLayout(frame)
        layout.setContentsMargins(10, 10, 10, 10)

        # 开始批量处理按钮
        self.start_button = QPushButton("开始批量处理")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #007acc;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #005a9e;
            }
            QPushButton:disabled {
                background-color: #ccc;
            }
        """)
        layout.addWidget(self.start_button)

        # 清空列表按钮
        self.clear_button = QPushButton("清空列表")
        self.clear_button.setStyleSheet("""
            QPushButton {
                background-color: white;
                color: #333;
                border: 1px solid #ccc;
                padding: 8px 16px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #f0f0f0;
            }
        """)
        layout.addWidget(self.clear_button)

        return frame

    def setup_connections(self):
        """设置信号槽连接"""
        self.start_button.clicked.connect(self.start_processing)
        self.clear_button.clicked.connect(self.clear_tasks)
        self.task_list.itemSelectionChanged.connect(self.on_selection_changed)

    def select_files(self):
        """选择文件对话框"""
        file_dialog = QFileDialog()
        file_paths, _ = file_dialog.getOpenFileNames(
            self,
            "选择图片文件",
            "",
            "图片文件 (*.jpg *.jpeg *.png *.webp *.bmp *.tiff)"
        )

        if file_paths:
            self.files_dropped.emit(file_paths)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        file_paths = []

        for url in urls:
            if url.isLocalFile():
                file_path = Path(url.toLocalFile())
                if file_path.is_file() and file_path.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff']:
                    file_paths.append(str(file_path))

        if file_paths:
            self.files_dropped.emit(file_paths)

        event.acceptProposedAction()

    def add_task_item(self, task):
        """添加任务项"""
        item_widget = TaskListItemWidget(task)
        item_widget.view_result.connect(self.view_result)
        item_widget.cancel_task.connect(self.cancel_task)

        list_item = QListWidgetItem()
        list_item.setSizeHint(item_widget.sizeHint())
        list_item.setData(Qt.UserRole, task.id)

        self.task_list.addItem(list_item)
        self.task_list.setItemWidget(list_item, item_widget)

    def on_selection_changed(self):
        """处理选择变化"""
        current_item = self.task_list.currentItem()
        if current_item:
            task_id = current_item.data(Qt.UserRole)
            self.selected_task_id = task_id
            self.task_selected.emit(task_id)

    def clear_tasks(self):
        """清空任务列表"""
        self.task_list.clear()
        self.selected_task_id = None
```

#### 5.1.4 任务列表项组件
```python
# views/task_list_item_widget.py
from PySide6.QtWidgets import (
    QWidget, QHBoxLayout, QVBoxLayout, QLabel, QPushButton,
    QProgressBar, QFrame
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QPixmap, QIcon, QFont
from pathlib import Path

class TaskListItemWidget(QWidget):
    """任务列表项组件"""

    # 信号定义
    view_result = Signal(str)
    cancel_task = Signal(str)
    retry_task = Signal(str)

    def __init__(self, task, parent=None):
        super().__init__(parent)
        self.task = task
        self.setup_ui()
        self.update_display()

    def setup_ui(self):
        """设置用户界面"""
        self.setFixedHeight(80)
        self.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                margin: 2px;
            }
            QWidget:hover {
                border-color: #007acc;
                background-color: #f8f9fa;
            }
        """)

        layout = QHBoxLayout(self)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)

        # 缩略图
        self.thumbnail_label = QLabel()
        self.thumbnail_label.setFixedSize(48, 48)
        self.thumbnail_label.setStyleSheet("""
            QLabel {
                background-color: #f0f0f0;
                border: 1px solid #ddd;
                border-radius: 4px;
            }
        """)
        self.thumbnail_label.setAlignment(Qt.AlignCenter)
        self.thumbnail_label.setText("📷")
        layout.addWidget(self.thumbnail_label)

        # 文件信息区域
        info_layout = QVBoxLayout()
        info_layout.setSpacing(4)

        # 文件名和状态图标
        name_layout = QHBoxLayout()
        name_layout.setSpacing(8)

        self.status_icon = QLabel()
        self.status_icon.setFixedSize(16, 16)
        name_layout.addWidget(self.status_icon)

        self.name_label = QLabel()
        self.name_label.setStyleSheet("font-weight: bold; color: #333;")
        name_layout.addWidget(self.name_label)
        name_layout.addStretch()

        info_layout.addLayout(name_layout)

        # 状态文本
        self.status_label = QLabel()
        self.status_label.setStyleSheet("color: #666; font-size: 11px;")
        info_layout.addWidget(self.status_label)

        # 进度条（仅在处理时显示）
        self.progress_bar = QProgressBar()
        self.progress_bar.setFixedHeight(4)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: none;
                background-color: #e0e0e0;
                border-radius: 2px;
            }
            QProgressBar::chunk {
                background-color: #007acc;
                border-radius: 2px;
            }
        """)
        self.progress_bar.setVisible(False)
        info_layout.addWidget(self.progress_bar)

        layout.addLayout(info_layout)
        layout.addStretch()

        # 操作按钮区域
        self.button_layout = QHBoxLayout()
        self.button_layout.setSpacing(4)

        # 查看结果按钮
        self.view_button = QPushButton("查看结果")
        self.view_button.setStyleSheet("""
            QPushButton {
                background-color: #e8f5e8;
                color: #2e7d32;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c8e6c9;
            }
        """)
        self.view_button.clicked.connect(lambda: self.view_result.emit(self.task.id))
        self.button_layout.addWidget(self.view_button)

        # 取消按钮
        self.cancel_button = QPushButton("取消")
        self.cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #ffebee;
                color: #c62828;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ffcdd2;
            }
        """)
        self.cancel_button.clicked.connect(lambda: self.cancel_task.emit(self.task.id))
        self.button_layout.addWidget(self.cancel_button)

        # 重试按钮
        self.retry_button = QPushButton("重试")
        self.retry_button.setStyleSheet("""
            QPushButton {
                background-color: #fff3e0;
                color: #ef6c00;
                border: none;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #ffe0b2;
            }
        """)
        self.retry_button.clicked.connect(lambda: self.retry_task.emit(self.task.id))
        self.button_layout.addWidget(self.retry_button)

        layout.addLayout(self.button_layout)

    def update_display(self):
        """更新显示内容"""
        # 设置文件名
        file_name = Path(self.task.input_path).name
        self.name_label.setText(file_name)

        # 根据状态更新显示
        status = self.task.status

        if status == 'completed':
            self.status_icon.setText("✅")
            self.status_label.setText("已完成")
            self.view_button.setVisible(True)
            self.cancel_button.setVisible(False)
            self.retry_button.setVisible(False)
            self.progress_bar.setVisible(False)

        elif status == 'processing':
            self.status_icon.setText("🔄")
            progress = int(self.task.progress * 100)
            self.status_label.setText(f"处理中... {progress}%")
            self.progress_bar.setValue(progress)
            self.view_button.setVisible(False)
            self.cancel_button.setVisible(True)
            self.retry_button.setVisible(False)
            self.progress_bar.setVisible(True)

        elif status == 'failed':
            self.status_icon.setText("❌")
            self.status_label.setText("处理失败")
            self.view_button.setVisible(False)
            self.cancel_button.setVisible(False)
            self.retry_button.setVisible(True)
            self.progress_bar.setVisible(False)

        elif status == 'pending':
            self.status_icon.setText("⏳")
            self.status_label.setText("等待中")
            self.view_button.setVisible(False)
            self.cancel_button.setVisible(False)
            self.retry_button.setVisible(False)
            self.progress_bar.setVisible(False)

        elif status == 'cancelled':
            self.status_icon.setText("🚫")
            self.status_label.setText("已取消")
            self.view_button.setVisible(False)
            self.cancel_button.setVisible(False)
            self.retry_button.setVisible(True)
            self.progress_bar.setVisible(False)

    def update_task(self, task):
        """更新任务数据"""
        self.task = task
        self.update_display()

    def sizeHint(self) -> QSize:
        """返回建议大小"""
        return QSize(400, 80)
```

#### 5.1.5 内容显示组件
```python
# views/content_widget.py
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QProgressBar,
    QFrame, QGridLayout, QSlider, QCheckBox, QStackedWidget,
    QGroupBox, QPushButton
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont
import psutil

class ContentWidget(QWidget):
    """内容显示组件"""

    # 信号定义
    back_to_status = Signal()
    settings_changed = Signal(str, object)  # setting_name, value

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_task_id = None
        self.setup_ui()
        self.setup_timer()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 使用堆叠组件切换不同视图
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)

        # 默认视图
        self.default_view = self.create_default_view()
        self.stacked_widget.addWidget(self.default_view)

        # 状态视图
        self.status_view = self.create_status_view()
        self.stacked_widget.addWidget(self.status_view)

        # 结果视图
        self.result_view = self.create_result_view()
        self.stacked_widget.addWidget(self.result_view)

    def create_default_view(self) -> QWidget:
        """创建默认视图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # 欢迎信息
        welcome_label = QLabel("欢迎使用水印去除工具")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #333;
                margin: 20px;
            }
        """)
        layout.addWidget(welcome_label)

        # 使用说明
        instruction_label = QLabel("请从左侧添加图片文件开始处理")
        instruction_label.setAlignment(Qt.AlignCenter)
        instruction_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #666;
                margin: 10px;
            }
        """)
        layout.addWidget(instruction_label)

        return widget

    def create_status_view(self) -> QWidget:
        """创建状态视图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 处理状态概览
        status_group = QGroupBox("处理状态")
        status_layout = QVBoxLayout(status_group)

        # 统计信息网格
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)

        # 已完成统计
        self.completed_label = QLabel("0")
        self.completed_label.setAlignment(Qt.AlignCenter)
        self.completed_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #2e7d32;
                background-color: #e8f5e8;
                padding: 20px;
                border-radius: 8px;
            }
        """)
        stats_layout.addWidget(self.completed_label, 0, 0)
        stats_layout.addWidget(QLabel("已完成"), 1, 0, Qt.AlignCenter)

        # 等待中统计
        self.pending_label = QLabel("0")
        self.pending_label.setAlignment(Qt.AlignCenter)
        self.pending_label.setStyleSheet("""
            QLabel {
                font-size: 24px;
                font-weight: bold;
                color: #f57c00;
                background-color: #fff3e0;
                padding: 20px;
                border-radius: 8px;
            }
        """)
        stats_layout.addWidget(self.pending_label, 0, 1)
        stats_layout.addWidget(QLabel("等待中"), 1, 1, Qt.AlignCenter)

        status_layout.addWidget(stats_frame)

        # 当前任务进度
        self.current_task_frame = QFrame()
        current_layout = QVBoxLayout(self.current_task_frame)

        self.current_task_label = QLabel("当前处理: 无")
        self.current_task_label.setStyleSheet("font-weight: bold; color: #333;")
        current_layout.addWidget(self.current_task_label)

        self.progress_bar = QProgressBar()
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #ddd;
                border-radius: 4px;
                text-align: center;
                background-color: #f0f0f0;
            }
            QProgressBar::chunk {
                background-color: #007acc;
                border-radius: 3px;
            }
        """)
        current_layout.addWidget(self.progress_bar)

        progress_info_layout = QHBoxLayout()
        self.time_label = QLabel("预计剩余: --")
        self.speed_label = QLabel("速度: --")
        progress_info_layout.addWidget(self.time_label)
        progress_info_layout.addStretch()
        progress_info_layout.addWidget(self.speed_label)
        current_layout.addLayout(progress_info_layout)

        self.current_task_frame.setVisible(False)
        status_layout.addWidget(self.current_task_frame)

        layout.addWidget(status_group)

        # 系统状态
        system_group = QGroupBox("系统状态")
        system_layout = QVBoxLayout(system_group)

        # 内存使用
        memory_layout = QHBoxLayout()
        memory_layout.addWidget(QLabel("内存使用:"))
        self.memory_progress = QProgressBar()
        self.memory_progress.setMaximum(100)
        self.memory_progress.setStyleSheet("""
            QProgressBar::chunk { background-color: #4caf50; }
        """)
        memory_layout.addWidget(self.memory_progress)
        self.memory_label = QLabel("0%")
        memory_layout.addWidget(self.memory_label)
        system_layout.addLayout(memory_layout)

        # GPU使用（如果可用）
        gpu_layout = QHBoxLayout()
        gpu_layout.addWidget(QLabel("GPU使用:"))
        self.gpu_progress = QProgressBar()
        self.gpu_progress.setMaximum(100)
        self.gpu_progress.setStyleSheet("""
            QProgressBar::chunk { background-color: #2196f3; }
        """)
        gpu_layout.addWidget(self.gpu_progress)
        self.gpu_label = QLabel("0%")
        gpu_layout.addWidget(self.gpu_label)
        system_layout.addLayout(gpu_layout)

        layout.addWidget(system_group)

        # 快速设置
        settings_group = QGroupBox("快速设置")
        settings_layout = QVBoxLayout(settings_group)

        # GPU加速开关
        self.gpu_checkbox = QCheckBox("启用GPU加速")
        self.gpu_checkbox.setChecked(True)
        self.gpu_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('use_gpu', checked)
        )
        settings_layout.addWidget(self.gpu_checkbox)

        # 自动保存开关
        self.auto_save_checkbox = QCheckBox("自动保存结果")
        self.auto_save_checkbox.setChecked(True)
        self.auto_save_checkbox.toggled.connect(
            lambda checked: self.settings_changed.emit('auto_save', checked)
        )
        settings_layout.addWidget(self.auto_save_checkbox)

        # 检测敏感度滑块
        sensitivity_layout = QVBoxLayout()
        self.sensitivity_label = QLabel("检测敏感度: 0.5")
        sensitivity_layout.addWidget(self.sensitivity_label)

        self.sensitivity_slider = QSlider(Qt.Horizontal)
        self.sensitivity_slider.setMinimum(10)
        self.sensitivity_slider.setMaximum(90)
        self.sensitivity_slider.setValue(50)
        self.sensitivity_slider.valueChanged.connect(self.on_sensitivity_changed)
        sensitivity_layout.addWidget(self.sensitivity_slider)

        settings_layout.addLayout(sensitivity_layout)
        layout.addWidget(settings_group)

        layout.addStretch()
        return widget

    def create_result_view(self) -> QWidget:
        """创建结果视图"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 返回按钮
        back_button = QPushButton("← 返回状态视图")
        back_button.clicked.connect(self.back_to_status)
        back_button.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                padding: 8px 16px;
                border-radius: 4px;
                text-align: left;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
        """)
        layout.addWidget(back_button)

        # 结果对比区域（这里简化显示）
        result_label = QLabel("结果对比视图")
        result_label.setAlignment(Qt.AlignCenter)
        result_label.setStyleSheet("""
            QLabel {
                font-size: 18px;
                color: #333;
                background-color: #f9f9f9;
                border: 2px dashed #ccc;
                padding: 40px;
                border-radius: 8px;
            }
        """)
        layout.addWidget(result_label)

        return widget

    def setup_timer(self):
        """设置定时器更新系统状态"""
        self.timer = QTimer()
        self.timer.timeout.connect(self.update_system_status)
        self.timer.start(2000)  # 每2秒更新一次

    def show_default_view(self):
        """显示默认视图"""
        self.stacked_widget.setCurrentWidget(self.default_view)

    def show_status_view(self, task_id: str = None):
        """显示状态视图"""
        self.current_task_id = task_id
        self.stacked_widget.setCurrentWidget(self.status_view)

    def show_result_view(self, task_id: str):
        """显示结果视图"""
        self.current_task_id = task_id
        self.stacked_widget.setCurrentWidget(self.result_view)

    def update_system_status(self):
        """更新系统状态"""
        # 更新内存使用
        memory = psutil.virtual_memory()
        memory_percent = int(memory.percent)
        self.memory_progress.setValue(memory_percent)
        self.memory_label.setText(f"{memory_percent}%")

        # 更新GPU使用（简化处理）
        # 实际应用中需要使用nvidia-ml-py等库获取GPU信息
        self.gpu_progress.setValue(0)
        self.gpu_label.setText("0%")

    def on_sensitivity_changed(self, value):
        """处理敏感度变化"""
        sensitivity = value / 100.0
        self.sensitivity_label.setText(f"检测敏感度: {sensitivity:.1f}")
        self.settings_changed.emit('confidence_threshold', sensitivity)

    def update_task_stats(self, completed: int, pending: int, processing: int):
        """更新任务统计"""
        self.completed_label.setText(str(completed))
        self.pending_label.setText(str(pending))

    def update_current_task(self, task_name: str, progress: float,
                           estimated_time: str = "", speed: str = ""):
        """更新当前任务信息"""
        if task_name:
            self.current_task_label.setText(f"当前处理: {task_name}")
            self.progress_bar.setValue(int(progress * 100))
            self.time_label.setText(f"预计剩余: {estimated_time}")
            self.speed_label.setText(f"速度: {speed}")
            self.current_task_frame.setVisible(True)
        else:
            self.current_task_frame.setVisible(False)
```

#### 5.1.6 结果对比组件
```python
# views/result_comparison_widget.py
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QButtonGroup, QGridLayout, QScrollArea, QFrame
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QPixmap
from pathlib import Path

class ResultComparisonWidget(QWidget):
    """结果对比组件"""

    # 信号定义
    back_to_status = Signal()
    save_result = Signal(str)  # task_id

    def __init__(self, parent=None):
        super().__init__(parent)
        self.current_task_id = None
        self.view_mode = 'side-by-side'
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)

        # 工具栏
        toolbar = self.create_toolbar()
        layout.addWidget(toolbar)

        # 对比显示区域
        self.comparison_area = QScrollArea()
        self.comparison_area.setWidgetResizable(True)
        self.comparison_area.setStyleSheet("background-color: #f8f9fa;")
        layout.addWidget(self.comparison_area)

        # 结果信息面板
        info_panel = self.create_info_panel()
        layout.addWidget(info_panel)

    def create_toolbar(self) -> QFrame:
        """创建工具栏"""
        toolbar = QFrame()
        toolbar.setStyleSheet("background-color: white; border-bottom: 1px solid #dee2e6;")

        layout = QHBoxLayout(toolbar)
        layout.setContentsMargins(16, 12, 16, 12)

        # 左侧：返回按钮和标题
        left_layout = QHBoxLayout()

        back_button = QPushButton("← 返回状态")
        back_button.setStyleSheet("""
            QPushButton {
                background: none;
                border: none;
                color: #6c757d;
                font-size: 14px;
                padding: 4px 8px;
            }
            QPushButton:hover {
                color: #495057;
                background-color: #e9ecef;
                border-radius: 4px;
            }
        """)
        back_button.clicked.connect(self.back_to_status)
        left_layout.addWidget(back_button)

        self.title_label = QLabel("图片对比")
        self.title_label.setStyleSheet("font-size: 16px; font-weight: bold; color: #212529; margin-left: 16px;")
        left_layout.addWidget(self.title_label)

        layout.addLayout(left_layout)
        layout.addStretch()

        # 右侧：查看模式和操作按钮
        right_layout = QHBoxLayout()

        # 查看模式切换
        mode_frame = QFrame()
        mode_frame.setStyleSheet("background-color: #f8f9fa; border-radius: 6px; padding: 2px;")
        mode_layout = QHBoxLayout(mode_frame)
        mode_layout.setContentsMargins(2, 2, 2, 2)
        mode_layout.setSpacing(0)

        self.mode_button_group = QButtonGroup()

        side_by_side_btn = QPushButton("并排对比")
        side_by_side_btn.setCheckable(True)
        side_by_side_btn.setChecked(True)
        side_by_side_btn.clicked.connect(lambda: self.set_view_mode('side-by-side'))
        self.mode_button_group.addButton(side_by_side_btn)
        mode_layout.addWidget(side_by_side_btn)

        overlay_btn = QPushButton("叠加对比")
        overlay_btn.setCheckable(True)
        overlay_btn.clicked.connect(lambda: self.set_view_mode('overlay'))
        self.mode_button_group.addButton(overlay_btn)
        mode_layout.addWidget(overlay_btn)

        slider_btn = QPushButton("滑动对比")
        slider_btn.setCheckable(True)
        slider_btn.clicked.connect(lambda: self.set_view_mode('slider'))
        self.mode_button_group.addButton(slider_btn)
        mode_layout.addWidget(slider_btn)

        # 设置按钮样式
        for button in self.mode_button_group.buttons():
            button.setStyleSheet("""
                QPushButton {
                    background: none;
                    border: none;
                    padding: 6px 12px;
                    border-radius: 4px;
                    font-size: 12px;
                    color: #6c757d;
                }
                QPushButton:checked {
                    background-color: white;
                    color: #212529;
                    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                }
                QPushButton:hover {
                    color: #495057;
                }
            """)

        right_layout.addWidget(mode_frame)

        # 保存按钮
        save_button = QPushButton("保存结果")
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                margin-left: 8px;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
        """)
        save_button.clicked.connect(self.on_save_clicked)
        right_layout.addWidget(save_button)

        layout.addLayout(right_layout)
        return toolbar

    def create_info_panel(self) -> QFrame:
        """创建信息面板"""
        panel = QFrame()
        panel.setStyleSheet("background-color: white; border-top: 1px solid #dee2e6;")

        layout = QGridLayout(panel)
        layout.setContentsMargins(16, 12, 16, 12)

        # 创建信息项
        self.confidence_label = self.create_info_item("--", "检测置信度")
        self.time_label = self.create_info_item("--", "处理时间")
        self.count_label = self.create_info_item("--", "检测到水印")
        self.size_label = self.create_info_item("--", "文件大小")

        layout.addWidget(self.confidence_label, 0, 0)
        layout.addWidget(self.time_label, 0, 1)
        layout.addWidget(self.count_label, 0, 2)
        layout.addWidget(self.size_label, 0, 3)

        return panel

    def create_info_item(self, value: str, label: str) -> QFrame:
        """创建信息项"""
        frame = QFrame()
        layout = QVBoxLayout(frame)
        layout.setAlignment(Qt.AlignCenter)
        layout.setContentsMargins(0, 0, 0, 0)

        value_label = QLabel(value)
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setStyleSheet("font-size: 18px; font-weight: bold; color: #212529;")
        layout.addWidget(value_label)

        desc_label = QLabel(label)
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setStyleSheet("font-size: 10px; color: #6c757d;")
        layout.addWidget(desc_label)

        # 保存值标签的引用以便更新
        frame.value_label = value_label

        return frame

    def show_comparison(self, task_id: str, task_name: str, original_path: str,
                       result_path: str, result_info: dict):
        """显示对比结果"""
        self.current_task_id = task_id
        self.title_label.setText(task_name)

        # 更新信息面板
        self.confidence_label.value_label.setText(f"{result_info.get('confidence', 0):.1f}%")
        self.time_label.value_label.setText(f"{result_info.get('processing_time', 0):.1f}s")
        self.count_label.value_label.setText(str(result_info.get('watermark_count', 0)))
        self.size_label.value_label.setText(result_info.get('file_size', '--'))

        # 创建对比内容
        comparison_widget = self.create_comparison_content(original_path, result_path)
        self.comparison_area.setWidget(comparison_widget)

    def create_comparison_content(self, original_path: str, result_path: str) -> QWidget:
        """创建对比内容"""
        widget = QWidget()

        if self.view_mode == 'side-by-side':
            layout = QGridLayout(widget)
            layout.setContentsMargins(24, 24, 24, 24)
            layout.setSpacing(24)

            # 原始图片
            original_frame = self.create_image_frame("原始图片", original_path)
            layout.addWidget(original_frame, 0, 0)

            # 处理结果
            result_frame = self.create_image_frame("处理结果", result_path)
            layout.addWidget(result_frame, 0, 1)

        # 其他对比模式可以在这里实现

        return widget

    def create_image_frame(self, title: str, image_path: str) -> QFrame:
        """创建图片显示框架"""
        frame = QFrame()
        frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }
        """)

        layout = QVBoxLayout(frame)
        layout.setContentsMargins(16, 16, 16, 16)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet("font-weight: bold; color: #495057; margin-bottom: 12px;")
        layout.addWidget(title_label)

        # 图片
        image_label = QLabel()
        image_label.setAlignment(Qt.AlignCenter)
        image_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border-radius: 6px;
                min-height: 400px;
                border: 1px solid #e9ecef;
            }
        """)

        # 加载图片
        if Path(image_path).exists():
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                scaled_pixmap = pixmap.scaled(500, 400, Qt.KeepAspectRatio, Qt.SmoothTransformation)
                image_label.setPixmap(scaled_pixmap)
            else:
                image_label.setText("无法加载图片")
                image_label.setStyleSheet(image_label.styleSheet() + "color: #6c757d;")
        else:
            image_label.setText("图片文件不存在")
            image_label.setStyleSheet(image_label.styleSheet() + "color: #6c757d;")

        layout.addWidget(image_label)
        return frame

    def set_view_mode(self, mode: str):
        """设置查看模式"""
        self.view_mode = mode
        # 重新创建对比内容（如果有当前任务）
        if self.current_task_id:
            # 这里需要重新获取图片路径并更新显示
            pass

    def on_save_clicked(self):
        """处理保存按钮点击"""
        if self.current_task_id:
            self.save_result.emit(self.current_task_id)
```

### 5.2 简化的设置界面

#### 5.2.1 设置界面设计原则
- **弱化设置**: 将设置功能移至菜单或弹窗中，不占用主要界面空间
- **分类简化**: 只保留最核心的设置选项
- **即时生效**: 设置更改立即生效，无需保存按钮

#### 5.2.2 设置对话框
```python
# views/settings_dialog.py
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QCheckBox,
    QSlider, QComboBox, QPushButton, QLineEdit, QFileDialog,
    QGroupBox, QFormLayout, QDialogButtonBox
)
from PySide6.QtCore import Qt, Signal
from pathlib import Path

class SettingsDialog(QDialog):
    """设置对话框"""

    # 信号定义
    settings_changed = Signal(dict)  # 设置变更信号

    def __init__(self, current_settings: dict, parent=None):
        super().__init__(parent)
        self.current_settings = current_settings.copy()
        self.setup_ui()
        self.load_settings()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("应用设置")
        self.setFixedSize(400, 500)
        self.setModal(True)

        layout = QVBoxLayout(self)
        layout.setSpacing(20)

        # 处理设置组
        processing_group = self.create_processing_group()
        layout.addWidget(processing_group)

        # 文件设置组
        file_group = self.create_file_group()
        layout.addWidget(file_group)

        # 界面设置组
        ui_group = self.create_ui_group()
        layout.addWidget(ui_group)

        layout.addStretch()

        # 按钮组
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.accept_settings)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self.apply_settings)
        layout.addWidget(button_box)

    def create_processing_group(self) -> QGroupBox:
        """创建处理设置组"""
        group = QGroupBox("处理设置")
        layout = QFormLayout(group)

        # GPU加速
        self.gpu_checkbox = QCheckBox("启用GPU加速")
        layout.addRow(self.gpu_checkbox)

        # 检测敏感度
        sensitivity_layout = QVBoxLayout()
        self.sensitivity_slider = QSlider(Qt.Horizontal)
        self.sensitivity_slider.setMinimum(10)
        self.sensitivity_slider.setMaximum(90)
        self.sensitivity_slider.setValue(50)
        self.sensitivity_slider.valueChanged.connect(self.update_sensitivity_label)

        self.sensitivity_label = QLabel("0.5")
        sensitivity_layout.addWidget(self.sensitivity_slider)
        sensitivity_layout.addWidget(self.sensitivity_label)

        layout.addRow("默认检测敏感度:", sensitivity_layout)

        # 最大并发任务数
        self.concurrent_combo = QComboBox()
        self.concurrent_combo.addItems(["1", "2", "4", "8"])
        self.concurrent_combo.setCurrentText("4")
        layout.addRow("最大并发任务数:", self.concurrent_combo)

        return group

    def create_file_group(self) -> QGroupBox:
        """创建文件设置组"""
        group = QGroupBox("文件设置")
        layout = QFormLayout(group)

        # 输出目录
        output_layout = QHBoxLayout()
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setReadOnly(True)

        browse_button = QPushButton("选择")
        browse_button.clicked.connect(self.browse_output_directory)

        output_layout.addWidget(self.output_path_edit)
        output_layout.addWidget(browse_button)

        layout.addRow("默认输出目录:", output_layout)

        # 自动保存
        self.auto_save_checkbox = QCheckBox("自动保存结果")
        layout.addRow(self.auto_save_checkbox)

        return group

    def create_ui_group(self) -> QGroupBox:
        """创建界面设置组"""
        group = QGroupBox("界面设置")
        layout = QFormLayout(group)

        # 主题选择
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["浅色", "深色", "跟随系统"])
        layout.addRow("主题:", self.theme_combo)

        # 语言选择
        self.language_combo = QComboBox()
        self.language_combo.addItems(["中文", "English"])
        layout.addRow("语言:", self.language_combo)

        return group

    def load_settings(self):
        """加载当前设置"""
        # 处理设置
        self.gpu_checkbox.setChecked(self.current_settings.get('use_gpu', True))

        sensitivity = self.current_settings.get('confidence_threshold', 0.5)
        self.sensitivity_slider.setValue(int(sensitivity * 100))
        self.update_sensitivity_label(int(sensitivity * 100))

        concurrent = str(self.current_settings.get('max_concurrent_tasks', 4))
        self.concurrent_combo.setCurrentText(concurrent)

        # 文件设置
        output_path = self.current_settings.get('output_path', './outputs')
        self.output_path_edit.setText(output_path)

        self.auto_save_checkbox.setChecked(self.current_settings.get('auto_save', True))

        # 界面设置
        theme_map = {'light': '浅色', 'dark': '深色', 'system': '跟随系统'}
        theme = self.current_settings.get('theme', 'system')
        self.theme_combo.setCurrentText(theme_map.get(theme, '跟随系统'))

        language_map = {'zh_CN': '中文', 'en_US': 'English'}
        language = self.current_settings.get('language', 'zh_CN')
        self.language_combo.setCurrentText(language_map.get(language, '中文'))

    def get_settings(self) -> dict:
        """获取当前设置"""
        theme_map = {'浅色': 'light', '深色': 'dark', '跟随系统': 'system'}
        language_map = {'中文': 'zh_CN', 'English': 'en_US'}

        return {
            'use_gpu': self.gpu_checkbox.isChecked(),
            'confidence_threshold': self.sensitivity_slider.value() / 100.0,
            'max_concurrent_tasks': int(self.concurrent_combo.currentText()),
            'output_path': self.output_path_edit.text(),
            'auto_save': self.auto_save_checkbox.isChecked(),
            'theme': theme_map.get(self.theme_combo.currentText(), 'system'),
            'language': language_map.get(self.language_combo.currentText(), 'zh_CN')
        }

    def update_sensitivity_label(self, value: int):
        """更新敏感度标签"""
        self.sensitivity_label.setText(f"{value / 100.0:.1f}")

    def browse_output_directory(self):
        """浏览输出目录"""
        directory = QFileDialog.getExistingDirectory(
            self, "选择输出目录", self.output_path_edit.text()
        )
        if directory:
            self.output_path_edit.setText(directory)

    def apply_settings(self):
        """应用设置"""
        new_settings = self.get_settings()
        self.current_settings.update(new_settings)
        self.settings_changed.emit(new_settings)

    def accept_settings(self):
        """接受并应用设置"""
        self.apply_settings()
        self.accept()
```
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>取消</Button>
          <Button onClick={handleSaveSettings}>保存设置</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
```

### 5.3 交互流程优化

#### 5.3.1 核心交互流程
```mermaid
graph TD
    A[用户启动应用] --> B[显示主界面]
    B --> C[左侧显示空的图片列表]
    C --> D[用户拖拽/选择图片]
    D --> E[图片添加到列表]
    E --> F[右侧显示处理状态]
    F --> G[用户点击开始处理]
    G --> H[显示实时处理进度]
    H --> I[处理完成]
    I --> J[列表项显示完成状态]
    J --> K[用户点击查看结果]
    K --> L[右侧切换到结果对比]
    L --> M[用户保存/分享结果]
    M --> N[返回状态视图或处理下一张]
```

#### 5.3.2 快捷操作设计
```python
# utils/keyboard_shortcuts.py
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QKeySequence, QShortcut

class KeyboardShortcutManager:
    """键盘快捷键管理器"""

    def __init__(self, main_window):
        self.main_window = main_window
        self.shortcuts = {}
        self.setup_shortcuts()

    def setup_shortcuts(self):
        """设置快捷键"""
        # Ctrl+O: 打开文件
        open_shortcut = QShortcut(QKeySequence.Open, self.main_window)
        open_shortcut.activated.connect(self.main_window.open_files)
        self.shortcuts['open'] = open_shortcut

        # Ctrl+S: 保存当前结果
        save_shortcut = QShortcut(QKeySequence.Save, self.main_window)
        save_shortcut.activated.connect(self.main_window.save_current_result)
        self.shortcuts['save'] = save_shortcut

        # Space: 开始/暂停处理
        space_shortcut = QShortcut(QKeySequence(Qt.Key_Space), self.main_window)
        space_shortcut.activated.connect(self.main_window.toggle_processing)
        self.shortcuts['toggle'] = space_shortcut

        # Escape: 返回状态视图
        escape_shortcut = QShortcut(QKeySequence(Qt.Key_Escape), self.main_window)
        escape_shortcut.activated.connect(self.main_window.return_to_status)
        self.shortcuts['escape'] = escape_shortcut

    def enable_shortcuts(self, enabled: bool = True):
        """启用或禁用快捷键"""
        for shortcut in self.shortcuts.values():
            shortcut.setEnabled(enabled)
```

### 5.4 响应式设计

#### 5.4.1 桌面端优化布局
```css
/* 桌面端主布局 */
.main-layout {
  display: flex;
  height: 100vh;
}

.image-list-panel {
  width: 40%;
  min-width: 320px;
  max-width: 480px;
  border-right: 1px solid #e5e7eb;
}

.content-panel {
  flex: 1;
  min-width: 600px;
}

/* 小屏幕适配 */
@media (max-width: 1024px) {
  .image-list-panel {
    width: 50%;
    min-width: 280px;
  }

  .content-panel {
    min-width: 400px;
  }
}

/* 超小屏幕 - 切换到垂直布局 */
@media (max-width: 768px) {
  .main-layout {
    flex-direction: column;
  }

  .image-list-panel {
    width: 100%;
    height: 40%;
    border-right: none;
    border-bottom: 1px solid #e5e7eb;
  }

  .content-panel {
    height: 60%;
    min-width: auto;
  }
}
```

## 6. 数据流设计

### 6.1 处理流程设计

#### 6.1.1 单图处理流程
```mermaid
graph TD
    A[用户选择图片] --> B[文件验证]
    B --> C[参数配置]
    C --> D[创建处理任务]
    D --> E[加载AI模型]
    E --> F[水印检测]
    F --> G[掩码生成]
    G --> H[图像修复]
    H --> I[结果保存]
    I --> J[显示结果]
    
    B --> K[验证失败]
    K --> L[错误提示]
    
    F --> M[检测失败]
    M --> N[降级处理]
    
    H --> O[修复失败]
    O --> P[备选方案]
```

#### 6.1.2 批处理流程
```mermaid
graph TD
    A[选择文件夹] --> B[扫描文件]
    B --> C[文件过滤]
    C --> D[创建任务队列]
    D --> E[并发处理]
    E --> F[任务调度]
    F --> G[单个处理]
    G --> H[结果收集]
    H --> I[进度更新]
    I --> J[完成报告]
    
    G --> K[处理失败]
    K --> L[错误记录]
    L --> M[继续下一个]
```

### 6.2 状态管理设计

#### 6.2.1 状态流转
```python
# models/task_state.py
from enum import Enum
from typing import Dict, List

class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 等待处理
    LOADING = "loading"         # 加载模型
    DETECTING = "detecting"     # 检测水印
    PROCESSING = "processing"   # 图像修复
    SAVING = "saving"          # 保存结果
    COMPLETED = "completed"     # 处理完成
    FAILED = "failed"          # 处理失败
    CANCELLED = "cancelled"     # 用户取消

class TaskStateManager:
    """任务状态管理器"""

    # 状态转换规则
    STATE_TRANSITIONS = {
        TaskStatus.PENDING: [TaskStatus.LOADING, TaskStatus.CANCELLED],
        TaskStatus.LOADING: [TaskStatus.DETECTING, TaskStatus.FAILED],
        TaskStatus.DETECTING: [TaskStatus.PROCESSING, TaskStatus.FAILED],
        TaskStatus.PROCESSING: [TaskStatus.SAVING, TaskStatus.FAILED, TaskStatus.CANCELLED],
        TaskStatus.SAVING: [TaskStatus.COMPLETED, TaskStatus.FAILED],
        TaskStatus.COMPLETED: [],
        TaskStatus.FAILED: [TaskStatus.PENDING],  # 支持重试
        TaskStatus.CANCELLED: [TaskStatus.PENDING]  # 支持重新开始
    }

    @classmethod
    def can_transition(cls, from_status: TaskStatus, to_status: TaskStatus) -> bool:
        """检查状态转换是否有效"""
        return to_status in cls.STATE_TRANSITIONS.get(from_status, [])

    @classmethod
    def get_valid_transitions(cls, current_status: TaskStatus) -> List[TaskStatus]:
        """获取当前状态的有效转换"""
        return cls.STATE_TRANSITIONS.get(current_status, [])
```

#### 6.2.2 事件驱动设计
```python
# models/events.py
from dataclasses import dataclass
from typing import Any, Dict, Optional
from enum import Enum

class EventType(Enum):
    """事件类型"""
    TASK_CREATED = "task_created"
    TASK_STARTED = "task_started"
    TASK_PROGRESS = "task_progress"
    TASK_COMPLETED = "task_completed"
    TASK_FAILED = "task_failed"
    MODEL_LOADED = "model_loaded"
    MODEL_ERROR = "model_error"
    SETTINGS_CHANGED = "settings_changed"

@dataclass
class AppEvent:
    """应用事件"""
    event_type: EventType
    task_id: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    progress: Optional[float] = None

class EventBus:
    """事件总线"""

    def __init__(self):
        self.subscribers = {}

    def subscribe(self, event_type: EventType, callback):
        """订阅事件"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        self.subscribers[event_type].append(callback)

    def unsubscribe(self, event_type: EventType, callback):
        """取消订阅"""
        if event_type in self.subscribers:
            self.subscribers[event_type].remove(callback)

    def emit(self, event: AppEvent):
        """发布事件"""
        if event.event_type in self.subscribers:
            for callback in self.subscribers[event.event_type]:
                callback(event)

# 全局事件总线实例
event_bus = EventBus()
```

## 7. 接口设计

### 7.1 Qt信号槽接口设计

#### 7.1.1 核心信号定义
```python
# models/signals.py
from PySide6.QtCore import QObject, Signal
from typing import Dict, Any, List

class ApplicationSignals(QObject):
    """应用程序全局信号"""

    # 任务相关信号
    task_added = Signal(str)  # task_id
    task_started = Signal(str)  # task_id
    task_progress = Signal(str, float)  # task_id, progress
    task_completed = Signal(str, dict)  # task_id, result
    task_failed = Signal(str, str)  # task_id, error
    task_cancelled = Signal(str)  # task_id

    # 文件相关信号
    files_added = Signal(list)  # file_paths
    file_validated = Signal(str, bool)  # file_path, is_valid

    # 设置相关信号
    setting_changed = Signal(str, object)  # setting_name, value
    settings_loaded = Signal(dict)  # settings

    # 系统状态信号
    memory_warning = Signal(float)  # memory_usage_percent
    gpu_status_changed = Signal(bool)  # gpu_available

    # 应用状态信号
    app_state_changed = Signal(str)  # new_state
    error_occurred = Signal(str, str)  # error_type, error_message

# 全局信号实例
app_signals = ApplicationSignals()
```

#### 7.1.2 服务接口设计
```python
# services/interfaces.py
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Callable
from pathlib import Path

class IFileService(ABC):
    """文件服务接口"""

    @abstractmethod
    def validate_file(self, file_path: Path) -> Dict[str, Any]:
        """验证文件"""
        pass

    @abstractmethod
    def scan_directory(self, directory: Path, recursive: bool = True) -> List[Path]:
        """扫描目录"""
        pass

    @abstractmethod
    def generate_output_path(self, input_path: Path, suffix: str = "_processed") -> Path:
        """生成输出路径"""
        pass

class ITaskService(ABC):
    """任务服务接口"""

    @abstractmethod
    def create_task(self, input_path: Path, params: Dict[str, Any]) -> str:
        """创建任务"""
        pass

    @abstractmethod
    def start_task(self, task_id: str) -> bool:
        """开始任务"""
        pass

    @abstractmethod
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        pass

    @abstractmethod
    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        pass
```

### 7.2 AI服务接口

#### 7.2.1 AI处理接口定义
```python
# services/ai_service.py - 基于实际的WatermarkRemovalPipeline
from typing import Dict, Any, Optional, Callable, List, Union
from pathlib import Path
import logging
import torch
from PIL import Image

# 导入实际的水印去除管道
from models.watermark_removal_pipeline import WatermarkRemovalPipeline

class WatermarkAIService:
    """水印AI服务 - 封装WatermarkRemovalPipeline"""

    def __init__(self, models_dir: Path = None):
        """
        初始化水印AI服务

        Args:
            models_dir: 模型目录，包含:
                - yolo/yolo11x-train28-best.pt (YOLO11检测模型)
                - lama/big-lama/models/best.ckpt (LAMA修复模型)
        """
        self.models_dir = models_dir or Path("models")
        self.pipeline = None
        self.is_loaded = False
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 模型路径配置
        self.detector_path = self.models_dir / "yolo" / "yolo11x-train28-best.pt"
        self.inpainter_path = self.models_dir / "lama" / "big-lama" / "models" / "best.ckpt"

        logging.info(f"水印AI服务初始化 - 设备: {self.device}")

    def load_models(self) -> bool:
        """加载AI模型"""
        try:
            logging.info("正在加载水印检测和修复模型...")

            # 检查模型文件是否存在
            if not self.detector_path.exists():
                logging.error(f"YOLO检测模型不存在: {self.detector_path}")
                return False

            if not self.inpainter_path.exists():
                logging.error(f"LAMA修复模型不存在: {self.inpainter_path}")
                return False

            # 初始化水印去除管道
            self.pipeline = WatermarkRemovalPipeline(
                detector_model_path=str(self.detector_path),
                inpainter_model_path=str(self.inpainter_path),
                device=str(self.device)
            )

            self.is_loaded = True
            logging.info("✅ 水印去除管道加载成功")
            return True

        except Exception as e:
            logging.error(f"❌ AI模型加载失败: {e}")
            self.is_loaded = False
            return False

    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.is_loaded

    def process_image(
        self,
        input_path: Union[Path, str],
        output_path: Union[Path, str],
        confidence_threshold: float = 0.3,
        enhance_mask: bool = True,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> Dict[str, Any]:
        """
        处理图像去除水印

        Args:
            input_path: 输入图像路径
            output_path: 输出图像路径
            confidence_threshold: 检测置信度阈值
            enhance_mask: 是否增强掩码
            use_smart_enhancement: 是否使用智能增强
            context_expansion_ratio: 上下文扩展比例
            progress_callback: 进度回调函数

        Returns:
            处理结果字典
        """
        if not self.is_loaded:
            return {"success": False, "error": "AI模型未加载"}

        try:
            if progress_callback:
                progress_callback(0.1)

            # 调用实际的水印去除管道
            result = self.pipeline.remove_watermark(
                image=input_path,
                confidence_threshold=confidence_threshold,
                enhance_mask=enhance_mask,
                use_smart_enhancement=use_smart_enhancement,
                context_expansion_ratio=context_expansion_ratio,
                return_intermediate=True
            )

            if progress_callback:
                progress_callback(0.8)

            # 保存结果
            if isinstance(result, dict) and 'result_image' in result:
                result['result_image'].save(str(output_path), 'JPEG', quality=95)

                if progress_callback:
                    progress_callback(1.0)

                return {
                    "success": True,
                    "output_path": str(output_path),
                    "processing_time": result.get('processing_time', 0),
                    "detection_time": result.get('detection_time', 0),
                    "inpainting_time": result.get('inpainting_time', 0),
                    "watermark_count": len(result.get('detection_info', [])),
                    "confidence": result.get('confidence', 0),
                    "watermark_detected": result.get('watermark_detected', False),
                    "detection_info": result.get('detection_info', [])
                }
            else:
                # 简单结果处理
                if hasattr(result, 'save'):
                    result.save(str(output_path), 'JPEG', quality=95)

                if progress_callback:
                    progress_callback(1.0)

                return {
                    "success": True,
                    "output_path": str(output_path),
                    "processing_time": 0,
                    "watermark_detected": False
                }

        except Exception as e:
            logging.error(f"图像处理失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def analyze_image(self, image_path: Union[Path, str]) -> Dict[str, Any]:
        """分析图像中的水印信息"""
        if not self.is_loaded:
            return {"success": False, "error": "AI模型未加载"}

        try:
            # 使用管道的分析功能
            analysis = self.pipeline.analyze_image(image_path)
            return {"success": True, **analysis}
        except Exception as e:
            logging.error(f"图像分析失败: {e}")
            return {"success": False, "error": str(e)}

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "detector": {
                "name": "YOLO11 Watermark Detector",
                "model": "yolo11x-train28-best.pt",
                "path": str(self.detector_path),
                "exists": self.detector_path.exists()
            },
            "inpainter": {
                "name": "LAMA Inpainter",
                "model": "best.ckpt",
                "path": str(self.inpainter_path),
                "exists": self.inpainter_path.exists()
            },
            "device": str(self.device),
            "is_loaded": self.is_loaded
        }

    def get_supported_formats(self) -> List[str]:
        """获取支持的图像格式"""
        return ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff']
```

#### 7.2.2 控制器接口设计
```python
# presenters/interfaces.py
from abc import ABC, abstractmethod
from typing import Any

class IPresenter(ABC):
    """控制器基接口"""

    @abstractmethod
    def setup_connections(self):
        """设置信号槽连接"""
        pass

    @abstractmethod
    def cleanup(self):
        """清理资源"""
        pass

class IMainPresenter(IPresenter):
    """主控制器接口"""

    @abstractmethod
    def handle_files_dropped(self, file_paths: list):
        """处理文件拖拽"""
        pass

    @abstractmethod
    def handle_start_processing(self):
        """处理开始处理"""
        pass

    @abstractmethod
    def handle_cancel_task(self, task_id: str):
        """处理取消任务"""
        pass

class ITaskPresenter(IPresenter):
    """任务控制器接口"""

    @abstractmethod
    def update_task_progress(self, task_id: str, progress: float):
        """更新任务进度"""
        pass

    @abstractmethod
    def handle_task_completed(self, task_id: str, result: dict):
        """处理任务完成"""
        pass

    @abstractmethod
    def handle_task_failed(self, task_id: str, error: str):
        """处理任务失败"""
        pass
```

### 7.3 数据库接口设计

#### 7.3.1 任务数据接口
```python
# services/database_service.py
import sqlite3
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

class DatabaseService:
    """数据库服务"""

    def __init__(self, db_path: Path):
        self.db_path = db_path
        self.init_database()

    def init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.executescript("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    uuid TEXT UNIQUE NOT NULL,
                    input_path TEXT NOT NULL,
                    output_path TEXT,
                    status TEXT NOT NULL,
                    parameters TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    completed_at DATETIME,
                    error_message TEXT
                );

                CREATE TABLE IF NOT EXISTS settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE TABLE IF NOT EXISTS presets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    parameters TEXT NOT NULL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                );

                CREATE INDEX IF NOT EXISTS idx_tasks_status ON tasks(status);
                CREATE INDEX IF NOT EXISTS idx_tasks_created_at ON tasks(created_at);
            """)

    def create_task(self, task_data: Dict[str, Any]) -> bool:
        """创建任务"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    INSERT INTO tasks (uuid, input_path, output_path, status, parameters)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    task_data['uuid'],
                    task_data['input_path'],
                    task_data.get('output_path'),
                    task_data['status'],
                    json.dumps(task_data.get('parameters', {}))
                ))
            return True
        except Exception as e:
            print(f"创建任务失败: {e}")
            return False

    def update_task_status(self, task_id: str, status: str,
                          completed_at: Optional[datetime] = None,
                          error_message: Optional[str] = None) -> bool:
        """更新任务状态"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                if completed_at:
                    conn.execute("""
                        UPDATE tasks
                        SET status = ?, completed_at = ?, error_message = ?
                        WHERE uuid = ?
                    """, (status, completed_at, error_message, task_id))
                else:
                    conn.execute("""
                        UPDATE tasks
                        SET status = ?, error_message = ?
                        WHERE uuid = ?
                    """, (status, error_message, task_id))
            return True
        except Exception as e:
            print(f"更新任务状态失败: {e}")
            return False

    def get_task_history(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取任务历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.execute("""
                    SELECT * FROM tasks
                    ORDER BY created_at DESC
                    LIMIT ?
                """, (limit,))

                tasks = []
                for row in cursor.fetchall():
                    task = dict(row)
                    if task['parameters']:
                        task['parameters'] = json.loads(task['parameters'])
                    tasks.append(task)

                return tasks
        except Exception as e:
            print(f"获取任务历史失败: {e}")
            return []
            .collect::<Result<Vec<_>>>()?;

        Ok(tasks)
    }
}
```

## 8. 性能优化设计

### 8.1 内存管理优化

#### 8.1.1 模型缓存策略
```python
# utils/model_cache.py
import time
import threading
from typing import Optional, Dict, Any
from datetime import datetime, timedelta

class ModelCache:
    """模型缓存管理器"""

    def __init__(self, max_idle_time: int = 300):  # 5分钟
        self.detector_cache: Optional[Any] = None
        self.inpainter_cache: Optional[Any] = None
        self.last_used: Dict[str, datetime] = {}
        self.max_idle_time = timedelta(seconds=max_idle_time)
        self.lock = threading.Lock()

    def get_or_load_detector(self):
        """获取或加载检测器模型"""
        with self.lock:
            if self.detector_cache is not None:
                self.last_used["detector"] = datetime.now()
                return self.detector_cache
            else:
                detector = self._load_detector()
                self.detector_cache = detector
                self.last_used["detector"] = datetime.now()
                return detector

    def cleanup_unused(self):
        """清理未使用的模型"""
        now = datetime.now()

        with self.lock:
            if "detector" in self.last_used:
                if now - self.last_used["detector"] > self.max_idle_time:
                    self.detector_cache = None
                    del self.last_used["detector"]

    def _load_detector(self):
        """加载检测器模型"""
        # 实际的模型加载逻辑
        pass
```

#### 8.1.2 图像处理优化
```python
# utils/image_buffer.py
import numpy as np
from PIL import Image
from pathlib import Path
from typing import Tuple

class ImageBuffer:
    """优化的图像缓冲区"""

    def __init__(self, data: np.ndarray, width: int, height: int, channels: int):
        self.data = data
        self.width = width
        self.height = height
        self.channels = channels

    @classmethod
    def from_file(cls, path: Path) -> 'ImageBuffer':
        """从文件加载图像"""
        with Image.open(path) as img:
            # 转换为RGB格式
            if img.mode != 'RGB':
                img = img.convert('RGB')

            # 转换为numpy数组
            data = np.array(img)
            height, width, channels = data.shape

            return cls(data, width, height, channels)

    def resize_if_needed(self, max_size: int) -> bool:
        """如果需要则调整图像大小"""
        if self.width > max_size or self.height > max_size:
            scale = min(max_size / self.width, max_size / self.height, 1.0)
            new_width = int(self.width * scale)
            new_height = int(self.height * scale)

            # 使用高质量缩放算法
            self.resize(new_width, new_height)
            return True
        return False

    def resize(self, new_width: int, new_height: int):
        """调整图像大小"""
        img = Image.fromarray(self.data)
        resized_img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)

        self.data = np.array(resized_img)
        self.width = new_width
        self.height = new_height
```

### 8.2 并发处理优化

#### 8.2.1 任务队列设计
```python
# utils/task_queue.py
import threading
from queue import Queue, Empty
from typing import Callable, Optional
import time

class TaskQueue:
    """任务队列管理器"""

    def __init__(self, max_concurrent: int = 4):
        self.task_queue = Queue()
        self.max_concurrent = max_concurrent
        self.semaphore = threading.Semaphore(max_concurrent)
        self.workers = []
        self.is_running = False

        # 回调函数
        self.on_task_completed: Optional[Callable] = None
        self.on_task_failed: Optional[Callable] = None

    def start(self):
        """启动工作线程"""
        self.is_running = True
        for i in range(self.max_concurrent):
            worker = TaskWorker(
                f"Worker-{i}",
                self.task_queue,
                self.semaphore,
                self.on_task_completed,
                self.on_task_failed
            )
            worker.start()
            self.workers.append(worker)

    def submit_task(self, task) -> bool:
        """提交任务"""
        if self.is_running:
            self.task_queue.put(task)
            return True
        return False

class TaskWorker(threading.Thread):
    """任务工作线程"""

    def __init__(self, name: str, task_queue: Queue, semaphore: threading.Semaphore,
                 on_completed: Optional[Callable] = None,
                 on_failed: Optional[Callable] = None):
        super().__init__(name=name, daemon=True)
        self.task_queue = task_queue
        self.semaphore = semaphore
        self.on_completed = on_completed
        self.on_failed = on_failed

    def run(self):
        """运行工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=1.0)
                if task is None:  # 停止信号
                    break

                self.semaphore.acquire()
                try:
                    # 处理任务
                    from ..services.ai_service import WatermarkProcessor
                    processor = WatermarkProcessor()

                    result = processor.process_image(
                        task.input_path,
                        task.output_path,
                        task.parameters.__dict__
                    )

                    if result.get("success"):
                        if self.on_completed:
                            self.on_completed(task.id, result)
                    else:
                        if self.on_failed:
                            self.on_failed(task.id, result.get("error", "Unknown error"))

                except Exception as e:
                    if self.on_failed:
                        self.on_failed(task.id, str(e))
                finally:
                    self.semaphore.release()
                    self.task_queue.task_done()

            except Empty:
                continue
```

### 8.3 GPU加速优化

#### 8.3.1 GPU资源管理
```python
# GPU资源管理
import torch
import psutil
from typing import Optional

class GPUManager:
    def __init__(self):
        self.device = self._select_device()
        self.memory_threshold = 0.8

    def _select_device(self) -> str:
        if torch.cuda.is_available():
            # 选择显存最多的GPU
            gpu_count = torch.cuda.device_count()
            max_memory = 0
            best_device = 0

            for i in range(gpu_count):
                memory = torch.cuda.get_device_properties(i).total_memory
                if memory > max_memory:
                    max_memory = memory
                    best_device = i

            return f"cuda:{best_device}"
        else:
            return "cpu"

    def check_memory_usage(self) -> float:
        if self.device.startswith("cuda"):
            torch.cuda.synchronize()
            allocated = torch.cuda.memory_allocated()
            total = torch.cuda.get_device_properties(self.device).total_memory
            return allocated / total
        else:
            return psutil.virtual_memory().percent / 100

    def cleanup_if_needed(self):
        if self.check_memory_usage() > self.memory_threshold:
            if self.device.startswith("cuda"):
                torch.cuda.empty_cache()
            import gc
            gc.collect()
```

## 9. 安全性设计

### 9.1 数据安全

#### 9.1.1 本地数据加密
```python
# utils/encryption.py
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64

class DataEncryption:
    """数据加密工具"""

    def __init__(self, password: str = None):
        if password:
            self.key = self._derive_key(password)
        else:
            self.key = Fernet.generate_key()
        self.cipher = Fernet(self.key)

    def _derive_key(self, password: str) -> bytes:
        """从密码派生密钥"""
        password_bytes = password.encode()
        salt = b'watermark_remover_salt'  # 实际应用中应使用随机salt

        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password_bytes))
        return key

    def encrypt(self, data: bytes) -> bytes:
        """加密数据"""
        return self.cipher.encrypt(data)

    def decrypt(self, encrypted_data: bytes) -> bytes:
        """解密数据"""
        return self.cipher.decrypt(encrypted_data)
```

#### 9.1.2 临时文件安全
```python
# utils/secure_temp.py
import tempfile
import os
import uuid
from pathlib import Path

class SecureTempFile:
    """安全临时文件"""

    def __init__(self, extension: str = ""):
        filename = f"{uuid.uuid4()}.{extension}" if extension else str(uuid.uuid4())
        self.temp_dir = Path(tempfile.gettempdir())
        self.path = self.temp_dir / filename

        # 创建文件并设置权限（仅所有者可读写）
        self.file = open(self.path, 'w+b')
        os.chmod(self.path, 0o600)

    def write(self, data: bytes):
        """写入数据"""
        self.file.write(data)
        self.file.flush()

    def read(self) -> bytes:
        """读取数据"""
        self.file.seek(0)
        return self.file.read()

    def close(self):
        """关闭文件"""
        if hasattr(self, 'file') and self.file:
            self.file.close()

    def __del__(self):
        """安全删除文件"""
        self.close()
        if hasattr(self, 'path') and self.path.exists():
            try:
                os.remove(self.path)
            except Exception:
                pass  # 忽略删除错误
```

### 9.2 模型安全验证

#### 9.2.1 模型文件校验
```python
# utils/model_validator.py
import hashlib
from pathlib import Path
from typing import Dict

class ModelValidator:
    """模型文件校验器"""

    def __init__(self):
        self.expected_hashes = {
            "yolo_model.pt": "abc123...",  # 实际应用中应使用真实的哈希值
            "lama_model.pt": "def456...",
        }

    def validate_model(self, model_path: Path) -> bool:
        """验证模型文件"""
        try:
            model_name = model_path.name

            if model_name not in self.expected_hashes:
                return False

            expected_hash = self.expected_hashes[model_name]
            actual_hash = self.calculate_file_hash(model_path)

            return actual_hash == expected_hash

        except Exception:
            return False

    def calculate_file_hash(self, path: Path) -> str:
        """计算文件哈希值"""
        hasher = hashlib.sha256()

        with open(path, 'rb') as f:
            while True:
                chunk = f.read(8192)
                if not chunk:
                    break
                hasher.update(chunk)

        return hasher.hexdigest()
```

## 10. PyInstaller部署和打包策略

### 10.1 PyInstaller配置

#### 10.1.1 基础打包配置
```python
# build.spec - PyInstaller配置文件
# -*- mode: python ; coding: utf-8 -*-

import sys
from pathlib import Path

# 项目根目录
project_root = Path(__file__).parent

# 数据文件和资源
datas = [
    # AI模型文件
    (str(project_root / 'models'), 'models'),
    # 配置文件
    (str(project_root / 'config'), 'config'),
    # UI资源文件
    (str(project_root / 'src' / 'resources'), 'resources'),
    # 图标和样式
    (str(project_root / 'assets'), 'assets'),
]

# 隐藏导入（PyInstaller可能无法自动检测的模块）
hiddenimports = [
    'torch',
    'torchvision',
    'cv2',
    'PIL',
    'numpy',
    'ultralytics',
    'PySide6.QtCore',
    'PySide6.QtWidgets',
    'PySide6.QtGui',
    'sqlite3',
    'json',
    'pathlib',
]

# 排除的模块（减少打包大小）
excludes = [
    'tkinter',
    'matplotlib',
    'jupyter',
    'IPython',
    'pytest',
    'sphinx',
]

# 分析配置
a = Analysis(
    ['src/main.py'],
    pathex=[str(project_root)],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 优化：移除重复文件
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 可执行文件配置
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='WatermarkRemover',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,  # 启用UPX压缩
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # 无控制台窗口
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=str(project_root / 'assets' / 'icon.ico'),  # Windows图标
)

# macOS应用包配置
if sys.platform == 'darwin':
    app = BUNDLE(
        exe,
        name='WatermarkRemover.app',
        icon=str(project_root / 'assets' / 'icon.icns'),  # macOS图标
        bundle_identifier='com.watermark-remover.app',
        info_plist={
            'CFBundleDisplayName': '水印去除工具',
            'CFBundleVersion': '1.0.0',
            'CFBundleShortVersionString': '1.0.0',
            'NSHighResolutionCapable': True,
            'NSRequiresAquaSystemAppearance': False,
        },
    )
```

#### 10.1.2 构建脚本
```python
# build.py - 自动化构建脚本
import os
import sys
import shutil
import subprocess
from pathlib import Path
import platform

class BuildManager:
    """构建管理器"""

    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / 'dist'
        self.build_dir = self.project_root / 'build'
        self.platform = platform.system().lower()

    def clean(self):
        """清理构建目录"""
        print("清理构建目录...")
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)

        # 清理PyInstaller缓存
        pycache_dirs = list(self.project_root.rglob('__pycache__'))
        for cache_dir in pycache_dirs:
            shutil.rmtree(cache_dir)

    def install_dependencies(self):
        """安装依赖"""
        print("安装依赖...")
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r',
            str(self.project_root / 'requirements.txt')
        ], check=True)

        # 安装PyInstaller
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'pyinstaller'
        ], check=True)

    def optimize_models(self):
        """优化AI模型"""
        print("优化AI模型...")
        models_dir = self.project_root / 'models'

        # 这里可以添加模型优化逻辑
        # 例如：模型量化、压缩等
        pass

    def build_application(self):
        """构建应用程序"""
        print(f"为 {self.platform} 平台构建应用程序...")

        spec_file = self.project_root / 'build.spec'
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            str(spec_file)
        ]

        subprocess.run(cmd, check=True, cwd=self.project_root)

    def create_installer(self):
        """创建安装包"""
        print("创建安装包...")

        if self.platform == 'windows':
            self._create_windows_installer()
        elif self.platform == 'darwin':
            self._create_macos_installer()
        elif self.platform == 'linux':
            self._create_linux_installer()

    def _create_windows_installer(self):
        """创建Windows安装包"""
        # 使用NSIS或Inno Setup创建安装包
        nsis_script = self.project_root / 'installer' / 'windows.nsi'
        if nsis_script.exists():
            subprocess.run(['makensis', str(nsis_script)], check=True)

    def _create_macos_installer(self):
        """创建macOS安装包"""
        # 创建DMG文件
        app_path = self.dist_dir / 'WatermarkRemover.app'
        dmg_path = self.dist_dir / 'WatermarkRemover.dmg'

        if app_path.exists():
            subprocess.run([
                'hdiutil', 'create', '-volname', 'WatermarkRemover',
                '-srcfolder', str(app_path), '-ov', '-format', 'UDZO',
                str(dmg_path)
            ], check=True)

    def _create_linux_installer(self):
        """创建Linux安装包"""
        # 创建AppImage或DEB包
        pass

    def run_tests(self):
        """运行测试"""
        print("运行测试...")
        subprocess.run([
            sys.executable, '-m', 'pytest', 'tests/', '-v'
        ], check=True)

    def build(self, skip_tests=False):
        """完整构建流程"""
        try:
            self.clean()
            self.install_dependencies()

            if not skip_tests:
                self.run_tests()

            self.optimize_models()
            self.build_application()
            self.create_installer()

            print("构建完成！")

        except subprocess.CalledProcessError as e:
            print(f"构建失败: {e}")
            sys.exit(1)

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='构建水印去除工具')
    parser.add_argument('--skip-tests', action='store_true', help='跳过测试')
    parser.add_argument('--clean-only', action='store_true', help='仅清理')

    args = parser.parse_args()

    builder = BuildManager()

    if args.clean_only:
        builder.clean()
    else:
        builder.build(skip_tests=args.skip_tests)
```

### 10.2 分发策略

#### 10.2.1 模型分发设计
```python
# services/model_downloader.py
import requests
from pathlib import Path
from typing import Optional, Callable

class ModelDownloader:
    """模型下载器"""

    def __init__(self, base_url: str, models_dir: Path):
        self.base_url = base_url
        self.models_dir = models_dir
        self.models_dir.mkdir(parents=True, exist_ok=True)
        self.session = requests.Session()

    def download_model(self, model_name: str, progress_callback: Optional[Callable] = None) -> bool:
        """下载模型"""
        try:
            url = f"{self.base_url}/models/{model_name}"
            response = self.session.get(url, stream=True)
            response.raise_for_status()

            total_size = int(response.headers.get('content-length', 0))
            downloaded = 0

            model_path = self.models_dir / model_name

            with open(model_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)

                        if progress_callback and total_size > 0:
                            progress = downloaded / total_size
                            progress_callback(model_name, progress)

            # 验证下载的模型
            from .model_validator import ModelValidator
            validator = ModelValidator()
            if not validator.validate_model(model_path):
                model_path.unlink()
                raise Exception("Model validation failed")

            return True

        except Exception as e:
            print(f"下载模型 {model_name} 失败: {e}")
            return False
```

### 10.3 自动更新机制

#### 10.3.1 更新检查
```python
import requests
from PySide6.QtWidgets import QMessageBox
from PySide6.QtCore import QThread, Signal

class UpdateChecker(QThread):
    """更新检查器"""

    update_available = Signal(dict)
    update_failed = Signal(str)

    def __init__(self, current_version: str, update_url: str):
        super().__init__()
        self.current_version = current_version
        self.update_url = update_url

    def run(self):
        """检查更新"""
        try:
            response = requests.get(f"{self.update_url}/latest", timeout=10)
            response.raise_for_status()

            update_info = response.json()
            latest_version = update_info.get("version")

            if self.is_newer_version(latest_version, self.current_version):
                self.update_available.emit(update_info)

        except Exception as e:
            self.update_failed.emit(str(e))

    def is_newer_version(self, latest: str, current: str) -> bool:
        """比较版本号"""
        def version_tuple(v):
            return tuple(map(int, (v.split("."))))
        return version_tuple(latest) > version_tuple(current)

def check_for_updates(current_version: str, update_url: str, parent_widget):
    """检查更新"""
    def show_update_dialog(update_info: dict):
        version = update_info.get("version", "未知")
        description = update_info.get("description", "无更新说明")

        message = f"发现新版本 {version}，是否立即更新？\n\n更新内容：\n{description}"

        reply = QMessageBox.question(
            parent_widget,
            "软件更新",
            message,
            QMessageBox.Yes | QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            download_url = update_info.get("download_url", "")
            QMessageBox.information(parent_widget, "更新提示", f"请访问 {download_url} 下载最新版本")

    checker = UpdateChecker(current_version, update_url)
    checker.update_available.connect(show_update_dialog)
    checker.start()
    return checker

## 11. 测试策略

### 11.1 测试架构

#### 11.1.1 测试分层
```
测试金字塔
├── E2E测试 (5%)
│   ├── 完整用户流程测试
│   ├── 跨平台兼容性测试
│   └── 性能基准测试
├── 集成测试 (25%)
│   ├── Qt信号槽通信测试
│   ├── AI模型集成测试
│   ├── 数据库操作测试
│   └── 文件系统操作测试
└── 单元测试 (70%)
    ├── PySide6组件测试
    ├── Python函数测试
    ├── AI模型测试
    └── 工具函数测试
```

#### 11.1.2 测试工具配置
```txt
# requirements-dev.txt - Python测试依赖
pytest>=7.0.0
pytest-qt>=4.0.0
pytest-cov>=4.0.0
pytest-mock>=3.0.0
pytest-benchmark>=4.0.0
pytest-xvfb>=2.0.0  # Linux GUI测试
```

```toml
# pyproject.toml - Python测试配置
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=html",
    "--cov-report=term-missing"
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]
```

### 11.2 核心测试用例

#### 11.2.1 图像处理测试
```python
# tests/test_image_processing.py
import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from src.services.ai_service import WatermarkProcessor
from src.models.task_model import ProcessingParams

class TestImageProcessing:
    """图像处理测试类"""

    @pytest.fixture
    def temp_dir(self):
        """临时目录fixture"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def processor(self):
        """处理器fixture"""
        processor = WatermarkProcessor(Path("./models"))
        processor.is_loaded = True  # 模拟已加载
        return processor

    def test_image_processing_pipeline(self, temp_dir, processor):
        """测试图像处理流程"""
        input_path = temp_dir / "test_input.jpg"
        output_path = temp_dir / "test_output.jpg"

        # 创建测试图像
        self.create_test_image(input_path)

        params = {
            'confidence_threshold': 0.5,
            'enhance_mask': True,
            'use_gpu': False
        }

        # 模拟处理结果
        with patch.object(processor, 'detector') as mock_detector, \
             patch.object(processor, 'inpainter') as mock_inpainter:

            mock_detector.detect.return_value = [{'bbox': [10, 10, 50, 50]}]
            mock_inpainter.inpaint.return_value = {'processing_time': 1.5}

            result = processor.process_image(
                input_path, output_path, params
            )

            assert result['success'] is True
            assert 'processing_time' in result
            assert result['processing_time'] > 0

    def test_parameter_validation(self):
        """测试参数验证"""
        # 测试无效的置信度阈值
        invalid_params = ProcessingParams(confidence_threshold=1.5)

        with pytest.raises(ValueError):
            invalid_params.validate()

        # 测试有效参数
        valid_params = ProcessingParams(confidence_threshold=0.5)
        assert valid_params.validate() is True

    def test_model_loading(self):
        """测试模型加载"""
        processor = WatermarkProcessor(Path("./models"))

        # 模拟模型加载失败
        with patch('src.services.ai_service.WatermarkDetector') as mock_detector:
            mock_detector.side_effect = Exception("模型文件不存在")

            result = processor.load_models()
            assert result is False
            assert processor.is_loaded is False

    @staticmethod
    def create_test_image(path: Path):
        """创建测试图像"""
        from PIL import Image
        import numpy as np

        # 创建一个简单的测试图像
        image_array = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        image = Image.fromarray(image_array)
        image.save(path)
```

#### 11.2.2 GUI组件测试
```python
# tests/test_gui_components.py
import pytest
from unittest.mock import Mock, patch
from PySide6.QtWidgets import QApplication
from PySide6.QtTest import QTest
from PySide6.QtCore import Qt, QMimeData
from PySide6.QtGui import QDragEnterEvent, QDropEvent

from src.views.task_list_widget import TaskListWidget
from src.views.main_window import MainWindow

class TestTaskListWidget:
    """任务列表组件测试"""

    @pytest.fixture
    def app(self):
        """Qt应用程序fixture"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app

    @pytest.fixture
    def widget(self, app):
        """任务列表组件fixture"""
        widget = TaskListWidget()
        widget.show()
        return widget

    def test_file_drop_handling(self, widget):
        """测试文件拖拽处理"""
        # 模拟文件拖拽事件
        mime_data = QMimeData()
        mime_data.setUrls([])  # 这里应该设置实际的文件URL

        # 创建拖拽事件
        drag_event = QDragEnterEvent(
            widget.rect().center(),
            Qt.CopyAction,
            mime_data,
            Qt.LeftButton,
            Qt.NoModifier
        )

        # 测试拖拽进入
        widget.dragEnterEvent(drag_event)
        assert drag_event.isAccepted()

    def test_file_validation(self, widget):
        """测试文件验证"""
        # 模拟文件验证
        valid_files = ['test.jpg', 'test.png', 'test.webp']
        invalid_files = ['test.txt', 'test.pdf', 'test.doc']

        for file_name in valid_files:
            # 这里应该调用实际的文件验证逻辑
            assert file_name.split('.')[-1].lower() in ['jpg', 'jpeg', 'png', 'webp']

        for file_name in invalid_files:
            assert file_name.split('.')[-1].lower() not in ['jpg', 'jpeg', 'png', 'webp']

    def test_task_item_creation(self, widget):
        """测试任务项创建"""
        from src.models.task_model import Task
        from pathlib import Path

        # 创建测试任务
        task = Task(
            input_path=Path("test.jpg"),
            output_path=Path("output.jpg")
        )

        # 添加任务项
        widget.add_task_item(task)

        # 验证任务项已添加
        assert widget.task_list.count() == 1

class TestMainWindow:
    """主窗口测试"""

    @pytest.fixture
    def app(self):
        """Qt应用程序fixture"""
        if not QApplication.instance():
            app = QApplication([])
        else:
            app = QApplication.instance()
        yield app

    @pytest.fixture
    def main_window(self, app):
        """主窗口fixture"""
        window = MainWindow()
        window.show()
        return window

    def test_window_initialization(self, main_window):
        """测试窗口初始化"""
        assert main_window.windowTitle() == "水印去除工具"
        assert main_window.isVisible()

    def test_menu_actions(self, main_window):
        """测试菜单操作"""
        # 测试菜单栏存在
        assert main_window.menuBar() is not None

        # 测试文件菜单
        file_menu = None
        for action in main_window.menuBar().actions():
            if "文件" in action.text():
                file_menu = action.menu()
                break

        assert file_menu is not None
```

### 11.3 性能测试

#### 11.3.1 基准测试
```python
# tests/test_performance.py
import pytest
import time
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from src.services.ai_service import WatermarkProcessor

class TestPerformance:
    """性能测试类"""

    @pytest.fixture
    def processor(self):
        """处理器fixture"""
        processor = WatermarkProcessor(Path("./models"))
        processor.is_loaded = True
        return processor

    def test_image_processing_benchmark(self, processor):
        """图像处理基准测试"""
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = Path(temp_dir) / "test_1080p.jpg"
            output_path = Path(temp_dir) / "output.jpg"

            # 创建测试图像
            self.create_test_image(input_path, size=(1920, 1080))

            params = {
                'confidence_threshold': 0.5,
                'use_gpu': False
            }

            # 模拟处理过程
            with patch.object(processor, 'detector') as mock_detector, \
                 patch.object(processor, 'inpainter') as mock_inpainter:

                mock_detector.detect.return_value = []
                mock_inpainter.inpaint.return_value = {'processing_time': 2.5}

                start_time = time.time()
                result = processor.process_image(input_path, output_path, params)
                end_time = time.time()

                processing_time = end_time - start_time

                # 性能断言
                assert processing_time < 5.0  # 处理时间应小于5秒
                assert result['success'] is True

    def test_memory_usage(self, processor):
        """内存使用测试"""
        import psutil
        import os

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 模拟处理多个图像
        for i in range(10):
            with tempfile.NamedTemporaryFile(suffix='.jpg') as temp_file:
                input_path = Path(temp_file.name)
                output_path = Path(temp_file.name + '_output.jpg')

                params = {'confidence_threshold': 0.5}

                with patch.object(processor, 'detector') as mock_detector, \
                     patch.object(processor, 'inpainter') as mock_inpainter:

                    mock_detector.detect.return_value = []
                    mock_inpainter.inpaint.return_value = {'processing_time': 1.0}

                    processor.process_image(input_path, output_path, params)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该在合理范围内
        assert memory_increase < 500  # 内存增长应小于500MB

    @staticmethod
    def create_test_image(path: Path, size=(100, 100)):
        """创建测试图像"""
        from PIL import Image
        import numpy as np

        image_array = np.random.randint(0, 255, (*size, 3), dtype=np.uint8)
        image = Image.fromarray(image_array)
        image.save(path)
```
```

## 12. 监控和日志

### 12.1 日志系统设计

#### 12.1.1 结构化日志
```python
# utils/logger.py
import logging
import logging.handlers
from pathlib import Path
from datetime import datetime

def init_logging(log_dir: Path = None) -> logging.Logger:
    """初始化日志系统"""
    if log_dir is None:
        log_dir = Path("logs")

    log_dir.mkdir(exist_ok=True)

    # 创建日志器
    logger = logging.getLogger("watermark_remover")
    logger.setLevel(logging.DEBUG)

    # 清除现有处理器
    logger.handlers.clear()

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # 文件处理器（按日期轮转）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_dir / "app.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)

    return logger

# 使用示例
def process_image_with_logging(task_id: str, image_path: Path, params: dict):
    """带日志的图像处理"""
    logger = logging.getLogger("watermark_remover")

    logger.info(f"开始处理图像 - 任务ID: {task_id}, 路径: {image_path}")

    try:
        # 处理逻辑
        result = process_image(image_path, params)
        logger.info(f"图像处理完成 - 任务ID: {task_id}")
        return result
    except Exception as e:
        logger.error(f"图像处理失败 - 任务ID: {task_id}, 错误: {e}")
        raise

    let start_time = std::time::Instant::now();

    match do_process_image(image_path, params).await {
        Ok(result) => {
            let duration = start_time.elapsed();
            info!(
                task_id = %task_id,
                duration_ms = duration.as_millis(),
                confidence = result.confidence,
                "图像处理完成"
            );
            Ok(result)
        }
        Err(e) => {
            error!(task_id = %task_id, error = %e, "图像处理失败");
            Err(e)
        }
    }
}
```

### 12.2 性能监控

#### 12.2.1 指标收集
```python
# utils/metrics.py
import threading
import time
from typing import Dict, Any
from dataclasses import dataclass, field

@dataclass
class Metrics:
    """性能指标收集器"""

    tasks_processed: int = 0
    tasks_failed: int = 0
    total_processing_time: float = 0.0
    memory_usage_peak: int = 0

    # 线程锁
    _lock: threading.Lock = field(default_factory=threading.Lock)

    def record_task_completion(self, duration_ms: float):
        """记录任务完成"""
        with self._lock:
            self.tasks_processed += 1
            self.total_processing_time += duration_ms

    def record_task_failure(self):
        """记录任务失败"""
        with self._lock:
            self.tasks_failed += 1

    def record_memory_usage(self, memory_mb: int):
        """记录内存使用"""
        with self._lock:
            if memory_mb > self.memory_usage_peak:
                self.memory_usage_peak = memory_mb

    def get_average_processing_time(self) -> float:
        """获取平均处理时间"""
        with self._lock:
            if self.tasks_processed == 0:
                return 0.0
            return self.total_processing_time / self.tasks_processed

    def get_success_rate(self) -> float:
        """获取成功率"""
        with self._lock:
            total = self.tasks_processed + self.tasks_failed
            if total == 0:
                return 1.0
            return self.tasks_processed / total

    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self._lock:
            return {
                "tasks_processed": self.tasks_processed,
                "tasks_failed": self.tasks_failed,
                "success_rate": self.get_success_rate(),
                "average_processing_time": self.get_average_processing_time(),
                "memory_usage_peak": self.memory_usage_peak
            }

# 全局指标实例
metrics = Metrics()
```

## 13. 文档和用户支持

### 13.1 用户文档结构

```
docs/
├── user-guide/
│   ├── getting-started.md      # 快速开始指南
│   ├── basic-usage.md          # 基本使用方法
│   ├── batch-processing.md     # 批处理指南
│   ├── parameter-tuning.md     # 参数调优指南
│   └── troubleshooting.md      # 故障排除
├── technical/
│   ├── architecture.md         # 技术架构
│   ├── api-reference.md        # API参考
│   ├── development-guide.md    # 开发指南
│   └── deployment.md           # 部署指南
├── tutorials/
│   ├── first-watermark-removal.md  # 第一次去水印
│   ├── advanced-techniques.md      # 高级技巧
│   └── batch-workflow.md           # 批处理工作流
└── faq.md                      # 常见问题
```

### 13.2 内置帮助系统

#### 13.2.1 上下文帮助
```python
# views/help_dialog.py
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton,
    QListWidget, QListWidgetItem, QTextBrowser, QSplitter,
    QButtonGroup, QFrame
)
from PySide6.QtCore import Qt, Signal
from typing import List, Dict, Any
from dataclasses import dataclass
from enum import Enum

class HelpCategory(Enum):
    """帮助分类"""
    BASIC = "basic"
    ADVANCED = "advanced"
    TROUBLESHOOTING = "troubleshooting"

@dataclass
class HelpContent:
    """帮助内容"""
    id: str
    title: str
    content: str
    category: HelpCategory

class HelpDialog(QDialog):
    """帮助对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.help_contents = self.load_help_contents()
        self.filtered_contents = self.help_contents.copy()
        self.setup_ui()
        self.setup_connections()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("帮助")
        self.setMinimumSize(800, 600)
        self.setModal(True)

        layout = QVBoxLayout(self)

        # 搜索栏
        search_layout = QHBoxLayout()
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("搜索帮助内容...")
        search_layout.addWidget(self.search_edit)
        layout.addLayout(search_layout)

        # 分类过滤器
        category_frame = QFrame()
        category_layout = QHBoxLayout(category_frame)

        self.category_group = QButtonGroup()

        all_button = QPushButton("全部")
        all_button.setCheckable(True)
        all_button.setChecked(True)
        all_button.clicked.connect(lambda: self.filter_by_category("all"))
        self.category_group.addButton(all_button)
        category_layout.addWidget(all_button)

        basic_button = QPushButton("基础")
        basic_button.setCheckable(True)
        basic_button.clicked.connect(lambda: self.filter_by_category("basic"))
        self.category_group.addButton(basic_button)
        category_layout.addWidget(basic_button)

        advanced_button = QPushButton("高级")
        advanced_button.setCheckable(True)
        advanced_button.clicked.connect(lambda: self.filter_by_category("advanced"))
        self.category_group.addButton(advanced_button)
        category_layout.addWidget(advanced_button)

        troubleshooting_button = QPushButton("故障排除")
        troubleshooting_button.setCheckable(True)
        troubleshooting_button.clicked.connect(lambda: self.filter_by_category("troubleshooting"))
        self.category_group.addButton(troubleshooting_button)
        category_layout.addWidget(troubleshooting_button)

        category_layout.addStretch()
        layout.addWidget(category_frame)

        # 主要内容区域
        splitter = QSplitter(Qt.Horizontal)

        # 帮助项列表
        self.help_list = QListWidget()
        self.help_list.setMaximumWidth(300)
        splitter.addWidget(self.help_list)

        # 内容显示区域
        self.content_browser = QTextBrowser()
        splitter.addWidget(self.content_browser)

        layout.addWidget(splitter)

        # 更新帮助列表
        self.update_help_list()

    def setup_connections(self):
        """设置信号槽连接"""
        self.search_edit.textChanged.connect(self.filter_content)
        self.help_list.currentItemChanged.connect(self.show_content)

    def load_help_contents(self) -> List[HelpContent]:
        """加载帮助内容"""
        return [
            HelpContent(
                id="getting_started",
                title="快速开始",
                content="""
                <h2>快速开始</h2>
                <p>欢迎使用水印去除工具！</p>
                <h3>第一步：添加图片</h3>
                <p>您可以通过以下方式添加图片：</p>
                <ul>
                <li>拖拽图片文件到左侧区域</li>
                <li>点击"添加文件"按钮选择图片</li>
                <li>使用快捷键 Ctrl+O 打开文件对话框</li>
                </ul>
                <h3>第二步：开始处理</h3>
                <p>添加图片后，点击"开始批量处理"按钮即可开始去除水印。</p>
                """,
                category=HelpCategory.BASIC
            ),
            HelpContent(
                id="batch_processing",
                title="批量处理",
                content="""
                <h2>批量处理</h2>
                <p>本工具支持同时处理多张图片。</p>
                <h3>添加多个文件</h3>
                <p>您可以一次性选择多个图片文件，或者分批添加。</p>
                <h3>处理顺序</h3>
                <p>图片将按照添加的顺序依次处理。</p>
                <h3>并发处理</h3>
                <p>可以在设置中调整同时处理的图片数量。</p>
                """,
                category=HelpCategory.ADVANCED
            ),
            HelpContent(
                id="troubleshooting",
                title="常见问题",
                content="""
                <h2>常见问题</h2>
                <h3>处理失败怎么办？</h3>
                <p>如果图片处理失败，请检查：</p>
                <ul>
                <li>图片格式是否支持（JPG、PNG、WebP等）</li>
                <li>图片文件是否损坏</li>
                <li>磁盘空间是否充足</li>
                </ul>
                <h3>处理速度慢怎么办？</h3>
                <p>可以尝试：</p>
                <ul>
                <li>启用GPU加速（在设置中）</li>
                <li>减少并发任务数</li>
                <li>关闭其他占用资源的程序</li>
                </ul>
                """,
                category=HelpCategory.TROUBLESHOOTING
            )
        ]

    def filter_content(self, search_term: str):
        """过滤内容"""
        if not search_term:
            self.filtered_contents = self.help_contents.copy()
        else:
            self.filtered_contents = [
                content for content in self.help_contents
                if search_term.lower() in content.title.lower() or
                   search_term.lower() in content.content.lower()
            ]
        self.update_help_list()

    def filter_by_category(self, category: str):
        """按分类过滤"""
        if category == "all":
            self.filtered_contents = self.help_contents.copy()
        else:
            self.filtered_contents = [
                content for content in self.help_contents
                if content.category.value == category
            ]
        self.update_help_list()

    def update_help_list(self):
        """更新帮助列表"""
        self.help_list.clear()
        for content in self.filtered_contents:
            item = QListWidgetItem(content.title)
            item.setData(Qt.UserRole, content)
            self.help_list.addItem(item)

        # 选择第一项
        if self.help_list.count() > 0:
            self.help_list.setCurrentRow(0)

    def show_content(self, current_item, previous_item):
        """显示内容"""
        if current_item:
            content = current_item.data(Qt.UserRole)
            if content:
                self.content_browser.setHtml(content.content)
```

## 14. 总结

### 14.1 设计亮点

1. **模块化架构**: 清晰的MVP分层设计，便于维护和扩展
2. **跨平台支持**: 基于PySide6的统一代码基础
3. **性能优化**: 多层次的性能优化策略
4. **用户体验**: 直观的界面设计和流畅的交互
5. **安全性**: 本地处理和数据隐私保护
6. **简化部署**: PyInstaller单文件打包，简化分发

### 14.2 技术创新

1. **PySide6 + PyInstaller集成**: 成熟稳定的技术组合方案
2. **智能资源管理**: 自适应的内存和GPU管理
3. **异步任务调度**: 基于QThread的高效并发处理机制
4. **模型热加载**: 支持AI模型的动态加载和卸载
5. **跨平台优化**: 针对不同平台的特定优化

### 14.3 实施建议

1. **分阶段开发**: 采用MVP方式，优先实现核心功能
2. **持续集成**: 建立完善的CI/CD流程
3. **用户反馈**: 建立用户反馈收集和处理机制
4. **性能监控**: 实施全面的性能监控和优化
5. **文档维护**: 保持文档的及时更新和完善

### 14.4 风险控制

1. **技术风险**: 提前验证关键技术方案
2. **性能风险**: 建立性能基准和监控体系
3. **兼容性风险**: 多平台测试和验证
4. **用户体验风险**: 用户测试和反馈收集
5. **维护风险**: 建立完善的文档和知识库

## 15. PySide6 + PyInstaller 最佳实践

### 15.1 开发最佳实践

#### 15.1.1 项目结构最佳实践
```
watermark_remover/
├── src/                        # 源代码
│   ├── main.py                 # 应用程序入口
│   ├── models/                 # 数据模型
│   │   ├── __init__.py
│   │   ├── task_model.py
│   │   ├── config_model.py
│   │   └── ai_models/
│   ├── views/                  # PySide6界面
│   │   ├── __init__.py
│   │   ├── main_window.py
│   │   ├── task_list_widget.py
│   │   ├── content_widget.py
│   │   └── dialogs/
│   ├── presenters/             # 控制器
│   │   ├── __init__.py
│   │   ├── main_presenter.py
│   │   ├── task_presenter.py
│   │   └── settings_presenter.py
│   ├── services/               # 业务服务
│   │   ├── __init__.py
│   │   ├── file_service.py
│   │   ├── task_service.py
│   │   ├── config_service.py
│   │   └── ai_service.py
│   ├── utils/                  # 工具类
│   │   ├── __init__.py
│   │   ├── logger.py
│   │   ├── thread_utils.py
│   │   └── image_utils.py
│   └── resources/              # 资源文件
│       ├── ui/                 # UI文件
│       ├── icons/              # 图标
│       ├── styles/             # 样式表
│       └── translations/       # 国际化文件
├── models/                     # AI模型文件
├── config/                     # 配置文件
├── tests/                      # 测试代码
├── docs/                       # 文档
├── assets/                     # 静态资源
├── requirements.txt            # Python依赖
├── build.spec                  # PyInstaller配置
├── build.py                    # 构建脚本
└── README.md
```

#### 15.3.1 项目依赖配置
```txt
# requirements.txt - 基于实际的水印去除系统依赖
# GUI框架
PySide6>=6.5.0

# AI框架和模型
torch>=2.1.1
torchvision>=0.16.1
ultralytics>=8.0.0              # YOLO11模型支持
huggingface-hub>=0.16.0         # LAMA模型支持

# 图像处理
opencv-python-headless>=4.8.1.78
pillow>=10.1.0
numpy>=1.24.4

# 系统工具
psutil>=5.9.0                   # 系统监控
requests>=2.28.0                # 网络请求

# 数据处理
pydantic>=2.5.0                 # 数据验证

# 打包工具
pyinstaller>=5.0.0

# 开发和测试依赖
pytest>=7.0.0
pytest-qt>=4.0.0               # Qt GUI测试
pytest-cov>=4.0.0              # 代码覆盖率
pytest-mock>=3.10.0            # Mock测试
pytest-benchmark>=4.0.0        # 性能测试
```

#### 15.3.2 模型文件配置
```python
# config/models.json - AI模型配置
{
    "detector": {
        "name": "YOLO11 Watermark Detector",
        "path": "models/yolo/yolo11x-train28-best.pt",
        "type": "yolo",
        "version": "11.0",
        "input_size": [640, 640],
        "confidence_threshold": 0.3,
        "description": "基于YOLOv11训练的水印检测模型"
    },
    "inpainter": {
        "name": "LAMA Inpainter",
        "path": "models/lama/big-lama/models/best.ckpt",
        "type": "lama",
        "version": "1.0",
        "input_size": [512, 512],
        "description": "基于LAMA的高质量图像修复模型"
    },
    "pipeline": {
        "default_confidence": 0.3,
        "enhance_mask": true,
        "use_smart_enhancement": true,
        "context_expansion_ratio": 0.12,
        "max_image_size": 2048
    }
}
```

#### 15.1.2 代码组织最佳实践
```python
# main.py - 应用程序入口
import sys
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale
from PySide6.QtGui import QIcon

# 设置应用程序路径
if getattr(sys, 'frozen', False):
    # PyInstaller打包后的路径
    application_path = Path(sys.executable).parent
else:
    # 开发环境路径
    application_path = Path(__file__).parent

# 添加到Python路径
sys.path.insert(0, str(application_path))

from src.views.main_window import MainWindow
from src.presenters.main_presenter import MainWindowPresenter
from src.models.application_model import ApplicationModel
from src.utils.logger import setup_logging

class WatermarkRemoverApp:
    """水印去除应用程序"""

    def __init__(self):
        self.app = QApplication(sys.argv)
        self.setup_application()
        self.setup_logging()
        self.setup_translation()
        self.setup_mvc()

    def setup_application(self):
        """设置应用程序基本信息"""
        self.app.setApplicationName("水印去除工具")
        self.app.setApplicationVersion("1.0.0")
        self.app.setOrganizationName("WatermarkRemover")
        self.app.setOrganizationDomain("watermark-remover.com")

        # 设置应用程序图标
        icon_path = application_path / "assets" / "icon.png"
        if icon_path.exists():
            self.app.setWindowIcon(QIcon(str(icon_path)))

    def setup_logging(self):
        """设置日志"""
        log_dir = application_path / "logs"
        setup_logging(log_dir)

    def setup_translation(self):
        """设置国际化"""
        translator = QTranslator()
        locale = QLocale.system().name()

        translation_file = application_path / "src" / "resources" / "translations" / f"{locale}.qm"
        if translation_file.exists():
            translator.load(str(translation_file))
            self.app.installTranslator(translator)

    def setup_mvc(self):
        """设置MVC架构"""
        # 创建模型
        self.model = ApplicationModel()

        # 创建视图
        self.view = MainWindow()

        # 创建控制器
        self.presenter = MainWindowPresenter(self.view, self.model)

    def run(self):
        """运行应用程序"""
        self.view.show()
        return self.app.exec()

def main():
    """主函数"""
    try:
        app = WatermarkRemoverApp()
        sys.exit(app.run())
    except Exception as e:
        logging.critical(f"应用程序启动失败: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    main()
```

### 15.2 性能优化最佳实践

#### 15.2.1 内存管理优化
```python
# utils/memory_manager.py
import gc
import psutil
import torch
from typing import Optional
from PySide6.QtCore import QTimer, QObject, Signal

class MemoryManager(QObject):
    """内存管理器"""

    memory_warning = Signal(float)  # 内存使用率警告

    def __init__(self, warning_threshold: float = 0.8):
        super().__init__()
        self.warning_threshold = warning_threshold
        self.monitor_timer = QTimer()
        self.monitor_timer.timeout.connect(self.check_memory)
        self.monitor_timer.start(5000)  # 每5秒检查一次

    def check_memory(self):
        """检查内存使用情况"""
        memory_percent = psutil.virtual_memory().percent / 100

        if memory_percent > self.warning_threshold:
            self.memory_warning.emit(memory_percent)
            self.cleanup_memory()

    def cleanup_memory(self):
        """清理内存"""
        # 强制垃圾回收
        gc.collect()

        # 清理PyTorch缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()

    def get_memory_usage(self) -> dict:
        """获取内存使用信息"""
        memory = psutil.virtual_memory()
        return {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used
        }
```

#### 15.2.2 异步处理优化
```python
# utils/async_worker.py
from PySide6.QtCore import QThread, Signal, QMutex, QWaitCondition
from queue import Queue, Empty
import threading

class AsyncWorkerPool:
    """异步工作线程池"""

    def __init__(self, max_workers: int = 4):
        self.max_workers = max_workers
        self.workers = []
        self.task_queue = Queue()
        self.is_running = False

    def start(self):
        """启动线程池"""
        self.is_running = True
        for i in range(self.max_workers):
            worker = AsyncWorker(f"Worker-{i}", self.task_queue)
            worker.start()
            self.workers.append(worker)

    def stop(self):
        """停止线程池"""
        self.is_running = False

        # 向队列添加停止信号
        for _ in range(self.max_workers):
            self.task_queue.put(None)

        # 等待所有工作线程结束
        for worker in self.workers:
            worker.wait()

        self.workers.clear()

    def submit_task(self, func, *args, **kwargs):
        """提交任务"""
        if self.is_running:
            task = (func, args, kwargs)
            self.task_queue.put(task)

class AsyncWorker(QThread):
    """异步工作线程"""

    task_completed = Signal(object)
    task_failed = Signal(str)

    def __init__(self, name: str, task_queue: Queue):
        super().__init__()
        self.name = name
        self.task_queue = task_queue

    def run(self):
        """运行工作线程"""
        while True:
            try:
                task = self.task_queue.get(timeout=1.0)

                if task is None:  # 停止信号
                    break

                func, args, kwargs = task
                result = func(*args, **kwargs)
                self.task_completed.emit(result)

            except Empty:
                continue
            except Exception as e:
                self.task_failed.emit(str(e))
```

### 15.3 用户体验优化

#### 15.3.1 响应式界面设计
```python
# views/responsive_widget.py
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import QSize, Signal
from PySide6.QtGui import QResizeEvent

class ResponsiveWidget(QWidget):
    """响应式组件基类"""

    size_changed = Signal(QSize)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.breakpoints = {
            'small': 768,
            'medium': 1024,
            'large': 1440
        }
        self.current_breakpoint = 'large'

    def resizeEvent(self, event: QResizeEvent):
        """处理窗口大小变化"""
        super().resizeEvent(event)

        new_size = event.size()
        new_breakpoint = self.get_breakpoint(new_size.width())

        if new_breakpoint != self.current_breakpoint:
            self.current_breakpoint = new_breakpoint
            self.adapt_to_breakpoint(new_breakpoint)

        self.size_changed.emit(new_size)

    def get_breakpoint(self, width: int) -> str:
        """根据宽度获取断点"""
        if width < self.breakpoints['small']:
            return 'small'
        elif width < self.breakpoints['medium']:
            return 'medium'
        else:
            return 'large'

    def adapt_to_breakpoint(self, breakpoint: str):
        """适配到指定断点"""
        # 子类重写此方法实现响应式布局
        pass
```

## 16. 详细开发实施计划

### 16.1 开发阶段规划

#### 16.1.1 第一阶段：基础架构搭建（2-3周）
**目标**: 建立项目基础架构和开发环境

**任务清单**:
1. **环境搭建**
   - 安装Python 3.8+和PySide6开发环境
   - 配置PyInstaller打包环境
   - 设置代码版本控制（Git）
   - 配置IDE和调试环境

2. **项目结构创建**
   - 创建标准化的项目目录结构
   - 设置Python包管理（requirements.txt）
   - 创建基础配置文件
   - 建立日志系统

3. **MVP架构实现**
   - 实现基础的Model-View-Presenter架构
   - 创建主窗口框架
   - 实现基础的信号槽机制
   - 建立配置管理系统

4. **开发工具配置**
   - 配置代码格式化工具（black, isort）
   - 设置代码质量检查（pylint, mypy）
   - 建立单元测试框架（pytest）
   - 配置CI/CD基础

**交付物**:
- 可运行的基础应用框架
- 完整的开发环境文档
- 代码规范和开发指南
- 基础测试用例

#### 16.1.2 第二阶段：核心功能开发（4-5周）
**目标**: 实现核心的水印检测和去除功能

**任务清单**:
1. **AI模型集成**
   - 集成YOLOv11水印检测模型
   - 集成LAMA图像修复模型
   - 实现模型加载和缓存机制
   - 优化模型推理性能

2. **图像处理流程**
   - 实现图像文件验证和加载
   - 开发水印检测算法接口
   - 实现图像修复处理流程
   - 添加处理进度反馈机制

3. **文件管理系统**
   - 实现文件拖拽上传功能
   - 开发文件格式验证
   - 创建临时文件管理
   - 实现输出路径管理

4. **基础UI组件**
   - 设计主窗口布局
   - 实现任务列表组件
   - 创建进度显示组件
   - 开发结果预览组件

**交付物**:
- 完整的单图片处理功能
- 基础的用户界面
- 核心算法性能测试报告
- 功能演示视频

#### 16.1.3 第三阶段：高级功能开发（3-4周）
**目标**: 实现批处理、历史记录等高级功能

**任务清单**:
1. **批处理功能**
   - 实现多文件队列管理
   - 开发并发处理机制
   - 添加批处理进度监控
   - 实现失败重试机制

2. **历史记录系统**
   - 设计SQLite数据库结构
   - 实现任务历史记录
   - 开发历史记录查询界面
   - 添加数据导出功能

3. **参数配置系统**
   - 创建参数配置界面
   - 实现参数预设管理
   - 开发配置导入导出
   - 添加参数验证机制

4. **结果对比功能**
   - 实现原图与结果对比显示
   - 开发多种对比模式
   - 添加图像缩放和平移
   - 实现结果保存功能

**交付物**:
- 完整的批处理功能
- 历史记录管理系统
- 参数配置界面
- 结果对比和保存功能

#### 16.1.4 第四阶段：优化和打包（2-3周）
**目标**: 性能优化和应用打包

**任务清单**:
1. **性能优化**
   - 内存使用优化
   - GPU加速优化
   - 界面响应速度优化
   - 启动时间优化

2. **用户体验优化**
   - 界面美化和主题支持
   - 错误处理和用户提示
   - 快捷键和右键菜单
   - 帮助文档和提示

3. **PyInstaller打包**
   - 配置打包脚本
   - 优化打包大小
   - 处理依赖问题
   - 跨平台兼容性测试

4. **安装包制作**
   - Windows安装包（NSIS/Inno Setup）
   - macOS应用包（DMG）
   - Linux包（AppImage/DEB）
   - 数字签名和认证

**交付物**:
- 优化后的应用程序
- 跨平台安装包
- 性能测试报告
- 用户使用手册

#### 16.1.5 第五阶段：测试和发布（1-2周）
**目标**: 全面测试和正式发布

**任务清单**:
1. **全面测试**
   - 功能测试
   - 性能测试
   - 兼容性测试
   - 用户验收测试

2. **文档完善**
   - 用户使用指南
   - 开发者文档
   - API参考文档
   - 故障排除指南

3. **发布准备**
   - 版本号管理
   - 更新日志编写
   - 发布渠道准备
   - 用户反馈收集机制

**交付物**:
- 正式发布版本
- 完整文档集
- 测试报告
- 发布计划

### 16.2 风险控制和质量保证

#### 16.2.1 技术风险控制
1. **AI模型集成风险**
   - 提前验证模型兼容性
   - 准备模型降级方案
   - 建立模型性能基准

2. **PyInstaller打包风险**
   - 早期进行打包测试
   - 准备依赖问题解决方案
   - 建立多平台测试环境

3. **性能风险控制**
   - 建立性能监控机制
   - 设置性能基准线
   - 准备性能优化方案

#### 16.2.2 质量保证措施
1. **代码质量**
   - 代码审查制度
   - 自动化测试
   - 代码覆盖率要求
   - 静态代码分析

2. **用户体验质量**
   - 用户界面设计评审
   - 可用性测试
   - 用户反馈收集
   - 迭代改进机制

这个设计文档为基于PySide6 + PyInstaller的水印去除桌面客户端开发提供了全面的技术指导，涵盖了从架构设计到实施细节的各个方面，为项目的成功实施奠定了坚实的基础。
