# 部署指南

本文档介绍如何在不同环境中部署水印去除系统。

## 环境要求

### 最低要求
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 20GB 可用空间
- Python: 3.8+

### 推荐配置
- CPU: 8核心+
- 内存: 16GB+ RAM
- GPU: NVIDIA GPU with 8GB+ VRAM (支持CUDA 11.0+)
- 存储: 50GB+ SSD
- Python: 3.9+

## 本地开发环境

### 1. 克隆项目
```bash
git clone <repository-url>
cd watermark-detection
```

### 2. 创建虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate  # Windows
```

### 3. 安装依赖
```bash
pip install -r requirements.txt
```

### 4. 启动服务
```bash
python app.py
```

服务将在 `http://localhost:8000` 启动。

## Docker 部署

### 1. 创建 Dockerfile

```dockerfile
FROM python:3.9-slim

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    wget \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p uploads outputs logs models

# 暴露端口
EXPOSE 8000

# 设置环境变量
ENV PYTHONPATH=/app
ENV LOG_LEVEL=INFO

# 启动命令
CMD ["python", "app.py"]
```

### 2. 构建镜像
```bash
docker build -t watermark-removal:latest .
```

### 3. 运行容器
```bash
# CPU版本
docker run -d \
  --name watermark-removal \
  -p 8000:8000 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/logs:/app/logs \
  watermark-removal:latest

# GPU版本（需要nvidia-docker）
docker run -d \
  --name watermark-removal \
  --gpus all \
  -p 8000:8000 \
  -v $(pwd)/models:/app/models \
  -v $(pwd)/logs:/app/logs \
  watermark-removal:latest
```

## Docker Compose 部署

### 1. 创建 docker-compose.yml

```yaml
version: '3.8'

services:
  watermark-removal:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./models:/app/models
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - ./outputs:/app/outputs
    environment:
      - LOG_LEVEL=INFO
      - MAX_FILE_SIZE=52428800
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - watermark-removal
    restart: unless-stopped
```

### 2. 启动服务
```bash
docker-compose up -d
```

## 生产环境部署

### 1. 使用 Gunicorn

安装 Gunicorn：
```bash
pip install gunicorn
```

创建 `gunicorn.conf.py`：
```python
bind = "0.0.0.0:8000"
workers = 4
worker_class = "uvicorn.workers.UvicornWorker"
worker_connections = 1000
max_requests = 1000
max_requests_jitter = 100
timeout = 300
keepalive = 2
preload_app = True
```

启动服务：
```bash
gunicorn app:app -c gunicorn.conf.py
```

### 2. 使用 Supervisor

安装 Supervisor：
```bash
sudo apt-get install supervisor
```

创建配置文件 `/etc/supervisor/conf.d/watermark-removal.conf`：
```ini
[program:watermark-removal]
command=/path/to/venv/bin/gunicorn app:app -c gunicorn.conf.py
directory=/path/to/watermark-detection
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/watermark-removal.log
environment=PYTHONPATH="/path/to/watermark-detection"
```

启动服务：
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start watermark-removal
```

### 3. Nginx 反向代理

创建 Nginx 配置 `/etc/nginx/sites-available/watermark-removal`：
```nginx
server {
    listen 80;
    server_name your-domain.com;

    client_max_body_size 100M;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }

    location /outputs/ {
        alias /path/to/watermark-detection/outputs/;
        expires 1d;
        add_header Cache-Control "public, immutable";
    }
}
```

启用站点：
```bash
sudo ln -s /etc/nginx/sites-available/watermark-removal /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 云平台部署

### AWS EC2

1. **启动实例**
   - 选择 Deep Learning AMI
   - 实例类型：g4dn.xlarge 或更高
   - 安全组：开放 8000 端口

2. **安装应用**
```bash
# 连接到实例
ssh -i your-key.pem ubuntu@your-instance-ip

# 克隆代码
git clone <repository-url>
cd watermark-detection

# 安装依赖
pip install -r requirements.txt

# 启动服务
python app.py
```

### Google Cloud Platform

1. **创建 Compute Engine 实例**
```bash
gcloud compute instances create watermark-removal \
    --zone=us-central1-a \
    --machine-type=n1-standard-4 \
    --accelerator=type=nvidia-tesla-t4,count=1 \
    --image-family=pytorch-latest-gpu \
    --image-project=deeplearning-platform-release \
    --boot-disk-size=50GB \
    --maintenance-policy=TERMINATE
```

2. **部署应用**
```bash
# SSH 连接
gcloud compute ssh watermark-removal --zone=us-central1-a

# 部署步骤同 AWS
```

### Azure

1. **创建虚拟机**
```bash
az vm create \
    --resource-group myResourceGroup \
    --name watermark-removal \
    --image UbuntuLTS \
    --size Standard_NC6 \
    --admin-username azureuser \
    --generate-ssh-keys
```

## 监控和日志

### 1. 日志配置

设置环境变量：
```bash
export LOG_LEVEL=INFO
export LOG_DIR=/var/log/watermark-removal
```

### 2. 健康检查

创建健康检查脚本：
```bash
#!/bin/bash
curl -f http://localhost:8000/health || exit 1
```

### 3. 性能监控

使用 Prometheus + Grafana：

```yaml
# prometheus.yml
scrape_configs:
  - job_name: 'watermark-removal'
    static_configs:
      - targets: ['localhost:8000']
    metrics_path: '/metrics'
```

## 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 8000/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-port=8000/tcp
sudo firewall-cmd --reload
```

### 2. SSL/TLS 配置

使用 Let's Encrypt：
```bash
sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 3. 访问控制

在 Nginx 中添加 IP 白名单：
```nginx
location / {
    allow ***********/24;
    deny all;
    proxy_pass http://127.0.0.1:8000;
}
```

## 故障排除

### 常见问题

1. **端口被占用**
```bash
sudo lsof -i :8000
sudo kill -9 <PID>
```

2. **内存不足**
```bash
# 增加交换空间
sudo fallocate -l 4G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

3. **GPU 不可用**
```bash
# 检查 CUDA
nvidia-smi
nvcc --version

# 重新安装 PyTorch
pip uninstall torch torchvision
pip install torch torchvision --index-url https://download.pytorch.org/whl/cu118
```

### 日志分析

查看应用日志：
```bash
tail -f logs/app.log
tail -f logs/error.log
```

查看系统资源：
```bash
htop
nvidia-smi
df -h
```

## 性能优化

### 1. 模型优化
- 使用 TensorRT 优化推理
- 启用混合精度训练
- 模型量化

### 2. 系统优化
- 调整 worker 数量
- 优化内存使用
- 使用 SSD 存储

### 3. 网络优化
- 启用 gzip 压缩
- 使用 CDN 分发静态文件
- 配置缓存策略
