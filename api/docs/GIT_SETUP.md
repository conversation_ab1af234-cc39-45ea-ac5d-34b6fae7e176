# 🔧 Git配置指南

## 📋 概述

本项目包含大型机器学习模型文件，需要特殊的Git配置来处理这些文件。

## 🗂️ 大文件情况

### 模型文件
- `models/yolo/yolo11x-train28-best.pt` (109.21 MB) - YOLO检测模型
- `models/lama/big-lama/models/best.ckpt` (391.05 MB) - LAMA修复模型

### 总大小
- 模型文件总计: ~500 MB
- 建议使用Git LFS管理

## 🚀 Git LFS设置

### 1. 安装Git LFS
```bash
# Windows (使用Git for Windows)
git lfs install

# macOS (使用Homebrew)
brew install git-lfs
git lfs install

# Ubuntu/Debian
sudo apt install git-lfs
git lfs install

# CentOS/RHEL
sudo yum install git-lfs
git lfs install
```

### 2. 验证安装
```bash
git lfs version
```

### 3. 初始化LFS (在项目根目录)
```bash
git lfs install
```

## 📁 文件忽略策略

### .gitignore配置
项目已配置完整的`.gitignore`文件，包含：

#### 🤖 模型文件
```gitignore
# YOLO模型
models/yolo/*.pt
models/yolo/*.pth

# LAMA模型
models/lama/**/*.ckpt
models/lama/**/*.pt

# 通用模型文件
*.pt
*.pth
*.onnx
*.bin
*.safetensors
```

#### 🖼️ 输出文件
```gitignore
# 输出结果
outputs/
results/
*_result.jpg
*_comparison.png
```

#### 🐍 Python文件
```gitignore
# 缓存文件
__pycache__/
*.pyc
.pytest_cache/

# 环境文件
.env
venv/
```

### .gitattributes配置
配置Git LFS处理大文件：

```gitattributes
# 模型文件使用LFS
*.pt filter=lfs diff=lfs merge=lfs -text
*.ckpt filter=lfs diff=lfs merge=lfs -text
*.onnx filter=lfs diff=lfs merge=lfs -text

# 大图像文件使用LFS
*.jpg filter=lfs diff=lfs merge=lfs -text
*.png filter=lfs diff=lfs merge=lfs -text
```

## 🔄 Git工作流程

### 初始化仓库
```bash
# 1. 初始化Git仓库
git init

# 2. 添加远程仓库
git remote add origin <your-repo-url>

# 3. 配置LFS
git lfs install
```

### 提交代码（忽略大文件）
```bash
# 1. 检查状态
git status

# 2. 添加代码文件（自动忽略大文件）
git add .

# 3. 提交
git commit -m "feat: 添加水印检测与去除系统"

# 4. 推送
git push -u origin main
```

### 如果需要包含模型文件
```bash
# 1. 跟踪大文件
git lfs track "models/yolo/*.pt"
git lfs track "models/lama/**/*.ckpt"

# 2. 添加LFS配置
git add .gitattributes

# 3. 添加模型文件
git add models/

# 4. 提交
git commit -m "feat: 添加预训练模型文件"

# 5. 推送（需要LFS支持的仓库）
git push
```

## 📊 仓库大小管理

### 检查仓库大小
```bash
# 检查仓库大小
git count-objects -vH

# 检查LFS文件
git lfs ls-files

# 检查大文件
git rev-list --objects --all | git cat-file --batch-check='%(objecttype) %(objectname) %(objectsize) %(rest)' | awk '/^blob/ {print substr($0,6)}' | sort --numeric-sort --key=2 | tail -10
```

### 清理历史
```bash
# 清理未跟踪的文件
git clean -fd

# 清理LFS缓存
git lfs prune

# 垃圾回收
git gc --aggressive --prune=now
```

## 🎯 推荐策略

### 方案1: 完全忽略模型文件 (推荐)
- ✅ 仓库小，克隆快
- ✅ 适合代码协作
- ❌ 需要单独下载模型

```bash
# 使用当前的.gitignore配置
git add .
git commit -m "feat: 添加水印检测系统代码"
```

### 方案2: 使用Git LFS
- ✅ 模型文件版本控制
- ✅ 完整的项目备份
- ❌ 需要LFS支持
- ❌ 仓库较大

```bash
# 配置LFS并提交模型
git lfs track "*.pt" "*.ckpt"
git add .gitattributes
git add .
git commit -m "feat: 添加完整项目（包含模型）"
```

### 方案3: 混合策略
- 代码仓库: 只包含代码
- 模型仓库: 单独管理模型
- 部署脚本: 自动下载模型

## 📝 模型下载脚本

如果选择忽略模型文件，可以创建下载脚本：

```bash
# download_models.sh
#!/bin/bash

echo "下载YOLO模型..."
wget -O models/yolo/yolo11x-train28-best.pt "https://your-model-host/yolo-model.pt"

echo "下载LAMA模型..."
wget -O models/lama/big-lama/models/best.ckpt "https://your-model-host/lama-model.ckpt"

echo "模型下载完成！"
```

## 🔍 常见问题

### Q: 为什么要忽略模型文件？
A: 模型文件通常很大（几百MB到几GB），会导致：
- 仓库克隆缓慢
- 存储空间浪费
- 版本控制困难

### Q: 如何在CI/CD中处理模型文件？
A: 可以：
- 使用云存储（AWS S3, Google Cloud Storage）
- 使用模型注册表（MLflow, DVC）
- 在部署时下载模型

### Q: 团队协作时如何共享模型？
A: 建议：
- 使用统一的模型存储
- 提供模型下载脚本
- 在README中说明模型获取方式

## 📚 相关资源

- [Git LFS官方文档](https://git-lfs.github.io/)
- [GitHub LFS指南](https://docs.github.com/en/repositories/working-with-files/managing-large-files)
- [DVC数据版本控制](https://dvc.org/)
- [MLflow模型注册表](https://mlflow.org/docs/latest/model-registry.html)
