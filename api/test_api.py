#!/usr/bin/env python3
import requests
import json

def test_health():
    print("=== 测试健康检查 ===")
    try:
        response = requests.get("http://localhost:8001/health")
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
    except Exception as e:
        print(f"错误: {e}")

def test_watermark_removal():
    print("\n=== 测试水印去除 ===")
    try:
        data = {
            "image_url": "https://picsum.photos/300/200",
            "confidence_threshold": 0.5,
            "enhance_mask": True,
            "use_smart_enhancement": True,
            "context_expansion_ratio": 0.12,
            "return_intermediate": False
        }
        
        print(f"发送请求: {json.dumps(data, indent=2)}")
        
        response = requests.post(
            "http://localhost:8001/remove-watermark-sync",
            json=data,
            timeout=60
        )
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"成功响应: {json.dumps(result, indent=2)}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    test_health()
    test_watermark_removal()
