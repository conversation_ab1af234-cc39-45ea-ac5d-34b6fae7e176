from fastapi import <PERSON><PERSON><PERSON>, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, HttpUrl
import uvicorn
import aiohttp
import asyncio
import os
import uuid
from datetime import datetime
import logging
from pathlib import Path
import cv2
import numpy as np
from PIL import Image
import torch
import hashlib
from typing import Optional, Dict, Any
import json
from contextlib import asynccontextmanager

# 导入新的模型集成
from models.watermark_removal_pipeline import WatermarkRemovalPipeline

# 导入工具模块
from utils import (
    init_default_logging,
    get_logger,
    LoggingContext,
    setup_error_handlers,
    setup_request_logging,
    WatermarkRemovalError,
    ModelError,
    FileError,
    TaskError,
    ProcessingError,
    ErrorCode,
    safe_execute,
    safe_execute_async,
    validate_file_size,
    validate_image_format
)

# 导入性能监控模块
from utils.performance import (
    memory_monitor,
    performance_monitor,
    memory_manager,
    model_cache,
    log_system_info,
    get_performance_stats
)

# 初始化日志系统
init_default_logging()
logger = get_logger(__name__)

# 配置
class Config:
    UPLOAD_DIR = "uploads"
    OUTPUT_DIR = "outputs"
    STATIC_DIR = "static"
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp'}
    CLEANUP_INTERVAL = 3600  # 1小时清理一次临时文件
    FILE_RETENTION_HOURS = 24  # 文件保留24小时
    
    # 模型配置
    WATERMARK_DETECTION_MODEL = None  # 自动下载模型
    LAMA_MODEL_PATH = None  # 自动下载模型
    CONFIDENCE_THRESHOLD = 0.3

config = Config()

# 创建必要的目录
for directory in [config.UPLOAD_DIR, config.OUTPUT_DIR, config.STATIC_DIR]:
    Path(directory).mkdir(exist_ok=True)

# 请求和响应模型
class WatermarkRemovalRequest(BaseModel):
    image_url: HttpUrl
    confidence_threshold: Optional[float] = config.CONFIDENCE_THRESHOLD
    enhance_mask: Optional[bool] = True
    use_smart_enhancement: Optional[bool] = True
    context_expansion_ratio: Optional[float] = 0.12
    return_intermediate: Optional[bool] = False

class BatchWatermarkRemovalRequest(BaseModel):
    image_urls: list[HttpUrl]
    confidence_threshold: Optional[float] = config.CONFIDENCE_THRESHOLD
    enhance_mask: Optional[bool] = True
    use_smart_enhancement: Optional[bool] = True
    context_expansion_ratio: Optional[float] = 0.12

class WatermarkRemovalResponse(BaseModel):
    success: bool
    message: str
    task_id: str
    result_url: Optional[str] = None
    processing_time: Optional[float] = None
    detection_time: Optional[float] = None
    inpainting_time: Optional[float] = None
    detection_confidence: Optional[float] = None
    watermark_count: Optional[int] = None
    mask_expansion_factor: Optional[float] = None
    model_type_used: Optional[str] = None  # 避免与pydantic的model_冲突

class TaskStatus(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int  # 0-100
    message: str
    result_url: Optional[str] = None
    created_at: datetime
    completed_at: Optional[datetime] = None

# 全局变量存储任务状态
task_storage: Dict[str, TaskStatus] = {}

class WatermarkRemover:
    def __init__(self):
        self.pipeline = None
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        logger.info(f"使用设备: {self.device}")

    async def load_models(self):
        """异步加载模型"""
        with LoggingContext(logger, "模型加载"):
            try:
                self.pipeline = WatermarkRemovalPipeline(
                    detector_model_path=config.WATERMARK_DETECTION_MODEL,
                    inpainter_model_path=config.LAMA_MODEL_PATH,
                    device=str(self.device)
                )
                logger.info("模型加载成功")
            except Exception as e:
                logger.warning(f"模型加载失败，将在首次使用时重试: {e}")
                # 不抛出异常，允许应用启动，在实际使用时再尝试加载
                self.pipeline = None

    def ensure_pipeline_loaded(self):
        """确保管道已加载，如果未加载则尝试加载"""
        if self.pipeline is None:
            try:
                logger.info("尝试延迟加载模型...")
                self.pipeline = WatermarkRemovalPipeline(
                    detector_model_path=config.WATERMARK_DETECTION_MODEL,
                    inpainter_model_path=config.LAMA_MODEL_PATH,
                    device=str(self.device)
                )
                logger.info("延迟模型加载成功")
            except Exception as e:
                raise ModelError(
                    message="模型加载失败，请检查网络连接或模型文件",
                    error_code=ErrorCode.MODEL_LOADING_FAILED,
                    details={"device": str(self.device), "error": str(e)}
                ) from e
    
    async def download_image(self, url: str, session: aiohttp.ClientSession) -> str:
        """下载图片到本地"""
        with LoggingContext(logger, f"下载图片: {url}"):
            try:
                async with session.get(str(url)) as response:
                    if response.status != 200:
                        raise FileError(
                            message=f"无法下载图片: HTTP {response.status}",
                            error_code=ErrorCode.FILE_DOWNLOAD_FAILED,
                            details={"url": str(url), "status_code": response.status}
                        )

                    # 检查文件大小
                    content_length = response.headers.get('content-length')
                    if content_length:
                        validate_file_size(int(content_length), config.MAX_FILE_SIZE)

                    content = await response.read()
                    validate_file_size(len(content), config.MAX_FILE_SIZE)

                    # 生成唯一文件名
                    file_hash = hashlib.md5(content).hexdigest()
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"{timestamp}_{file_hash[:8]}.jpg"
                    filepath = Path(config.UPLOAD_DIR) / filename

                    # 保存文件
                    with open(filepath, 'wb') as f:
                        f.write(content)

                    # 验证是否为有效图片
                    try:
                        with Image.open(filepath) as img:
                            img.verify()
                    except Exception as e:
                        os.remove(filepath)
                        raise FileError(
                            message="无效的图片文件",
                            error_code=ErrorCode.INVALID_FILE_FORMAT,
                            details={"filepath": str(filepath)}
                        ) from e

                    return str(filepath)

            except aiohttp.ClientError as e:
                raise FileError(
                    message="图片下载失败",
                    error_code=ErrorCode.FILE_DOWNLOAD_FAILED,
                    details={"url": str(url), "error": str(e)}
                ) from e
    
    def remove_watermark_unified(
        self,
        image_path: str,
        confidence_threshold: float = 0.3,
        enhance_mask: bool = True,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12,
        return_intermediate: bool = False
    ) -> dict:
        """使用统一接口进行水印去除"""
        with LoggingContext(logger, "统一水印去除"):
            # 确保管道已加载
            self.ensure_pipeline_loaded()

            try:
                # 加载图像
                image = Image.open(image_path).convert('RGB')

                # 使用统一的水印去除管道
                result = self.pipeline.remove_watermark(
                    image=image,
                    confidence_threshold=confidence_threshold,
                    enhance_mask=enhance_mask,
                    use_smart_enhancement=use_smart_enhancement,
                    context_expansion_ratio=context_expansion_ratio,
                    return_intermediate=return_intermediate
                )

                # 处理结果
                if isinstance(result, dict):
                    result_image = result['result_image']

                    # 生成输出文件名
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_filename = f"nowatermark_{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
                    output_path = Path(config.OUTPUT_DIR) / output_filename

                    # 保存结果
                    result_image.save(str(output_path), 'JPEG', quality=95)

                    # 返回详细信息
                    return {
                        'result_path': str(output_path),
                        'processing_time': result.get('processing_time', 0),
                        'detection_time': result.get('detection_time', 0),
                        'inpainting_time': result.get('inpainting_time', 0),
                        'watermark_count': len(result.get('detection_info', [])),
                        'detection_confidence': result.get('detection_info', [{}])[0].get('confidence', 0) if result.get('detection_info') else 0,
                        'mask_expansion_factor': self._calculate_mask_expansion(result) if return_intermediate else None,
                        'model_type_used': self._get_model_type()
                    }
                else:
                    # 简单结果
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    output_filename = f"nowatermark_{timestamp}_{uuid.uuid4().hex[:8]}.jpg"
                    output_path = Path(config.OUTPUT_DIR) / output_filename

                    result.save(str(output_path), 'JPEG', quality=95)

                    return {
                        'result_path': str(output_path),
                        'processing_time': 0,
                        'detection_time': 0,
                        'inpainting_time': 0,
                        'watermark_count': 0,
                        'detection_confidence': 0,
                        'mask_expansion_factor': None,
                        'model_type_used': self._get_model_type()
                    }

            except Exception as e:
                raise ProcessingError(
                    message="统一水印去除失败",
                    error_code=ErrorCode.WATERMARK_REMOVAL_FAILED,
                    details={"image_path": image_path}
                ) from e

    def _calculate_mask_expansion(self, result: dict) -> float:
        """计算掩码扩展倍数"""
        try:
            if 'original_mask' in result and 'enhanced_mask' in result:
                original_mask = result['original_mask']
                enhanced_mask = result['enhanced_mask']

                original_ratio = (original_mask > 127).sum() / (original_mask.shape[0] * original_mask.shape[1])
                enhanced_ratio = (enhanced_mask > 127).sum() / (enhanced_mask.shape[0] * enhanced_mask.shape[1])

                return enhanced_ratio / original_ratio if original_ratio > 0 else 1.0
            return None
        except:
            return None

    def _get_model_type(self) -> str:
        """获取当前使用的模型类型"""
        try:
            if hasattr(self.pipeline.inpainter, 'get_model_info'):
                model_info = self.pipeline.inpainter.get_model_info()
                return model_info.get('model_type', 'unknown')
            return type(self.pipeline.inpainter).__name__
        except:
            return 'unknown'

# 创建全局实例
watermark_remover = WatermarkRemover()

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 记录系统信息
    log_system_info()

    # 启动时加载模型
    await watermark_remover.load_models()

    # 启动清理任务
    cleanup_task = asyncio.create_task(cleanup_old_files())

    yield

    # 关闭时清理
    cleanup_task.cancel()

    # 清理模型缓存
    model_cache.clear()

    # 最终内存清理
    memory_manager.cleanup_memory(force=True)

# 创建FastAPI应用
app = FastAPI(
    title="水印自动去除服务",
    description="提供图片URL，自动检测并去除水印",
    version="1.0.0",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001", "http://127.0.0.1:3000", "http://127.0.0.1:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 设置错误处理器
setup_error_handlers(app)

# 添加请求日志中间件
app.add_middleware(setup_request_logging())

# 挂载静态文件服务
app.mount("/static", StaticFiles(directory=config.STATIC_DIR), name="static")
app.mount("/outputs", StaticFiles(directory=config.OUTPUT_DIR), name="outputs")
app.mount("/uploads", StaticFiles(directory=config.UPLOAD_DIR), name="uploads")

async def process_watermark_removal(
    task_id: str,
    image_url: str,
    confidence_threshold: float,
    enhance_mask: bool,
    use_smart_enhancement: bool = True,
    context_expansion_ratio: float = 0.12
):
    """后台处理水印去除任务（使用统一接口）"""
    try:
        # 更新任务状态
        task_storage[task_id].status = "processing"
        task_storage[task_id].progress = 10
        task_storage[task_id].message = "正在下载图片..."

        # 下载图片
        async with aiohttp.ClientSession() as session:
            image_path = await watermark_remover.download_image(image_url, session)

        task_storage[task_id].progress = 30
        task_storage[task_id].message = "正在使用AI模型处理..."

        # 使用统一接口进行水印去除
        result_info = watermark_remover.remove_watermark_unified(
            image_path=image_path,
            confidence_threshold=confidence_threshold,
            enhance_mask=enhance_mask,
            use_smart_enhancement=use_smart_enhancement,
            context_expansion_ratio=context_expansion_ratio,
            return_intermediate=True
        )

        # 生成访问URL
        result_filename = Path(result_info['result_path']).name
        result_url = f"http://localhost:8001/outputs/{result_filename}"

        # 更新任务状态
        task_storage[task_id].status = "completed"
        task_storage[task_id].progress = 100

        # 根据检测结果生成消息
        if result_info['watermark_count'] == 0:
            task_storage[task_id].message = f"未检测到明显水印 (置信度: {result_info['detection_confidence']:.2f})"
        else:
            message_parts = [
                f"水印去除完成",
                f"检测到{result_info['watermark_count']}个水印",
                f"使用{result_info['model_type_used']}模型"
            ]
            if result_info['mask_expansion_factor']:
                message_parts.append(f"掩码扩展{result_info['mask_expansion_factor']:.1f}倍")

            task_storage[task_id].message = " | ".join(message_parts)

        task_storage[task_id].result_url = result_url
        task_storage[task_id].completed_at = datetime.now()

        # 清理原始文件
        try:
            os.remove(image_path)
        except:
            pass

    except Exception as e:
        logger.error(f"处理任务 {task_id} 失败: {e}")
        task_storage[task_id].status = "failed"
        task_storage[task_id].progress = 0
        task_storage[task_id].message = f"处理失败: {str(e)}"
        task_storage[task_id].completed_at = datetime.now()

@app.post("/remove-watermark", response_model=WatermarkRemovalResponse)
async def remove_watermark_endpoint(
    request: WatermarkRemovalRequest,
    background_tasks: BackgroundTasks
):
    """
    水印去除接口（异步处理）

    - **image_url**: 待处理图片的URL地址
    - **confidence_threshold**: 水印检测置信度阈值 (0.0-1.0，默认0.3)
    - **enhance_mask**: 是否增强检测掩码 (默认True)
    - **use_smart_enhancement**: 是否使用智能掩码增强 (默认True)
    - **context_expansion_ratio**: 上下文扩展比例 (0.0-0.3，默认0.12)
    - **return_intermediate**: 是否返回中间结果 (默认False)

    智能掩码增强可以显著提升修复质量，建议保持开启。
    """
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())
        
        # 创建任务状态
        task_storage[task_id] = TaskStatus(
            task_id=task_id,
            status="pending",
            progress=0,
            message="任务已创建，等待处理...",
            created_at=datetime.now()
        )
        
        # 添加后台任务
        background_tasks.add_task(
            process_watermark_removal,
            task_id,
            str(request.image_url),
            request.confidence_threshold,
            request.enhance_mask,
            request.use_smart_enhancement,
            request.context_expansion_ratio
        )
        
        return WatermarkRemovalResponse(
            success=True,
            message="任务已提交，正在处理中...",
            task_id=task_id
        )
        
    except Exception as e:
        logger.error(f"创建任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务内部错误: {str(e)}")

@app.get("/task/{task_id}", response_model=TaskStatus)
async def get_task_status(task_id: str):
    """
    查询任务状态
    
    - **task_id**: 任务ID
    """
    if task_id not in task_storage:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return task_storage[task_id]

@app.post("/remove-watermark-sync", response_model=WatermarkRemovalResponse)
async def remove_watermark_sync(request: WatermarkRemovalRequest):
    """
    同步水印去除接口（直接返回结果）

    - **image_url**: 待处理图片的URL地址
    - **confidence_threshold**: 水印检测置信度阈值 (0.0-1.0，默认0.3)
    - **enhance_mask**: 是否增强检测掩码 (默认True)
    - **use_smart_enhancement**: 是否使用智能掩码增强 (默认True)
    - **context_expansion_ratio**: 上下文扩展比例 (0.0-0.3，默认0.12)
    - **return_intermediate**: 是否返回中间结果 (默认False)

    注意：此接口可能耗时较长（10-20秒），建议使用异步接口。
    使用真正的LAMA深度学习模型，修复质量显著优于传统方法。
    """
    start_time = datetime.now()
    
    try:
        # 下载图片
        async with aiohttp.ClientSession() as session:
            image_path = await watermark_remover.download_image(str(request.image_url), session)

        # 使用统一接口进行水印去除
        result_info = watermark_remover.remove_watermark_unified(
            image_path=image_path,
            confidence_threshold=request.confidence_threshold,
            enhance_mask=request.enhance_mask,
            use_smart_enhancement=request.use_smart_enhancement,
            context_expansion_ratio=request.context_expansion_ratio,
            return_intermediate=request.return_intermediate
        )

        # 生成访问URL
        result_filename = Path(result_info['result_path']).name
        result_url = f"http://localhost:8001/outputs/{result_filename}"

        # 清理原始文件
        try:
            os.remove(image_path)
        except:
            pass

        # 根据检测结果生成消息
        if result_info['watermark_count'] == 0:
            message = f"未检测到明显水印 (置信度: {result_info['detection_confidence']:.2f})"
        else:
            message = f"水印去除完成 | 检测到{result_info['watermark_count']}个水印 | 使用{result_info['model_type_used']}模型"

        return WatermarkRemovalResponse(
            success=True,
            message=message,
            task_id="sync",
            result_url=result_url,
            processing_time=result_info['processing_time'],
            detection_time=result_info['detection_time'],
            inpainting_time=result_info['inpainting_time'],
            detection_confidence=result_info['detection_confidence'],
            watermark_count=result_info['watermark_count'],
            mask_expansion_factor=result_info['mask_expansion_factor'],
            model_type_used=result_info['model_type_used']
        )
        
    except Exception as e:
        logger.error(f"同步处理失败: {e}")
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")

@app.get("/health")
async def health_check():
    """健康检查接口"""
    memory_status = memory_manager.check_memory_usage()

    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "device": str(watermark_remover.device),
        "active_tasks": len([t for t in task_storage.values() if t.status == "processing"]),
        "memory": {
            "system_usage_percent": memory_status['system_memory']['usage_percent'],
            "gpu_usage_percent": memory_status.get('gpu_memory', {}).get('usage_percent', 0),
            "needs_cleanup": memory_status['needs_cleanup']
        },
        "model_cache_size": model_cache.size()
    }

@app.post("/upload")
async def upload_file(file: UploadFile = File(...)):
    """
    文件上传接口

    上传图片文件并返回可访问的URL
    """
    try:
        # 验证文件类型
        if not file.content_type or not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="只支持图片文件")

        # 验证文件扩展名
        file_ext = Path(file.filename or '').suffix.lower()
        if file_ext not in config.ALLOWED_EXTENSIONS:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的文件格式。支持的格式: {', '.join(config.ALLOWED_EXTENSIONS)}"
            )

        # 读取文件内容
        content = await file.read()

        # 验证文件大小
        if len(content) > config.MAX_FILE_SIZE:
            raise HTTPException(
                status_code=400,
                detail=f"文件过大。最大支持 {config.MAX_FILE_SIZE // (1024*1024)}MB"
            )

        # 生成唯一文件名
        file_hash = hashlib.md5(content).hexdigest()
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"upload_{timestamp}_{file_hash[:8]}{file_ext}"
        filepath = Path(config.UPLOAD_DIR) / filename

        # 保存文件
        with open(filepath, 'wb') as f:
            f.write(content)

        # 返回可访问的URL
        file_url = f"/uploads/{filename}"

        logger.info(f"文件上传成功: {filename} ({len(content)} bytes)")

        return {
            "success": True,
            "message": "文件上传成功",
            "file_url": file_url,
            "filename": filename,
            "size": len(content)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文件上传失败: {e}")
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")

@app.get("/performance")
async def get_performance():
    """获取性能统计信息"""
    return get_performance_stats()

@app.post("/performance/cleanup")
async def cleanup_memory():
    """手动清理内存"""
    try:
        memory_manager.cleanup_memory(force=True)
        return {
            "success": True,
            "message": "内存清理完成",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"内存清理失败: {e}")
        return {
            "success": False,
            "message": f"内存清理失败: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }

@app.delete("/task/{task_id}")
async def delete_task(task_id: str):
    """删除任务记录"""
    if task_id in task_storage:
        del task_storage[task_id]
        return {"message": "任务已删除"}
    else:
        raise HTTPException(status_code=404, detail="任务不存在")

@app.post("/remove-watermark-batch")
async def remove_watermark_batch(
    request: BatchWatermarkRemovalRequest,
    background_tasks: BackgroundTasks
):
    """
    批量水印去除接口

    - **image_urls**: 待处理图片的URL地址列表
    - **confidence_threshold**: 水印检测置信度阈值 (0.0-1.0，默认0.3)
    - **enhance_mask**: 是否增强检测掩码 (默认True)
    - **use_smart_enhancement**: 是否使用智能掩码增强 (默认True)
    - **context_expansion_ratio**: 上下文扩展比例 (0.0-0.3，默认0.12)

    批量处理使用真正的LAMA深度学习模型，每张图片处理时间约10-20秒。
    """
    try:
        batch_id = str(uuid.uuid4())
        task_ids = []

        # 为每个图片创建单独的任务
        for i, image_url in enumerate(request.image_urls):
            task_id = f"{batch_id}_{i}"

            # 创建任务状态
            task_storage[task_id] = TaskStatus(
                task_id=task_id,
                status="pending",
                progress=0,
                message=f"批量任务 {i+1}/{len(request.image_urls)} 已创建，等待处理...",
                created_at=datetime.now()
            )

            # 添加后台任务
            background_tasks.add_task(
                process_watermark_removal,
                task_id,
                str(image_url),
                request.confidence_threshold,
                request.enhance_mask,
                request.use_smart_enhancement,
                request.context_expansion_ratio
            )

            task_ids.append(task_id)

        return {
            "success": True,
            "message": f"批量任务已提交，共 {len(request.image_urls)} 张图片",
            "batch_id": batch_id,
            "task_ids": task_ids
        }

    except Exception as e:
        logger.error(f"创建批量任务失败: {e}")
        raise HTTPException(status_code=500, detail=f"服务内部错误: {str(e)}")

@app.get("/batch/{batch_id}")
async def get_batch_status(batch_id: str):
    """
    查询批量任务状态

    - **batch_id**: 批量任务ID
    """
    # 查找所有相关的任务
    batch_tasks = {
        task_id: task for task_id, task in task_storage.items()
        if task_id.startswith(batch_id)
    }

    if not batch_tasks:
        raise HTTPException(status_code=404, detail="批量任务不存在")

    # 统计状态
    total_tasks = len(batch_tasks)
    completed_tasks = sum(1 for task in batch_tasks.values() if task.status == "completed")
    failed_tasks = sum(1 for task in batch_tasks.values() if task.status == "failed")
    processing_tasks = sum(1 for task in batch_tasks.values() if task.status == "processing")

    # 计算总体进度
    total_progress = sum(task.progress for task in batch_tasks.values()) // total_tasks

    # 确定批量状态
    if completed_tasks + failed_tasks == total_tasks:
        batch_status = "completed"
    elif processing_tasks > 0:
        batch_status = "processing"
    else:
        batch_status = "pending"

    return {
        "batch_id": batch_id,
        "status": batch_status,
        "progress": total_progress,
        "total_tasks": total_tasks,
        "completed_tasks": completed_tasks,
        "failed_tasks": failed_tasks,
        "processing_tasks": processing_tasks,
        "tasks": batch_tasks
    }

async def cleanup_old_files():
    """定期清理旧文件"""
    while True:
        try:
            await asyncio.sleep(config.CLEANUP_INTERVAL)
            
            current_time = datetime.now()
            cutoff_time = current_time.timestamp() - (config.FILE_RETENTION_HOURS * 3600)
            
            # 清理输出文件
            for file_path in Path(config.OUTPUT_DIR).glob("*"):
                if file_path.stat().st_mtime < cutoff_time:
                    try:
                        os.remove(file_path)
                        logger.info(f"清理旧文件: {file_path}")
                    except:
                        pass
            
            # 清理上传文件
            for file_path in Path(config.UPLOAD_DIR).glob("*"):
                if file_path.stat().st_mtime < cutoff_time:
                    try:
                        os.remove(file_path)
                        logger.info(f"清理旧文件: {file_path}")
                    except:
                        pass
            
            # 清理过期任务
            expired_tasks = [
                task_id for task_id, task in task_storage.items()
                if (current_time - task.created_at).total_seconds() > (config.FILE_RETENTION_HOURS * 3600)
            ]
            
            for task_id in expired_tasks:
                del task_storage[task_id]
                logger.info(f"清理过期任务: {task_id}")
                
        except Exception as e:
            logger.error(f"清理任务出错: {e}")

if __name__ == "__main__":
    uvicorn.run(
        "app:app",
        host="0.0.0.0",
        port=8001,
        reload=True,
        log_level="info"
    )