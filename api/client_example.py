import requests
import time
import json
from typing import List, Dict, Any

class WatermarkRemovalClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    def remove_watermark_async(self, image_url, confidence_threshold=0.3):
        """异步提交水印去除任务"""
        response = requests.post(
            f"{self.base_url}/remove-watermark",
            json={
                "image_url": image_url,
                "confidence_threshold": confidence_threshold,
                "enhance_mask": True
            }
        )
        return response.json()
    
    def get_task_status(self, task_id):
        """查询任务状态"""
        response = requests.get(f"{self.base_url}/task/{task_id}")
        return response.json()
    
    def wait_for_completion(self, task_id, timeout=300):
        """等待任务完成"""
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_task_status(task_id)
            
            print(f"状态: {status['status']}, 进度: {status['progress']}%, 消息: {status['message']}")
            
            if status['status'] == 'completed':
                return status
            elif status['status'] == 'failed':
                raise Exception(f"任务失败: {status['message']}")
            
            time.sleep(2)
        
        raise TimeoutError("任务超时")
    
    def remove_watermark_sync(self, image_url, confidence_threshold=0.3):
        """同步水印去除"""
        response = requests.post(
            f"{self.base_url}/remove-watermark-sync",
            json={
                "image_url": image_url,
                "confidence_threshold": confidence_threshold,
                "enhance_mask": True
            }
        )
        return response.json()

    def remove_watermark_batch(self, image_urls: List[str], confidence_threshold=0.3):
        """批量水印去除"""
        response = requests.post(
            f"{self.base_url}/remove-watermark-batch",
            json={
                "image_urls": image_urls,
                "confidence_threshold": confidence_threshold,
                "enhance_mask": True
            }
        )
        return response.json()

    def get_batch_status(self, batch_id: str):
        """查询批量任务状态"""
        response = requests.get(f"{self.base_url}/batch/{batch_id}")
        return response.json()

    def wait_for_batch_completion(self, batch_id: str, timeout=600):
        """等待批量任务完成"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            status = self.get_batch_status(batch_id)

            print(f"批量状态: {status['status']}, 进度: {status['progress']}%")
            print(f"总任务: {status['total_tasks']}, 完成: {status['completed_tasks']}, 失败: {status['failed_tasks']}")

            if status['status'] == 'completed':
                return status

            time.sleep(5)

        raise TimeoutError("批量任务超时")

# 使用示例
if __name__ == "__main__":
    client = WatermarkRemovalClient()
    
    # 示例1: 异步处理
    print("=== 异步处理示例 ===")
    image_url = "https://upload.ezprompt.org/jimeng-2025-07-17-6603-%E5%AF%AB%E5%AF%A6%E9%A2%A8%E6%A0%BC%EF%BC%8C%E3%80%8A%E7%81%AB%E5%BD%B1%E5%BF%8D%E8%80%85%E3%80%8B%E6%98%A5%E9%87%8E%E6%AB%BB%E5%BE%9E%E9%9B%BB%E8%85%A6%E5%B1%8F%E5%B9%95%E7%88%AC%E5%87%BA%E4%BE%86%E5%87%BA%E4%BE%86%EF%BC%8C%E7%9C%9F%E4%BA%BAcosplay%EF%BC%8C%EF%BC%8C%E8%A7%92%E8%89%B2%E6%89%AE%E6%BC%94..._processed.webp"
    
    # 提交任务
    result = client.remove_watermark_async(image_url)
    task_id = result['task_id']
    print(f"任务已提交，ID: {task_id}")
    
    # 等待完成
    try:
        final_status = client.wait_for_completion(task_id)
        if final_status['result_url']:
            print(f"处理完成！结果图片: http://localhost:8000{final_status['result_url']}")
        else:
            print(f"处理结果: {final_status['message']}")
    except Exception as e:
        print(f"处理失败: {e}")
    
    # 示例2: 同步处理
    print("\n=== 同步处理示例 ===")
    try:
        result = client.remove_watermark_sync(image_url)
        if result['success'] and result['result_url']:
            print(f"处理完成！结果图片: http://localhost:8000{result['result_url']}")
            print(f"处理时间: {result['processing_time']:.2f}秒")
            print(f"检测置信度: {result['detection_confidence']:.2f}")
        else:
            print(f"处理结果: {result['message']}")
    except Exception as e:
        print(f"处理失败: {e}")

    # 示例3: 批量处理
    print("\n=== 批量处理示例 ===")
    batch_image_urls = [
        "https://upload.ezprompt.org/jimeng-2025-07-17-6603-%E5%AF%AB%E5%AF%A6%E9%A2%A8%E6%A0%BC%EF%BC%8C%E3%80%8A%E7%81%AB%E5%BD%B1%E5%BF%8D%E8%80%85%E3%80%8B%E6%98%A5%E9%87%8E%E6%AB%BB%E5%BE%9E%E9%9B%BB%E8%85%A6%E5%B1%8F%E5%B9%95%E7%88%AC%E5%87%BA%E4%BE%86%E5%87%BA%E4%BE%86%EF%BC%8C%E7%9C%9F%E4%BA%BAcosplay%EF%BC%8C%EF%BC%8C%E8%A7%92%E8%89%B2%E6%89%AE%E6%BC%94..._processed.webp",
        "https://upload.ezprompt.org/jimeng-2025-07-17-6603-%E5%AF%AB%E5%AF%A6%E9%A2%A8%E6%A0%BC%EF%BC%8C%E3%80%8A%E7%81%AB%E5%BD%B1%E5%BF%8D%E8%80%85%E3%80%8B%E6%98%A5%E9%87%8E%E6%AB%BB%E5%BE%9E%E9%9B%BB%E8%85%A6%E5%B1%8F%E5%B9%95%E7%88%AC%E5%87%BA%E4%BE%86%E5%87%BA%E4%BE%86%EF%BC%8C%E7%9C%9F%E4%BA%BAcosplay%EF%BC%8C%EF%BC%8C%E8%A7%92%E8%89%B2%E6%89%AE%E6%BC%94..._processed.webp"
    ]

    try:
        # 提交批量任务
        batch_result = client.remove_watermark_batch(batch_image_urls)
        batch_id = batch_result['batch_id']
        print(f"批量任务已提交，ID: {batch_id}")
        print(f"任务数量: {len(batch_result['task_ids'])}")

        # 等待批量完成
        final_status = client.wait_for_batch_completion(batch_id)
        print(f"批量处理完成！")
        print(f"成功: {final_status['completed_tasks']}, 失败: {final_status['failed_tasks']}")

        # 显示每个任务的结果
        for task_id, task in final_status['tasks'].items():
            if task['result_url']:
                print(f"任务 {task_id}: http://localhost:8000{task['result_url']}")
            else:
                print(f"任务 {task_id}: {task['message']}")

    except Exception as e:
        print(f"批量处理失败: {e}")