"""
水印检测可视化模块
提供水印检测结果的可视化功能，包括检测框、置信度显示、掩码叠加等
"""

import numpy as np
from PIL import Image, ImageDraw, ImageFont
import cv2
from typing import List, Dict, Any, Tuple, Optional, Union
import logging
from pathlib import Path
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.colors import ListedColormap
import io

logger = logging.getLogger(__name__)


class WatermarkVisualization:
    """水印检测可视化类"""
    
    def __init__(self, font_size: int = 20):
        """
        初始化可视化器
        
        Args:
            font_size: 文字大小
        """
        self.font_size = font_size
        self.colors = [
            (255, 0, 0),    # 红色
            (0, 255, 0),    # 绿色
            (0, 0, 255),    # 蓝色
            (255, 255, 0),  # 黄色
            (255, 0, 255),  # 紫色
            (0, 255, 255),  # 青色
        ]
        
        # 尝试加载字体
        self.font = self._load_font()
    
    def _load_font(self) -> Optional[ImageFont.FreeTypeFont]:
        """加载字体"""
        try:
            # 尝试加载系统字体
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
            
            for font_path in font_paths:
                if Path(font_path).exists():
                    return ImageFont.truetype(font_path, self.font_size)
            
            # 如果没有找到字体，使用默认字体
            return ImageFont.load_default()
            
        except Exception as e:
            logger.warning(f"加载字体失败: {e}，使用默认字体")
            return ImageFont.load_default()
    
    def draw_detection_boxes(
        self,
        image: Image.Image,
        detection_info: List[Dict[str, Any]],
        show_confidence: bool = True,
        box_thickness: int = 3
    ) -> Image.Image:
        """
        在图像上绘制检测框
        
        Args:
            image: 原始图像
            detection_info: 检测信息列表
            show_confidence: 是否显示置信度
            box_thickness: 检测框粗细
            
        Returns:
            带有检测框的图像
        """
        # 创建图像副本
        result_image = image.copy()
        draw = ImageDraw.Draw(result_image)
        
        for i, detection in enumerate(detection_info):
            bbox = detection['bbox']
            confidence = detection['confidence']
            
            # 选择颜色
            color = self.colors[i % len(self.colors)]
            
            # 绘制检测框
            x1, y1, x2, y2 = bbox
            for thickness in range(box_thickness):
                draw.rectangle(
                    [x1 - thickness, y1 - thickness, x2 + thickness, y2 + thickness],
                    outline=color,
                    width=1
                )
            
            # 绘制置信度标签
            if show_confidence:
                label = f"Watermark {confidence:.2f}"
                
                # 计算文本位置
                text_bbox = draw.textbbox((0, 0), label, font=self.font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                
                # 绘制背景矩形
                label_x = x1
                label_y = max(0, y1 - text_height - 5)
                draw.rectangle(
                    [label_x, label_y, label_x + text_width + 10, label_y + text_height + 5],
                    fill=color,
                    outline=color
                )
                
                # 绘制文本
                draw.text(
                    (label_x + 5, label_y + 2),
                    label,
                    fill=(255, 255, 255),
                    font=self.font
                )
        
        return result_image
    
    def create_mask_overlay(
        self,
        image: Image.Image,
        mask: np.ndarray,
        alpha: float = 0.5,
        mask_color: Tuple[int, int, int] = (255, 0, 0)
    ) -> Image.Image:
        """
        创建掩码叠加图像
        
        Args:
            image: 原始图像
            mask: 掩码数组
            alpha: 透明度
            mask_color: 掩码颜色
            
        Returns:
            带有掩码叠加的图像
        """
        # 确保掩码是二值的
        binary_mask = (mask > 127).astype(np.uint8) * 255
        
        # 创建彩色掩码
        colored_mask = np.zeros((*binary_mask.shape, 3), dtype=np.uint8)
        colored_mask[binary_mask > 0] = mask_color
        
        # 转换为PIL图像
        mask_image = Image.fromarray(colored_mask)
        
        # 调整大小以匹配原图
        if mask_image.size != image.size:
            mask_image = mask_image.resize(image.size, Image.LANCZOS)
        
        # 创建叠加图像
        result = Image.blend(image, mask_image, alpha)
        
        return result
    
    def create_side_by_side_comparison(
        self,
        original: Image.Image,
        processed: Image.Image,
        titles: Tuple[str, str] = ("原图", "处理后"),
        title_font_size: int = 24
    ) -> Image.Image:
        """
        创建并排对比图像
        
        Args:
            original: 原始图像
            processed: 处理后图像
            titles: 图像标题
            title_font_size: 标题字体大小
            
        Returns:
            并排对比图像
        """
        # 确保两个图像大小相同
        if original.size != processed.size:
            processed = processed.resize(original.size, Image.LANCZOS)
        
        # 计算新图像尺寸
        width, height = original.size
        new_width = width * 2 + 20  # 中间留20像素间隔
        new_height = height + 60    # 顶部留60像素给标题
        
        # 创建新图像
        result = Image.new('RGB', (new_width, new_height), (255, 255, 255))
        
        # 粘贴图像
        result.paste(original, (0, 60))
        result.paste(processed, (width + 20, 60))
        
        # 添加标题
        draw = ImageDraw.Draw(result)
        title_font = self._load_title_font(title_font_size)
        
        # 左侧标题
        left_title_bbox = draw.textbbox((0, 0), titles[0], font=title_font)
        left_title_width = left_title_bbox[2] - left_title_bbox[0]
        left_title_x = (width - left_title_width) // 2
        draw.text((left_title_x, 20), titles[0], fill=(0, 0, 0), font=title_font)
        
        # 右侧标题
        right_title_bbox = draw.textbbox((0, 0), titles[1], font=title_font)
        right_title_width = right_title_bbox[2] - right_title_bbox[0]
        right_title_x = width + 20 + (width - right_title_width) // 2
        draw.text((right_title_x, 20), titles[1], fill=(0, 0, 0), font=title_font)
        
        return result
    
    def _load_title_font(self, size: int) -> Optional[ImageFont.FreeTypeFont]:
        """加载标题字体"""
        try:
            font_paths = [
                "C:/Windows/Fonts/arial.ttf",
                "C:/Windows/Fonts/calibri.ttf",
                "/System/Library/Fonts/Arial.ttf",
                "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            ]
            
            for font_path in font_paths:
                if Path(font_path).exists():
                    return ImageFont.truetype(font_path, size)
            
            return ImageFont.load_default()
            
        except Exception:
            return ImageFont.load_default()
    
    def create_detection_summary(
        self,
        image: Image.Image,
        detection_info: List[Dict[str, Any]],
        mask: np.ndarray,
        processing_time: float = None
    ) -> Image.Image:
        """
        创建检测结果摘要图像
        
        Args:
            image: 原始图像
            detection_info: 检测信息
            mask: 检测掩码
            processing_time: 处理时间
            
        Returns:
            检测摘要图像
        """
        # 创建带检测框的图像
        boxed_image = self.draw_detection_boxes(image, detection_info)
        
        # 创建掩码叠加图像
        mask_overlay = self.create_mask_overlay(image, mask, alpha=0.3)
        
        # 创建并排对比
        comparison = self.create_side_by_side_comparison(
            boxed_image, 
            mask_overlay,
            titles=("检测框", "检测掩码")
        )
        
        # 添加统计信息
        stats_text = self._create_stats_text(detection_info, processing_time)
        result = self._add_stats_to_image(comparison, stats_text)
        
        return result
    
    def _create_stats_text(
        self, 
        detection_info: List[Dict[str, Any]], 
        processing_time: float = None
    ) -> str:
        """创建统计信息文本"""
        stats = []
        stats.append(f"检测到水印数量: {len(detection_info)}")
        
        if detection_info:
            confidences = [d['confidence'] for d in detection_info]
            stats.append(f"平均置信度: {np.mean(confidences):.3f}")
            stats.append(f"最高置信度: {max(confidences):.3f}")
            stats.append(f"最低置信度: {min(confidences):.3f}")
        
        if processing_time is not None:
            stats.append(f"处理时间: {processing_time:.3f}秒")
        
        return "\n".join(stats)
    
    def _add_stats_to_image(self, image: Image.Image, stats_text: str) -> Image.Image:
        """在图像底部添加统计信息"""
        # 计算需要的额外高度
        lines = stats_text.split('\n')
        line_height = 25
        stats_height = len(lines) * line_height + 20
        
        # 创建新图像
        new_height = image.height + stats_height
        result = Image.new('RGB', (image.width, new_height), (240, 240, 240))
        
        # 粘贴原图像
        result.paste(image, (0, 0))
        
        # 添加统计文本
        draw = ImageDraw.Draw(result)
        y_offset = image.height + 10
        
        for line in lines:
            draw.text((20, y_offset), line, fill=(0, 0, 0), font=self.font)
            y_offset += line_height
        
        return result
