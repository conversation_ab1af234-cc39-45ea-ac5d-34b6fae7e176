"""
LAMA模型验证框架
用于测试和验证LAMA模型在水印移除任务中的准确性和性能
"""

import numpy as np
from PIL import Image, ImageChops, ImageStat
import cv2
from typing import List, Dict, Any, Tuple, Optional, Union
import logging
from pathlib import Path
import time
import json
from dataclasses import dataclass
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
import matplotlib.pyplot as plt

from .watermark_detector import WatermarkDetector
from .lama_inpainter import LamaInpainter
from .visualization import WatermarkVisualization

logger = logging.getLogger(__name__)


@dataclass
class ValidationMetrics:
    """验证指标数据类"""
    ssim_score: float
    psnr_score: float
    mse_score: float
    mae_score: float
    processing_time: float
    watermark_removal_completeness: float
    visual_quality_score: float


@dataclass
class ValidationResult:
    """验证结果数据类"""
    image_id: str
    watermark_count: int
    detection_confidence: float
    metrics: ValidationMetrics
    success: bool
    error_message: Optional[str] = None


class LamaValidationFramework:
    """LAMA模型验证框架"""
    
    def __init__(
        self,
        detector: WatermarkDetector,
        inpainter: LamaInpainter,
        visualizer: Optional[WatermarkVisualization] = None
    ):
        """
        初始化验证框架
        
        Args:
            detector: 水印检测器
            inpainter: LAMA修复器
            visualizer: 可视化器（可选）
        """
        self.detector = detector
        self.inpainter = inpainter
        self.visualizer = visualizer or WatermarkVisualization()
        
    def validate_single_watermark_removal(
        self,
        image: Union[Image.Image, str, Path],
        reference_image: Optional[Union[Image.Image, str, Path]] = None,
        confidence_threshold: float = 0.5,
        save_results: bool = False,
        output_dir: Optional[str] = None
    ) -> ValidationResult:
        """
        验证单个水印移除的效果
        
        Args:
            image: 带水印的图像
            reference_image: 参考图像（无水印的原图，可选）
            confidence_threshold: 检测置信度阈值
            save_results: 是否保存结果
            output_dir: 输出目录
            
        Returns:
            验证结果
        """
        start_time = time.time()
        image_id = f"single_{int(time.time())}"
        
        try:
            # 加载图像
            if isinstance(image, (str, Path)):
                image = Image.open(image).convert('RGB')
            
            if reference_image and isinstance(reference_image, (str, Path)):
                reference_image = Image.open(reference_image).convert('RGB')
            
            # 检测水印
            mask, avg_confidence, detection_info = self.detector.detect(
                image=image,
                confidence_threshold=confidence_threshold,
                return_detailed_info=True
            )
            
            # 如果没有检测到水印
            if len(detection_info) == 0:
                return ValidationResult(
                    image_id=image_id,
                    watermark_count=0,
                    detection_confidence=0.0,
                    metrics=ValidationMetrics(0, 0, 0, 0, time.time() - start_time, 0, 0),
                    success=False,
                    error_message="未检测到水印"
                )
            
            # 使用LAMA进行修复
            inpaint_start = time.time()
            restored_image = self.inpainter.inpaint(image, mask)
            processing_time = time.time() - inpaint_start
            
            # 计算验证指标
            metrics = self._calculate_metrics(
                original=image,
                restored=restored_image,
                reference=reference_image,
                mask=mask,
                processing_time=processing_time
            )
            
            # 保存结果
            if save_results and output_dir:
                self._save_validation_results(
                    image_id, image, restored_image, mask, 
                    detection_info, metrics, output_dir
                )
            
            return ValidationResult(
                image_id=image_id,
                watermark_count=len(detection_info),
                detection_confidence=avg_confidence,
                metrics=metrics,
                success=True
            )
            
        except Exception as e:
            logger.error(f"单个水印验证失败: {e}")
            return ValidationResult(
                image_id=image_id,
                watermark_count=0,
                detection_confidence=0.0,
                metrics=ValidationMetrics(0, 0, 0, 0, time.time() - start_time, 0, 0),
                success=False,
                error_message=str(e)
            )
    
    def validate_multiple_watermark_removal(
        self,
        image: Union[Image.Image, str, Path],
        reference_image: Optional[Union[Image.Image, str, Path]] = None,
        confidence_threshold: float = 0.5,
        save_results: bool = False,
        output_dir: Optional[str] = None
    ) -> ValidationResult:
        """
        验证多个水印移除的效果
        
        Args:
            image: 带多个水印的图像
            reference_image: 参考图像（可选）
            confidence_threshold: 检测置信度阈值
            save_results: 是否保存结果
            output_dir: 输出目录
            
        Returns:
            验证结果
        """
        start_time = time.time()
        image_id = f"multiple_{int(time.time())}"
        
        try:
            # 加载图像
            if isinstance(image, (str, Path)):
                image = Image.open(image).convert('RGB')
            
            if reference_image and isinstance(reference_image, (str, Path)):
                reference_image = Image.open(reference_image).convert('RGB')
            
            # 检测水印
            mask, avg_confidence, detection_info = self.detector.detect(
                image=image,
                confidence_threshold=confidence_threshold,
                return_detailed_info=True
            )
            
            # 检查是否检测到多个水印
            if len(detection_info) < 2:
                return ValidationResult(
                    image_id=image_id,
                    watermark_count=len(detection_info),
                    detection_confidence=avg_confidence,
                    metrics=ValidationMetrics(0, 0, 0, 0, time.time() - start_time, 0, 0),
                    success=False,
                    error_message=f"只检测到{len(detection_info)}个水印，需要至少2个"
                )
            
            # 使用LAMA进行修复
            inpaint_start = time.time()
            restored_image = self.inpainter.inpaint(image, mask)
            processing_time = time.time() - inpaint_start
            
            # 计算验证指标
            metrics = self._calculate_metrics(
                original=image,
                restored=restored_image,
                reference=reference_image,
                mask=mask,
                processing_time=processing_time
            )
            
            # 保存结果
            if save_results and output_dir:
                self._save_validation_results(
                    image_id, image, restored_image, mask, 
                    detection_info, metrics, output_dir
                )
            
            return ValidationResult(
                image_id=image_id,
                watermark_count=len(detection_info),
                detection_confidence=avg_confidence,
                metrics=metrics,
                success=True
            )
            
        except Exception as e:
            logger.error(f"多个水印验证失败: {e}")
            return ValidationResult(
                image_id=image_id,
                watermark_count=0,
                detection_confidence=0.0,
                metrics=ValidationMetrics(0, 0, 0, 0, time.time() - start_time, 0, 0),
                success=False,
                error_message=str(e)
            )
    
    def _calculate_metrics(
        self,
        original: Image.Image,
        restored: Image.Image,
        reference: Optional[Image.Image],
        mask: np.ndarray,
        processing_time: float
    ) -> ValidationMetrics:
        """
        计算验证指标
        
        Args:
            original: 原始图像
            restored: 修复后图像
            reference: 参考图像（可选）
            mask: 检测掩码
            processing_time: 处理时间
            
        Returns:
            验证指标
        """
        # 确保图像大小一致
        if restored.size != original.size:
            restored = restored.resize(original.size, Image.LANCZOS)
        
        # 转换为numpy数组
        original_array = np.array(original)
        restored_array = np.array(restored)
        
        # 如果有参考图像，使用参考图像计算指标
        if reference is not None:
            if reference.size != original.size:
                reference = reference.resize(original.size, Image.LANCZOS)
            reference_array = np.array(reference)
            comparison_array = reference_array
        else:
            # 没有参考图像时，使用原图作为比较基准
            comparison_array = original_array
        
        # 计算基础指标
        mse_score = np.mean((restored_array - comparison_array) ** 2)
        mae_score = np.mean(np.abs(restored_array - comparison_array))
        
        # 计算SSIM和PSNR
        try:
            # 转换为灰度图计算SSIM
            original_gray = cv2.cvtColor(original_array, cv2.COLOR_RGB2GRAY)
            restored_gray = cv2.cvtColor(restored_array, cv2.COLOR_RGB2GRAY)
            
            if reference is not None:
                reference_gray = cv2.cvtColor(reference_array, cv2.COLOR_RGB2GRAY)
                ssim_score = ssim(reference_gray, restored_gray, data_range=255)
                psnr_score = psnr(reference_array, restored_array, data_range=255)
            else:
                ssim_score = ssim(original_gray, restored_gray, data_range=255)
                psnr_score = psnr(original_array, restored_array, data_range=255)
                
        except Exception as e:
            logger.warning(f"计算SSIM/PSNR失败: {e}")
            ssim_score = 0.0
            psnr_score = 0.0
        
        # 计算水印移除完整性
        watermark_removal_completeness = self._calculate_watermark_removal_completeness(
            original_array, restored_array, mask
        )
        
        # 计算视觉质量分数
        visual_quality_score = self._calculate_visual_quality_score(
            original_array, restored_array, mask
        )
        
        return ValidationMetrics(
            ssim_score=ssim_score,
            psnr_score=psnr_score,
            mse_score=mse_score,
            mae_score=mae_score,
            processing_time=processing_time,
            watermark_removal_completeness=watermark_removal_completeness,
            visual_quality_score=visual_quality_score
        )

    def _calculate_watermark_removal_completeness(
        self,
        original: np.ndarray,
        restored: np.ndarray,
        mask: np.ndarray
    ) -> float:
        """
        计算水印移除完整性

        Args:
            original: 原始图像数组
            restored: 修复后图像数组
            mask: 水印掩码

        Returns:
            完整性分数 (0-1)
        """
        try:
            # 确保掩码是二值的
            binary_mask = (mask > 127).astype(np.uint8)

            if np.sum(binary_mask) == 0:
                return 1.0  # 没有水印区域

            # 计算水印区域的变化
            diff = np.abs(original.astype(np.float32) - restored.astype(np.float32))

            # 在水印区域内的平均变化
            watermark_region_change = np.mean(diff[binary_mask > 0])

            # 在非水印区域的平均变化
            non_watermark_region_change = np.mean(diff[binary_mask == 0])

            # 完整性分数：水印区域变化大，非水印区域变化小
            if watermark_region_change == 0:
                return 0.0

            completeness = min(1.0, watermark_region_change / (watermark_region_change + non_watermark_region_change + 1e-8))

            return completeness

        except Exception as e:
            logger.warning(f"计算水印移除完整性失败: {e}")
            return 0.0

    def _calculate_visual_quality_score(
        self,
        original: np.ndarray,
        restored: np.ndarray,
        mask: np.ndarray
    ) -> float:
        """
        计算视觉质量分数

        Args:
            original: 原始图像数组
            restored: 修复后图像数组
            mask: 水印掩码

        Returns:
            视觉质量分数 (0-1)
        """
        try:
            # 计算修复区域的平滑度
            binary_mask = (mask > 127).astype(np.uint8)

            if np.sum(binary_mask) == 0:
                return 1.0

            # 使用拉普拉斯算子检测边缘
            restored_gray = cv2.cvtColor(restored, cv2.COLOR_RGB2GRAY)
            laplacian = cv2.Laplacian(restored_gray, cv2.CV_64F)

            # 计算修复区域的边缘强度
            edge_strength_in_mask = np.mean(np.abs(laplacian[binary_mask > 0]))
            edge_strength_outside_mask = np.mean(np.abs(laplacian[binary_mask == 0]))

            # 视觉质量分数：修复区域边缘强度应该与周围区域相似
            if edge_strength_outside_mask == 0:
                return 0.5

            edge_ratio = edge_strength_in_mask / (edge_strength_outside_mask + 1e-8)

            # 理想情况下边缘强度比例应该接近1
            visual_quality = 1.0 - abs(1.0 - edge_ratio)
            visual_quality = max(0.0, min(1.0, visual_quality))

            return visual_quality

        except Exception as e:
            logger.warning(f"计算视觉质量分数失败: {e}")
            return 0.0

    def _save_validation_results(
        self,
        image_id: str,
        original: Image.Image,
        restored: Image.Image,
        mask: np.ndarray,
        detection_info: List[Dict[str, Any]],
        metrics: ValidationMetrics,
        output_dir: str
    ):
        """
        保存验证结果

        Args:
            image_id: 图像ID
            original: 原始图像
            restored: 修复后图像
            mask: 检测掩码
            detection_info: 检测信息
            metrics: 验证指标
            output_dir: 输出目录
        """
        try:
            output_path = Path(output_dir) / image_id
            output_path.mkdir(parents=True, exist_ok=True)

            # 保存图像
            original.save(output_path / "original.jpg", 'JPEG', quality=95)
            restored.save(output_path / "restored.jpg", 'JPEG', quality=95)

            # 保存掩码
            mask_image = Image.fromarray(mask)
            mask_image.save(output_path / "mask.png")

            # 创建可视化
            detection_boxes = self.visualizer.draw_detection_boxes(
                original, detection_info, show_confidence=True
            )
            detection_boxes.save(output_path / "detection_boxes.jpg", 'JPEG', quality=95)

            mask_overlay = self.visualizer.create_mask_overlay(
                original, mask, alpha=0.4
            )
            mask_overlay.save(output_path / "mask_overlay.jpg", 'JPEG', quality=95)

            comparison = self.visualizer.create_side_by_side_comparison(
                original, restored, titles=("原图", "修复后")
            )
            comparison.save(output_path / "comparison.jpg", 'JPEG', quality=95)

            # 保存指标数据
            metrics_data = {
                'image_id': image_id,
                'watermark_count': len(detection_info),
                'detection_info': detection_info,
                'metrics': {
                    'ssim_score': metrics.ssim_score,
                    'psnr_score': metrics.psnr_score,
                    'mse_score': metrics.mse_score,
                    'mae_score': metrics.mae_score,
                    'processing_time': metrics.processing_time,
                    'watermark_removal_completeness': metrics.watermark_removal_completeness,
                    'visual_quality_score': metrics.visual_quality_score
                }
            }

            with open(output_path / "metrics.json", 'w', encoding='utf-8') as f:
                json.dump(metrics_data, f, indent=2, ensure_ascii=False)

            logger.info(f"验证结果已保存到: {output_path}")

        except Exception as e:
            logger.error(f"保存验证结果失败: {e}")

    def run_batch_validation(
        self,
        image_paths: List[str],
        confidence_threshold: float = 0.5,
        save_results: bool = False,
        output_dir: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        批量验证

        Args:
            image_paths: 图像路径列表
            confidence_threshold: 检测置信度阈值
            save_results: 是否保存结果
            output_dir: 输出目录

        Returns:
            批量验证结果
        """
        results = []
        single_watermark_count = 0
        multiple_watermark_count = 0
        failed_count = 0

        for image_path in image_paths:
            try:
                # 先检测水印数量
                image = Image.open(image_path).convert('RGB')
                _, _, detection_info = self.detector.detect(
                    image=image,
                    confidence_threshold=confidence_threshold
                )

                # 根据水印数量选择验证方法
                if len(detection_info) == 1:
                    result = self.validate_single_watermark_removal(
                        image_path, confidence_threshold=confidence_threshold,
                        save_results=save_results, output_dir=output_dir
                    )
                    single_watermark_count += 1
                elif len(detection_info) > 1:
                    result = self.validate_multiple_watermark_removal(
                        image_path, confidence_threshold=confidence_threshold,
                        save_results=save_results, output_dir=output_dir
                    )
                    multiple_watermark_count += 1
                else:
                    result = ValidationResult(
                        image_id=f"no_watermark_{int(time.time())}",
                        watermark_count=0,
                        detection_confidence=0.0,
                        metrics=ValidationMetrics(0, 0, 0, 0, 0, 0, 0),
                        success=False,
                        error_message="未检测到水印"
                    )
                    failed_count += 1

                results.append(result)

            except Exception as e:
                logger.error(f"处理图像 {image_path} 失败: {e}")
                failed_count += 1
                results.append(ValidationResult(
                    image_id=f"error_{int(time.time())}",
                    watermark_count=0,
                    detection_confidence=0.0,
                    metrics=ValidationMetrics(0, 0, 0, 0, 0, 0, 0),
                    success=False,
                    error_message=str(e)
                ))

        # 计算统计信息
        successful_results = [r for r in results if r.success]

        if successful_results:
            avg_metrics = self._calculate_average_metrics(successful_results)
        else:
            avg_metrics = ValidationMetrics(0, 0, 0, 0, 0, 0, 0)

        return {
            'total_images': len(image_paths),
            'successful_validations': len(successful_results),
            'failed_validations': failed_count,
            'single_watermark_images': single_watermark_count,
            'multiple_watermark_images': multiple_watermark_count,
            'average_metrics': avg_metrics,
            'individual_results': results
        }

    def _calculate_average_metrics(self, results: List[ValidationResult]) -> ValidationMetrics:
        """计算平均指标"""
        if not results:
            return ValidationMetrics(0, 0, 0, 0, 0, 0, 0)

        total_ssim = sum(r.metrics.ssim_score for r in results)
        total_psnr = sum(r.metrics.psnr_score for r in results)
        total_mse = sum(r.metrics.mse_score for r in results)
        total_mae = sum(r.metrics.mae_score for r in results)
        total_time = sum(r.metrics.processing_time for r in results)
        total_completeness = sum(r.metrics.watermark_removal_completeness for r in results)
        total_quality = sum(r.metrics.visual_quality_score for r in results)

        count = len(results)

        return ValidationMetrics(
            ssim_score=total_ssim / count,
            psnr_score=total_psnr / count,
            mse_score=total_mse / count,
            mae_score=total_mae / count,
            processing_time=total_time / count,
            watermark_removal_completeness=total_completeness / count,
            visual_quality_score=total_quality / count
        )
