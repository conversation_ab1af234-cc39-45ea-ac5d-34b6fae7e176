"""
增强水印分析示例
展示新的可视化、验证和调试功能
"""

import os
import sys
import logging
from pathlib import Path
import argparse

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from models import (
    WatermarkDetector, LamaInpainter, WatermarkRemovalPipeline,
    WatermarkVisualization, LamaValidationFramework, WatermarkDebuggingTool
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def setup_models():
    """设置模型"""
    logger.info("正在初始化模型...")
    
    # 初始化检测器
    detector = WatermarkDetector(device="auto")
    
    # 初始化修复器
    inpainter = LamaInpainter(device="auto")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline(device="auto")
    
    # 初始化可视化器
    visualizer = WatermarkVisualization()
    
    # 初始化验证框架
    validator = LamaValidationFramework(detector, inpainter, visualizer)
    
    # 初始化调试工具
    debugger = WatermarkDebuggingTool(detector, inpainter, visualizer, validator)
    
    logger.info("模型初始化完成")
    return pipeline, visualizer, validator, debugger


def demonstrate_visualization(pipeline, visualizer, image_path, output_dir):
    """演示可视化功能"""
    logger.info("=== 演示可视化功能 ===")
    
    # 创建输出目录
    vis_dir = Path(output_dir) / "visualization"
    vis_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建检测可视化
    logger.info("创建检测可视化...")
    vis_result = pipeline.create_detection_visualization(
        image_path,
        confidence_threshold=0.1,
        save_path=str(vis_dir)
    )
    
    logger.info(f"检测到 {vis_result['watermark_count']} 个水印")
    logger.info(f"平均置信度: {vis_result['average_confidence']:.3f}")
    
    # 保存详细信息
    for i, detection in enumerate(vis_result['detection_info']):
        logger.info(f"水印 {i+1}: 置信度={detection['confidence']:.3f}, "
                   f"位置={detection['bbox']}, 面积={detection['area']}")
    
    logger.info(f"可视化结果已保存到: {vis_dir}")
    return vis_result


def demonstrate_validation(validator, image_path, output_dir):
    """演示验证功能"""
    logger.info("=== 演示验证功能 ===")
    
    # 创建输出目录
    val_dir = Path(output_dir) / "validation"
    val_dir.mkdir(parents=True, exist_ok=True)
    
    # 单个水印验证
    logger.info("执行单个水印移除验证...")
    single_result = validator.validate_single_watermark_removal(
        image_path,
        confidence_threshold=0.1,
        save_results=True,
        output_dir=str(val_dir)
    )
    
    if single_result.success:
        logger.info("单个水印验证成功:")
        logger.info(f"  检测到水印数量: {single_result.watermark_count}")
        logger.info(f"  检测置信度: {single_result.detection_confidence:.3f}")
        logger.info(f"  SSIM分数: {single_result.metrics.ssim_score:.3f}")
        logger.info(f"  PSNR分数: {single_result.metrics.psnr_score:.3f}")
        logger.info(f"  处理时间: {single_result.metrics.processing_time:.3f}秒")
        logger.info(f"  水印移除完整性: {single_result.metrics.watermark_removal_completeness:.3f}")
        logger.info(f"  视觉质量分数: {single_result.metrics.visual_quality_score:.3f}")
    else:
        logger.warning(f"单个水印验证失败: {single_result.error_message}")
    
    # 多个水印验证
    logger.info("执行多个水印移除验证...")
    multiple_result = validator.validate_multiple_watermark_removal(
        image_path,
        confidence_threshold=0.1,
        save_results=True,
        output_dir=str(val_dir)
    )
    
    if multiple_result.success:
        logger.info("多个水印验证成功:")
        logger.info(f"  检测到水印数量: {multiple_result.watermark_count}")
        logger.info(f"  检测置信度: {multiple_result.detection_confidence:.3f}")
    else:
        logger.warning(f"多个水印验证失败: {multiple_result.error_message}")
    
    logger.info(f"验证结果已保存到: {val_dir}")
    return single_result, multiple_result


def demonstrate_debugging(debugger, image_path, output_dir):
    """演示调试功能"""
    logger.info("=== 演示调试功能 ===")
    
    # 创建输出目录
    debug_dir = Path(output_dir) / "debugging"
    debug_dir.mkdir(parents=True, exist_ok=True)
    
    # 完整管道调试
    logger.info("执行完整管道调试...")
    debug_result = debugger.debug_full_pipeline(
        image_path,
        confidence_threshold=0.1,
        save_debug_results=True,
        output_dir=str(debug_dir)
    )
    
    if debug_result.overall_success:
        logger.info("管道调试成功:")
        
        # 检测阶段信息
        det_debug = debug_result.detection_debug
        logger.info(f"  检测阶段:")
        logger.info(f"    总时间: {det_debug.detection_time:.3f}秒")
        logger.info(f"    推理时间: {det_debug.inference_time:.3f}秒")
        logger.info(f"    掩码质量分数: {det_debug.mask_quality_score:.3f}")
        logger.info(f"    假阳性可能性: {det_debug.false_positive_likelihood:.3f}")
        
        # 修复阶段信息
        inp_debug = debug_result.inpainting_debug
        logger.info(f"  修复阶段:")
        logger.info(f"    总时间: {inp_debug.inpainting_time:.3f}秒")
        logger.info(f"    模型推理时间: {inp_debug.model_inference_time:.3f}秒")
        logger.info(f"    掩码覆盖率: {inp_debug.mask_coverage_ratio:.3f}")
        logger.info(f"    修复方法: {inp_debug.inpainting_method}")
        
        # 性能分析
        logger.info(f"  性能瓶颈: {debug_result.bottleneck_stage}")
        
        # 建议
        logger.info("  优化建议:")
        for rec in debug_result.recommendations:
            logger.info(f"    - {rec}")
        
        # 错误分析
        if any(debug_result.error_analysis.values()):
            logger.info("  发现的问题:")
            for category, issues in debug_result.error_analysis.items():
                if issues:
                    logger.info(f"    {category}:")
                    for issue in issues:
                        logger.info(f"      - {issue}")
    else:
        logger.warning(f"管道调试失败: {debug_result.error_analysis}")
    
    logger.info(f"调试结果已保存到: {debug_dir}")
    return debug_result


def demonstrate_batch_validation(validator, image_paths, output_dir):
    """演示批量验证功能"""
    logger.info("=== 演示批量验证功能 ===")
    
    if not image_paths:
        logger.warning("没有提供批量验证的图像路径")
        return None
    
    # 创建输出目录
    batch_dir = Path(output_dir) / "batch_validation"
    batch_dir.mkdir(parents=True, exist_ok=True)
    
    # 执行批量验证
    logger.info(f"对 {len(image_paths)} 张图像执行批量验证...")
    batch_result = validator.run_batch_validation(
        image_paths,
        confidence_threshold=0.1,
        save_results=True,
        output_dir=str(batch_dir)
    )
    
    # 显示批量结果
    logger.info("批量验证结果:")
    logger.info(f"  总图像数: {batch_result['total_images']}")
    logger.info(f"  成功验证: {batch_result['successful_validations']}")
    logger.info(f"  失败验证: {batch_result['failed_validations']}")
    logger.info(f"  单水印图像: {batch_result['single_watermark_images']}")
    logger.info(f"  多水印图像: {batch_result['multiple_watermark_images']}")
    
    # 显示平均指标
    avg_metrics = batch_result['average_metrics']
    logger.info("平均性能指标:")
    logger.info(f"  平均SSIM: {avg_metrics.ssim_score:.3f}")
    logger.info(f"  平均PSNR: {avg_metrics.psnr_score:.3f}")
    logger.info(f"  平均处理时间: {avg_metrics.processing_time:.3f}秒")
    logger.info(f"  平均水印移除完整性: {avg_metrics.watermark_removal_completeness:.3f}")
    logger.info(f"  平均视觉质量: {avg_metrics.visual_quality_score:.3f}")
    
    logger.info(f"批量验证结果已保存到: {batch_dir}")
    return batch_result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="增强水印分析示例")
    parser.add_argument("--image", required=True, help="输入图像路径")
    parser.add_argument("--output", default="./analysis_results", help="输出目录")
    parser.add_argument("--batch", nargs="*", help="批量验证的图像路径列表")
    parser.add_argument("--skip-visualization", action="store_true", help="跳过可视化演示")
    parser.add_argument("--skip-validation", action="store_true", help="跳过验证演示")
    parser.add_argument("--skip-debugging", action="store_true", help="跳过调试演示")
    parser.add_argument("--skip-batch", action="store_true", help="跳过批量验证演示")
    parser.add_argument("--use-smart-mask", action="store_true", help="使用智能掩码增强")
    parser.add_argument("--context-expansion", type=float, default=0.15, help="上下文扩展比例")
    
    args = parser.parse_args()
    
    # 检查输入文件
    if not os.path.exists(args.image):
        logger.error(f"输入图像不存在: {args.image}")
        return
    
    # 创建输出目录
    output_dir = Path(args.output)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    try:
        # 设置模型
        pipeline, visualizer, validator, debugger = setup_models()
        
        # 演示可视化功能
        if not args.skip_visualization:
            vis_result = demonstrate_visualization(pipeline, visualizer, args.image, args.output)
        
        # 演示验证功能
        if not args.skip_validation:
            single_result, multiple_result = demonstrate_validation(validator, args.image, args.output)
        
        # 演示调试功能
        if not args.skip_debugging:
            debug_result = demonstrate_debugging(debugger, args.image, args.output)
        
        # 演示批量验证功能
        if not args.skip_batch and args.batch:
            batch_result = demonstrate_batch_validation(validator, args.batch, args.output)
        
        logger.info("=== 分析完成 ===")
        logger.info(f"所有结果已保存到: {args.output}")
        
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        raise


if __name__ == "__main__":
    main()
