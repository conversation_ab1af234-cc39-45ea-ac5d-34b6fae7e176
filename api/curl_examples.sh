#!/bin/bash

BASE_URL="http://localhost:8000"
IMAGE_URL="https://example.com/watermarked_image.jpg"

echo "=== 健康检查 ==="
curl -X GET "$BASE_URL/health"

echo -e "\n\n=== 异步提交任务 ==="
RESPONSE=$(curl -s -X POST "$BASE_URL/remove-watermark" \
  -H "Content-Type: application/json" \
  -d "{
    \"image_url\": \"$IMAGE_URL\",
    \"confidence_threshold\": 0.3,
    \"enhance_mask\": true
  }")

echo $RESPONSE

# 提取task_id
TASK_ID=$(echo $RESPONSE | jq -r '.task_id')
echo "Task ID: $TASK_ID"

echo -e "\n\n=== 查询任务状态 ==="
while true; do
  STATUS=$(curl -s -X GET "$BASE_URL/task/$TASK_ID")
  echo $STATUS
  
  TASK_STATUS=$(echo $STATUS | jq -r '.status')
  if [ "$TASK_STATUS" = "completed" ] || [ "$TASK_STATUS" = "failed" ]; then
    break
  fi
  
  sleep 2
done

echo -e "\n\n=== 同步处理 ==="
curl -X POST "$BASE_URL/remove-watermark-sync" \
  -H "Content-Type: application/json" \
  -d "{
    \"image_url\": \"$IMAGE_URL\",
    \"confidence_threshold\": 0.3,
    \"enhance_mask\": true
  }"