"""
项目清理脚本
清理测试生成的临时文件和输出
"""

import os
import glob
from pathlib import Path
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def cleanup_test_outputs():
    """清理测试输出文件"""
    logger.info("🧹 清理测试输出文件...")
    
    # 测试生成的图像文件
    test_patterns = [
        "*_result.jpg",
        "*_mask.png", 
        "*_comparison.jpg",
        "channel_comparison.png",
        "difference_map.png",
        "mask_enhancement_comparison.jpg",
        "simple_comparison.jpg",
        "enhanced_*.jpg",
        "improved_*.jpg",
        "real_lama_*.jpg",
        "real_lama_*.png",
        "conservative_*.jpg",
        "conservative_*.png",
        "final_*.jpg",
        "traditional_*.jpg",
        "smart_*.jpg"
    ]
    
    cleaned_count = 0
    for pattern in test_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                logger.info(f"删除: {file_path}")
                cleaned_count += 1
            except Exception as e:
                logger.warning(f"无法删除 {file_path}: {e}")
    
    logger.info(f"✅ 清理了 {cleaned_count} 个测试输出文件")


def cleanup_outputs_dir():
    """清理outputs目录"""
    logger.info("🧹 清理outputs目录...")
    
    outputs_dir = Path("outputs")
    if outputs_dir.exists():
        cleaned_count = 0
        for file_path in outputs_dir.glob("*"):
            if file_path.is_file():
                try:
                    file_path.unlink()
                    logger.info(f"删除: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    logger.warning(f"无法删除 {file_path}: {e}")
        
        logger.info(f"✅ 清理了outputs目录中的 {cleaned_count} 个文件")
    else:
        logger.info("outputs目录不存在")


def cleanup_cache_files():
    """清理缓存文件"""
    logger.info("🧹 清理缓存文件...")
    
    cache_patterns = [
        "__pycache__",
        "*.pyc",
        "*.pyo",
        ".pytest_cache"
    ]
    
    cleaned_count = 0
    
    # 清理Python缓存
    for root, dirs, files in os.walk("."):
        # 清理__pycache__目录
        if "__pycache__" in dirs:
            cache_dir = Path(root) / "__pycache__"
            try:
                import shutil
                shutil.rmtree(cache_dir)
                logger.info(f"删除缓存目录: {cache_dir}")
                cleaned_count += 1
            except Exception as e:
                logger.warning(f"无法删除缓存目录 {cache_dir}: {e}")
        
        # 清理.pyc文件
        for file in files:
            if file.endswith(('.pyc', '.pyo')):
                file_path = Path(root) / file
                try:
                    file_path.unlink()
                    logger.info(f"删除: {file_path}")
                    cleaned_count += 1
                except Exception as e:
                    logger.warning(f"无法删除 {file_path}: {e}")
    
    logger.info(f"✅ 清理了 {cleaned_count} 个缓存文件")


def cleanup_logs():
    """清理日志文件"""
    logger.info("🧹 清理日志文件...")
    
    log_patterns = [
        "*.log",
        "logs/*.log"
    ]
    
    cleaned_count = 0
    for pattern in log_patterns:
        for file_path in glob.glob(pattern):
            try:
                os.remove(file_path)
                logger.info(f"删除日志: {file_path}")
                cleaned_count += 1
            except Exception as e:
                logger.warning(f"无法删除日志 {file_path}: {e}")
    
    logger.info(f"✅ 清理了 {cleaned_count} 个日志文件")


def show_project_status():
    """显示项目状态"""
    logger.info("\n📊 项目状态:")
    
    # 统计文件数量
    stats = {
        "Python文件": len(list(Path(".").rglob("*.py"))),
        "测试文件": len(list(Path("tests").rglob("*.py"))) if Path("tests").exists() else 0,
        "文档文件": len(list(Path("docs").rglob("*.md"))) if Path("docs").exists() else 0,
        "图像文件": len(list(Path(".").rglob("*.jpg"))) + len(list(Path(".").rglob("*.png"))),
        "输出文件": len(list(Path("outputs").rglob("*"))) if Path("outputs").exists() else 0
    }
    
    for category, count in stats.items():
        logger.info(f"  {category}: {count}")
    
    # 检查重要目录
    important_dirs = ["models", "tests", "docs", "images", "outputs"]
    logger.info("\n📁 目录结构:")
    for dir_name in important_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            logger.info(f"  ✅ {dir_name}/")
        else:
            logger.info(f"  ❌ {dir_name}/ (不存在)")


def main():
    """主函数"""
    logger.info("🧹 项目清理工具")
    logger.info("=" * 50)
    
    # 显示当前状态
    show_project_status()
    
    # 询问用户确认
    logger.info("\n将要执行以下清理操作:")
    logger.info("1. 清理测试输出文件")
    logger.info("2. 清理outputs目录")
    logger.info("3. 清理Python缓存")
    logger.info("4. 清理日志文件")
    
    confirm = input("\n是否继续? (y/N): ").lower().strip()
    
    if confirm == 'y':
        logger.info("\n开始清理...")
        
        cleanup_test_outputs()
        cleanup_outputs_dir()
        cleanup_cache_files()
        cleanup_logs()
        
        logger.info("\n" + "=" * 50)
        logger.info("🎉 项目清理完成!")
        
        # 显示清理后状态
        show_project_status()
        
    else:
        logger.info("取消清理操作")


if __name__ == "__main__":
    main()
