"""
调试工具测试
"""

import pytest
import numpy as np
from PIL import Image
import tempfile
import os
from unittest.mock import Mock, patch

from models.debugging import (
    WatermarkDebuggingTool, DetectionDebugInfo, InpaintingDebugInfo, 
    PipelineDebugResult
)
from models.watermark_detector import WatermarkDetector
from models.lama_inpainter import Lama<PERSON>npainter
from models.visualization import WatermarkVisualization
from models.validation import LamaValidationFramework


class TestWatermarkDebuggingTool:
    """水印调试工具测试类"""
    
    @pytest.fixture
    def sample_image(self):
        """创建测试图像"""
        return Image.new('RGB', (512, 512), color='white')
    
    @pytest.fixture
    def sample_mask(self):
        """创建测试掩码"""
        mask = np.zeros((512, 512), dtype=np.uint8)
        mask[100:200, 100:200] = 255
        return mask
    
    @pytest.fixture
    def mock_detector(self):
        """创建模拟检测器"""
        detector = Mock(spec=WatermarkDetector)
        detector.detect.return_value = (
            np.zeros((512, 512), dtype=np.uint8),  # mask
            0.8,  # confidence
            [{'id': 0, 'bbox': [100, 100, 200, 200], 'confidence': 0.8}]  # detection_info
        )
        return detector
    
    @pytest.fixture
    def mock_inpainter(self):
        """创建模拟修复器"""
        inpainter = Mock(spec=LamaInpainter)
        inpainter.inpaint.return_value = Image.new('RGB', (512, 512), color='blue')
        inpainter.model = "lama_model"  # 模拟LAMA模型
        return inpainter
    
    @pytest.fixture
    def mock_visualizer(self):
        """创建模拟可视化器"""
        visualizer = Mock(spec=WatermarkVisualization)
        visualizer.draw_detection_boxes.return_value = Image.new('RGB', (512, 512), color='red')
        visualizer.create_mask_overlay.return_value = Image.new('RGB', (512, 512), color='green')
        visualizer.create_side_by_side_comparison.return_value = Image.new('RGB', (1024, 512), color='yellow')
        return visualizer
    
    @pytest.fixture
    def debugging_tool(self, mock_detector, mock_inpainter, mock_visualizer):
        """创建调试工具实例"""
        return WatermarkDebuggingTool(mock_detector, mock_inpainter, mock_visualizer)
    
    def test_init(self, mock_detector, mock_inpainter):
        """测试初始化"""
        tool = WatermarkDebuggingTool(mock_detector, mock_inpainter)
        
        assert tool.detector == mock_detector
        assert tool.inpainter == mock_inpainter
        assert tool.visualizer is not None
        assert tool.validator is not None
    
    def test_debug_detection_stage(self, debugging_tool, sample_image):
        """测试检测阶段调试"""
        debug_info = debugging_tool.debug_detection_stage(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(debug_info, DetectionDebugInfo)
        assert debug_info.detection_time >= 0
        assert debug_info.preprocessing_time >= 0
        assert debug_info.inference_time >= 0
        assert debug_info.postprocessing_time >= 0
        assert len(debug_info.model_confidence_scores) == 1
        assert debug_info.model_confidence_scores[0] == 0.8
        assert len(debug_info.detection_boxes) == 1
        assert 0 <= debug_info.mask_quality_score <= 1
        assert 0 <= debug_info.false_positive_likelihood <= 1
    
    def test_debug_detection_stage_with_save(self, debugging_tool, sample_image):
        """测试保存检测调试图像"""
        with tempfile.TemporaryDirectory() as temp_dir:
            debug_info = debugging_tool.debug_detection_stage(
                sample_image, confidence_threshold=0.5,
                save_debug_images=True, output_dir=temp_dir
            )
            
            assert isinstance(debug_info, DetectionDebugInfo)
            # 检查调试图像是否保存
            debug_dir = os.path.join(temp_dir, "detection_debug")
            assert os.path.exists(debug_dir)
    
    def test_debug_inpainting_stage(self, debugging_tool, sample_image, sample_mask):
        """测试修复阶段调试"""
        debug_info = debugging_tool.debug_inpainting_stage(
            sample_image, sample_mask
        )
        
        assert isinstance(debug_info, InpaintingDebugInfo)
        assert debug_info.inpainting_time > 0
        assert debug_info.preprocessing_time >= 0
        assert debug_info.model_inference_time >= 0
        assert debug_info.postprocessing_time >= 0
        assert 0 <= debug_info.mask_coverage_ratio <= 1
        assert debug_info.inpainting_method in ["LAMA", "OpenCV"]
        assert isinstance(debug_info.quality_metrics, dict)
    
    def test_debug_inpainting_stage_with_save(self, debugging_tool, sample_image, sample_mask):
        """测试保存修复调试图像"""
        with tempfile.TemporaryDirectory() as temp_dir:
            debug_info = debugging_tool.debug_inpainting_stage(
                sample_image, sample_mask,
                save_debug_images=True, output_dir=temp_dir
            )
            
            assert isinstance(debug_info, InpaintingDebugInfo)
            # 检查调试图像是否保存
            debug_dir = os.path.join(temp_dir, "inpainting_debug")
            assert os.path.exists(debug_dir)
    
    def test_debug_full_pipeline_success(self, debugging_tool, sample_image):
        """测试完整管道调试（成功）"""
        result = debugging_tool.debug_full_pipeline(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, PipelineDebugResult)
        assert result.overall_success is True
        assert isinstance(result.detection_debug, DetectionDebugInfo)
        assert isinstance(result.inpainting_debug, InpaintingDebugInfo)
        assert result.bottleneck_stage in ["detection", "inpainting", "balanced"]
        assert isinstance(result.recommendations, list)
        assert isinstance(result.error_analysis, dict)
    
    def test_debug_full_pipeline_no_detection(self, debugging_tool, sample_image):
        """测试完整管道调试（未检测到水印）"""
        # 模拟未检测到水印
        debugging_tool.detector.detect.return_value = (
            np.zeros((512, 512), dtype=np.uint8),
            0.0,
            []
        )
        
        result = debugging_tool.debug_full_pipeline(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, PipelineDebugResult)
        assert result.overall_success is False
        assert result.bottleneck_stage == "detection"
        assert "检查图像质量" in result.recommendations
    
    def test_debug_full_pipeline_with_save(self, debugging_tool, sample_image):
        """测试保存完整管道调试结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = debugging_tool.debug_full_pipeline(
                sample_image, confidence_threshold=0.5,
                save_debug_results=True, output_dir=temp_dir
            )
            
            assert isinstance(result, PipelineDebugResult)
            # 检查调试结果是否保存
            debug_dir = os.path.join(temp_dir, "pipeline_debug")
            assert os.path.exists(debug_dir)
    
    def test_calculate_mask_quality(self, debugging_tool):
        """测试掩码质量计算"""
        # 创建高质量掩码（紧凑的矩形）
        good_mask = np.zeros((100, 100), dtype=np.uint8)
        good_mask[40:60, 40:60] = 255
        
        quality_score = debugging_tool._calculate_mask_quality(good_mask, (100, 100))
        
        assert 0 <= quality_score <= 1
    
    def test_calculate_mask_quality_empty(self, debugging_tool):
        """测试空掩码质量计算"""
        empty_mask = np.zeros((100, 100), dtype=np.uint8)
        
        quality_score = debugging_tool._calculate_mask_quality(empty_mask, (100, 100))
        
        assert quality_score == 0.0
    
    def test_calculate_false_positive_likelihood(self, debugging_tool, sample_image):
        """测试假阳性可能性计算"""
        detection_info = [
            {'id': 0, 'bbox': [100, 100, 200, 200], 'confidence': 0.3}  # 低置信度
        ]
        
        likelihood = debugging_tool._calculate_false_positive_likelihood(
            detection_info, sample_image
        )
        
        assert 0 <= likelihood <= 1
    
    def test_calculate_false_positive_likelihood_empty(self, debugging_tool, sample_image):
        """测试空检测信息的假阳性可能性计算"""
        likelihood = debugging_tool._calculate_false_positive_likelihood([], sample_image)
        
        assert likelihood == 0.0
    
    def test_calculate_inpainting_quality_metrics(self, debugging_tool, sample_image, sample_mask):
        """测试修复质量指标计算"""
        restored_image = Image.new('RGB', (512, 512), color='blue')
        
        metrics = debugging_tool._calculate_inpainting_quality_metrics(
            sample_image, restored_image, sample_mask
        )
        
        assert isinstance(metrics, dict)
        if "error" not in metrics:
            assert "color_consistency" in metrics
            assert "texture_consistency" in metrics
            assert "boundary_smoothness" in metrics
            assert "overall_quality" in metrics
    
    def test_calculate_color_consistency(self, debugging_tool):
        """测试颜色一致性计算"""
        original = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        restored = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[25:75, 25:75] = 255
        
        consistency = debugging_tool._calculate_color_consistency(original, restored, mask)
        
        assert 0 <= consistency <= 1
    
    def test_calculate_texture_consistency(self, debugging_tool):
        """测试纹理一致性计算"""
        original = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        restored = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[25:75, 25:75] = 255
        
        consistency = debugging_tool._calculate_texture_consistency(original, restored, mask)
        
        assert 0 <= consistency <= 1
    
    def test_calculate_boundary_smoothness(self, debugging_tool):
        """测试边界平滑度计算"""
        restored = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[25:75, 25:75] = 255
        
        smoothness = debugging_tool._calculate_boundary_smoothness(restored, mask)
        
        assert 0 <= smoothness <= 1
    
    def test_identify_bottleneck(self, debugging_tool):
        """测试瓶颈识别"""
        # 检测慢的情况
        detection_debug = DetectionDebugInfo(10.0, 1.0, 8.0, 1.0, [0.8], [[100, 100, 200, 200]], 0.8, 0.2)
        inpainting_debug = InpaintingDebugInfo(2.0, 0.5, 1.0, 0.5, 0.1, "LAMA", {})
        
        bottleneck = debugging_tool._identify_bottleneck(detection_debug, inpainting_debug)
        assert bottleneck == "detection"
        
        # 修复慢的情况
        detection_debug = DetectionDebugInfo(2.0, 0.5, 1.0, 0.5, [0.8], [[100, 100, 200, 200]], 0.8, 0.2)
        inpainting_debug = InpaintingDebugInfo(10.0, 1.0, 8.0, 1.0, 0.1, "LAMA", {})
        
        bottleneck = debugging_tool._identify_bottleneck(detection_debug, inpainting_debug)
        assert bottleneck == "inpainting"
        
        # 平衡的情况
        detection_debug = DetectionDebugInfo(3.0, 0.5, 2.0, 0.5, [0.8], [[100, 100, 200, 200]], 0.8, 0.2)
        inpainting_debug = InpaintingDebugInfo(3.5, 0.5, 2.5, 0.5, 0.1, "LAMA", {})
        
        bottleneck = debugging_tool._identify_bottleneck(detection_debug, inpainting_debug)
        assert bottleneck == "balanced"
    
    def test_generate_recommendations(self, debugging_tool):
        """测试建议生成"""
        detection_debug = DetectionDebugInfo(2.0, 0.5, 1.0, 0.5, [0.3], [[100, 100, 200, 200]], 0.3, 0.8)
        inpainting_debug = InpaintingDebugInfo(3.0, 0.5, 2.0, 0.5, 0.4, "OpenCV", {"overall_quality": 0.4})
        
        recommendations = debugging_tool._generate_recommendations(detection_debug, inpainting_debug)
        
        assert isinstance(recommendations, list)
        assert len(recommendations) > 0
        assert any("假阳性" in rec for rec in recommendations)
        assert any("OpenCV" in rec for rec in recommendations)
    
    def test_analyze_errors(self, debugging_tool):
        """测试错误分析"""
        detection_debug = DetectionDebugInfo(2.0, 0.5, 1.0, 0.5, [], [], 0.2, 0.9)
        inpainting_debug = InpaintingDebugInfo(3.0, 0.5, 2.0, 0.5, 0.6, "LAMA", {"error": "计算失败"})
        
        error_analysis = debugging_tool._analyze_errors(detection_debug, inpainting_debug)
        
        assert isinstance(error_analysis, dict)
        assert "detection_issues" in error_analysis
        assert "inpainting_issues" in error_analysis
        assert "performance_issues" in error_analysis
        assert "未检测到任何水印" in error_analysis["detection_issues"]
        assert any("计算失败" in issue for issue in error_analysis["inpainting_issues"])
    
    def test_create_difference_image(self, debugging_tool, sample_image):
        """测试差异图像创建"""
        img2 = Image.new('RGB', (512, 512), color='blue')
        
        diff_image = debugging_tool._create_difference_image(sample_image, img2)
        
        assert isinstance(diff_image, Image.Image)
        assert diff_image.size == sample_image.size
    
    def test_error_handling(self, debugging_tool, sample_image):
        """测试错误处理"""
        # 模拟检测器抛出异常
        debugging_tool.detector.detect.side_effect = Exception("检测失败")
        
        result = debugging_tool.debug_full_pipeline(sample_image)
        
        assert isinstance(result, PipelineDebugResult)
        assert result.overall_success is False
        assert result.bottleneck_stage == "unknown"
        assert "检测失败" in result.error_analysis["error"]


if __name__ == "__main__":
    pytest.main([__file__])
