"""
API接口测试
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from unittest.mock import Mock, patch, AsyncMock
import json
import tempfile
import os
from PIL import Image
import io
import base64

from app import app, watermark_remover, task_storage


class TestAPI:
    """API测试类"""
    
    @pytest.fixture
    def client(self):
        """创建测试客户端"""
        return TestClient(app)
    
    @pytest.fixture
    def mock_pipeline(self):
        """模拟水印去除管道"""
        with patch.object(watermark_remover, 'pipeline') as mock:
            # 模拟检测器
            mock_detector = Mock()
            mock_detector.detect.return_value = (
                [[255, 255, 255], [0, 0, 0]],  # 模拟掩码
                0.8,  # 置信度
                [{'bbox': [100, 100, 200, 200], 'confidence': 0.8}]  # 检测信息
            )
            mock.detector = mock_detector
            
            # 模拟修复器
            mock_inpainter = Mock()
            test_image = Image.new('RGB', (100, 100), color='red')
            mock_inpainter.inpaint.return_value = test_image
            mock.inpainter = mock_inpainter
            
            return mock
    
    @pytest.fixture
    def sample_image_url(self):
        """示例图片URL"""
        return "https://example.com/test_image.jpg"
    
    def test_health_endpoint(self, client):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert "status" in data
        assert data["status"] == "healthy"
        assert "timestamp" in data
        assert "device" in data
        assert "active_tasks" in data
        assert "memory" in data
    
    def test_performance_endpoint(self, client):
        """测试性能统计端点"""
        response = client.get("/performance")
        assert response.status_code == 200
        
        data = response.json()
        assert "memory" in data
        assert "model_cache_size" in data
        assert "performance_metrics" in data
    
    def test_cleanup_memory_endpoint(self, client):
        """测试内存清理端点"""
        response = client.post("/performance/cleanup")
        assert response.status_code == 200
        
        data = response.json()
        assert "success" in data
        assert "message" in data
        assert "timestamp" in data
    
    @patch('app.aiohttp.ClientSession')
    def test_remove_watermark_sync_success(self, mock_session, client, mock_pipeline, sample_image_url):
        """测试同步水印去除成功"""
        # 模拟图片下载
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {'content-length': '1000'}
        mock_response.read.return_value = b'fake_image_data'
        mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = mock_response
        
        # 模拟文件操作
        with patch('app.Image.open') as mock_open, \
             patch('app.Path.mkdir'), \
             patch('builtins.open', create=True), \
             patch('app.hashlib.md5') as mock_md5:
            
            mock_md5.return_value.hexdigest.return_value = "abcd1234"
            mock_image = Mock()
            mock_image.verify.return_value = None
            mock_image.convert.return_value = mock_image
            mock_open.return_value.__enter__.return_value = mock_image
            
            response = client.post("/remove-watermark-sync", json={
                "image_url": sample_image_url,
                "confidence_threshold": 0.5,
                "enhance_mask": True
            })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "result_url" in data
        assert "processing_time" in data
        assert "detection_confidence" in data
    
    def test_remove_watermark_async_success(self, client, sample_image_url):
        """测试异步水印去除成功"""
        response = client.post("/remove-watermark", json={
            "image_url": sample_image_url,
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data
        assert "message" in data
    
    def test_get_task_status(self, client):
        """测试任务状态查询"""
        # 先创建一个任务
        response = client.post("/remove-watermark", json={
            "image_url": "https://example.com/test.jpg",
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        task_id = response.json()["task_id"]
        
        # 查询任务状态
        response = client.get(f"/task/{task_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert "task_id" in data
        assert "status" in data
        assert "progress" in data
        assert "message" in data
    
    def test_get_task_status_not_found(self, client):
        """测试查询不存在的任务"""
        response = client.get("/task/nonexistent-task-id")
        assert response.status_code == 404
    
    def test_delete_task(self, client):
        """测试删除任务"""
        # 先创建一个任务
        response = client.post("/remove-watermark", json={
            "image_url": "https://example.com/test.jpg",
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        task_id = response.json()["task_id"]
        
        # 删除任务
        response = client.delete(f"/task/{task_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert "message" in data
        
        # 验证任务已被删除
        response = client.get(f"/task/{task_id}")
        assert response.status_code == 404
    
    def test_batch_watermark_removal(self, client):
        """测试批量水印去除"""
        image_urls = [
            "https://example.com/image1.jpg",
            "https://example.com/image2.jpg",
            "https://example.com/image3.jpg"
        ]
        
        response = client.post("/remove-watermark-batch", json={
            "image_urls": image_urls,
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "batch_id" in data
        assert "task_ids" in data
        assert len(data["task_ids"]) == len(image_urls)
    
    def test_get_batch_status(self, client):
        """测试批量任务状态查询"""
        # 先创建批量任务
        image_urls = ["https://example.com/image1.jpg", "https://example.com/image2.jpg"]
        
        response = client.post("/remove-watermark-batch", json={
            "image_urls": image_urls,
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        batch_id = response.json()["batch_id"]
        
        # 查询批量状态
        response = client.get(f"/batch/{batch_id}")
        assert response.status_code == 200
        
        data = response.json()
        assert "batch_id" in data
        assert "status" in data
        assert "progress" in data
        assert "total_tasks" in data
        assert "completed_tasks" in data
        assert "failed_tasks" in data
        assert "processing_tasks" in data
        assert "tasks" in data
    
    def test_get_batch_status_not_found(self, client):
        """测试查询不存在的批量任务"""
        response = client.get("/batch/nonexistent-batch-id")
        assert response.status_code == 404
    
    def test_invalid_image_url(self, client):
        """测试无效图片URL"""
        response = client.post("/remove-watermark-sync", json={
            "image_url": "invalid-url",
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        assert response.status_code == 422  # Validation error
    
    def test_missing_required_fields(self, client):
        """测试缺少必需字段"""
        response = client.post("/remove-watermark-sync", json={
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        assert response.status_code == 422  # Validation error
    
    def test_invalid_confidence_threshold(self, client):
        """测试无效置信度阈值"""
        response = client.post("/remove-watermark-sync", json={
            "image_url": "https://example.com/test.jpg",
            "confidence_threshold": 1.5,  # 超出范围
            "enhance_mask": True
        })
        
        # 应该接受但会被内部处理
        assert response.status_code in [200, 400, 422]
    
    def test_empty_batch_request(self, client):
        """测试空批量请求"""
        response = client.post("/remove-watermark-batch", json={
            "image_urls": [],
            "confidence_threshold": 0.5,
            "enhance_mask": True
        })
        
        assert response.status_code == 200
        data = response.json()
        assert len(data["task_ids"]) == 0


if __name__ == "__main__":
    pytest.main([__file__])
