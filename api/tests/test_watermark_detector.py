"""
水印检测器测试
"""

import pytest
import numpy as np
from PIL import Image
import tempfile
import os
from unittest.mock import Mock, patch

from models.watermark_detector import WatermarkDetector


class TestWatermarkDetector:
    """水印检测器测试类"""
    
    @pytest.fixture
    def sample_image(self):
        """创建测试图像"""
        # 创建一个简单的测试图像
        image = Image.new('RGB', (512, 512), color='white')
        return image
    
    @pytest.fixture
    def detector(self):
        """创建检测器实例"""
        with patch('models.watermark_detector.YOLO') as mock_yolo:
            # 模拟YOLO模型
            mock_model = Mock()
            mock_yolo.return_value = mock_model
            
            detector = WatermarkDetector(device="cpu")
            detector.model = mock_model
            return detector
    
    def test_init_auto_device(self):
        """测试自动设备选择"""
        with patch('models.watermark_detector.torch.cuda.is_available', return_value=False):
            detector = WatermarkDetector(device="auto")
            assert detector.device == "cpu"
    
    def test_init_cuda_device(self):
        """测试CUDA设备选择"""
        with patch('models.watermark_detector.torch.cuda.is_available', return_value=True):
            detector = WatermarkDetector(device="auto")
            assert detector.device == "cuda"
    
    def test_detect_no_watermarks(self, detector, sample_image):
        """测试无水印检测"""
        # 模拟无检测结果
        mock_result = Mock()
        mock_result.boxes = None
        detector.model.return_value = [mock_result]
        
        mask, confidence, detection_info = detector.detect(sample_image)
        
        assert isinstance(mask, np.ndarray)
        assert mask.shape == (sample_image.height, sample_image.width)
        assert confidence == 0.0
        assert len(detection_info) == 0
    
    def test_detect_with_watermarks(self, detector, sample_image):
        """测试有水印检测"""
        # 模拟检测结果
        mock_result = Mock()
        mock_boxes = Mock()
        mock_boxes.xyxy.cpu.return_value.numpy.return_value = np.array([[100, 100, 200, 200]])
        mock_boxes.conf.cpu.return_value.numpy.return_value = np.array([0.8])
        mock_result.boxes = mock_boxes
        detector.model.return_value = [mock_result]
        
        mask, confidence, detection_info = detector.detect(sample_image)
        
        assert isinstance(mask, np.ndarray)
        assert mask.shape == (sample_image.height, sample_image.width)
        assert confidence == 0.8
        assert len(detection_info) == 1
        assert detection_info[0]['confidence'] == 0.8
        assert detection_info[0]['bbox'] == [100, 100, 200, 200]
    
    def test_enhance_mask(self, detector):
        """测试掩码增强"""
        # 创建测试掩码
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[40:60, 40:60] = 255
        
        enhanced_mask = detector.enhance_mask(mask, dilation_size=3)
        
        assert isinstance(enhanced_mask, np.ndarray)
        assert enhanced_mask.shape == mask.shape
        # 增强后的掩码应该比原掩码有更多的白色像素
        assert np.sum(enhanced_mask > 0) >= np.sum(mask > 0)
    
    def test_get_detection_visualization(self, detector, sample_image):
        """测试检测可视化"""
        # 模拟检测结果
        mock_result = Mock()
        mock_result.plot.return_value = np.random.randint(0, 255, (512, 512, 3), dtype=np.uint8)
        detector.model.return_value = [mock_result]
        
        vis_image = detector.get_detection_visualization(sample_image)
        
        assert isinstance(vis_image, Image.Image)
        assert vis_image.size == sample_image.size
    
    def test_model_not_loaded_error(self):
        """测试模型未加载错误"""
        detector = WatermarkDetector()
        detector.model = None
        
        sample_image = Image.new('RGB', (100, 100), color='white')
        
        with pytest.raises(RuntimeError, match="模型未加载"):
            detector.detect(sample_image)
    
    @patch('models.watermark_detector.hf_hub_download')
    @patch('models.watermark_detector.YOLO')
    def test_model_loading(self, mock_yolo, mock_download):
        """测试模型加载"""
        # 模拟文件下载和模型加载
        mock_download.return_value = None
        mock_model = Mock()
        mock_yolo.return_value = mock_model
        
        detector = WatermarkDetector(device="cpu")
        
        assert detector.model is not None
        mock_yolo.assert_called_once()
    
    def test_detect_with_custom_parameters(self, detector, sample_image):
        """测试自定义参数检测"""
        # 模拟检测结果
        mock_result = Mock()
        mock_result.boxes = None
        detector.model.return_value = [mock_result]
        
        mask, confidence, detection_info = detector.detect(
            sample_image,
            confidence_threshold=0.7,
            iou_threshold=0.4,
            image_size=640,
            augment=False
        )
        
        # 验证模型被正确调用
        detector.model.assert_called_once_with(
            sample_image,
            imgsz=640,
            conf=0.7,
            iou=0.4,
            augment=False,
            verbose=False
        )
    
    def test_coordinate_bounds_checking(self, detector, sample_image):
        """测试坐标边界检查"""
        # 模拟超出边界的检测结果
        mock_result = Mock()
        mock_boxes = Mock()
        # 超出图像边界的坐标
        mock_boxes.xyxy.cpu.return_value.numpy.return_value = np.array([[-10, -10, 600, 600]])
        mock_boxes.conf.cpu.return_value.numpy.return_value = np.array([0.8])
        mock_result.boxes = mock_boxes
        detector.model.return_value = [mock_result]
        
        mask, confidence, detection_info = detector.detect(sample_image)
        
        # 检查坐标是否被正确限制在图像边界内
        bbox = detection_info[0]['bbox']
        assert bbox[0] >= 0  # x1
        assert bbox[1] >= 0  # y1
        assert bbox[2] <= sample_image.width  # x2
        assert bbox[3] <= sample_image.height  # y2


if __name__ == "__main__":
    pytest.main([__file__])
