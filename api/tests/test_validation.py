"""
验证框架测试
"""

import pytest
import numpy as np
from PIL import Image
import tempfile
import os
from unittest.mock import Mock, patch

from models.validation import LamaValidationFramework, ValidationMetrics, ValidationResult
from models.watermark_detector import WatermarkDetector
from models.lama_inpainter import LamaInpainter
from models.visualization import WatermarkVisualization


class TestLamaValidationFramework:
    """LAMA验证框架测试类"""
    
    @pytest.fixture
    def sample_image(self):
        """创建测试图像"""
        return Image.new('RGB', (512, 512), color='white')
    
    @pytest.fixture
    def sample_mask(self):
        """创建测试掩码"""
        mask = np.zeros((512, 512), dtype=np.uint8)
        mask[100:200, 100:200] = 255
        return mask
    
    @pytest.fixture
    def mock_detector(self):
        """创建模拟检测器"""
        detector = Mock(spec=WatermarkDetector)
        detector.detect.return_value = (
            np.zeros((512, 512), dtype=np.uint8),  # mask
            0.8,  # confidence
            [{'id': 0, 'bbox': [100, 100, 200, 200], 'confidence': 0.8}]  # detection_info
        )
        return detector
    
    @pytest.fixture
    def mock_inpainter(self):
        """创建模拟修复器"""
        inpainter = Mock(spec=LamaInpainter)
        inpainter.inpaint.return_value = Image.new('RGB', (512, 512), color='blue')
        return inpainter
    
    @pytest.fixture
    def mock_visualizer(self):
        """创建模拟可视化器"""
        return Mock(spec=WatermarkVisualization)
    
    @pytest.fixture
    def validation_framework(self, mock_detector, mock_inpainter, mock_visualizer):
        """创建验证框架实例"""
        return LamaValidationFramework(mock_detector, mock_inpainter, mock_visualizer)
    
    def test_init(self, mock_detector, mock_inpainter):
        """测试初始化"""
        framework = LamaValidationFramework(mock_detector, mock_inpainter)
        
        assert framework.detector == mock_detector
        assert framework.inpainter == mock_inpainter
        assert framework.visualizer is not None
    
    def test_validate_single_watermark_removal_success(self, validation_framework, sample_image):
        """测试单个水印移除验证（成功）"""
        result = validation_framework.validate_single_watermark_removal(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, ValidationResult)
        assert result.success is True
        assert result.watermark_count == 1
        assert result.detection_confidence == 0.8
        assert isinstance(result.metrics, ValidationMetrics)
    
    def test_validate_single_watermark_removal_no_detection(self, validation_framework, sample_image):
        """测试单个水印移除验证（未检测到水印）"""
        # 模拟未检测到水印
        validation_framework.detector.detect.return_value = (
            np.zeros((512, 512), dtype=np.uint8),
            0.0,
            []
        )
        
        result = validation_framework.validate_single_watermark_removal(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, ValidationResult)
        assert result.success is False
        assert result.watermark_count == 0
        assert "未检测到水印" in result.error_message
    
    def test_validate_multiple_watermark_removal_success(self, validation_framework, sample_image):
        """测试多个水印移除验证（成功）"""
        # 模拟检测到多个水印
        validation_framework.detector.detect.return_value = (
            np.zeros((512, 512), dtype=np.uint8),
            0.75,
            [
                {'id': 0, 'bbox': [100, 100, 200, 200], 'confidence': 0.8},
                {'id': 1, 'bbox': [300, 300, 400, 400], 'confidence': 0.7}
            ]
        )
        
        result = validation_framework.validate_multiple_watermark_removal(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, ValidationResult)
        assert result.success is True
        assert result.watermark_count == 2
        assert result.detection_confidence == 0.75
    
    def test_validate_multiple_watermark_removal_insufficient(self, validation_framework, sample_image):
        """测试多个水印移除验证（水印数量不足）"""
        # 模拟只检测到一个水印
        validation_framework.detector.detect.return_value = (
            np.zeros((512, 512), dtype=np.uint8),
            0.8,
            [{'id': 0, 'bbox': [100, 100, 200, 200], 'confidence': 0.8}]
        )
        
        result = validation_framework.validate_multiple_watermark_removal(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, ValidationResult)
        assert result.success is False
        assert result.watermark_count == 1
        assert "需要至少2个" in result.error_message
    
    def test_calculate_metrics(self, validation_framework, sample_image, sample_mask):
        """测试指标计算"""
        restored_image = Image.new('RGB', (512, 512), color='blue')
        
        metrics = validation_framework._calculate_metrics(
            original=sample_image,
            restored=restored_image,
            reference=None,
            mask=sample_mask,
            processing_time=1.5
        )
        
        assert isinstance(metrics, ValidationMetrics)
        assert metrics.processing_time == 1.5
        assert 0 <= metrics.ssim_score <= 1
        assert metrics.psnr_score >= 0
        assert metrics.mse_score >= 0
        assert metrics.mae_score >= 0
        assert 0 <= metrics.watermark_removal_completeness <= 1
        assert 0 <= metrics.visual_quality_score <= 1
    
    def test_calculate_metrics_with_reference(self, validation_framework, sample_image, sample_mask):
        """测试使用参考图像的指标计算"""
        restored_image = Image.new('RGB', (512, 512), color='blue')
        reference_image = Image.new('RGB', (512, 512), color='green')
        
        metrics = validation_framework._calculate_metrics(
            original=sample_image,
            restored=restored_image,
            reference=reference_image,
            mask=sample_mask,
            processing_time=2.0
        )
        
        assert isinstance(metrics, ValidationMetrics)
        assert metrics.processing_time == 2.0
    
    def test_calculate_watermark_removal_completeness(self, validation_framework):
        """测试水印移除完整性计算"""
        original = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        restored = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[25:75, 25:75] = 255
        
        completeness = validation_framework._calculate_watermark_removal_completeness(
            original, restored, mask
        )
        
        assert 0 <= completeness <= 1
    
    def test_calculate_watermark_removal_completeness_empty_mask(self, validation_framework):
        """测试空掩码的水印移除完整性计算"""
        original = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        restored = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        mask = np.zeros((100, 100), dtype=np.uint8)
        
        completeness = validation_framework._calculate_watermark_removal_completeness(
            original, restored, mask
        )
        
        assert completeness == 1.0
    
    def test_calculate_visual_quality_score(self, validation_framework):
        """测试视觉质量分数计算"""
        original = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        restored = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        mask = np.zeros((100, 100), dtype=np.uint8)
        mask[25:75, 25:75] = 255
        
        quality_score = validation_framework._calculate_visual_quality_score(
            original, restored, mask
        )
        
        assert 0 <= quality_score <= 1
    
    def test_save_validation_results(self, validation_framework, sample_image, sample_mask):
        """测试保存验证结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            detection_info = [{'id': 0, 'bbox': [100, 100, 200, 200], 'confidence': 0.8}]
            metrics = ValidationMetrics(0.8, 25.0, 100.0, 50.0, 1.5, 0.9, 0.85)
            
            validation_framework._save_validation_results(
                "test_image", sample_image, sample_image, sample_mask,
                detection_info, metrics, temp_dir
            )
            
            # 检查文件是否创建
            result_dir = os.path.join(temp_dir, "test_image")
            assert os.path.exists(result_dir)
            assert os.path.exists(os.path.join(result_dir, "original.jpg"))
            assert os.path.exists(os.path.join(result_dir, "restored.jpg"))
            assert os.path.exists(os.path.join(result_dir, "mask.png"))
            assert os.path.exists(os.path.join(result_dir, "metrics.json"))
    
    def test_run_batch_validation(self, validation_framework):
        """测试批量验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试图像文件
            test_image = Image.new('RGB', (512, 512), color='white')
            image_path = os.path.join(temp_dir, "test_image.jpg")
            test_image.save(image_path)
            
            result = validation_framework.run_batch_validation(
                [image_path], confidence_threshold=0.5
            )
            
            assert isinstance(result, dict)
            assert 'total_images' in result
            assert 'successful_validations' in result
            assert 'individual_results' in result
            assert result['total_images'] == 1
    
    def test_calculate_average_metrics(self, validation_framework):
        """测试平均指标计算"""
        results = [
            ValidationResult(
                "test1", 1, 0.8,
                ValidationMetrics(0.8, 25.0, 100.0, 50.0, 1.5, 0.9, 0.85),
                True
            ),
            ValidationResult(
                "test2", 1, 0.7,
                ValidationMetrics(0.7, 20.0, 120.0, 60.0, 2.0, 0.8, 0.75),
                True
            )
        ]
        
        avg_metrics = validation_framework._calculate_average_metrics(results)
        
        assert isinstance(avg_metrics, ValidationMetrics)
        assert avg_metrics.ssim_score == 0.75
        assert avg_metrics.psnr_score == 22.5
        assert avg_metrics.processing_time == 1.75
    
    def test_calculate_average_metrics_empty(self, validation_framework):
        """测试空结果列表的平均指标计算"""
        avg_metrics = validation_framework._calculate_average_metrics([])
        
        assert isinstance(avg_metrics, ValidationMetrics)
        assert avg_metrics.ssim_score == 0
        assert avg_metrics.psnr_score == 0
    
    def test_validation_with_file_path(self, validation_framework):
        """测试使用文件路径进行验证"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建测试图像文件
            test_image = Image.new('RGB', (512, 512), color='white')
            image_path = os.path.join(temp_dir, "test_image.jpg")
            test_image.save(image_path)
            
            result = validation_framework.validate_single_watermark_removal(
                image_path, confidence_threshold=0.5
            )
            
            assert isinstance(result, ValidationResult)
    
    def test_validation_error_handling(self, validation_framework, sample_image):
        """测试验证过程中的错误处理"""
        # 模拟检测器抛出异常
        validation_framework.detector.detect.side_effect = Exception("检测失败")
        
        result = validation_framework.validate_single_watermark_removal(
            sample_image, confidence_threshold=0.5
        )
        
        assert isinstance(result, ValidationResult)
        assert result.success is False
        assert "检测失败" in result.error_message


if __name__ == "__main__":
    pytest.main([__file__])
