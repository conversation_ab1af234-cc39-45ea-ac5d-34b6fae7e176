"""
pytest配置文件
"""

import pytest
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import patch

# 设置测试环境变量
import os
os.environ["LOG_LEVEL"] = "DEBUG"
os.environ["LOG_DIR"] = "test_logs"


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield temp_path
    shutil.rmtree(temp_path, ignore_errors=True)


@pytest.fixture
def mock_config():
    """模拟配置"""
    with patch('app.config') as mock:
        mock.UPLOAD_DIR = "test_uploads"
        mock.OUTPUT_DIR = "test_outputs"
        mock.STATIC_DIR = "test_static"
        mock.MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
        mock.ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp'}
        mock.CONFIDENCE_THRESHOLD = 0.3
        mock.WATERMARK_DETECTION_MODEL = "test_detector"
        mock.LAMA_MODEL_PATH = "test_lama"
        yield mock


@pytest.fixture(autouse=True)
def setup_test_dirs():
    """自动设置测试目录"""
    test_dirs = ["test_uploads", "test_outputs", "test_static", "test_logs"]
    
    for dir_name in test_dirs:
        Path(dir_name).mkdir(exist_ok=True)
    
    yield
    
    # 清理测试目录
    for dir_name in test_dirs:
        path = Path(dir_name)
        if path.exists():
            shutil.rmtree(path, ignore_errors=True)


@pytest.fixture
def sample_image_data():
    """示例图片数据"""
    from PIL import Image
    import io
    
    # 创建一个简单的测试图像
    image = Image.new('RGB', (100, 100), color='blue')
    
    # 转换为字节数据
    img_byte_arr = io.BytesIO()
    image.save(img_byte_arr, format='JPEG')
    img_byte_arr = img_byte_arr.getvalue()
    
    return img_byte_arr


@pytest.fixture
def mock_model_loading():
    """模拟模型加载"""
    with patch('models.watermark_detector.YOLO'), \
         patch('models.lama_inpainter.LamaInpainter._load_model'), \
         patch('models.watermark_removal_pipeline.WatermarkRemovalPipeline.__init__', return_value=None):
        yield


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )


# 测试收集钩子
def pytest_collection_modifyitems(config, items):
    """修改测试项"""
    for item in items:
        # 为所有测试添加单元测试标记（除非已有其他标记）
        if not any(mark.name in ['integration', 'slow'] for mark in item.iter_markers()):
            item.add_marker(pytest.mark.unit)


# 测试报告钩子
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """生成测试报告"""
    outcome = yield
    rep = outcome.get_result()
    
    # 添加测试持续时间到报告中
    if rep.when == "call":
        rep.duration = call.duration
    
    return rep
