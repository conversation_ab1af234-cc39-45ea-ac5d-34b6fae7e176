"""
增强效果测试
测试更明显的智能掩码增强效果
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image, ImageDraw, ImageFont
import numpy as np
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def enhanced_effect_test(image_path: str):
    """增强效果测试"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 测试配置
    test_configs = [
        {
            "name": "传统掩码增强",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": False,
                "return_intermediate": True
            },
            "filename": "traditional"
        },
        {
            "name": "智能掩码增强（中等）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.12,  # 新的默认值
                "return_intermediate": True
            },
            "filename": "smart_medium"
        },
        {
            "name": "智能掩码增强（强化）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.18,  # 更强的效果
                "return_intermediate": True
            },
            "filename": "smart_strong"
        }
    ]
    
    results = {}
    mask_info = {}
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n=== 测试 {i+1}/{len(test_configs)}: {config['name']} ===")
        
        try:
            result = pipeline.remove_watermark(image, **config['params'])
            
            if isinstance(result, dict):
                result_image = result['result_image']
                results[config['filename']] = result_image
                
                logger.info(f"✅ 检测到 {len(result['detection_info'])} 个水印")
                logger.info(f"✅ 处理时间: {result['processing_time']:.2f}秒")
                
                # 计算掩码覆盖率
                original_binary = (result['original_mask'] > 127).astype(int)
                enhanced_binary = (result['enhanced_mask'] > 127).astype(int)
                
                original_ratio = original_binary.sum() / (original_binary.shape[0] * original_binary.shape[1])
                enhanced_ratio = enhanced_binary.sum() / (enhanced_binary.shape[0] * enhanced_binary.shape[1])
                expansion_factor = enhanced_ratio / original_ratio if original_ratio > 0 else 1
                
                logger.info(f"📊 掩码扩展: {original_ratio:.1%} → {enhanced_ratio:.1%} ({expansion_factor:.1f}x)")
                
                # 保存掩码信息
                mask_info[config['filename']] = {
                    'original_mask': result['original_mask'],
                    'enhanced_mask': result['enhanced_mask'],
                    'original_ratio': original_ratio,
                    'enhanced_ratio': enhanced_ratio,
                    'expansion_factor': expansion_factor
                }
                
                # 保存结果图像
                filename = f"enhanced_{config['filename']}_result.jpg"
                result_image.save(filename, 'JPEG', quality=95)
                logger.info(f"✅ 结果已保存: {filename}")
                
            else:
                results[config['filename']] = result
                filename = f"enhanced_{config['filename']}_result.jpg"
                result.save(filename, 'JPEG', quality=95)
                logger.info(f"✅ 结果已保存: {filename}")
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 失败: {e}")
            results[config['filename']] = image.copy()
    
    # 创建掩码对比图
    logger.info("\n=== 创建掩码对比图 ===")
    try:
        create_mask_comparison(mask_info)
        logger.info("✅ 掩码对比图已保存")
    except Exception as e:
        logger.error(f"❌ 创建掩码对比图失败: {e}")
    
    # 创建结果对比图
    logger.info("\n=== 创建结果对比图 ===")
    try:
        create_result_comparison(image, results)
        logger.info("✅ 结果对比图已保存")
    except Exception as e:
        logger.error(f"❌ 创建结果对比图失败: {e}")
    
    # 输出总结
    logger.info("\n" + "="*60)
    logger.info("增强效果测试完成！")
    logger.info("="*60)
    
    logger.info("掩码扩展对比:")
    for filename, info in mask_info.items():
        method_name = "传统方法" if filename == "traditional" else f"智能方法({filename})"
        logger.info(f"  {method_name}: {info['expansion_factor']:.1f}x 扩展 "
                   f"({info['original_ratio']:.1%} → {info['enhanced_ratio']:.1%})")
    
    logger.info("\n生成的文件:")
    logger.info("- enhanced_*_result.jpg: 各种方法的修复结果")
    logger.info("- enhanced_mask_comparison.jpg: 掩码对比图")
    logger.info("- enhanced_result_comparison.jpg: 结果对比图")
    
    logger.info("\n💡 评估建议:")
    logger.info("1. 对比不同方法的掩码扩展程度")
    logger.info("2. 观察修复效果的差异")
    logger.info("3. 选择效果最自然的方法")
    logger.info("4. 注意边界的平滑度和纹理连续性")


def create_mask_comparison(mask_info):
    """创建掩码对比图"""
    
    if not mask_info:
        return
    
    # 准备掩码图像
    mask_images = []
    titles = []
    
    for filename, info in mask_info.items():
        # 原始掩码
        original_mask_img = Image.fromarray(info['original_mask']).convert('RGB')
        mask_images.append(original_mask_img)
        method_name = "传统" if filename == "traditional" else filename.replace("smart_", "智能")
        titles.append(f"{method_name}-原始")
        
        # 增强掩码
        enhanced_mask_img = Image.fromarray(info['enhanced_mask']).convert('RGB')
        mask_images.append(enhanced_mask_img)
        titles.append(f"{method_name}-增强({info['expansion_factor']:.1f}x)")
    
    # 创建网格布局
    cols = 2
    rows = len(mask_images) // cols
    if len(mask_images) % cols != 0:
        rows += 1
    
    target_size = (200, 300)
    gap = 15
    title_height = 25
    
    grid_width = cols * target_size[0] + (cols + 1) * gap
    grid_height = rows * target_size[1] + (rows + 1) * gap + rows * title_height
    
    grid = Image.new('RGB', (grid_width, grid_height), (255, 255, 255))
    draw = ImageDraw.Draw(grid)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 12)
    except:
        font = ImageFont.load_default()
    
    # 放置图像和标题
    for i, (img, title) in enumerate(zip(mask_images, titles)):
        row = i // cols
        col = i % cols
        
        x = gap + col * (target_size[0] + gap)
        y = gap + title_height + row * (target_size[1] + gap + title_height)
        
        # 调整图像大小并粘贴
        resized_img = img.resize(target_size, Image.LANCZOS)
        grid.paste(resized_img, (x, y))
        
        # 添加标题
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x = x + (target_size[0] - text_width) // 2
        title_y = y - 20
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    grid.save("enhanced_mask_comparison.jpg", 'JPEG', quality=95)


def create_result_comparison(original, results):
    """创建结果对比图"""
    
    # 准备图像和标题
    images = [original]
    titles = ["原图"]
    
    method_names = {
        "traditional": "传统方法",
        "smart_medium": "智能方法(中等)",
        "smart_strong": "智能方法(强化)"
    }
    
    for filename in ["traditional", "smart_medium", "smart_strong"]:
        if filename in results:
            images.append(results[filename])
            titles.append(method_names[filename])
    
    # 创建并排对比
    target_size = original.size
    gap = 20
    title_height = 30
    
    width = len(images) * target_size[0] + (len(images) + 1) * gap
    height = target_size[1] + title_height + gap * 2
    
    comparison = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(comparison)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # 放置图像和标题
    for i, (img, title) in enumerate(zip(images, titles)):
        x = gap + i * (target_size[0] + gap)
        y = title_height + gap
        
        # 确保图像大小一致
        if img.size != target_size:
            img = img.resize(target_size, Image.LANCZOS)
        
        # 粘贴图像
        comparison.paste(img, (x, y))
        
        # 添加标题
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x = x + (target_size[0] - text_width) // 2
        title_y = 5
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    comparison.save("enhanced_result_comparison.jpg", 'JPEG', quality=95)


def main():
    parser = argparse.ArgumentParser(description="增强效果测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    enhanced_effect_test(args.image)


if __name__ == "__main__":
    main()
