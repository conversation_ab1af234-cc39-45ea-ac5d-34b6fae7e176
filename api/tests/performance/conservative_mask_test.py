"""
保守掩码测试
使用更保守的参数测试智能掩码增强
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def conservative_test(image_path: str):
    """保守参数测试"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 测试配置
    test_configs = [
        {
            "name": "传统掩码增强",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": False,
                "return_intermediate": True
            },
            "filename": "traditional"
        },
        {
            "name": "智能掩码增强（极保守）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.03,  # 极小扩展
                "return_intermediate": True
            },
            "filename": "smart_conservative"
        },
        {
            "name": "智能掩码增强（保守）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.05,  # 小扩展
                "return_intermediate": True
            },
            "filename": "smart_moderate"
        },
        {
            "name": "智能掩码增强（默认）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.08,  # 新的默认值
                "return_intermediate": True
            },
            "filename": "smart_default"
        }
    ]
    
    results = []
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n=== 测试 {i+1}/{len(test_configs)}: {config['name']} ===")
        
        try:
            result = pipeline.remove_watermark(image, **config['params'])
            
            if isinstance(result, dict):
                result_image = result['result_image']
                
                logger.info(f"✅ 检测到 {len(result['detection_info'])} 个水印")
                logger.info(f"✅ 处理时间: {result['processing_time']:.2f}秒")
                
                # 保存结果
                filename = f"{config['filename']}_result.jpg"
                result_image.save(filename, 'JPEG', quality=95)
                logger.info(f"✅ 结果已保存: {filename}")
                
                # 保存掩码信息
                if 'original_mask' in result and 'enhanced_mask' in result:
                    original_mask_img = Image.fromarray(result['original_mask'])
                    enhanced_mask_img = Image.fromarray(result['enhanced_mask'])
                    
                    original_mask_img.save(f"{config['filename']}_original_mask.png")
                    enhanced_mask_img.save(f"{config['filename']}_enhanced_mask.png")
                    
                    # 计算掩码覆盖率
                    original_ratio = result['original_mask'].sum() / (result['original_mask'].shape[0] * result['original_mask'].shape[1])
                    enhanced_ratio = result['enhanced_mask'].sum() / (result['enhanced_mask'].shape[0] * result['enhanced_mask'].shape[1])
                    
                    logger.info(f"📊 原始掩码覆盖率: {original_ratio:.1%}")
                    logger.info(f"📊 增强掩码覆盖率: {enhanced_ratio:.1%}")
                    logger.info(f"📊 扩展倍数: {enhanced_ratio/original_ratio:.1f}x")
                
                results.append({
                    "name": config['name'],
                    "success": True,
                    "watermark_count": len(result['detection_info']),
                    "processing_time": result['processing_time']
                })
                
            else:
                filename = f"{config['filename']}_result.jpg"
                result.save(filename, 'JPEG', quality=95)
                logger.info(f"✅ 结果已保存: {filename}")
                
                results.append({
                    "name": config['name'],
                    "success": True,
                    "watermark_count": "未知",
                    "processing_time": "未知"
                })
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 失败: {e}")
            results.append({
                "name": config['name'],
                "success": False,
                "error": str(e)
            })
    
    # 总结结果
    logger.info("\n" + "="*60)
    logger.info("测试结果总结:")
    logger.info("="*60)
    
    for result in results:
        if result['success']:
            logger.info(f"✅ {result['name']}: "
                       f"水印数量={result['watermark_count']}, "
                       f"处理时间={result['processing_time']}")
        else:
            logger.info(f"❌ {result['name']}: 失败 - {result.get('error', '未知错误')}")
    
    logger.info("\n💡 使用建议:")
    logger.info("1. 对比各种方法的修复效果")
    logger.info("2. 选择修复效果最自然、没有过度修复的方法")
    logger.info("3. 观察掩码覆盖率，避免过度扩展")
    logger.info("4. 如果智能方法效果好，可以在实际使用中采用")
    
    return results


def main():
    parser = argparse.ArgumentParser(description="保守掩码测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    logger.info("开始保守掩码增强测试...")
    
    # 执行测试
    results = conservative_test(args.image)
    
    logger.info("\n🎉 测试完成！")
    logger.info("请查看生成的图像文件，选择效果最好的方法。")


if __name__ == "__main__":
    main()
