"""
可视化模块测试
"""

import pytest
import numpy as np
from PIL import Image
import tempfile
import os
from unittest.mock import Mock, patch

from models.visualization import WatermarkVisualization


class TestWatermarkVisualization:
    """水印可视化测试类"""
    
    @pytest.fixture
    def sample_image(self):
        """创建测试图像"""
        return Image.new('RGB', (512, 512), color='white')
    
    @pytest.fixture
    def sample_detection_info(self):
        """创建测试检测信息"""
        return [
            {
                'id': 0,
                'bbox': [100, 100, 200, 200],
                'confidence': 0.85,
                'area': 10000,
                'width': 100,
                'height': 100,
                'aspect_ratio': 1.0,
                'center': [150, 150]
            },
            {
                'id': 1,
                'bbox': [300, 300, 400, 400],
                'confidence': 0.75,
                'area': 10000,
                'width': 100,
                'height': 100,
                'aspect_ratio': 1.0,
                'center': [350, 350]
            }
        ]
    
    @pytest.fixture
    def sample_mask(self):
        """创建测试掩码"""
        mask = np.zeros((512, 512), dtype=np.uint8)
        mask[100:200, 100:200] = 255
        mask[300:400, 300:400] = 255
        return mask
    
    @pytest.fixture
    def visualizer(self):
        """创建可视化器实例"""
        return WatermarkVisualization()
    
    def test_init(self, visualizer):
        """测试初始化"""
        assert visualizer.font_size == 20
        assert len(visualizer.colors) == 6
        assert visualizer.font is not None
    
    def test_draw_detection_boxes(self, visualizer, sample_image, sample_detection_info):
        """测试绘制检测框"""
        result = visualizer.draw_detection_boxes(
            sample_image, sample_detection_info, show_confidence=True
        )
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
        assert result.mode == 'RGB'
    
    def test_draw_detection_boxes_no_confidence(self, visualizer, sample_image, sample_detection_info):
        """测试绘制检测框（不显示置信度）"""
        result = visualizer.draw_detection_boxes(
            sample_image, sample_detection_info, show_confidence=False
        )
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
    
    def test_create_mask_overlay(self, visualizer, sample_image, sample_mask):
        """测试创建掩码叠加"""
        result = visualizer.create_mask_overlay(
            sample_image, sample_mask, alpha=0.5
        )
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
        assert result.mode == 'RGB'
    
    def test_create_mask_overlay_different_size(self, visualizer, sample_image):
        """测试不同大小掩码的叠加"""
        # 创建不同大小的掩码
        small_mask = np.zeros((256, 256), dtype=np.uint8)
        small_mask[50:150, 50:150] = 255
        
        result = visualizer.create_mask_overlay(
            sample_image, small_mask, alpha=0.3
        )
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
    
    def test_create_side_by_side_comparison(self, visualizer, sample_image):
        """测试创建并排对比"""
        # 创建第二个图像
        processed_image = sample_image.copy()
        
        result = visualizer.create_side_by_side_comparison(
            sample_image, processed_image, titles=("原图", "处理后")
        )
        
        assert isinstance(result, Image.Image)
        assert result.width == sample_image.width * 2 + 20
        assert result.height == sample_image.height + 60
    
    def test_create_side_by_side_comparison_different_sizes(self, visualizer, sample_image):
        """测试不同大小图像的并排对比"""
        # 创建不同大小的图像
        small_image = Image.new('RGB', (256, 256), color='blue')
        
        result = visualizer.create_side_by_side_comparison(
            sample_image, small_image, titles=("大图", "小图")
        )
        
        assert isinstance(result, Image.Image)
        assert result.width == sample_image.width * 2 + 20
    
    def test_create_detection_summary(self, visualizer, sample_image, sample_detection_info, sample_mask):
        """测试创建检测摘要"""
        result = visualizer.create_detection_summary(
            sample_image, sample_detection_info, sample_mask, processing_time=1.5
        )
        
        assert isinstance(result, Image.Image)
        assert result.width > sample_image.width
        assert result.height > sample_image.height
    
    def test_create_stats_text(self, visualizer, sample_detection_info):
        """测试创建统计文本"""
        stats_text = visualizer._create_stats_text(sample_detection_info, 2.5)
        
        assert "检测到水印数量: 2" in stats_text
        assert "平均置信度:" in stats_text
        assert "处理时间: 2.500秒" in stats_text
    
    def test_create_stats_text_no_detections(self, visualizer):
        """测试无检测结果的统计文本"""
        stats_text = visualizer._create_stats_text([], 1.0)
        
        assert "检测到水印数量: 0" in stats_text
        assert "处理时间: 1.000秒" in stats_text
    
    def test_add_stats_to_image(self, visualizer, sample_image):
        """测试添加统计信息到图像"""
        stats_text = "测试统计信息\n第二行信息"
        result = visualizer._add_stats_to_image(sample_image, stats_text)
        
        assert isinstance(result, Image.Image)
        assert result.width == sample_image.width
        assert result.height > sample_image.height
    
    def test_load_font_fallback(self):
        """测试字体加载回退"""
        # 测试在没有字体文件的情况下的回退机制
        with patch('pathlib.Path.exists', return_value=False):
            visualizer = WatermarkVisualization()
            assert visualizer.font is not None
    
    def test_empty_detection_info(self, visualizer, sample_image):
        """测试空检测信息"""
        result = visualizer.draw_detection_boxes(sample_image, [])
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
    
    def test_custom_colors(self, visualizer, sample_image):
        """测试多个检测框使用不同颜色"""
        # 创建超过颜色数量的检测信息
        many_detections = []
        for i in range(10):
            many_detections.append({
                'id': i,
                'bbox': [i*40, i*40, i*40+50, i*40+50],
                'confidence': 0.8,
                'area': 2500,
                'width': 50,
                'height': 50,
                'aspect_ratio': 1.0,
                'center': [i*40+25, i*40+25]
            })
        
        result = visualizer.draw_detection_boxes(sample_image, many_detections)
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
    
    def test_mask_overlay_custom_color(self, visualizer, sample_image, sample_mask):
        """测试自定义掩码颜色"""
        result = visualizer.create_mask_overlay(
            sample_image, sample_mask, alpha=0.7, mask_color=(0, 255, 0)
        )
        
        assert isinstance(result, Image.Image)
        assert result.size == sample_image.size
    
    def test_save_visualization_results(self, visualizer, sample_image, sample_detection_info, sample_mask):
        """测试保存可视化结果"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 创建各种可视化
            detection_boxes = visualizer.draw_detection_boxes(
                sample_image, sample_detection_info
            )
            mask_overlay = visualizer.create_mask_overlay(
                sample_image, sample_mask
            )
            
            # 保存文件
            detection_boxes.save(os.path.join(temp_dir, "detection_boxes.jpg"))
            mask_overlay.save(os.path.join(temp_dir, "mask_overlay.jpg"))
            
            # 验证文件存在
            assert os.path.exists(os.path.join(temp_dir, "detection_boxes.jpg"))
            assert os.path.exists(os.path.join(temp_dir, "mask_overlay.jpg"))


if __name__ == "__main__":
    pytest.main([__file__])
