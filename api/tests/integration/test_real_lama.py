"""
测试真正的LAMA模型
验证lama-cleaner的效果
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def test_real_lama(image_path: str):
    """测试真正的LAMA模型"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    logger.info("正在初始化水印去除管道...")
    pipeline = WatermarkRemovalPipeline()
    
    # 检查使用的是哪种修复器
    inpainter_type = type(pipeline.inpainter).__name__
    logger.info(f"当前使用的修复器: {inpainter_type}")
    
    if hasattr(pipeline.inpainter, 'get_model_info'):
        model_info = pipeline.inpainter.get_model_info()
        logger.info(f"模型信息: {model_info}")
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 测试真正的LAMA模型
    logger.info("\n=== 测试真正的LAMA模型 ===")
    try:
        result = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=True,
            context_expansion_ratio=0.12,
            return_intermediate=True
        )
        
        if isinstance(result, dict):
            result_image = result['result_image']
            
            logger.info(f"✅ 检测到 {len(result['detection_info'])} 个水印")
            logger.info(f"✅ 总处理时间: {result['processing_time']:.2f}秒")
            logger.info(f"✅ 检测时间: {result['detection_time']:.2f}秒")
            logger.info(f"✅ 修复时间: {result['inpainting_time']:.2f}秒")
            
            # 保存结果
            result_image.save("real_lama_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 结果已保存: real_lama_result.jpg")
            
            # 保存掩码信息
            if 'original_mask' in result and 'enhanced_mask' in result:
                Image.fromarray(result['original_mask']).save("real_lama_original_mask.png")
                Image.fromarray(result['enhanced_mask']).save("real_lama_enhanced_mask.png")
                
                # 计算掩码覆盖率
                original_binary = (result['original_mask'] > 127).astype(int)
                enhanced_binary = (result['enhanced_mask'] > 127).astype(int)
                
                original_ratio = original_binary.sum() / (original_binary.shape[0] * original_binary.shape[1])
                enhanced_ratio = enhanced_binary.sum() / (enhanced_binary.shape[0] * enhanced_binary.shape[1])
                expansion_factor = enhanced_ratio / original_ratio if original_ratio > 0 else 1
                
                logger.info(f"📊 掩码扩展: {original_ratio:.1%} → {enhanced_ratio:.1%} ({expansion_factor:.1f}x)")
                logger.info("✅ 掩码已保存")
                
            # 检测信息
            for i, detection in enumerate(result['detection_info']):
                logger.info(f"水印 {i+1}: 置信度={detection['confidence']:.3f}, "
                           f"位置=({detection['bbox'][0]:.0f}, {detection['bbox'][1]:.0f}, "
                           f"{detection['bbox'][2]:.0f}, {detection['bbox'][3]:.0f})")
        else:
            result.save("real_lama_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 结果已保存: real_lama_result.jpg")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("\n=== 测试完成 ===")
    logger.info("生成的文件:")
    logger.info("- real_lama_result.jpg: 真正LAMA模型的修复结果")
    logger.info("- real_lama_original_mask.png: 原始检测掩码")
    logger.info("- real_lama_enhanced_mask.png: 智能增强掩码")
    
    logger.info("\n💡 LAMA模型优势:")
    logger.info("1. 深度学习理解图像语义")
    logger.info("2. 全局上下文感知修复")
    logger.info("3. 自然的纹理生成")
    logger.info("4. 边界无缝融合")
    logger.info("5. 适应各种图像类型")
    
    # 对比建议
    logger.info("\n🔍 效果评估:")
    logger.info("请对比以下方面:")
    logger.info("- 修复区域是否自然")
    logger.info("- 边界是否平滑")
    logger.info("- 纹理是否连续")
    logger.info("- 颜色是否一致")
    logger.info("- 是否有明显的涂抹痕迹")


def main():
    parser = argparse.ArgumentParser(description="测试真正的LAMA模型")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    logger.info("🚀 开始真正的LAMA模型测试...")
    
    # 执行测试
    test_real_lama(args.image)
    
    logger.info("\n🎉 测试完成！")
    logger.info("如果使用了真正的LAMA模型，修复效果应该显著优于OpenCV算法。")


if __name__ == "__main__":
    main()
