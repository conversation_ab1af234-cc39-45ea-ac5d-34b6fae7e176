"""
测试app.py与优化方案的完整对接
验证所有新功能是否正确集成
"""

import requests
import json
import time
import logging
from pathlib import Path

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def test_app_integration():
    """测试app.py集成"""
    
    base_url = "http://localhost:8001"
    
    # 测试图片URL（使用一个公开的测试图片）
    test_image_url = "https://via.placeholder.com/400x300/0000FF/FFFFFF?text=Test+Watermark"
    
    logger.info("🚀 开始测试app.py与优化方案的完整对接...")
    
    # 1. 测试健康检查
    logger.info("\n=== 1. 测试健康检查 ===")
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code == 200:
            health_data = response.json()
            logger.info("✅ 健康检查通过")
            logger.info(f"设备: {health_data.get('device', 'unknown')}")
            logger.info(f"活跃任务: {health_data.get('active_tasks', 0)}")
            logger.info(f"模型缓存大小: {health_data.get('model_cache_size', 0)}")
        else:
            logger.error(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ 健康检查异常: {e}")
        return False
    
    # 2. 测试同步接口（完整参数）
    logger.info("\n=== 2. 测试同步接口（智能增强） ===")
    try:
        sync_request = {
            "image_url": test_image_url,
            "confidence_threshold": 0.1,
            "enhance_mask": True,
            "use_smart_enhancement": True,
            "context_expansion_ratio": 0.12,
            "return_intermediate": True
        }
        
        logger.info("发送同步请求...")
        start_time = time.time()
        
        response = requests.post(
            f"{base_url}/remove-watermark-sync",
            json=sync_request,
            timeout=60
        )
        
        elapsed_time = time.time() - start_time
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✅ 同步接口测试成功")
            logger.info(f"处理时间: {result.get('processing_time', 0):.2f}秒")
            logger.info(f"检测时间: {result.get('detection_time', 0):.2f}秒")
            logger.info(f"修复时间: {result.get('inpainting_time', 0):.2f}秒")
            logger.info(f"水印数量: {result.get('watermark_count', 0)}")
            logger.info(f"检测置信度: {result.get('detection_confidence', 0):.3f}")
            logger.info(f"掩码扩展倍数: {result.get('mask_expansion_factor', 'N/A')}")
            logger.info(f"模型类型: {result.get('model_type', 'unknown')}")
            logger.info(f"结果URL: {result.get('result_url', 'N/A')}")
            logger.info(f"实际耗时: {elapsed_time:.2f}秒")
        else:
            logger.error(f"❌ 同步接口失败: {response.status_code}")
            logger.error(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 同步接口异常: {e}")
        return False
    
    # 3. 测试异步接口
    logger.info("\n=== 3. 测试异步接口 ===")
    try:
        async_request = {
            "image_url": test_image_url,
            "confidence_threshold": 0.1,
            "enhance_mask": True,
            "use_smart_enhancement": True,
            "context_expansion_ratio": 0.15,  # 更强的增强
            "return_intermediate": False
        }
        
        # 提交异步任务
        response = requests.post(
            f"{base_url}/remove-watermark",
            json=async_request
        )
        
        if response.status_code == 200:
            task_info = response.json()
            task_id = task_info.get('task_id')
            logger.info(f"✅ 异步任务已提交: {task_id}")
            
            # 轮询任务状态
            max_wait = 60  # 最多等待60秒
            wait_time = 0
            
            while wait_time < max_wait:
                time.sleep(2)
                wait_time += 2
                
                status_response = requests.get(f"{base_url}/task/{task_id}")
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    status = status_data.get('status')
                    progress = status_data.get('progress', 0)
                    message = status_data.get('message', '')
                    
                    logger.info(f"任务状态: {status} ({progress}%) - {message}")
                    
                    if status == "completed":
                        logger.info("✅ 异步任务完成")
                        logger.info(f"结果URL: {status_data.get('result_url', 'N/A')}")
                        break
                    elif status == "failed":
                        logger.error(f"❌ 异步任务失败: {message}")
                        return False
                else:
                    logger.error(f"❌ 查询任务状态失败: {status_response.status_code}")
                    return False
            
            if wait_time >= max_wait:
                logger.warning("⚠️  异步任务超时")
                
        else:
            logger.error(f"❌ 异步接口失败: {response.status_code}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 异步接口异常: {e}")
        return False
    
    # 4. 测试性能监控
    logger.info("\n=== 4. 测试性能监控 ===")
    try:
        response = requests.get(f"{base_url}/performance")
        if response.status_code == 200:
            perf_data = response.json()
            logger.info("✅ 性能监控正常")
            logger.info(f"性能数据: {json.dumps(perf_data, indent=2, ensure_ascii=False)}")
        else:
            logger.warning(f"⚠️  性能监控接口异常: {response.status_code}")
    except Exception as e:
        logger.warning(f"⚠️  性能监控异常: {e}")
    
    # 5. 测试参数验证
    logger.info("\n=== 5. 测试参数验证 ===")
    try:
        # 测试无效参数
        invalid_request = {
            "image_url": "invalid_url",
            "confidence_threshold": 1.5,  # 超出范围
            "context_expansion_ratio": 0.5  # 超出推荐范围
        }
        
        response = requests.post(
            f"{base_url}/remove-watermark-sync",
            json=invalid_request,
            timeout=10
        )
        
        if response.status_code != 200:
            logger.info("✅ 参数验证正常（正确拒绝无效参数）")
        else:
            logger.warning("⚠️  参数验证可能存在问题")
            
    except Exception as e:
        logger.info(f"✅ 参数验证正常（正确抛出异常）: {e}")
    
    logger.info("\n" + "="*60)
    logger.info("app.py集成测试完成！")
    logger.info("="*60)
    
    logger.info("\n✅ 验证的功能:")
    logger.info("1. 真正的LAMA模型集成")
    logger.info("2. 智能掩码增强")
    logger.info("3. 上下文扩展配置")
    logger.info("4. 详细的性能指标")
    logger.info("5. 完整的API响应")
    logger.info("6. 异步和同步处理")
    logger.info("7. 健康检查和监控")
    
    logger.info("\n💡 使用建议:")
    logger.info("- 生产环境推荐使用异步接口")
    logger.info("- 保持use_smart_enhancement=True以获得最佳效果")
    logger.info("- context_expansion_ratio建议范围: 0.08-0.15")
    logger.info("- 监控处理时间和内存使用")
    
    return True


def test_api_documentation():
    """测试API文档"""
    logger.info("\n=== API文档测试 ===")
    
    try:
        response = requests.get("http://localhost:8001/docs")
        if response.status_code == 200:
            logger.info("✅ API文档可访问: http://localhost:8001/docs")
        else:
            logger.warning("⚠️  API文档不可访问")
    except Exception as e:
        logger.warning(f"⚠️  API文档测试失败: {e}")


def main():
    """主函数"""
    logger.info("🔧 app.py集成测试工具")
    logger.info("确保app.py服务正在运行: python app.py")
    
    # 自动开始测试
    logger.info("\n自动开始测试...")
    
    # 执行测试
    success = test_app_integration()
    
    # 测试API文档
    test_api_documentation()
    
    if success:
        logger.info("\n🎉 所有测试通过！app.py已完全对接优化方案。")
    else:
        logger.info("\n❌ 部分测试失败，请检查app.py配置。")


if __name__ == "__main__":
    main()
