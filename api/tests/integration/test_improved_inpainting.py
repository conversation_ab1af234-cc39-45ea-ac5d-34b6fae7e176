"""
测试改进的修复算法
对比原始OpenCV修复和改进后的修复效果
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image, ImageDraw, ImageFont
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def test_improved_inpainting(image_path: str):
    """测试改进的修复算法"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 测试配置
    test_configs = [
        {
            "name": "传统OpenCV修复",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": False,  # 不增强掩码，看原始修复效果
                "return_intermediate": True
            },
            "filename": "original_opencv"
        },
        {
            "name": "传统掩码增强 + OpenCV",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": False,
                "return_intermediate": True
            },
            "filename": "traditional_enhanced"
        },
        {
            "name": "智能掩码增强 + 改进修复",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.12,
                "return_intermediate": True
            },
            "filename": "smart_enhanced"
        },
        {
            "name": "智能掩码增强 + 强化修复",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.15,
                "inpaint_radius": 8,  # 增大修复半径
                "return_intermediate": True
            },
            "filename": "smart_strong"
        }
    ]
    
    results = {}
    processing_info = {}
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n=== 测试 {i+1}/{len(test_configs)}: {config['name']} ===")
        
        try:
            result = pipeline.remove_watermark(image, **config['params'])
            
            if isinstance(result, dict):
                result_image = result['result_image']
                results[config['filename']] = result_image
                
                logger.info(f"✅ 检测到 {len(result['detection_info'])} 个水印")
                logger.info(f"✅ 处理时间: {result['processing_time']:.2f}秒")
                logger.info(f"✅ 检测时间: {result['detection_time']:.2f}秒")
                logger.info(f"✅ 修复时间: {result['inpainting_time']:.2f}秒")
                
                # 计算掩码信息
                if 'original_mask' in result and 'enhanced_mask' in result:
                    original_binary = (result['original_mask'] > 127).astype(int)
                    enhanced_binary = (result['enhanced_mask'] > 127).astype(int)
                    
                    original_ratio = original_binary.sum() / (original_binary.shape[0] * original_binary.shape[1])
                    enhanced_ratio = enhanced_binary.sum() / (enhanced_binary.shape[0] * enhanced_binary.shape[1])
                    expansion_factor = enhanced_ratio / original_ratio if original_ratio > 0 else 1
                    
                    logger.info(f"📊 掩码扩展: {original_ratio:.1%} → {enhanced_ratio:.1%} ({expansion_factor:.1f}x)")
                
                processing_info[config['filename']] = {
                    'total_time': result['processing_time'],
                    'detection_time': result['detection_time'],
                    'inpainting_time': result['inpainting_time'],
                    'watermark_count': len(result['detection_info'])
                }
                
                # 保存结果
                filename = f"improved_{config['filename']}_result.jpg"
                result_image.save(filename, 'JPEG', quality=95)
                logger.info(f"✅ 结果已保存: {filename}")
                
            else:
                results[config['filename']] = result
                filename = f"improved_{config['filename']}_result.jpg"
                result.save(filename, 'JPEG', quality=95)
                logger.info(f"✅ 结果已保存: {filename}")
                
        except Exception as e:
            logger.error(f"❌ {config['name']} 失败: {e}")
            results[config['filename']] = image.copy()
    
    # 创建对比图
    logger.info("\n=== 创建修复效果对比图 ===")
    try:
        create_inpainting_comparison(image, results)
        logger.info("✅ 修复效果对比图已保存")
    except Exception as e:
        logger.error(f"❌ 创建对比图失败: {e}")
    
    # 输出性能分析
    logger.info("\n" + "="*60)
    logger.info("修复效果测试完成！")
    logger.info("="*60)
    
    logger.info("性能对比:")
    method_names = {
        "original_opencv": "传统OpenCV",
        "traditional_enhanced": "传统增强",
        "smart_enhanced": "智能增强",
        "smart_strong": "智能强化"
    }
    
    for filename, info in processing_info.items():
        method_name = method_names.get(filename, filename)
        logger.info(f"  {method_name}: 总时间={info['total_time']:.2f}s, "
                   f"修复时间={info['inpainting_time']:.2f}s, "
                   f"水印数量={info['watermark_count']}")
    
    logger.info("\n生成的文件:")
    logger.info("- improved_*_result.jpg: 各种方法的修复结果")
    logger.info("- improved_inpainting_comparison.jpg: 修复效果对比图")
    
    logger.info("\n💡 评估建议:")
    logger.info("1. 对比不同方法的修复自然度")
    logger.info("2. 观察边界是否平滑，是否有涂抹痕迹")
    logger.info("3. 检查纹理连续性和颜色一致性")
    logger.info("4. 评估整体视觉质量")
    logger.info("5. 如果改进效果明显，可以在实际使用中采用")


def create_inpainting_comparison(original, results):
    """创建修复效果对比图"""
    
    # 准备图像和标题
    images = [original]
    titles = ["原图"]
    
    method_names = {
        "original_opencv": "传统OpenCV",
        "traditional_enhanced": "传统增强",
        "smart_enhanced": "智能增强",
        "smart_strong": "智能强化"
    }
    
    for filename in ["original_opencv", "traditional_enhanced", "smart_enhanced", "smart_strong"]:
        if filename in results:
            images.append(results[filename])
            titles.append(method_names[filename])
    
    # 创建网格布局
    cols = min(len(images), 3)  # 最多3列
    rows = (len(images) + cols - 1) // cols
    
    target_size = (300, 400)  # 适应纵向图像
    gap = 15
    title_height = 25
    
    grid_width = cols * target_size[0] + (cols + 1) * gap
    grid_height = rows * target_size[1] + (rows + 1) * gap + rows * title_height
    
    grid = Image.new('RGB', (grid_width, grid_height), (255, 255, 255))
    draw = ImageDraw.Draw(grid)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 14)
        title_font = ImageFont.truetype("arial.ttf", 18)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # 放置图像和标题
    for i, (img, title) in enumerate(zip(images, titles)):
        row = i // cols
        col = i % cols
        
        x = gap + col * (target_size[0] + gap)
        y = gap + title_height + row * (target_size[1] + gap + title_height)
        
        # 调整图像大小并粘贴
        if img.size != target_size:
            resized_img = img.resize(target_size, Image.LANCZOS)
        else:
            resized_img = img
        
        grid.paste(resized_img, (x, y))
        
        # 添加标题
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x = x + (target_size[0] - text_width) // 2
        title_y = y - 20
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    # 添加主标题
    main_title = "修复算法效果对比"
    bbox = draw.textbbox((0, 0), main_title, font=title_font)
    text_width = bbox[2] - bbox[0]
    main_title_x = (grid_width - text_width) // 2
    draw.text((main_title_x, 5), main_title, fill=(0, 0, 0), font=title_font)
    
    grid.save("improved_inpainting_comparison.jpg", 'JPEG', quality=95)


def main():
    parser = argparse.ArgumentParser(description="测试改进的修复算法")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    logger.info("开始改进修复算法测试...")
    
    # 执行测试
    test_improved_inpainting(args.image)
    
    logger.info("\n🎉 测试完成！")
    logger.info("请查看生成的对比图像，评估改进效果。")


if __name__ == "__main__":
    main()
