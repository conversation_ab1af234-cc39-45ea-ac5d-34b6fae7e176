"""
最终对比测试
对比传统方法和修复后的智能掩码增强
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image, ImageDraw, ImageFont
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def final_comparison_test(image_path: str):
    """最终对比测试"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    results = {}
    
    # 测试1: 传统掩码增强
    logger.info("\n=== 测试1: 传统掩码增强 ===")
    try:
        result1 = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=False,
            return_intermediate=True
        )
        
        if isinstance(result1, dict):
            results['traditional'] = result1['result_image']
            logger.info(f"✅ 检测到 {len(result1['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result1['processing_time']:.2f}秒")
            
            # 计算掩码覆盖率
            original_ratio = (result1['original_mask'] > 127).sum() / (result1['original_mask'].shape[0] * result1['original_mask'].shape[1])
            enhanced_ratio = (result1['enhanced_mask'] > 127).sum() / (result1['enhanced_mask'].shape[0] * result1['enhanced_mask'].shape[1])
            logger.info(f"📊 掩码扩展: {original_ratio:.1%} → {enhanced_ratio:.1%} ({enhanced_ratio/original_ratio:.1f}x)")
        else:
            results['traditional'] = result1
            logger.info("✅ 处理完成")
            
    except Exception as e:
        logger.error(f"❌ 传统方法失败: {e}")
        results['traditional'] = image.copy()
    
    # 测试2: 智能掩码增强
    logger.info("\n=== 测试2: 智能掩码增强 ===")
    try:
        result2 = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=True,
            context_expansion_ratio=0.1,  # 保守参数
            return_intermediate=True
        )
        
        if isinstance(result2, dict):
            results['smart'] = result2['result_image']
            logger.info(f"✅ 检测到 {len(result2['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result2['processing_time']:.2f}秒")
            
            # 计算掩码覆盖率
            original_ratio = (result2['original_mask'] > 127).sum() / (result2['original_mask'].shape[0] * result2['original_mask'].shape[1])
            enhanced_ratio = (result2['enhanced_mask'] > 127).sum() / (result2['enhanced_mask'].shape[0] * result2['enhanced_mask'].shape[1])
            logger.info(f"📊 掩码扩展: {original_ratio:.1%} → {enhanced_ratio:.1%} ({enhanced_ratio/original_ratio:.1f}x)")
        else:
            results['smart'] = result2
            logger.info("✅ 处理完成")
            
    except Exception as e:
        logger.error(f"❌ 智能方法失败: {e}")
        results['smart'] = image.copy()
    
    # 保存单独的结果
    for method, result_image in results.items():
        filename = f"final_{method}_result.jpg"
        result_image.save(filename, 'JPEG', quality=95)
        logger.info(f"✅ {method} 结果已保存: {filename}")
    
    # 创建对比图
    logger.info("\n=== 创建最终对比图 ===")
    try:
        comparison = create_final_comparison(image, results)
        comparison.save("final_comparison.jpg", 'JPEG', quality=95)
        logger.info("✅ 最终对比图已保存: final_comparison.jpg")
    except Exception as e:
        logger.error(f"❌ 创建对比图失败: {e}")
    
    logger.info("\n" + "="*60)
    logger.info("最终测试完成！")
    logger.info("="*60)
    logger.info("生成的文件:")
    logger.info("- final_traditional_result.jpg: 传统掩码增强结果")
    logger.info("- final_smart_result.jpg: 智能掩码增强结果")
    logger.info("- final_comparison.jpg: 三图对比")
    logger.info("\n💡 评估建议:")
    logger.info("1. 对比修复效果的自然度")
    logger.info("2. 检查是否有明显的修复边界")
    logger.info("3. 观察纹理的连续性")
    logger.info("4. 评估整体视觉质量")


def create_final_comparison(original, results):
    """创建最终对比图"""
    
    # 确保所有图像大小相同
    target_size = original.size
    images = [original]
    titles = ["原图"]
    
    for method in ['traditional', 'smart']:
        if method in results:
            img = results[method]
            if img.size != target_size:
                img = img.resize(target_size, Image.LANCZOS)
            images.append(img)
            titles.append("传统方法" if method == 'traditional' else "智能方法")
    
    # 创建三图并排对比
    gap = 20
    title_height = 40
    
    width = target_size[0] * len(images) + gap * (len(images) + 1)
    height = target_size[1] + title_height + gap * 2
    
    comparison = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(comparison)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 16)
        title_font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # 放置图像和标题
    for i, (img, title) in enumerate(zip(images, titles)):
        x = gap + i * (target_size[0] + gap)
        y = title_height + gap
        
        # 粘贴图像
        comparison.paste(img, (x, y))
        
        # 添加标题
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x = x + (target_size[0] - text_width) // 2
        title_y = 10
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    # 添加主标题
    main_title = "掩码增强效果最终对比"
    bbox = draw.textbbox((0, 0), main_title, font=title_font)
    text_width = bbox[2] - bbox[0]
    main_title_x = (width - text_width) // 2
    draw.text((main_title_x, height - 25), main_title, fill=(0, 0, 0), font=title_font)
    
    return comparison


def main():
    parser = argparse.ArgumentParser(description="最终对比测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    final_comparison_test(args.image)


if __name__ == "__main__":
    main()
