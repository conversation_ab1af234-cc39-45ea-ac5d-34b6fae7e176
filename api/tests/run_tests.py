"""
测试运行器
统一运行各种测试的入口脚本
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path
import logging

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def run_unit_tests():
    """运行单元测试"""
    logger.info("🧪 运行单元测试...")
    
    unit_tests = [
        "tests/unit/test_mask_enhancement.py",
        "tests/unit/quick_detection_test.py",
        "tests/unit/quick_mask_test.py",
        "tests/unit/simple_mask_test.py",
        "tests/unit/simple_inpaint_test.py",
        "tests/unit/minimal_test.py"
    ]
    
    for test_file in unit_tests:
        if Path(test_file).exists():
            logger.info(f"运行: {test_file}")
            try:
                subprocess.run([sys.executable, test_file, "--image", "images/test_image2.jpeg"], 
                             check=True, cwd=project_root)
                logger.info(f"✅ {test_file} 通过")
            except subprocess.CalledProcessError:
                logger.error(f"❌ {test_file} 失败")
        else:
            logger.warning(f"⚠️  文件不存在: {test_file}")


def run_integration_tests():
    """运行集成测试"""
    logger.info("🔗 运行集成测试...")
    
    integration_tests = [
        "tests/integration/test_real_lama.py",
        "tests/integration/test_improved_inpainting.py",
        "tests/integration/final_comparison_test.py"
    ]
    
    for test_file in integration_tests:
        if Path(test_file).exists():
            logger.info(f"运行: {test_file}")
            try:
                subprocess.run([sys.executable, test_file, "--image", "images/test_image2.jpeg"], 
                             check=True, cwd=project_root)
                logger.info(f"✅ {test_file} 通过")
            except subprocess.CalledProcessError:
                logger.error(f"❌ {test_file} 失败")
        else:
            logger.warning(f"⚠️  文件不存在: {test_file}")


def run_performance_tests():
    """运行性能测试"""
    logger.info("⚡ 运行性能测试...")
    
    performance_tests = [
        "tests/performance/enhanced_effect_test.py",
        "tests/performance/conservative_mask_test.py"
    ]
    
    for test_file in performance_tests:
        if Path(test_file).exists():
            logger.info(f"运行: {test_file}")
            try:
                subprocess.run([sys.executable, test_file, "--image", "images/test_image2.jpeg"], 
                             check=True, cwd=project_root)
                logger.info(f"✅ {test_file} 通过")
            except subprocess.CalledProcessError:
                logger.error(f"❌ {test_file} 失败")
        else:
            logger.warning(f"⚠️  文件不存在: {test_file}")


def run_app_tests():
    """运行API测试"""
    logger.info("🌐 运行API集成测试...")
    
    app_test = "tests/integration/test_app_integration.py"
    if Path(app_test).exists():
        logger.info("注意: 请确保app.py服务正在运行")
        try:
            subprocess.run([sys.executable, app_test], check=True, cwd=project_root)
            logger.info("✅ API测试通过")
        except subprocess.CalledProcessError:
            logger.error("❌ API测试失败")
    else:
        logger.warning(f"⚠️  文件不存在: {app_test}")


def run_examples():
    """运行示例代码"""
    logger.info("📚 运行示例代码...")
    
    examples = [
        "tests/examples/debug_detection_comparison.py",
        "tests/examples/debug_lama_output.py"
    ]
    
    for example_file in examples:
        if Path(example_file).exists():
            logger.info(f"运行示例: {example_file}")
            try:
                subprocess.run([sys.executable, example_file, "--original", "images/test_image2.jpeg", 
                              "--result", "real_lama_result.jpg"], 
                             check=True, cwd=project_root)
                logger.info(f"✅ {example_file} 完成")
            except subprocess.CalledProcessError:
                logger.error(f"❌ {example_file} 失败")
        else:
            logger.warning(f"⚠️  文件不存在: {example_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="水印检测系统测试运行器")
    parser.add_argument("--type", choices=["unit", "integration", "performance", "app", "examples", "all"], 
                       default="all", help="测试类型")
    parser.add_argument("--verbose", "-v", action="store_true", help="详细输出")
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    logger.info("🚀 水印检测系统测试运行器")
    logger.info(f"项目根目录: {project_root}")
    logger.info(f"测试类型: {args.type}")
    
    if args.type == "unit" or args.type == "all":
        run_unit_tests()
    
    if args.type == "integration" or args.type == "all":
        run_integration_tests()
    
    if args.type == "performance" or args.type == "all":
        run_performance_tests()
    
    if args.type == "app" or args.type == "all":
        run_app_tests()
    
    if args.type == "examples" or args.type == "all":
        run_examples()
    
    logger.info("\n🎉 测试运行完成!")


if __name__ == "__main__":
    main()
