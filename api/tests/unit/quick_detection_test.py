"""
快速检测测试
验证不同置信度设置的影响
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkDetector
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def quick_test(image_path: str):
    """快速测试不同置信度设置"""
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"测试图像: {image_path}")
    logger.info(f"图像尺寸: {image.size}")
    
    # 初始化检测器
    detector = WatermarkDetector(device="auto")
    
    # 测试配置
    configs = [
        ("原始YOLO（模拟第三方）", {"use_model_default_conf": True}),
        ("当前项目（conf=0.3）", {"confidence_threshold": 0.3}),
        ("低置信度（conf=0.1）", {"confidence_threshold": 0.1}),
        ("极低置信度（conf=0.05）", {"confidence_threshold": 0.05}),
    ]
    
    logger.info("\n" + "="*50)
    logger.info("检测结果对比:")
    logger.info("="*50)
    
    best_count = 0
    best_method = ""
    
    for name, params in configs:
        try:
            mask, avg_conf, detections = detector.detect(
                image=image,
                iou_threshold=0.5,
                image_size=1024,
                augment=True,
                return_detailed_info=True,
                **params
            )
            
            count = len(detections)
            logger.info(f"\n{name}:")
            logger.info(f"  检测数量: {count}")
            logger.info(f"  平均置信度: {avg_conf:.3f}")
            
            if count > 0:
                logger.info("  详细信息:")
                for i, det in enumerate(detections):
                    logger.info(f"    水印{i+1}: 置信度={det['confidence']:.3f}, "
                               f"位置={det['bbox']}, 面积={det['area']}")
            
            if count > best_count:
                best_count = count
                best_method = name
                
        except Exception as e:
            logger.error(f"{name}: 错误 - {e}")
    
    logger.info("\n" + "="*50)
    logger.info("结论:")
    logger.info("="*50)
    
    if best_count > 1:
        logger.info(f"✅ 最佳方法: {best_method}")
        logger.info(f"✅ 检测到 {best_count} 个水印")
        logger.info("\n建议修改:")
        if "原始YOLO" in best_method:
            logger.info("1. 在detect方法中使用 use_model_default_conf=True")
            logger.info("2. 或者将默认confidence_threshold改为更低的值（如0.1）")
        elif "低置信度" in best_method:
            logger.info("1. 将默认confidence_threshold改为0.1")
            logger.info("2. 在API中使用更低的默认置信度")
    elif best_count == 1:
        logger.info(f"⚠️  最多只检测到1个水印")
        logger.info("可能原因:")
        logger.info("1. 图像中确实只有一个水印")
        logger.info("2. 两个水印重叠，被NMS合并了")
        logger.info("3. 模型版本差异")
        logger.info("\n建议尝试:")
        logger.info("1. 降低IoU阈值（如0.3）")
        logger.info("2. 检查是否使用了正确的模型")
    else:
        logger.info("❌ 所有方法都未检测到水印")
        logger.info("建议检查:")
        logger.info("1. 图像是否包含水印")
        logger.info("2. 模型是否正确加载")
        logger.info("3. 图像格式是否正确")


def main():
    parser = argparse.ArgumentParser(description="快速检测测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    quick_test(args.image)


if __name__ == "__main__":
    main()
