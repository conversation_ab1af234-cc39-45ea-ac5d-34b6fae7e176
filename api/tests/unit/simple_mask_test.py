"""
简单掩码测试
验证智能掩码增强功能
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def simple_test(image_path: str):
    """简单测试智能掩码增强"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 测试1: 传统方法
    logger.info("\n=== 测试传统掩码增强 ===")
    try:
        result1 = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=False,
            return_intermediate=True
        )
        
        if isinstance(result1, dict):
            logger.info(f"✅ 检测到 {len(result1['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result1['processing_time']:.2f}秒")
            
            # 保存结果
            result1['result_image'].save("traditional_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 传统方法结果已保存: traditional_result.jpg")
        else:
            result1.save("traditional_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 传统方法结果已保存: traditional_result.jpg")
            
    except Exception as e:
        logger.error(f"❌ 传统方法失败: {e}")
    
    # 测试2: 智能方法
    logger.info("\n=== 测试智能掩码增强 ===")
    try:
        result2 = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=True,
            context_expansion_ratio=0.15,
            return_intermediate=True
        )
        
        if isinstance(result2, dict):
            logger.info(f"✅ 检测到 {len(result2['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result2['processing_time']:.2f}秒")
            
            # 保存结果
            result2['result_image'].save("smart_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 智能方法结果已保存: smart_result.jpg")
            
            # 保存掩码对比
            if 'original_mask' in result2 and 'enhanced_mask' in result2:
                original_mask_img = Image.fromarray(result2['original_mask'])
                enhanced_mask_img = Image.fromarray(result2['enhanced_mask'])
                
                original_mask_img.save("original_mask.png")
                enhanced_mask_img.save("enhanced_mask.png")
                logger.info("✅ 掩码已保存: original_mask.png, enhanced_mask.png")
        else:
            result2.save("smart_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 智能方法结果已保存: smart_result.jpg")
            
    except Exception as e:
        logger.error(f"❌ 智能方法失败: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("\n=== 测试完成 ===")
    logger.info("请对比以下文件:")
    logger.info("- traditional_result.jpg: 传统掩码增强结果")
    logger.info("- smart_result.jpg: 智能掩码增强结果")
    logger.info("- original_mask.png: 原始检测掩码")
    logger.info("- enhanced_mask.png: 智能增强掩码")


def main():
    parser = argparse.ArgumentParser(description="简单掩码测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    simple_test(args.image)


if __name__ == "__main__":
    main()
