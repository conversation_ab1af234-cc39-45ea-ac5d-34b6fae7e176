"""
最小化测试
只测试一种保守的智能掩码增强
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def minimal_test(image_path: str):
    """最小化测试"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 只测试一种保守的智能方法
    logger.info("\n=== 测试保守智能掩码增强 ===")
    try:
        result = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=True,
            context_expansion_ratio=0.05,  # 很保守的扩展
            return_intermediate=True
        )
        
        if isinstance(result, dict):
            result_image = result['result_image']
            
            logger.info(f"✅ 检测到 {len(result['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result['processing_time']:.2f}秒")
            
            # 保存结果
            result_image.save("conservative_smart_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 结果已保存: conservative_smart_result.jpg")
            
            # 检查掩码覆盖率
            if 'original_mask' in result and 'enhanced_mask' in result:
                # 正确计算掩码覆盖率（二值化后计算）
                original_binary = (result['original_mask'] > 127).astype(int)
                enhanced_binary = (result['enhanced_mask'] > 127).astype(int)

                original_ratio = original_binary.sum() / (original_binary.shape[0] * original_binary.shape[1])
                enhanced_ratio = enhanced_binary.sum() / (enhanced_binary.shape[0] * enhanced_binary.shape[1])
                
                logger.info(f"📊 原始掩码覆盖率: {original_ratio:.1%}")
                logger.info(f"📊 增强掩码覆盖率: {enhanced_ratio:.1%}")
                logger.info(f"📊 扩展倍数: {enhanced_ratio/original_ratio:.1f}x")
                
                # 保存掩码
                Image.fromarray(result['original_mask']).save("conservative_original_mask.png")
                Image.fromarray(result['enhanced_mask']).save("conservative_enhanced_mask.png")
                logger.info("✅ 掩码已保存")
                
                # 安全检查
                if enhanced_ratio > 0.3:
                    logger.warning("⚠️  增强掩码覆盖率较高，可能存在过度修复风险")
                elif enhanced_ratio < 0.1:
                    logger.info("✅ 增强掩码覆盖率合理")
                else:
                    logger.info("✅ 增强掩码覆盖率适中")
        else:
            result.save("conservative_smart_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 结果已保存: conservative_smart_result.jpg")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("\n=== 测试完成 ===")
    logger.info("请检查 conservative_smart_result.jpg 查看修复效果")


def main():
    parser = argparse.ArgumentParser(description="最小化测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    minimal_test(args.image)


if __name__ == "__main__":
    main()
