"""
测试掩码增强策略
对比不同掩码增强方法对LAMA修复效果的影响
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline, WatermarkVisualization
from PIL import Image
import numpy as np
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def test_mask_enhancement_strategies(image_path: str, output_dir: str = "./mask_test_results"):
    """测试不同掩码增强策略"""
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"测试图像: {image_path}")
    logger.info(f"图像尺寸: {image.size}")
    
    # 初始化组件
    pipeline = WatermarkRemovalPipeline()
    visualizer = WatermarkVisualization()
    
    # 保存原图
    image.save(output_path / "00_original.jpg", 'JPEG', quality=95)
    
    # 测试配置
    test_configs = [
        {
            "name": "无掩码增强",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": False,
                "return_intermediate": True
            },
            "filename": "01_no_enhancement"
        },
        {
            "name": "传统掩码增强",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": False,
                "mask_dilation_size": 5,
                "mask_blur_size": 5,
                "return_intermediate": True
            },
            "filename": "02_traditional_enhancement"
        },
        {
            "name": "智能掩码增强（默认）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.15,
                "return_intermediate": True
            },
            "filename": "03_smart_enhancement_default"
        },
        {
            "name": "智能掩码增强（保守）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.1,
                "return_intermediate": True
            },
            "filename": "04_smart_enhancement_conservative"
        },
        {
            "name": "智能掩码增强（激进）",
            "params": {
                "confidence_threshold": 0.1,
                "enhance_mask": True,
                "use_smart_enhancement": True,
                "context_expansion_ratio": 0.25,
                "return_intermediate": True
            },
            "filename": "05_smart_enhancement_aggressive"
        }
    ]
    
    results = []
    
    logger.info("\n" + "="*60)
    logger.info("开始测试不同掩码增强策略...")
    logger.info("="*60)
    
    for i, config in enumerate(test_configs):
        logger.info(f"\n--- 测试 {i+1}/{len(test_configs)}: {config['name']} ---")
        
        try:
            # 执行水印去除
            result = pipeline.remove_watermark(image, **config['params'])
            
            if isinstance(result, dict):
                # 获取结果图像和中间信息
                result_image = result['result_image']
                original_mask = result['original_mask']
                enhanced_mask = result.get('enhanced_mask', original_mask)
                detection_info = result['detection_info']
                
                logger.info(f"检测到 {len(detection_info)} 个水印")
                logger.info(f"处理时间: {result['processing_time']:.2f}秒")
                
                # 保存结果图像
                result_image.save(output_path / f"{config['filename']}_result.jpg", 'JPEG', quality=95)
                
                # 保存原始掩码
                original_mask_img = Image.fromarray(original_mask)
                original_mask_img.save(output_path / f"{config['filename']}_original_mask.png")
                
                # 保存增强掩码
                enhanced_mask_img = Image.fromarray(enhanced_mask)
                enhanced_mask_img.save(output_path / f"{config['filename']}_enhanced_mask.png")
                
                # 创建掩码对比图
                mask_comparison = visualizer.create_side_by_side_comparison(
                    original_mask_img, enhanced_mask_img,
                    titles=("原始掩码", "增强掩码")
                )
                mask_comparison.save(output_path / f"{config['filename']}_mask_comparison.jpg", 'JPEG', quality=95)
                
                # 创建修复对比图
                repair_comparison = visualizer.create_side_by_side_comparison(
                    image, result_image,
                    titles=("原图", config['name'])
                )
                repair_comparison.save(output_path / f"{config['filename']}_repair_comparison.jpg", 'JPEG', quality=95)
                
                # 记录结果
                results.append({
                    "name": config['name'],
                    "watermark_count": len(detection_info),
                    "processing_time": result['processing_time'],
                    "success": True
                })
                
                logger.info("✅ 处理成功")
                
            else:
                # 简单结果
                result.save(output_path / f"{config['filename']}_result.jpg", 'JPEG', quality=95)
                
                results.append({
                    "name": config['name'],
                    "watermark_count": "未知",
                    "processing_time": "未知",
                    "success": True
                })
                
                logger.info("✅ 处理成功（简单模式）")
                
        except Exception as e:
            logger.error(f"❌ 处理失败: {e}")
            results.append({
                "name": config['name'],
                "watermark_count": 0,
                "processing_time": 0,
                "success": False,
                "error": str(e)
            })
    
    # 创建总体对比图
    logger.info("\n--- 创建总体对比图 ---")
    try:
        create_overall_comparison(output_path, test_configs)
        logger.info("✅ 总体对比图创建成功")
    except Exception as e:
        logger.error(f"❌ 创建总体对比图失败: {e}")
    
    # 输出结果总结
    logger.info("\n" + "="*60)
    logger.info("测试结果总结:")
    logger.info("="*60)
    
    for result in results:
        if result['success']:
            logger.info(f"✅ {result['name']}: "
                       f"水印数量={result['watermark_count']}, "
                       f"处理时间={result['processing_time']}")
        else:
            logger.info(f"❌ {result['name']}: 失败 - {result.get('error', '未知错误')}")
    
    logger.info(f"\n所有结果已保存到: {output_path}")
    
    # 提供使用建议
    logger.info("\n" + "="*60)
    logger.info("使用建议:")
    logger.info("="*60)
    logger.info("1. 对比各种方法的修复效果，选择最自然的结果")
    logger.info("2. 智能掩码增强通常能提供更好的上下文信息")
    logger.info("3. 如果修复区域边界明显，尝试增加context_expansion_ratio")
    logger.info("4. 如果修复过度，尝试减少context_expansion_ratio")
    logger.info("5. 观察掩码对比图，确保掩码覆盖了完整的水印区域")
    
    return results


def create_overall_comparison(output_path: Path, test_configs: list):
    """创建总体对比图"""
    from PIL import Image, ImageDraw, ImageFont
    
    # 加载所有结果图像
    images = []
    titles = []
    
    # 添加原图
    original = Image.open(output_path / "00_original.jpg")
    images.append(original)
    titles.append("原图")
    
    # 添加各种方法的结果
    for config in test_configs:
        result_path = output_path / f"{config['filename']}_result.jpg"
        if result_path.exists():
            result_img = Image.open(result_path)
            images.append(result_img)
            titles.append(config['name'])
    
    if len(images) < 2:
        return
    
    # 调整图像大小
    target_size = (400, 300)
    resized_images = []
    for img in images:
        resized = img.resize(target_size, Image.LANCZOS)
        resized_images.append(resized)
    
    # 计算网格布局
    cols = 3
    rows = (len(resized_images) + cols - 1) // cols
    
    # 创建网格图像
    grid_width = cols * target_size[0] + (cols + 1) * 20
    grid_height = rows * target_size[1] + (rows + 1) * 60
    
    grid = Image.new('RGB', (grid_width, grid_height), (255, 255, 255))
    draw = ImageDraw.Draw(grid)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 14)
    except:
        font = ImageFont.load_default()
    
    # 放置图像和标题
    for i, (img, title) in enumerate(zip(resized_images, titles)):
        row = i // cols
        col = i % cols
        
        x = 20 + col * (target_size[0] + 20)
        y = 40 + row * (target_size[1] + 60)
        
        # 粘贴图像
        grid.paste(img, (x, y))
        
        # 添加标题
        title_x = x + target_size[0] // 2
        title_y = y - 25
        
        # 计算文本宽度以居中
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x -= text_width // 2
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    # 保存网格图像
    grid.save(output_path / "comparison_grid.jpg", 'JPEG', quality=95)


def main():
    parser = argparse.ArgumentParser(description="测试掩码增强策略")
    parser.add_argument("--image", required=True, help="测试图像路径")
    parser.add_argument("--output", default="./mask_test_results", help="输出目录")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    logger.info("开始掩码增强策略测试...")
    
    # 执行测试
    results = test_mask_enhancement_strategies(args.image, args.output)
    
    logger.info("\n🎉 测试完成！")
    logger.info("请查看输出目录中的对比图像，选择效果最好的掩码增强策略。")


if __name__ == "__main__":
    main()
