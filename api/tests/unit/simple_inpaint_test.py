"""
简单修复测试
测试改进的修复算法
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def simple_inpaint_test(image_path: str):
    """简单修复测试"""
    
    logger.info(f"测试图像: {image_path}")
    
    # 初始化管道
    pipeline = WatermarkRemovalPipeline()
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"图像尺寸: {image.size}")
    
    # 测试改进的修复算法
    logger.info("\n=== 测试改进的修复算法 ===")
    try:
        result = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=True,
            context_expansion_ratio=0.12,
            inpaint_radius=6,  # 增大修复半径
            return_intermediate=True
        )
        
        if isinstance(result, dict):
            result_image = result['result_image']
            
            logger.info(f"✅ 检测到 {len(result['detection_info'])} 个水印")
            logger.info(f"✅ 总处理时间: {result['processing_time']:.2f}秒")
            logger.info(f"✅ 检测时间: {result['detection_time']:.2f}秒")
            logger.info(f"✅ 修复时间: {result['inpainting_time']:.2f}秒")
            
            # 保存结果
            result_image.save("improved_inpaint_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 结果已保存: improved_inpaint_result.jpg")
            
            # 保存掩码信息
            if 'original_mask' in result and 'enhanced_mask' in result:
                Image.fromarray(result['original_mask']).save("improved_original_mask.png")
                Image.fromarray(result['enhanced_mask']).save("improved_enhanced_mask.png")
                
                # 计算掩码覆盖率
                original_binary = (result['original_mask'] > 127).astype(int)
                enhanced_binary = (result['enhanced_mask'] > 127).astype(int)
                
                original_ratio = original_binary.sum() / (original_binary.shape[0] * original_binary.shape[1])
                enhanced_ratio = enhanced_binary.sum() / (enhanced_binary.shape[0] * enhanced_binary.shape[1])
                expansion_factor = enhanced_ratio / original_ratio if original_ratio > 0 else 1
                
                logger.info(f"📊 掩码扩展: {original_ratio:.1%} → {enhanced_ratio:.1%} ({expansion_factor:.1f}x)")
                logger.info("✅ 掩码已保存: improved_original_mask.png, improved_enhanced_mask.png")
        else:
            result.save("improved_inpaint_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 结果已保存: improved_inpaint_result.jpg")
            
    except Exception as e:
        logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("\n=== 测试完成 ===")
    logger.info("生成的文件:")
    logger.info("- improved_inpaint_result.jpg: 改进修复算法的结果")
    logger.info("- improved_original_mask.png: 原始检测掩码")
    logger.info("- improved_enhanced_mask.png: 智能增强掩码")
    
    logger.info("\n💡 改进要点:")
    logger.info("1. 多尺度修复：在不同分辨率下进行修复并融合")
    logger.info("2. 算法混合：结合Telea和NS算法的优势")
    logger.info("3. 边缘增强：使用双边滤波保持边缘清晰")
    logger.info("4. 颜色校正：确保修复区域颜色与周围一致")
    logger.info("5. 后处理：边缘羽化和平滑过渡")


def main():
    parser = argparse.ArgumentParser(description="简单修复测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    simple_inpaint_test(args.image)


if __name__ == "__main__":
    main()
