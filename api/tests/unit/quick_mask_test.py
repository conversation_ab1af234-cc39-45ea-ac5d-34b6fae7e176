"""
快速掩码增强测试
快速对比传统掩码增强和智能掩码增强的效果
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkRemovalPipeline, WatermarkVisualization
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def quick_mask_comparison(image_path: str, output_dir: str = "./quick_mask_results"):
    """快速对比掩码增强效果"""
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"测试图像: {image_path}")
    logger.info(f"图像尺寸: {image.size}")
    
    # 初始化组件
    pipeline = WatermarkRemovalPipeline()
    visualizer = WatermarkVisualization()
    
    # 保存原图
    image.save(output_path / "original.jpg", 'JPEG', quality=95)
    
    logger.info("\n" + "="*50)
    logger.info("开始对比测试...")
    logger.info("="*50)
    
    # 测试1: 传统掩码增强
    logger.info("\n--- 测试1: 传统掩码增强 ---")
    try:
        result1 = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=False,
            mask_dilation_size=5,
            mask_blur_size=5,
            return_intermediate=True
        )
        
        if isinstance(result1, dict):
            result1_image = result1['result_image']
            mask1_original = result1['original_mask']
            mask1_enhanced = result1.get('enhanced_mask', mask1_original)
            
            logger.info(f"✅ 检测到 {len(result1['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result1['processing_time']:.2f}秒")
            
            # 保存结果
            result1_image.save(output_path / "traditional_result.jpg", 'JPEG', quality=95)
            Image.fromarray(mask1_enhanced).save(output_path / "traditional_mask.png")
            
        else:
            result1_image = result1
            result1_image.save(output_path / "traditional_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 处理完成（简单模式）")
            
    except Exception as e:
        logger.error(f"❌ 传统掩码增强失败: {e}")
        result1_image = image.copy()
    
    # 测试2: 智能掩码增强
    logger.info("\n--- 测试2: 智能掩码增强 ---")
    try:
        result2 = pipeline.remove_watermark(
            image,
            confidence_threshold=0.1,
            enhance_mask=True,
            use_smart_enhancement=True,
            context_expansion_ratio=0.15,
            return_intermediate=True
        )
        
        if isinstance(result2, dict):
            result2_image = result2['result_image']
            mask2_original = result2['original_mask']
            mask2_enhanced = result2.get('enhanced_mask', mask2_original)
            
            logger.info(f"✅ 检测到 {len(result2['detection_info'])} 个水印")
            logger.info(f"✅ 处理时间: {result2['processing_time']:.2f}秒")
            
            # 保存结果
            result2_image.save(output_path / "smart_result.jpg", 'JPEG', quality=95)
            Image.fromarray(mask2_enhanced).save(output_path / "smart_mask.png")
            
        else:
            result2_image = result2
            result2_image.save(output_path / "smart_result.jpg", 'JPEG', quality=95)
            logger.info("✅ 处理完成（简单模式）")
            
    except Exception as e:
        logger.error(f"❌ 智能掩码增强失败: {e}")
        result2_image = image.copy()
    
    # 创建对比图
    logger.info("\n--- 创建对比图 ---")
    try:
        # 三图对比：原图 vs 传统方法 vs 智能方法
        comparison = create_three_way_comparison(
            image, result1_image, result2_image,
            ["原图", "传统掩码增强", "智能掩码增强"]
        )
        comparison.save(output_path / "comparison.jpg", 'JPEG', quality=95)
        
        logger.info("✅ 对比图创建成功")
        
    except Exception as e:
        logger.error(f"❌ 创建对比图失败: {e}")
    
    # 如果有掩码信息，创建掩码对比
    try:
        if 'mask1_enhanced' in locals() and 'mask2_enhanced' in locals():
            mask_comparison = visualizer.create_side_by_side_comparison(
                Image.fromarray(mask1_enhanced),
                Image.fromarray(mask2_enhanced),
                titles=("传统掩码", "智能掩码")
            )
            mask_comparison.save(output_path / "mask_comparison.jpg", 'JPEG', quality=95)
            logger.info("✅ 掩码对比图创建成功")
    except Exception as e:
        logger.error(f"❌ 创建掩码对比图失败: {e}")
    
    logger.info("\n" + "="*50)
    logger.info("测试完成！")
    logger.info("="*50)
    logger.info(f"结果已保存到: {output_path}")
    logger.info("\n查看文件:")
    logger.info("- original.jpg: 原图")
    logger.info("- traditional_result.jpg: 传统掩码增强结果")
    logger.info("- smart_result.jpg: 智能掩码增强结果")
    logger.info("- comparison.jpg: 三图对比")
    logger.info("- mask_comparison.jpg: 掩码对比（如果可用）")
    
    logger.info("\n💡 使用建议:")
    logger.info("1. 对比两种方法的修复效果")
    logger.info("2. 观察边界是否自然，是否有明显的修复痕迹")
    logger.info("3. 智能掩码增强通常能提供更好的上下文信息")
    logger.info("4. 如果智能方法效果更好，建议在实际使用中启用")


def create_three_way_comparison(img1, img2, img3, titles):
    """创建三图对比"""
    from PIL import Image, ImageDraw, ImageFont
    
    # 确保所有图像大小相同
    base_size = img1.size
    if img2.size != base_size:
        img2 = img2.resize(base_size, Image.LANCZOS)
    if img3.size != base_size:
        img3 = img3.resize(base_size, Image.LANCZOS)
    
    # 创建对比图
    gap = 20
    title_height = 40
    total_width = base_size[0] * 3 + gap * 4
    total_height = base_size[1] + title_height + gap * 2
    
    comparison = Image.new('RGB', (total_width, total_height), (255, 255, 255))
    
    # 粘贴图像
    positions = [
        gap,
        gap + base_size[0] + gap,
        gap + base_size[0] * 2 + gap * 2
    ]
    
    for i, (img, x_pos) in enumerate(zip([img1, img2, img3], positions)):
        comparison.paste(img, (x_pos, title_height + gap))
        
        # 添加标题
        draw = ImageDraw.Draw(comparison)
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        title = titles[i]
        # 计算文本宽度以居中
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x = x_pos + (base_size[0] - text_width) // 2
        title_y = 10
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    return comparison


def main():
    parser = argparse.ArgumentParser(description="快速掩码增强测试")
    parser.add_argument("--image", required=True, help="测试图像路径")
    parser.add_argument("--output", default="./quick_mask_results", help="输出目录")
    
    args = parser.parse_args()
    
    if not Path(args.image).exists():
        logger.error(f"图像文件不存在: {args.image}")
        return
    
    logger.info("开始快速掩码增强对比测试...")
    
    # 执行测试
    quick_mask_comparison(args.image, args.output)
    
    logger.info("\n🎉 测试完成！请查看输出目录中的对比图像。")


if __name__ == "__main__":
    main()
