"""
测试第三方应用行为
验证为什么第三方应用能检测到更多水印
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkDetector, WatermarkVisualization
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def compare_detection_methods(image_path: str, output_dir: str = "./comparison_results"):
    """对比不同检测方法"""
    
    # 创建输出目录
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"测试图像: {image_path}, 尺寸: {image.size}")
    
    # 初始化组件
    detector = WatermarkDetector(device="auto")
    visualizer = WatermarkVisualization()
    
    # 测试方法1: 当前项目方法
    logger.info("\n=== 方法1: 当前项目方法 ===")
    mask1, conf1, detections1 = detector.detect(
        image=image,
        confidence_threshold=0.3,
        iou_threshold=0.5,
        image_size=1024,
        augment=True,
        return_detailed_info=True,
        use_model_default_conf=False
    )
    
    logger.info(f"检测到水印数量: {len(detections1)}")
    logger.info(f"平均置信度: {conf1:.3f}")
    for i, det in enumerate(detections1):
        logger.info(f"  水印 {i+1}: 置信度={det['confidence']:.3f}, 位置={det['bbox']}")
    
    # 保存可视化
    if detections1:
        vis1 = visualizer.draw_detection_boxes(image, detections1, show_confidence=True)
        vis1.save(output_path / "method1_current_project.jpg")
    
    # 测试方法2: 模拟第三方应用方法
    logger.info("\n=== 方法2: 模拟第三方应用方法 ===")
    mask2, conf2, detections2 = detector.detect(
        image=image,
        confidence_threshold=0.3,  # 这个值会被忽略
        iou_threshold=0.5,
        image_size=1024,
        augment=True,
        return_detailed_info=True,
        use_model_default_conf=True  # 关键差异：使用模型默认置信度
    )
    
    logger.info(f"检测到水印数量: {len(detections2)}")
    logger.info(f"平均置信度: {conf2:.3f}")
    for i, det in enumerate(detections2):
        logger.info(f"  水印 {i+1}: 置信度={det['confidence']:.3f}, 位置={det['bbox']}")
    
    # 保存可视化
    if detections2:
        vis2 = visualizer.draw_detection_boxes(image, detections2, show_confidence=True)
        vis2.save(output_path / "method2_third_party_simulation.jpg")
    
    # 测试方法3: 极低置信度
    logger.info("\n=== 方法3: 极低置信度测试 ===")
    mask3, conf3, detections3 = detector.detect(
        image=image,
        confidence_threshold=0.1,
        iou_threshold=0.5,
        image_size=1024,
        augment=True,
        return_detailed_info=True,
        use_model_default_conf=False
    )
    
    logger.info(f"检测到水印数量: {len(detections3)}")
    logger.info(f"平均置信度: {conf3:.3f}")
    for i, det in enumerate(detections3):
        logger.info(f"  水印 {i+1}: 置信度={det['confidence']:.3f}, 位置={det['bbox']}")
    
    # 保存可视化
    if detections3:
        vis3 = visualizer.draw_detection_boxes(image, detections3, show_confidence=True)
        vis3.save(output_path / "method3_low_confidence.jpg")
    
    # 测试方法4: 原始YOLO调用
    logger.info("\n=== 方法4: 原始YOLO调用 ===")
    try:
        # 直接调用YOLO模型，完全模拟第三方应用
        results = detector.model(image, imgsz=1024, augment=True, iou=0.5)
        
        if len(results) > 0:
            result = results[0]
            
            if result.boxes is not None and len(result.boxes) > 0:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                
                logger.info(f"原始YOLO检测到 {len(boxes)} 个对象")
                
                raw_detections = []
                for i, (box, conf) in enumerate(zip(boxes, confidences)):
                    x1, y1, x2, y2 = map(int, box)
                    detection = {
                        'id': i,
                        'bbox': [x1, y1, x2, y2],
                        'confidence': float(conf),
                        'area': (x2 - x1) * (y2 - y1),
                        'width': x2 - x1,
                        'height': y2 - y1,
                        'aspect_ratio': (x2 - x1) / (y2 - y1) if (y2 - y1) > 0 else 0,
                        'center': [(x1 + x2) // 2, (y1 + y2) // 2]
                    }
                    raw_detections.append(detection)
                    logger.info(f"  对象 {i+1}: 置信度={conf:.3f}, 位置={box}")
                
                # 保存可视化
                if raw_detections:
                    vis4 = visualizer.draw_detection_boxes(image, raw_detections, show_confidence=True)
                    vis4.save(output_path / "method4_raw_yolo.jpg")
            else:
                logger.info("原始YOLO未检测到任何对象")
                raw_detections = []
        else:
            logger.info("原始YOLO返回空结果")
            raw_detections = []
            
    except Exception as e:
        logger.error(f"原始YOLO调用失败: {e}")
        raw_detections = []
    
    # 创建对比图
    logger.info("\n=== 创建对比图 ===")
    try:
        # 创建四种方法的对比
        methods = [
            ("当前项目方法", detections1),
            ("第三方应用模拟", detections2),
            ("极低置信度", detections3),
            ("原始YOLO", raw_detections)
        ]
        
        comparison_images = []
        for method_name, detections in methods:
            if detections:
                vis = visualizer.draw_detection_boxes(image, detections, show_confidence=True)
                comparison_images.append(vis)
            else:
                # 如果没有检测到，使用原图
                comparison_images.append(image.copy())
        
        # 创建2x2网格对比图
        if len(comparison_images) >= 4:
            grid_image = create_comparison_grid(comparison_images, methods)
            grid_image.save(output_path / "comparison_grid.jpg")
            logger.info(f"对比图已保存: {output_path / 'comparison_grid.jpg'}")
        
    except Exception as e:
        logger.error(f"创建对比图失败: {e}")
    
    # 总结分析
    logger.info("\n" + "="*60)
    logger.info("检测结果总结:")
    logger.info("="*60)
    
    results = [
        ("当前项目方法", len(detections1), conf1),
        ("第三方应用模拟", len(detections2), conf2),
        ("极低置信度", len(detections3), conf3),
        ("原始YOLO", len(raw_detections), sum(d['confidence'] for d in raw_detections) / len(raw_detections) if raw_detections else 0.0)
    ]
    
    for method, count, avg_conf in results:
        logger.info(f"{method}: {count} 个水印, 平均置信度: {avg_conf:.3f}")
    
    # 找出最佳方法
    max_count = max(count for _, count, _ in results)
    best_methods = [method for method, count, _ in results if count == max_count]
    
    if max_count > 0:
        logger.info(f"\n最佳检测方法（检测到 {max_count} 个水印）:")
        for method in best_methods:
            logger.info(f"  - {method}")
    else:
        logger.info("\n所有方法都未检测到水印")
    
    return results


def create_comparison_grid(images, method_names):
    """创建2x2对比网格"""
    from PIL import Image, ImageDraw, ImageFont
    
    # 确保所有图像大小相同
    base_size = images[0].size
    resized_images = []
    for img in images:
        if img.size != base_size:
            resized_images.append(img.resize(base_size, Image.LANCZOS))
        else:
            resized_images.append(img)
    
    # 创建网格
    grid_width = base_size[0] * 2 + 60  # 两列，中间留间隔
    grid_height = base_size[1] * 2 + 120  # 两行，上下留标题空间
    
    grid = Image.new('RGB', (grid_width, grid_height), (255, 255, 255))
    
    # 粘贴图像
    positions = [
        (20, 40),  # 左上
        (base_size[0] + 40, 40),  # 右上
        (20, base_size[1] + 80),  # 左下
        (base_size[0] + 40, base_size[1] + 80)  # 右下
    ]
    
    for i, (img, pos) in enumerate(zip(resized_images, positions)):
        grid.paste(img, pos)
        
        # 添加标题
        draw = ImageDraw.Draw(grid)
        try:
            font = ImageFont.truetype("arial.ttf", 16)
        except:
            font = ImageFont.load_default()
        
        title = method_names[i][0]
        title_pos = (pos[0], pos[1] - 25)
        draw.text(title_pos, title, fill=(0, 0, 0), font=font)
    
    return grid


def main():
    parser = argparse.ArgumentParser(description="测试第三方应用行为")
    parser.add_argument("--image", required=True, help="测试图像路径")
    parser.add_argument("--output", default="./comparison_results", help="输出目录")
    
    args = parser.parse_args()
    
    logger.info("开始第三方应用行为测试...")
    
    # 执行对比测试
    results = compare_detection_methods(args.image, args.output)
    
    logger.info(f"\n测试完成！结果已保存到: {args.output}")
    
    # 提供建议
    logger.info("\n" + "="*60)
    logger.info("建议:")
    logger.info("="*60)
    
    max_count = max(count for _, count, _ in results)
    
    if max_count > 1:
        logger.info("✅ 找到了能检测到多个水印的方法！")
        logger.info("建议:")
        logger.info("1. 使用 use_model_default_conf=True 参数")
        logger.info("2. 或者降低 confidence_threshold 到 0.1-0.25")
        logger.info("3. 检查第三方应用使用的具体模型版本")
    else:
        logger.info("⚠️  所有方法检测到的水印数量都不超过1个")
        logger.info("可能的原因:")
        logger.info("1. 图像中确实只有一个水印")
        logger.info("2. 模型版本差异")
        logger.info("3. 需要调整其他参数（如IoU阈值）")


if __name__ == "__main__":
    main()
