"""
调试检测对比脚本
对比不同参数设置下的检测结果
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from models import WatermarkDetector
from PIL import Image
import argparse
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def test_detection_parameters(image_path: str):
    """测试不同参数下的检测结果"""
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    logger.info(f"测试图像: {image_path}, 尺寸: {image.size}")
    
    # 初始化检测器
    detector = WatermarkDetector(device="auto")
    
    # 测试不同的参数组合
    test_configs = [
        {
            "name": "第三方应用模拟（无conf参数）",
            "params": {
                "confidence_threshold": 0.001,  # 设置极低值模拟不设置conf
                "iou_threshold": 0.5,
                "image_size": 1024,
                "augment": True
            }
        },
        {
            "name": "当前项目默认参数",
            "params": {
                "confidence_threshold": 0.3,
                "iou_threshold": 0.5,
                "image_size": 1024,
                "augment": True
            }
        },
        {
            "name": "更低置信度测试",
            "params": {
                "confidence_threshold": 0.1,
                "iou_threshold": 0.5,
                "image_size": 1024,
                "augment": True
            }
        },
        {
            "name": "YOLO默认置信度",
            "params": {
                "confidence_threshold": 0.25,
                "iou_threshold": 0.5,
                "image_size": 1024,
                "augment": True
            }
        },
        {
            "name": "更低IoU阈值",
            "params": {
                "confidence_threshold": 0.3,
                "iou_threshold": 0.3,
                "image_size": 1024,
                "augment": True
            }
        }
    ]
    
    results = []
    
    for config in test_configs:
        logger.info(f"\n=== {config['name']} ===")
        logger.info(f"参数: {config['params']}")
        
        try:
            mask, avg_confidence, detection_info = detector.detect(
                image=image,
                return_detailed_info=True,
                **config['params']
            )
            
            logger.info(f"检测到水印数量: {len(detection_info)}")
            logger.info(f"平均置信度: {avg_confidence:.3f}")
            
            for i, detection in enumerate(detection_info):
                logger.info(f"  水印 {i+1}: 置信度={detection['confidence']:.3f}, "
                           f"位置={detection['bbox']}, 面积={detection['area']}")
            
            results.append({
                "config": config['name'],
                "count": len(detection_info),
                "avg_confidence": avg_confidence,
                "detections": detection_info
            })
            
        except Exception as e:
            logger.error(f"检测失败: {e}")
            results.append({
                "config": config['name'],
                "count": 0,
                "avg_confidence": 0.0,
                "error": str(e)
            })
    
    # 总结结果
    logger.info("\n" + "="*50)
    logger.info("检测结果总结:")
    logger.info("="*50)
    
    for result in results:
        if "error" in result:
            logger.info(f"{result['config']}: 错误 - {result['error']}")
        else:
            logger.info(f"{result['config']}: {result['count']} 个水印, "
                       f"平均置信度: {result['avg_confidence']:.3f}")
    
    return results


def test_raw_yolo_inference(image_path: str):
    """测试原始YOLO推理（模拟第三方应用）"""
    
    logger.info("\n" + "="*50)
    logger.info("原始YOLO推理测试（模拟第三方应用）")
    logger.info("="*50)
    
    # 加载图像
    image = Image.open(image_path).convert('RGB')
    
    # 初始化检测器
    detector = WatermarkDetector(device="auto")
    
    # 直接调用YOLO模型，不设置conf参数（模拟第三方应用）
    try:
        results = detector.model(image, imgsz=1024, augment=True, iou=0.5)
        
        if len(results) > 0:
            result = results[0]
            
            if result.boxes is not None and len(result.boxes) > 0:
                boxes = result.boxes.xyxy.cpu().numpy()
                confidences = result.boxes.conf.cpu().numpy()
                
                logger.info(f"原始YOLO检测结果:")
                logger.info(f"检测到 {len(boxes)} 个对象")
                
                for i, (box, conf) in enumerate(zip(boxes, confidences)):
                    logger.info(f"  对象 {i+1}: 置信度={conf:.3f}, 位置={box}")
            else:
                logger.info("原始YOLO未检测到任何对象")
        else:
            logger.info("原始YOLO返回空结果")
            
    except Exception as e:
        logger.error(f"原始YOLO推理失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="调试检测参数对比")
    parser.add_argument("--image", required=True, help="测试图像路径")
    
    args = parser.parse_args()
    
    logger.info("开始检测参数对比测试...")
    
    # 测试不同参数
    results = test_detection_parameters(args.image)
    
    # 测试原始YOLO推理
    test_raw_yolo_inference(args.image)
    
    logger.info("\n测试完成！")
    
    # 分析建议
    logger.info("\n" + "="*50)
    logger.info("分析建议:")
    logger.info("="*50)
    
    max_detections = max(r.get('count', 0) for r in results)
    best_configs = [r for r in results if r.get('count', 0) == max_detections and max_detections > 0]
    
    if best_configs:
        logger.info(f"最佳检测数量: {max_detections} 个水印")
        logger.info("最佳配置:")
        for config in best_configs:
            logger.info(f"  - {config['config']}")
    else:
        logger.info("所有配置都未检测到水印，建议:")
        logger.info("  1. 检查图像是否包含水印")
        logger.info("  2. 尝试更低的置信度阈值")
        logger.info("  3. 检查模型是否正确加载")


if __name__ == "__main__":
    main()
