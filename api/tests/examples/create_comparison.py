"""
创建对比图
将测试结果组合成对比图像
"""

from PIL import Image, ImageDraw, ImageFont
import sys
from pathlib import Path

def create_comparison_grid():
    """创建对比网格图"""
    
    # 检查文件是否存在
    files = {
        "original": "images/test_image2.jpeg",
        "traditional": "traditional_result.jpg", 
        "smart": "smart_result.jpg",
        "original_mask": "original_mask.png",
        "enhanced_mask": "enhanced_mask.png"
    }
    
    missing_files = []
    for name, path in files.items():
        if not Path(path).exists():
            missing_files.append(f"{name}: {path}")
    
    if missing_files:
        print("缺少以下文件:")
        for file in missing_files:
            print(f"  - {file}")
        return
    
    # 加载图像
    images = {}
    for name, path in files.items():
        try:
            img = Image.open(path)
            if img.mode != 'RGB':
                img = img.convert('RGB')
            images[name] = img
            print(f"✅ 加载 {name}: {img.size}")
        except Exception as e:
            print(f"❌ 加载 {name} 失败: {e}")
            return
    
    # 设置目标大小
    target_size = (300, 400)  # 适应原图的纵向比例
    
    # 调整所有图像大小
    resized_images = {}
    for name, img in images.items():
        resized = img.resize(target_size, Image.LANCZOS)
        resized_images[name] = resized
    
    # 创建网格布局 (2行3列)
    # 第一行: 原图, 传统方法, 智能方法
    # 第二行: 空白, 原始掩码, 增强掩码
    
    cols = 3
    rows = 2
    gap = 20
    title_height = 30
    
    grid_width = cols * target_size[0] + (cols + 1) * gap
    grid_height = rows * target_size[1] + (rows + 1) * gap + rows * title_height
    
    # 创建白色背景
    grid = Image.new('RGB', (grid_width, grid_height), (255, 255, 255))
    draw = ImageDraw.Draw(grid)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 16)
        title_font = ImageFont.truetype("arial.ttf", 20)
    except:
        font = ImageFont.load_default()
        title_font = ImageFont.load_default()
    
    # 定义布局
    layout = [
        # 第一行
        ("original", "原图", 0, 0),
        ("traditional", "传统掩码增强", 1, 0),
        ("smart", "智能掩码增强", 2, 0),
        # 第二行
        ("original_mask", "原始检测掩码", 1, 1),
        ("enhanced_mask", "智能增强掩码", 2, 1)
    ]
    
    # 放置图像和标题
    for img_name, title, col, row in layout:
        if img_name in resized_images:
            x = gap + col * (target_size[0] + gap)
            y = gap + title_height + row * (target_size[1] + gap + title_height)
            
            # 粘贴图像
            grid.paste(resized_images[img_name], (x, y))
            
            # 添加标题
            title_x = x + target_size[0] // 2
            title_y = y - 25
            
            # 计算文本宽度以居中
            bbox = draw.textbbox((0, 0), title, font=font)
            text_width = bbox[2] - bbox[0]
            title_x -= text_width // 2
            
            draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    # 添加总标题
    main_title = "掩码增强效果对比"
    bbox = draw.textbbox((0, 0), main_title, font=title_font)
    text_width = bbox[2] - bbox[0]
    title_x = (grid_width - text_width) // 2
    draw.text((title_x, 10), main_title, fill=(0, 0, 0), font=title_font)
    
    # 保存对比图
    output_path = "mask_enhancement_comparison.jpg"
    grid.save(output_path, 'JPEG', quality=95)
    print(f"✅ 对比图已保存: {output_path}")
    
    # 创建简化的并排对比
    create_simple_comparison(resized_images)


def create_simple_comparison(resized_images):
    """创建简单的三图并排对比"""
    
    target_size = resized_images['original'].size
    gap = 20
    title_height = 40
    
    # 创建三图并排
    width = target_size[0] * 3 + gap * 4
    height = target_size[1] + title_height + gap * 2
    
    comparison = Image.new('RGB', (width, height), (255, 255, 255))
    draw = ImageDraw.Draw(comparison)
    
    # 尝试加载字体
    try:
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        font = ImageFont.load_default()
    
    # 图像和标题
    items = [
        (resized_images['original'], "原图"),
        (resized_images['traditional'], "传统方法"),
        (resized_images['smart'], "智能方法")
    ]
    
    # 放置图像
    for i, (img, title) in enumerate(items):
        x = gap + i * (target_size[0] + gap)
        y = title_height + gap
        
        # 粘贴图像
        comparison.paste(img, (x, y))
        
        # 添加标题
        bbox = draw.textbbox((0, 0), title, font=font)
        text_width = bbox[2] - bbox[0]
        title_x = x + (target_size[0] - text_width) // 2
        title_y = 10
        
        draw.text((title_x, title_y), title, fill=(0, 0, 0), font=font)
    
    # 保存简单对比图
    output_path = "simple_comparison.jpg"
    comparison.save(output_path, 'JPEG', quality=95)
    print(f"✅ 简单对比图已保存: {output_path}")


def main():
    print("创建掩码增强效果对比图...")
    create_comparison_grid()
    print("\n对比图创建完成！")
    print("生成的文件:")
    print("- mask_enhancement_comparison.jpg: 完整对比图")
    print("- simple_comparison.jpg: 简单三图对比")
    print("\n请查看这些图像来评估智能掩码增强的效果。")


if __name__ == "__main__":
    main()
