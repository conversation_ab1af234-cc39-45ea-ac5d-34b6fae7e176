"""
调试LAMA输出
对比原图和修复结果，检查RGB通道问题
"""

import sys
from pathlib import Path
sys.path.append(str(Path(__file__).parent))

from PIL import Image
import numpy as np
import matplotlib.pyplot as plt
import argparse
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def analyze_image_channels(image_path: str, title: str):
    """分析图像的RGB通道"""
    
    image = Image.open(image_path).convert('RGB')
    image_array = np.array(image)
    
    logger.info(f"\n=== {title} ===")
    logger.info(f"图像尺寸: {image.size}")
    logger.info(f"数组形状: {image_array.shape}")
    logger.info(f"数据类型: {image_array.dtype}")
    logger.info(f"数值范围: [{image_array.min()}, {image_array.max()}]")
    
    # 分析各通道
    r_channel = image_array[:, :, 0]
    g_channel = image_array[:, :, 1]
    b_channel = image_array[:, :, 2]
    
    logger.info(f"R通道范围: [{r_channel.min()}, {r_channel.max()}], 均值: {r_channel.mean():.1f}")
    logger.info(f"G通道范围: [{g_channel.min()}, {g_channel.max()}], 均值: {g_channel.mean():.1f}")
    logger.info(f"B通道范围: [{b_channel.min()}, {b_channel.max()}], 均值: {b_channel.mean():.1f}")
    
    # 检查是否有异常
    if r_channel.max() == r_channel.min():
        logger.warning("⚠️  R通道值完全相同，可能有问题")
    if g_channel.max() == g_channel.min():
        logger.warning("⚠️  G通道值完全相同，可能有问题")
    if b_channel.max() == b_channel.min():
        logger.warning("⚠️  B通道值完全相同，可能有问题")
    
    return image_array


def create_channel_comparison(original_path: str, result_path: str):
    """创建通道对比图"""
    
    try:
        # 加载图像
        original = Image.open(original_path).convert('RGB')
        result = Image.open(result_path).convert('RGB')
        
        # 转换为numpy数组
        orig_array = np.array(original)
        result_array = np.array(result)
        
        # 创建对比图
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        
        # 原图
        axes[0, 0].imshow(original)
        axes[0, 0].set_title('原图')
        axes[0, 0].axis('off')
        
        # 原图各通道
        axes[0, 1].imshow(orig_array[:, :, 0], cmap='Reds')
        axes[0, 1].set_title('原图 R通道')
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(orig_array[:, :, 1], cmap='Greens')
        axes[0, 2].set_title('原图 G通道')
        axes[0, 2].axis('off')
        
        axes[0, 3].imshow(orig_array[:, :, 2], cmap='Blues')
        axes[0, 3].set_title('原图 B通道')
        axes[0, 3].axis('off')
        
        # 修复结果
        axes[1, 0].imshow(result)
        axes[1, 0].set_title('LAMA修复结果')
        axes[1, 0].axis('off')
        
        # 修复结果各通道
        axes[1, 1].imshow(result_array[:, :, 0], cmap='Reds')
        axes[1, 1].set_title('修复结果 R通道')
        axes[1, 1].axis('off')
        
        axes[1, 2].imshow(result_array[:, :, 1], cmap='Greens')
        axes[1, 2].set_title('修复结果 G通道')
        axes[1, 2].axis('off')
        
        axes[1, 3].imshow(result_array[:, :, 2], cmap='Blues')
        axes[1, 3].set_title('修复结果 B通道')
        axes[1, 3].axis('off')
        
        plt.tight_layout()
        plt.savefig('channel_comparison.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        logger.info("✅ 通道对比图已保存: channel_comparison.png")
        
    except Exception as e:
        logger.error(f"❌ 创建通道对比图失败: {e}")


def create_difference_map(original_path: str, result_path: str):
    """创建差异图"""
    
    try:
        # 加载图像
        original = np.array(Image.open(original_path).convert('RGB'))
        result = np.array(Image.open(result_path).convert('RGB'))
        
        # 确保尺寸相同
        if original.shape != result.shape:
            logger.warning(f"图像尺寸不匹配: {original.shape} vs {result.shape}")
            return
        
        # 计算差异
        diff = np.abs(original.astype(np.float32) - result.astype(np.float32))
        diff_normalized = (diff / 255.0 * 255).astype(np.uint8)
        
        # 创建差异图
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        axes[0].imshow(original)
        axes[0].set_title('原图')
        axes[0].axis('off')
        
        axes[1].imshow(result)
        axes[1].set_title('修复结果')
        axes[1].axis('off')
        
        axes[2].imshow(diff_normalized)
        axes[2].set_title('差异图 (彩色)')
        axes[2].axis('off')
        
        # 差异的灰度图
        diff_gray = np.mean(diff, axis=2)
        axes[3].imshow(diff_gray, cmap='hot')
        axes[3].set_title('差异图 (热力图)')
        axes[3].axis('off')
        
        plt.tight_layout()
        plt.savefig('difference_map.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        logger.info("✅ 差异图已保存: difference_map.png")
        logger.info(f"📊 平均差异: {np.mean(diff):.2f}")
        logger.info(f"📊 最大差异: {np.max(diff):.2f}")
        
    except Exception as e:
        logger.error(f"❌ 创建差异图失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="调试LAMA输出")
    parser.add_argument("--original", required=True, help="原图路径")
    parser.add_argument("--result", default="real_lama_result.jpg", help="修复结果路径")
    
    args = parser.parse_args()
    
    if not Path(args.original).exists():
        logger.error(f"原图文件不存在: {args.original}")
        return
    
    if not Path(args.result).exists():
        logger.error(f"结果文件不存在: {args.result}")
        return
    
    logger.info("🔍 开始分析LAMA输出...")
    
    # 分析原图
    orig_array = analyze_image_channels(args.original, "原图分析")
    
    # 分析修复结果
    result_array = analyze_image_channels(args.result, "修复结果分析")
    
    # 创建对比图
    logger.info("\n=== 创建可视化对比 ===")
    create_channel_comparison(args.original, args.result)
    create_difference_map(args.original, args.result)
    
    # 简单的质量评估
    logger.info("\n=== 质量评估 ===")
    
    # 检查是否有明显的颜色偏移
    orig_mean = np.mean(orig_array, axis=(0, 1))
    result_mean = np.mean(result_array, axis=(0, 1))
    color_shift = np.abs(orig_mean - result_mean)
    
    logger.info(f"原图平均RGB: [{orig_mean[0]:.1f}, {orig_mean[1]:.1f}, {orig_mean[2]:.1f}]")
    logger.info(f"结果平均RGB: [{result_mean[0]:.1f}, {result_mean[1]:.1f}, {result_mean[2]:.1f}]")
    logger.info(f"颜色偏移: [{color_shift[0]:.1f}, {color_shift[1]:.1f}, {color_shift[2]:.1f}]")
    
    if np.max(color_shift) > 20:
        logger.warning("⚠️  检测到明显的颜色偏移，可能存在RGB通道问题")
    else:
        logger.info("✅ 颜色偏移在正常范围内")
    
    logger.info("\n📁 生成的文件:")
    logger.info("- channel_comparison.png: RGB通道对比图")
    logger.info("- difference_map.png: 差异热力图")


if __name__ == "__main__":
    main()
