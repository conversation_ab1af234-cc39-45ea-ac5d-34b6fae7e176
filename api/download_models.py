"""
模型下载脚本
用于下载项目所需的预训练模型文件
"""

import os
import sys
import requests
import hashlib
from pathlib import Path
from tqdm import tqdm
import logging

logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


class ModelDownloader:
    """模型下载器"""
    
    def __init__(self):
        self.models_config = {
            "yolo": {
                "name": "YOLO水印检测模型",
                "file": "models/yolo/yolo11x-train28-best.pt",
                "size_mb": 109.21,
                "url": "https://example.com/models/yolo11x-train28-best.pt",  # 替换为实际URL
                "md5": "your_yolo_model_md5_hash",  # 替换为实际MD5
                "description": "基于YOLOv11的水印检测模型，用于精确定位图像中的水印位置"
            },
            "lama": {
                "name": "LAMA图像修复模型", 
                "file": "models/lama/big-lama/models/best.ckpt",
                "size_mb": 391.05,
                "url": "https://example.com/models/big-lama-best.ckpt",  # 替换为实际URL
                "md5": "your_lama_model_md5_hash",  # 替换为实际MD5
                "description": "大型LAMA模型，用于高质量的图像修复和水印去除"
            }
        }
    
    def check_file_exists(self, file_path: str) -> bool:
        """检查文件是否存在"""
        return Path(file_path).exists()
    
    def get_file_size_mb(self, file_path: str) -> float:
        """获取文件大小（MB）"""
        if not self.check_file_exists(file_path):
            return 0
        return Path(file_path).stat().st_size / (1024 * 1024)
    
    def calculate_md5(self, file_path: str) -> str:
        """计算文件MD5哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    
    def download_file(self, url: str, file_path: str, expected_size_mb: float) -> bool:
        """下载文件"""
        try:
            # 创建目录
            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 发送请求
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            # 获取文件大小
            total_size = int(response.headers.get('content-length', 0))
            
            # 下载文件
            with open(file_path, 'wb') as f, tqdm(
                desc=f"下载 {Path(file_path).name}",
                total=total_size,
                unit='B',
                unit_scale=True,
                unit_divisor=1024,
            ) as pbar:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        pbar.update(len(chunk))
            
            # 验证文件大小
            actual_size_mb = self.get_file_size_mb(file_path)
            if abs(actual_size_mb - expected_size_mb) > 1:  # 允许1MB误差
                logger.warning(f"文件大小不匹配: 期望 {expected_size_mb:.2f}MB, 实际 {actual_size_mb:.2f}MB")
            
            return True
            
        except Exception as e:
            logger.error(f"下载失败: {e}")
            # 删除不完整的文件
            if Path(file_path).exists():
                Path(file_path).unlink()
            return False
    
    def verify_model(self, model_key: str) -> bool:
        """验证模型文件"""
        config = self.models_config[model_key]
        file_path = config["file"]
        
        if not self.check_file_exists(file_path):
            return False
        
        # 检查文件大小
        actual_size = self.get_file_size_mb(file_path)
        expected_size = config["size_mb"]
        
        if abs(actual_size - expected_size) > 1:  # 允许1MB误差
            logger.warning(f"{config['name']} 文件大小异常")
            return False
        
        # 检查MD5（如果提供）
        if config.get("md5") and config["md5"] != "your_yolo_model_md5_hash":
            actual_md5 = self.calculate_md5(file_path)
            if actual_md5 != config["md5"]:
                logger.error(f"{config['name']} MD5校验失败")
                return False
        
        return True
    
    def download_model(self, model_key: str, force: bool = False) -> bool:
        """下载指定模型"""
        config = self.models_config[model_key]
        file_path = config["file"]
        
        logger.info(f"\n📦 {config['name']}")
        logger.info(f"描述: {config['description']}")
        logger.info(f"文件: {file_path}")
        logger.info(f"大小: {config['size_mb']:.2f} MB")
        
        # 检查文件是否已存在
        if self.check_file_exists(file_path) and not force:
            if self.verify_model(model_key):
                logger.info("✅ 模型已存在且验证通过，跳过下载")
                return True
            else:
                logger.warning("⚠️  模型文件存在但验证失败，重新下载")
        
        # 检查URL是否为示例URL
        if "example.com" in config["url"]:
            logger.error("❌ 请先配置正确的模型下载URL")
            logger.info("💡 提示: 编辑 download_models.py 中的 models_config")
            return False
        
        # 下载模型
        logger.info("🚀 开始下载...")
        success = self.download_file(config["url"], file_path, config["size_mb"])
        
        if success:
            if self.verify_model(model_key):
                logger.info("✅ 下载完成并验证通过")
                return True
            else:
                logger.error("❌ 下载完成但验证失败")
                return False
        else:
            logger.error("❌ 下载失败")
            return False
    
    def download_all_models(self, force: bool = False) -> bool:
        """下载所有模型"""
        logger.info("🤖 开始下载所有模型...")
        
        success_count = 0
        total_count = len(self.models_config)
        
        for model_key in self.models_config:
            if self.download_model(model_key, force):
                success_count += 1
        
        logger.info(f"\n📊 下载结果: {success_count}/{total_count} 成功")
        
        if success_count == total_count:
            logger.info("🎉 所有模型下载完成！")
            return True
        else:
            logger.error("❌ 部分模型下载失败")
            return False
    
    def check_all_models(self) -> bool:
        """检查所有模型状态"""
        logger.info("🔍 检查模型状态...")
        
        all_ok = True
        for model_key, config in self.models_config.items():
            file_path = config["file"]
            
            if self.check_file_exists(file_path):
                if self.verify_model(model_key):
                    status = "✅ 正常"
                else:
                    status = "⚠️  异常"
                    all_ok = False
                
                size_mb = self.get_file_size_mb(file_path)
                logger.info(f"{config['name']}: {status} ({size_mb:.2f} MB)")
            else:
                logger.info(f"{config['name']}: ❌ 缺失")
                all_ok = False
        
        return all_ok
    
    def show_help(self):
        """显示帮助信息"""
        logger.info("""
🤖 模型下载工具

用法:
  python download_models.py [选项]

选项:
  --all          下载所有模型
  --yolo         只下载YOLO模型
  --lama         只下载LAMA模型
  --check        检查模型状态
  --force        强制重新下载
  --help         显示此帮助信息

示例:
  python download_models.py --all          # 下载所有模型
  python download_models.py --yolo         # 只下载YOLO模型
  python download_models.py --check        # 检查模型状态
  python download_models.py --all --force  # 强制重新下载所有模型

注意:
  首次使用前请配置正确的模型下载URL和MD5哈希值
        """)


def main():
    """主函数"""
    downloader = ModelDownloader()
    
    # 解析命令行参数
    args = sys.argv[1:]
    
    if not args or "--help" in args:
        downloader.show_help()
        return
    
    force = "--force" in args
    
    if "--check" in args:
        downloader.check_all_models()
    elif "--all" in args:
        downloader.download_all_models(force)
    elif "--yolo" in args:
        downloader.download_model("yolo", force)
    elif "--lama" in args:
        downloader.download_model("lama", force)
    else:
        logger.error("❌ 未知选项，使用 --help 查看帮助")


if __name__ == "__main__":
    main()
