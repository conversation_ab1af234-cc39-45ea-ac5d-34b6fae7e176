# 🗂️ Git属性配置文件
# 用于配置Git LFS和文件处理规则

# ==========================================
# 🤖 机器学习模型文件 (使用Git LFS)
# ==========================================

# PyTorch模型文件
*.pt filter=lfs diff=lfs merge=lfs -text
*.pth filter=lfs diff=lfs merge=lfs -text
*.ckpt filter=lfs diff=lfs merge=lfs -text

# ONNX模型文件
*.onnx filter=lfs diff=lfs merge=lfs -text

# TensorFlow模型文件
*.pb filter=lfs diff=lfs merge=lfs -text
*.h5 filter=lfs diff=lfs merge=lfs -text
*.hdf5 filter=lfs diff=lfs merge=lfs -text

# 其他模型格式
*.bin filter=lfs diff=lfs merge=lfs -text
*.safetensors filter=lfs diff=lfs merge=lfs -text
*.pkl filter=lfs diff=lfs merge=lfs -text
*.pickle filter=lfs diff=lfs merge=lfs -text
*.joblib filter=lfs diff=lfs merge=lfs -text

# ==========================================
# 🖼️ 大图像文件 (使用Git LFS)
# ==========================================

# 大图像文件 (>10MB通常使用LFS)
*.jpg filter=lfs diff=lfs merge=lfs -text
*.jpeg filter=lfs diff=lfs merge=lfs -text
*.png filter=lfs diff=lfs merge=lfs -text
*.bmp filter=lfs diff=lfs merge=lfs -text
*.tiff filter=lfs diff=lfs merge=lfs -text
*.tif filter=lfs diff=lfs merge=lfs -text

# 但小的测试图像和图标文件不使用LFS
images/test_image*.jpg -filter
images/test_image*.jpeg -filter
images/test_image*.png -filter
**/icons/*.png -filter
**/icons/*.jpg -filter
**/icons/*.jpeg -filter
**/icons/*.ico -filter
**/icons/*.icns -filter

# ==========================================
# 📊 数据文件 (使用Git LFS)
# ==========================================

# 大数据文件
*.csv filter=lfs diff=lfs merge=lfs -text
*.parquet filter=lfs diff=lfs merge=lfs -text
*.feather filter=lfs diff=lfs merge=lfs -text

# 数据库文件
*.db filter=lfs diff=lfs merge=lfs -text
*.sqlite filter=lfs diff=lfs merge=lfs -text
*.sqlite3 filter=lfs diff=lfs merge=lfs -text

# ==========================================
# 📦 压缩文件 (使用Git LFS)
# ==========================================

*.zip filter=lfs diff=lfs merge=lfs -text
*.tar.gz filter=lfs diff=lfs merge=lfs -text
*.rar filter=lfs diff=lfs merge=lfs -text
*.7z filter=lfs diff=lfs merge=lfs -text

# ==========================================
# 📝 文本文件处理
# ==========================================

# 确保文本文件使用LF换行符
*.py text eol=lf
*.md text eol=lf
*.txt text eol=lf
*.json text eol=lf
*.yaml text eol=lf
*.yml text eol=lf
*.xml text eol=lf
*.html text eol=lf
*.css text eol=lf
*.js text eol=lf
*.sh text eol=lf

# 配置文件
*.ini text eol=lf
*.cfg text eol=lf
*.conf text eol=lf
requirements.txt text eol=lf
Dockerfile text eol=lf
.gitignore text eol=lf
.gitattributes text eol=lf

# ==========================================
# 🔧 二进制文件
# ==========================================

# 明确标记为二进制文件
*.exe binary
*.dll binary
*.so binary
*.dylib binary
*.ico binary
*.pdf binary
