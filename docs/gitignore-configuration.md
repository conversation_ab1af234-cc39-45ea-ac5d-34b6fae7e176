# Git 忽略文件配置说明

## 概述

本项目的 `.gitignore` 文件已经配置为忽略所有不应该提交到版本控制的文件，包括：

## 🤖 机器学习模型文件

### YOLO 模型文件
- `*.pt`, `*.pth`, `*.onnx`, `*.weights`, `*.cfg`
- 位置：`models/yolo/`

### LAMA 模型文件  
- `*.ckpt`, `*.pt`, `*.pth`, `*.bin`, `*.safetensors`
- 位置：`models/lama/`

### 通用模型文件
- `*.h5`, `*.hdf5`, `*.pkl`, `*.pickle`, `*.joblib`
- 模型检查点：`checkpoints/`, `*.checkpoint`

## 🐍 Python 虚拟环境

### 虚拟环境目录
- `venv/`, `.venv/`, `env/`, `ENV/`
- `build_env/` (项目特定)
- `env.bak/`, `venv.bak/`

### Python 编译文件
- `__pycache__/`
- `*.py[cod]`, `*$py.class`
- `*.so`

### Python 打包文件
- `build/`, `dist/`, `*.egg-info/`
- `wheels/`, `*.egg`

## 🦀 Rust/Tauri 构建文件

### Rust 编译输出
- `target/` (全局)
- `src-tauri/target/` (Tauri 特定)
- `Cargo.lock` (应用程序级别)

### Tauri 构建产物
- `src-tauri/gen/`
- `**/bundle/`
- `*.app/`, `*.dmg`, `*.msi`, `*.deb`, `*.rpm`, `*.AppImage`

## 🌐 前端构建文件

### Node.js 依赖
- `node_modules/`
- `npm-debug.log*`, `yarn-debug.log*`, `pnpm-debug.log*`
- `.pnpm-store/`

### 构建输出
- `dist/`, `build/`
- `.vite/`, `.next/`, `.nuxt/`
- `out/`, `.output/`

### TypeScript 构建信息
- `*.tsbuildinfo`

### 代码质量工具缓存
- `.eslintcache`, `.stylelintcache`

## 🖼️ 媒体和输出文件

### 输出目录
- `outputs/`, `results/`, `temp/`, `tmp/`
- `uploads/`, `static/`

### 图像文件
- `*.jpg`, `*.jpeg`, `*.png`, `*.bmp`, `*.tiff`, `*.gif`, `*.webp`, `*.svg`
- 例外：保留小的测试图像和图标文件

### 测试生成的文件
- `*_result.jpg`, `*_mask.png`, `*_comparison.jpg`
- `enhanced_*.jpg`, `improved_*.jpg`

## 📝 日志和临时文件

### 日志文件
- `logs/`
- `*.log`, `*.log.*`

### 临时文件
- `*.tmp`, `*.temp`, `*.swp`, `*.swo`, `*~`

## 🔐 敏感信息

### 环境变量文件
- `.env`, `.env.local`, `.env.development.local`
- `.env.test.local`, `.env.production.local`
- 例外：保留 `.env.example`

### 密钥和证书
- `*.key`, `*.pem`, `*.crt`, `*.csr`
- `config.json`, `secrets.json`

### 数据库文件
- `*.db`, `*.sqlite`, `*.sqlite3`

## 🖥️ 操作系统文件

### macOS
- `.DS_Store`, `.AppleDouble`, `._*`
- `.Spotlight-V100`, `.Trashes`

### Windows
- `Thumbs.db`, `Desktop.ini`
- `$RECYCLE.BIN/`

### Linux
- `.directory`, `.Trash-*`, `.nfs*`

## 🔧 IDE 和编辑器

### VSCode
- `.vscode/`, `*.code-workspace`

### PyCharm
- `.idea/`

### 其他编辑器
- `*.sublime-project`, `*.sublime-workspace`

## 📊 数据文件

### 大数据文件
- `*.csv`, `*.json`, `*.xml`
- `*.parquet`, `*.feather`

### 例外（保留的配置文件）
- `package.json`, `package-lock.json`, `pnpm-lock.yaml`
- `tsconfig.json`, `tauri.conf.json`
- `requirements.txt`, `*.yaml`, `*.yml`

## 🎯 项目特定

### 缓存目录
- `cache/`, `.cache/`

### 测试结果
- `test_results/`, `mask_test_results/`

### 性能分析
- `*.prof`, `*.profile`

### 备份和压缩文件
- `*.bak`, `*.backup`, `*.old`
- `*.zip`, `*.tar.gz`, `*.rar`, `*.7z`

## 使用建议

1. **模型文件管理**：使用 Git LFS 或外部存储来管理大型模型文件
2. **环境配置**：提供 `.env.example` 文件作为环境变量模板
3. **文档更新**：当添加新的文件类型时，及时更新此配置
4. **定期检查**：使用 `git status` 确认没有意外提交大文件

## 验证配置

### 自动验证脚本

项目提供了自动验证脚本 `scripts/verify-gitignore.sh`：

```bash
# 运行验证脚本
./scripts/verify-gitignore.sh
```

该脚本会检查：
- 大文件是否被正确忽略
- 模型文件是否被正确忽略
- 虚拟环境是否被正确忽略
- 构建输出是否被正确忽略
- 日志和临时文件是否被正确忽略

### 手动验证命令

```bash
# 检查未跟踪的文件
git status

# 检查大文件 (>10MB)
find . -size +10M -not -path "./.git/*" -not -path "./node_modules/*" -not -path "./target/*" -not -path "./build_env/*" -type f

# 检查模型文件是否被正确忽略
git status --porcelain | grep -E "\.(pt|ckpt|bin|safetensors|h5|pkl)$" || echo "✅ 没有模型文件在 git 跟踪中"

# 检查虚拟环境是否被正确忽略
git status --porcelain | grep -E "(venv|env|build_env)" || echo "✅ 没有虚拟环境文件在 git 跟踪中"
```

## 配置验证结果

✅ **大文件已被正确忽略** - 所有 >10MB 的文件都不在 Git 跟踪中
✅ **模型文件已被正确忽略** - 所有 .pt, .ckpt, .bin 等模型文件都被忽略
✅ **虚拟环境已被正确忽略** - build_env/, venv/ 等目录都被忽略
✅ **构建输出已被正确忽略** - target/, dist/, node_modules/ 等都被忽略
✅ **日志和临时文件已被正确忽略** - logs/, temp/, outputs/ 等都被忽略
