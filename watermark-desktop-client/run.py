#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水印去除桌面客户端启动脚本
提供更友好的启动体验和错误处理
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查必要的依赖"""
    required_packages = [
        ('PySide6', 'PySide6'),
        ('torch', 'PyTorch'),
        ('PIL', 'Pillow'),
        ('numpy', 'NumPy')
    ]
    
    missing_packages = []
    
    for package, display_name in required_packages:
        try:
            __import__(package)
            print(f"✅ {display_name}")
        except ImportError:
            missing_packages.append(display_name)
            print(f"❌ {display_name} 未安装")
    
    if missing_packages:
        print(f"\n请安装缺失的依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_models():
    """检查模型文件"""
    model_files = [
        "models/yolo/yolo11x-train28-best.pt",
        "models/lama/big-lama/models/best.ckpt"
    ]
    
    missing_models = []
    
    for model_file in model_files:
        if Path(model_file).exists():
            print(f"✅ {model_file}")
        else:
            missing_models.append(model_file)
            print(f"❌ {model_file} 不存在")
    
    if missing_models:
        print(f"\n请确保以下模型文件存在:")
        for model in missing_models:
            print(f"  - {model}")
        print("\n请参考README.md获取模型文件")
        return False
    
    return True

def main():
    """主函数"""
    print("🚀 启动水印去除桌面客户端")
    print("=" * 50)

    # 检查Python版本
    print("🔍 检查Python版本...")
    if not check_python_version():
        return 1

    # 检查依赖（简化版本，直接尝试启动）
    print("\n🔍 检查依赖包...")
    try:
        import PySide6
        import torch
        import PIL
        import numpy
        print("✅ 所有依赖包已安装")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装缺失的依赖:")
        print("pip install -r requirements.txt")
        return 1

    # 检查模型文件
    print("\n🔍 检查模型文件...")
    if not check_models():
        return 1

    print("\n" + "=" * 50)
    print("✅ 所有检查通过，启动应用程序...")
    print("=" * 50)

    # 启动应用程序
    try:
        from main import main as app_main
        return app_main()
    except KeyboardInterrupt:
        print("\n👋 用户中断，应用程序退出")
        return 0
    except Exception as e:
        print(f"\n❌ 应用程序启动失败: {e}")
        print("请检查日志文件: logs/app.log")
        return 1

if __name__ == "__main__":
    sys.exit(main())
