[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "watermark-desktop-client"
version = "1.0.0"
description = "基于PySide6的水印去除桌面客户端"
authors = [
    {name = "Watermark Remover Team", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: End Users/Desktop",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Multimedia :: Graphics :: Graphics Conversion",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "PySide6>=6.5.0",
    "torch>=2.1.1",
    "torchvision>=0.16.1",
    "ultralytics>=8.0.0",
    "huggingface-hub>=0.16.0",
    "opencv-python-headless>=********",
    "pillow>=10.1.0",
    "numpy>=1.24.4",
    "psutil>=5.9.0",
    "requests>=2.28.0",
    "pydantic>=2.5.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-qt>=4.0.0",
    "pytest-cov>=4.0.0",
    "pytest-mock>=3.10.0",
    "pytest-benchmark>=4.0.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "isort>=5.12.0",
    "mypy>=1.0.0",
]
build = [
    "pyinstaller>=5.0.0",
]

[project.scripts]
watermark-remover = "main:main"

[project.urls]
Homepage = "https://github.com/watermark-remover/desktop-client"
Repository = "https://github.com/watermark-remover/desktop-client.git"
Issues = "https://github.com/watermark-remover/desktop-client/issues"

# 测试配置
[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = [
    "--verbose",
    "--tb=short",
    "--cov=src",
    "--cov-report=html",
    "--cov-report=term-missing"
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*"
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]

# Black代码格式化配置
[tool.black]
line-length = 88
target-version = ['py310']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

# isort导入排序配置
[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]

# MyPy类型检查配置
[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "torch.*",
    "torchvision.*",
    "ultralytics.*",
    "cv2.*",
    "PIL.*",
    "numpy.*",
]
ignore_missing_imports = true
