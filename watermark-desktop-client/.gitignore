# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp

# PyInstaller
*.manifest
*.spec

# AI Models (large files)
models/*.pt
models/*.ckpt
models/*.pth
models/*.bin

# Output files
outputs/
results/

# Test coverage
.coverage
htmlcov/

# Qt
*.qm
*.pro.user
