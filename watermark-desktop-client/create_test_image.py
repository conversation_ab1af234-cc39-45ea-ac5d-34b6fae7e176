#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试图片
用于测试水印去除功能
"""

from PIL import Image, ImageDraw, ImageFont
import numpy as np

def create_test_image_with_watermark():
    """创建带水印的测试图片"""
    
    # 创建基础图像 (800x600)
    width, height = 800, 600
    
    # 创建渐变背景
    image = Image.new('RGB', (width, height))
    pixels = []
    
    for y in range(height):
        for x in range(width):
            # 创建彩色渐变
            r = int(255 * (x / width))
            g = int(255 * (y / height))
            b = int(255 * ((x + y) / (width + height)))
            pixels.append((r, g, b))
    
    image.putdata(pixels)
    
    # 添加一些图案
    draw = ImageDraw.Draw(image)
    
    # 绘制一些圆形
    for i in range(5):
        x = 100 + i * 150
        y = 200 + i * 50
        draw.ellipse([x-30, y-30, x+30, y+30], fill=(255, 255, 255), outline=(0, 0, 0), width=2)
    
    # 绘制矩形
    draw.rectangle([50, 400, 750, 500], fill=(200, 200, 255), outline=(0, 0, 255), width=3)
    
    # 添加文字水印
    try:
        font = ImageFont.truetype("arial.ttf", 40)
    except:
        font = ImageFont.load_default()
    
    # 半透明水印
    watermark = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    watermark_draw = ImageDraw.Draw(watermark)
    
    # 添加多个水印文字
    watermark_texts = [
        ("SAMPLE", 150, 100),
        ("WATERMARK", 400, 300),
        ("TEST", 600, 450)
    ]
    
    for text, x, y in watermark_texts:
        watermark_draw.text((x, y), text, fill=(255, 0, 0, 128), font=font)
    
    # 合成图像
    final_image = Image.alpha_composite(image.convert('RGBA'), watermark).convert('RGB')
    
    return final_image

def main():
    """主函数"""
    print("🎨 创建测试图片...")
    
    # 创建测试图片
    test_image = create_test_image_with_watermark()
    
    # 保存图片
    output_path = "test_image_with_watermark.jpg"
    test_image.save(output_path, 'JPEG', quality=95)
    
    print(f"✅ 测试图片已保存: {output_path}")
    print(f"📏 图片尺寸: {test_image.size}")
    print("💡 您可以将此图片拖拽到应用程序中测试水印去除功能")

if __name__ == "__main__":
    main()
