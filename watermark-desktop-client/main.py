# -*- coding: utf-8 -*-
"""
水印去除桌面客户端 - 主入口文件
基于PySide6 + PyInstaller的跨平台桌面应用
"""

import sys
import os
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt

from src.utils.app_initializer import initialize_app, cleanup_app, get_qt_app
from src.utils.logger import get_logger
from src.utils.style_manager import style_manager
from src.views.redesigned_main_window import RedesignedMainWindow
from src.services.ai_service import ai_service
from src.services.task_processor import task_processor


class WatermarkRemoverApp:
    """水印去除应用程序主类"""

    def __init__(self):
        self.logger = None
        self.main_window = None
        self.task_list_widget = None
        self.result_compare_widget = None
        self.settings_dialog = None

    def run(self):
        """运行应用程序"""
        try:
            # 初始化应用程序
            if not initialize_app():
                self.show_error("应用程序初始化失败")
                return 1

            self.logger = get_logger(__name__)
            self.logger.info("启动水印去除桌面客户端")

            # 创建主窗口
            self.create_main_window()

            # 设置应用程序属性
            app = get_qt_app()
            app.setQuitOnLastWindowClosed(True)

            # 应用现代化主题
            self.logger.info("应用现代化主题...")
            if style_manager.apply_theme("modern"):
                self.logger.info("现代化主题应用成功")
            else:
                self.logger.warning("现代化主题应用失败，使用默认样式")

            # 显示主窗口
            self.main_window.show()

            # 运行应用程序
            result = app.exec()

            # 清理资源
            self.cleanup()

            return result

        except Exception as e:
            self.show_error(f"应用程序运行失败: {e}")
            return 1

    def create_main_window(self):
        """创建主窗口和组件"""
        # 创建重新设计的主窗口
        self.main_window = RedesignedMainWindow()

        # 连接信号
        self.setup_connections()

    def setup_connections(self):
        """设置信号连接"""
        # 主窗口信号连接
        self.main_window.files_selected.connect(self.on_files_selected)
        self.main_window.processing_requested.connect(self.on_processing_requested)

    def on_files_selected(self, files):
        """文件选择事件"""
        self.logger.info(f"选择了 {len(files)} 个文件")
        # 这里可以添加文件验证逻辑

    def on_processing_requested(self, image_items, settings):
        """处理请求事件"""
        self.logger.info(f"请求处理 {len(image_items)} 个图片")
        self.logger.info(f"处理设置: {settings}")

        try:
            # 创建处理任务
            from src.models.task_model import Task
            from src.utils.constants import TaskType, TaskStatus
            import uuid

            tasks = []
            for image_item in image_items:
                task = Task(
                    id=str(uuid.uuid4()),
                    type=TaskType.WATERMARK_REMOVAL,
                    input_path=Path(image_item.file_path),
                    output_path=Path(self._get_output_path(image_item, settings)),
                    status=TaskStatus.PENDING
                )
                tasks.append(task)

                # 更新图片项状态
                image_item.status = "PROCESSING"

            # 刷新界面显示
            self.main_window.refresh_image_list()

            # 启动任务处理器
            task_processor.start_processing()

            # 提交任务到处理队列
            for task in tasks:
                task_processor.submit_task(task)

            # 连接任务完成信号
            task_processor.task_completed.connect(self.on_task_completed)
            task_processor.task_failed.connect(self.on_task_failed)
            task_processor.all_tasks_completed.connect(self.on_all_tasks_completed)

            self.logger.info(f"已添加 {len(tasks)} 个任务到处理队列")

        except Exception as e:
            self.logger.error(f"创建处理任务失败: {e}")
            self.main_window.on_processing_failed(str(e))

    def _get_output_path(self, image_item, settings):
        """获取输出路径"""
        from pathlib import Path

        # 获取保存路径，如果为空或无效则使用默认路径
        save_path_str = settings.get("save_path", "")
        if not save_path_str or save_path_str.strip() == "":
            # 使用项目根目录下的outputs文件夹
            save_path = Path("outputs")
        else:
            save_path = Path(save_path_str)

        # 确保目录存在
        try:
            save_path.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            self.logger.warning(f"无法创建输出目录 {save_path}，使用默认目录: {e}")
            save_path = Path("outputs")
            save_path.mkdir(parents=True, exist_ok=True)

        if settings.get("keep_original_name", True):
            # 保留原始名称，添加processed前缀
            original_name = Path(image_item.file_name).stem
            ext = Path(image_item.file_name).suffix or '.jpg'
            filename = f"processed_{original_name}{ext}"
        else:
            # 使用时间戳命名
            timestamp = int(time.time())
            ext = Path(image_item.file_path).suffix or '.jpg'
            filename = f"watermark_removed_{timestamp}{ext}"

        output_path = save_path / filename
        self.logger.info(f"输出路径: {output_path}")
        return str(output_path)

    def on_task_completed(self, task_id: str, result: dict):
        """任务完成事件"""
        self.logger.info(f"任务完成: {task_id}")
        self.logger.info(f"处理结果: {result}")

        # 获取输入路径
        input_path = result.get("input_path")
        if not input_path:
            self.logger.error(f"任务结果缺少input_path: {result}")
            return

        # 更新对应的图片项状态
        updated = False
        self.logger.info(f"查找图片项，输入路径: {input_path}")
        self.logger.info(f"当前图片项数量: {len(self.main_window.image_items)}")

        for i, image_item in enumerate(self.main_window.image_items):
            item_path = str(image_item.file_path)
            self.logger.info(f"图片项 {i}: {item_path}")

            # 使用路径比较，支持不同的路径格式
            if (item_path == input_path or
                Path(item_path).resolve() == Path(input_path).resolve()):

                # 检查处理是否成功
                if result.get("success", False):
                    image_item.status = "COMPLETED"
                    image_item.processed_path = result.get("output_path")
                    self.logger.info(f"图片处理成功: {image_item.file_name} -> {image_item.processed_path}")
                else:
                    image_item.status = "FAILED"
                    self.logger.error(f"图片处理失败: {image_item.file_name}, 错误: {result.get('error', '未知错误')}")
                updated = True
                break

        if not updated:
            self.logger.warning(f"未找到对应的图片项: {input_path}")
            self.logger.warning("可能的原因: 路径格式不匹配或图片项已被删除")

        # 刷新界面显示
        self.main_window.refresh_image_list()

    def on_task_failed(self, task_id: str, error: str):
        """任务失败事件"""
        self.logger.error(f"任务失败: {task_id}, 错误: {error}")

        # 更新对应的图片项状态
        for image_item in self.main_window.image_items:
            # 这里需要通过任务ID找到对应的图片项
            # 简化处理，标记为失败
            if image_item.status == "PROCESSING":
                image_item.status = "FAILED"
                break

        # 刷新界面显示
        self.main_window.refresh_image_list()

    def on_all_tasks_completed(self):
        """所有任务完成事件"""
        self.logger.info("所有任务处理完成")
        self.main_window.on_processing_completed()

        # 显示完成统计
        completed_count = sum(1 for item in self.main_window.image_items if item.status == "COMPLETED")
        failed_count = sum(1 for item in self.main_window.image_items if item.status == "FAILED")

        QMessageBox.information(
            self.main_window,
            "处理完成",
            f"批量处理完成！\n"
            f"成功: {completed_count} 张\n"
            f"失败: {failed_count} 张"
        )



    def show_error(self, message: str):
        """显示错误消息"""
        if QApplication.instance():
            QMessageBox.critical(None, "错误", message)
        else:
            print(f"错误: {message}")

    def cleanup(self):
        """清理资源"""
        if self.logger:
            self.logger.info("正在清理应用程序资源...")

        # 清理资源（如果需要的话）
        pass

        # 清理应用程序
        cleanup_app()

        if self.logger:
            self.logger.info("应用程序退出")


def main():
    """主函数"""
    # 设置高DPI支持 (Qt6中这些属性默认启用，无需手动设置)
    # QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)  # Qt6中已弃用
    # QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)     # Qt6中已弃用

    # Qt6中高DPI支持默认启用，如果需要可以设置缩放策略
    try:
        QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
    except AttributeError:
        # 如果Qt版本不支持此方法，则忽略
        pass

    # 创建并运行应用程序
    app = WatermarkRemoverApp()
    return app.run()


if __name__ == "__main__":
    sys.exit(main())
