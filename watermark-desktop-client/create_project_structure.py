#!/usr/bin/env python3
"""
创建水印去除桌面客户端项目结构
基于设计文档的标准化目录结构
"""

import os
from pathlib import Path

def create_project_structure():
    """创建项目目录结构"""
    
    # 项目根目录
    project_root = Path(__file__).parent
    
    # 定义目录结构
    directories = [
        # 源代码目录
        "src",
        "src/models",
        "src/views", 
        "src/presenters",
        "src/services",
        "src/utils",
        "src/resources",
        "src/resources/ui",
        "src/resources/icons", 
        "src/resources/styles",
        "src/resources/translations",
        
        # AI模型目录
        "models",
        "models/yolo",
        "models/lama",
        "models/lama/big-lama",
        "models/lama/big-lama/models",
        
        # 配置目录
        "config",
        
        # 测试目录
        "tests",
        "tests/unit",
        "tests/integration", 
        "tests/gui",
        
        # 文档目录
        "docs",
        
        # 静态资源
        "assets",
        "assets/icons",
        "assets/images",
        
        # 输出目录
        "outputs",
        "temp",
        
        # 日志目录
        "logs",
        
        # 构建目录
        "build",
        "dist"
    ]
    
    # 创建目录
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {directory}")
        
        # 创建 __init__.py 文件（仅对Python包目录）
        if directory.startswith("src/") and not directory.endswith(("ui", "icons", "styles", "translations")):
            init_file = dir_path / "__init__.py"
            if not init_file.exists():
                init_file.write_text("# -*- coding: utf-8 -*-\n")
                print(f"   📄 创建 __init__.py: {directory}")
    
    # 创建基础文件
    files_to_create = [
        ("src/__init__.py", "# -*- coding: utf-8 -*-\n"),
        ("tests/__init__.py", "# -*- coding: utf-8 -*-\n"),
        ("README.md", "# 水印去除桌面客户端\n\n基于PySide6 + PyInstaller的跨平台桌面应用\n"),
        (".gitignore", create_gitignore_content()),
        ("main.py", "# -*- coding: utf-8 -*-\n# 应用程序入口文件\n"),
    ]
    
    for file_path, content in files_to_create:
        file_full_path = project_root / file_path
        if not file_full_path.exists():
            file_full_path.write_text(content, encoding='utf-8')
            print(f"📄 创建文件: {file_path}")
    
    print(f"\n🎉 项目结构创建完成！")
    print(f"📁 项目根目录: {project_root}")

def create_gitignore_content():
    """创建.gitignore文件内容"""
    return """# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
env/
ENV/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
logs/
*.log

# Temporary files
temp/
tmp/
*.tmp

# PyInstaller
*.manifest
*.spec

# AI Models (large files)
models/*.pt
models/*.ckpt
models/*.pth
models/*.bin

# Output files
outputs/
results/

# Test coverage
.coverage
htmlcov/

# Qt
*.qm
*.pro.user
"""

if __name__ == "__main__":
    create_project_structure()
