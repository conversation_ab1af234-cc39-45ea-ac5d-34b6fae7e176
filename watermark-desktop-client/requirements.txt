# Watermark Removal Desktop Client Dependencies
# Based on actual watermark removal system + PySide6 GUI framework

# GUI Framework
PySide6>=6.5.0

# AI Framework and Models
torch>=2.1.1
torchvision>=0.16.1
ultralytics>=8.0.0              # YOLO11 model support
huggingface-hub>=0.16.0         # LAMA model support

# Image Processing
opencv-python-headless>=4.8.1.78
pillow>=10.1.0
numpy>=1.24.4

# System Tools
psutil>=5.9.0                   # System monitoring
requests>=2.28.0                # Network requests

# Data Processing and Validation
pydantic>=2.5.0                 # Data validation

# Packaging Tools
pyinstaller>=5.0.0

# Development and Testing Dependencies
pytest>=7.0.0
pytest-qt>=4.0.0               # Qt GUI testing
pytest-cov>=4.0.0              # Code coverage
pytest-mock>=3.10.0            # Mock testing
pytest-benchmark>=4.0.0        # Performance testing
pytest-xvfb>=2.0.0             # Linux GUI testing

# Other Tools
python-multipart>=0.0.6        # File processing
aiohttp>=3.9.1                 # Async HTTP (if needed)
