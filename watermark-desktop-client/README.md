# 水印去除桌面客户端

基于PySide6 + PyTorch的智能水印检测和去除工具，集成YOLO11检测模型和LAMA修复模型。

## 🌟 功能特性

- **智能水印检测**: 基于YOLO11的高精度水印检测
- **高质量修复**: 使用LAMA模型进行图像修复
- **批量处理**: 支持多文件批量处理
- **实时预览**: 原图与处理结果对比显示
- **拖拽操作**: 支持文件拖拽添加
- **参数调节**: 可调节检测置信度、修复参数等
- **跨平台**: 支持Windows、macOS、Linux

## 📋 系统要求

- Python 3.8+
- PyTorch 1.8+
- PySide6 6.0+
- 8GB+ RAM (推荐)
- GPU支持 (可选，用于加速)

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 模型文件

确保以下模型文件存在：
- `models/yolo/yolo11x-train28-best.pt` - YOLO11水印检测模型
- `models/lama/big-lama/models/best.ckpt` - LAMA图像修复模型

### 3. 运行应用

```bash
# 启动桌面客户端
python main.py

# 或运行测试
python test_app.py
```

## 📖 使用指南

### 基本操作

1. **添加图片**
   - 点击"打开文件"按钮选择图片
   - 或直接拖拽图片文件到任务列表区域
   - 支持格式：JPG、PNG、WebP、BMP、TIFF

2. **开始处理**
   - 点击"开始处理"按钮
   - 查看任务列表中的处理进度
   - 在右侧查看处理结果对比

3. **保存结果**
   - 在结果对比区域点击"保存结果"
   - 选择保存位置和文件名
