# -*- coding: utf-8 -*-
"""
任务模型定义
包含任务数据结构、状态管理和处理参数
"""

import uuid
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum

from src.utils.constants import TaskStatus, TaskType


@dataclass
class ProcessingParams:
    """处理参数"""
    confidence_threshold: float = 0.3
    enhance_mask: bool = True
    use_smart_enhancement: bool = True
    context_expansion_ratio: float = 0.12
    use_gpu: bool = True
    
    def validate(self) -> bool:
        """验证参数有效性"""
        if not (0.1 <= self.confidence_threshold <= 0.9):
            raise ValueError("置信度阈值必须在0.1-0.9之间")
        
        if not (0.05 <= self.context_expansion_ratio <= 0.3):
            raise ValueError("上下文扩展比例必须在0.05-0.3之间")
        
        return True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'confidence_threshold': self.confidence_threshold,
            'enhance_mask': self.enhance_mask,
            'use_smart_enhancement': self.use_smart_enhancement,
            'context_expansion_ratio': self.context_expansion_ratio,
            'use_gpu': self.use_gpu
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProcessingParams':
        """从字典创建"""
        return cls(**data)


@dataclass
class ProcessingResult:
    """处理结果"""
    success: bool
    output_path: Optional[str] = None
    processing_time: float = 0.0
    detection_time: float = 0.0
    inpainting_time: float = 0.0
    watermark_count: int = 0
    confidence: float = 0.0
    watermark_detected: bool = False
    detection_info: List[Dict[str, Any]] = field(default_factory=list)
    error: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'success': self.success,
            'output_path': self.output_path,
            'processing_time': self.processing_time,
            'detection_time': self.detection_time,
            'inpainting_time': self.inpainting_time,
            'watermark_count': self.watermark_count,
            'confidence': self.confidence,
            'watermark_detected': self.watermark_detected,
            'detection_info': self.detection_info,
            'error': self.error
        }


@dataclass
class Task:
    """任务模型"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: TaskType = TaskType.WATERMARK_REMOVAL
    input_path: Path = None
    output_path: Optional[Path] = None
    status: TaskStatus = TaskStatus.PENDING
    params: ProcessingParams = field(default_factory=ProcessingParams)
    result: Optional[ProcessingResult] = None
    created_at: datetime = field(default_factory=datetime.now)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    progress: float = 0.0
    error_message: Optional[str] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.input_path and not self.output_path:
            # 自动生成输出路径
            input_path = Path(self.input_path)
            output_dir = Path("outputs")
            output_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_name = f"nowatermark_{timestamp}_{self.id[:8]}{input_path.suffix}"
            self.output_path = output_dir / output_name
    
    @property
    def duration(self) -> Optional[float]:
        """获取处理时长（秒）"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
    
    @property
    def is_completed(self) -> bool:
        """是否已完成"""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
    
    @property
    def is_processing(self) -> bool:
        """是否正在处理"""
        return self.status in [TaskStatus.LOADING, TaskStatus.DETECTING, TaskStatus.PROCESSING, TaskStatus.SAVING]
    
    def start(self):
        """开始处理"""
        self.status = TaskStatus.LOADING
        self.started_at = datetime.now()
        self.progress = 0.0
    
    def update_progress(self, progress: float, status: Optional[TaskStatus] = None):
        """更新进度"""
        self.progress = max(0.0, min(1.0, progress))
        if status:
            self.status = status
    
    def complete(self, result: ProcessingResult):
        """完成处理"""
        self.result = result
        self.status = TaskStatus.COMPLETED if result.success else TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.progress = 1.0
        
        if not result.success and result.error:
            self.error_message = result.error
    
    def cancel(self):
        """取消处理"""
        self.status = TaskStatus.CANCELLED
        self.completed_at = datetime.now()
        self.error_message = "用户取消"
    
    def fail(self, error_message: str):
        """处理失败"""
        self.status = TaskStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
        self.progress = 0.0
    
    def reset(self):
        """重置任务状态"""
        self.status = TaskStatus.PENDING
        self.result = None
        self.started_at = None
        self.completed_at = None
        self.progress = 0.0
        self.error_message = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'input_path': str(self.input_path) if self.input_path else None,
            'output_path': str(self.output_path) if self.output_path else None,
            'status': self.status.value,
            'params': self.params.to_dict(),
            'result': self.result.to_dict() if self.result else None,
            'created_at': self.created_at.isoformat(),
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'progress': self.progress,
            'error_message': self.error_message
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Task':
        """从字典创建任务"""
        task = cls()
        task.id = data.get('id', task.id)
        task.input_path = Path(data['input_path']) if data.get('input_path') else None
        task.output_path = Path(data['output_path']) if data.get('output_path') else None
        task.status = TaskStatus(data.get('status', TaskStatus.PENDING.value))
        task.params = ProcessingParams.from_dict(data.get('params', {}))
        
        if data.get('result'):
            task.result = ProcessingResult(**data['result'])
        
        if data.get('created_at'):
            task.created_at = datetime.fromisoformat(data['created_at'])
        if data.get('started_at'):
            task.started_at = datetime.fromisoformat(data['started_at'])
        if data.get('completed_at'):
            task.completed_at = datetime.fromisoformat(data['completed_at'])
        
        task.progress = data.get('progress', 0.0)
        task.error_message = data.get('error_message')
        
        return task


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.task_order: List[str] = []  # 保持任务顺序
    
    def add_task(self, task: Task) -> str:
        """添加任务"""
        self.tasks[task.id] = task
        self.task_order.append(task.id)
        return task.id
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if task_id in self.tasks:
            del self.tasks[task_id]
            if task_id in self.task_order:
                self.task_order.remove(task_id)
            return True
        return False
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务（按添加顺序）"""
        return [self.tasks[task_id] for task_id in self.task_order if task_id in self.tasks]
    
    def get_tasks_by_status(self, status: TaskStatus) -> List[Task]:
        """按状态获取任务"""
        return [task for task in self.tasks.values() if task.status == status]
    
    def get_pending_tasks(self) -> List[Task]:
        """获取待处理任务"""
        return self.get_tasks_by_status(TaskStatus.PENDING)
    
    def get_processing_tasks(self) -> List[Task]:
        """获取正在处理的任务"""
        return [task for task in self.tasks.values() if task.is_processing]
    
    def get_completed_tasks(self) -> List[Task]:
        """获取已完成任务"""
        return [task for task in self.tasks.values() if task.is_completed]
    
    def clear_completed_tasks(self):
        """清除已完成的任务"""
        completed_ids = [task.id for task in self.tasks.values() if task.is_completed]
        for task_id in completed_ids:
            self.remove_task(task_id)
    
    def clear_all_tasks(self):
        """清除所有任务"""
        self.tasks.clear()
        self.task_order.clear()


# 全局任务管理器实例
task_manager = TaskManager()
