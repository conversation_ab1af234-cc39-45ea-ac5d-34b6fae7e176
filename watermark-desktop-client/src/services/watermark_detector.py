"""
JoyCaption水印检测模型集成
基于YOLOv11的水印检测模型，来自fancyfeast/joycaption-watermark-detection
"""

import os
import torch
import numpy as np
from PIL import Image
from ultralytics import YOL<PERSON>
from huggingface_hub import hf_hub_download
from pathlib import Path
import logging
from typing import Tuple, Optional
import time

logger = logging.getLogger(__name__)


class WatermarkDetector:
    """JoyCaption水印检测器"""
    
    def __init__(self, model_path: Optional[str] = None, device: str = "auto"):
        """
        初始化水印检测器
        
        Args:
            model_path: 模型文件路径，如果为None则自动下载
            device: 设备类型 ("auto", "cpu", "cuda")
        """
        self.device = self._get_device(device)
        self.model = None
        self.model_path = model_path
        self._load_model()
    
    def _get_device(self, device: str) -> str:
        """获取设备类型"""
        if device == "auto":
            return "cuda" if torch.cuda.is_available() else "cpu"
        return device
    
    def _load_model(self):
        """加载YOLO模型"""
        try:
            if self.model_path is None:
                # 自动下载模型
                model_dir = Path("models/yolo")
                model_dir.mkdir(parents=True, exist_ok=True)
                
                model_filename = "yolo11x-train28-best.pt"
                self.model_path = model_dir / model_filename
                
                if not self.model_path.exists():
                    logger.info("正在下载JoyCaption水印检测模型...")
                    hf_hub_download(
                        repo_id="lrzjason/joy_caption_watermark_yolo",
                        filename=model_filename,
                        local_dir=str(model_dir)
                    )
                    logger.info("模型下载完成")
            
            # 加载YOLO模型
            logger.info(f"正在加载水印检测模型: {self.model_path}")
            self.model = YOLO(str(self.model_path))
            
            # 设置设备
            if self.device == "cuda" and torch.cuda.is_available():
                self.model.to("cuda")
            
            logger.info(f"水印检测模型加载完成，使用设备: {self.device}")
            
        except Exception as e:
            logger.error(f"加载水印检测模型失败: {e}")
            raise
    
    def detect(
        self,
        image: Image.Image,
        confidence_threshold: float = 0.5,
        iou_threshold: float = 0.5,
        image_size: int = 1024,
        augment: bool = True,
        return_detailed_info: bool = False,
        use_model_default_conf: bool = False
    ) -> Tuple[np.ndarray, float, list]:
        """
        检测图片中的水印

        Args:
            image: PIL图像
            confidence_threshold: 置信度阈值
            iou_threshold: IoU阈值
            image_size: 推理图像大小
            augment: 是否使用数据增强
            return_detailed_info: 是否返回详细信息
            use_model_default_conf: 是否使用模型默认置信度（模拟第三方应用行为）

        Returns:
            tuple: (掩码数组, 平均置信度, 检测框列表)
        """
        if self.model is None:
            raise RuntimeError("模型未加载")

        start_time = time.time()

        try:
            # YOLO推理 - 添加错误处理
            try:
                if use_model_default_conf:
                    # 模拟第三方应用行为：不设置conf参数，使用模型默认值
                    results = self.model(
                        image,
                        imgsz=image_size,
                        iou=iou_threshold,
                        augment=augment,
                        verbose=False
                    )
                else:
                    # 标准行为：设置所有参数
                    results = self.model(
                        image,
                        imgsz=image_size,
                        conf=confidence_threshold,
                        iou=iou_threshold,
                        augment=augment,
                        verbose=False
                    )
            except AttributeError as e:
                if "'Conv' object has no attribute 'bn'" in str(e):
                    logger.error("YOLO模型版本兼容性问题，尝试重新加载模型...")
                    # 尝试重新加载模型
                    self._load_model()
                    # 重试推理，禁用增强以避免兼容性问题
                    results = self.model(
                        image,
                        imgsz=image_size,
                        conf=confidence_threshold,
                        iou=iou_threshold,
                        augment=False,  # 禁用增强以避免兼容性问题
                        verbose=False
                    )
                else:
                    raise

            inference_time = time.time() - start_time
            
            if len(results) == 0:
                # 没有检测到任何对象
                mask = np.zeros((image.height, image.width), dtype=np.uint8)
                return mask, 0.0, []
            
            result = results[0]
            
            # 获取检测框和置信度
            if result.boxes is None or len(result.boxes) == 0:
                # 没有检测到水印
                mask = np.zeros((image.height, image.width), dtype=np.uint8)
                return mask, 0.0, []
            
            boxes = result.boxes.xyxy.cpu().numpy()  # [x1, y1, x2, y2]
            confidences = result.boxes.conf.cpu().numpy()
            
            # 创建二值掩码
            mask = np.zeros((image.height, image.width), dtype=np.uint8)
            
            detection_info = []
            for idx, (box, conf) in enumerate(zip(boxes, confidences)):
                x1, y1, x2, y2 = map(int, box)

                # 确保坐标在图像范围内
                x1 = max(0, min(x1, image.width - 1))
                y1 = max(0, min(y1, image.height - 1))
                x2 = max(0, min(x2, image.width))
                y2 = max(0, min(y2, image.height))

                # 在掩码中标记水印区域
                mask[y1:y2, x1:x2] = 255

                # 计算检测框的属性
                width = x2 - x1
                height = y2 - y1
                area = width * height
                aspect_ratio = width / height if height > 0 else 0

                # 基础检测信息
                detection_data = {
                    'id': idx,
                    'bbox': [x1, y1, x2, y2],
                    'confidence': float(conf),
                    'area': area,
                    'width': width,
                    'height': height,
                    'aspect_ratio': aspect_ratio,
                    'center': [(x1 + x2) // 2, (y1 + y2) // 2]
                }

                # 如果需要详细信息，添加更多属性
                if return_detailed_info:
                    detection_data.update({
                        'relative_position': {
                            'x_ratio': (x1 + x2) / (2 * image.width),
                            'y_ratio': (y1 + y2) / (2 * image.height)
                        },
                        'relative_size': {
                            'width_ratio': width / image.width,
                            'height_ratio': height / image.height,
                            'area_ratio': area / (image.width * image.height)
                        },
                        'position_category': self._categorize_position(
                            (x1 + x2) / (2 * image.width),
                            (y1 + y2) / (2 * image.height)
                        )
                    })

                detection_info.append(detection_data)
            
            # 计算平均置信度
            avg_confidence = float(np.mean(confidences)) if len(confidences) > 0 else 0.0

            logger.info(f"检测到 {len(boxes)} 个水印区域，平均置信度: {avg_confidence:.3f}，推理耗时: {inference_time:.3f}s")

            return mask, avg_confidence, detection_info
            
        except Exception as e:
            logger.error(f"水印检测失败: {e}")
            raise

    def _categorize_position(self, x_ratio: float, y_ratio: float) -> str:
        """
        根据相对位置对水印进行分类

        Args:
            x_ratio: X轴相对位置 (0-1)
            y_ratio: Y轴相对位置 (0-1)

        Returns:
            位置类别字符串
        """
        # 定义区域边界
        left_boundary = 0.33
        right_boundary = 0.67
        top_boundary = 0.33
        bottom_boundary = 0.67

        # 确定水平位置
        if x_ratio < left_boundary:
            h_pos = "左"
        elif x_ratio > right_boundary:
            h_pos = "右"
        else:
            h_pos = "中"

        # 确定垂直位置
        if y_ratio < top_boundary:
            v_pos = "上"
        elif y_ratio > bottom_boundary:
            v_pos = "下"
        else:
            v_pos = "中"

        # 组合位置
        if h_pos == "中" and v_pos == "中":
            return "中心"
        else:
            return f"{v_pos}{h_pos}"
    
    def get_detection_visualization(
        self, 
        image: Image.Image, 
        confidence_threshold: float = 0.5,
        **kwargs
    ) -> Image.Image:
        """
        获取带有检测框的可视化图像
        
        Args:
            image: PIL图像
            confidence_threshold: 置信度阈值
            **kwargs: 其他检测参数
            
        Returns:
            带有检测框的PIL图像
        """
        if self.model is None:
            raise RuntimeError("模型未加载")
        
        try:
            # YOLO推理
            results = self.model(
                image,
                conf=confidence_threshold,
                verbose=False,
                **kwargs
            )
            
            if len(results) == 0:
                return image.copy()
            
            result = results[0]
            
            # 获取带有检测框的图像
            annotated_array = result.plot()
            
            # 转换为PIL图像 (BGR -> RGB)
            annotated_image = Image.fromarray(annotated_array[..., ::-1])
            
            return annotated_image
            
        except Exception as e:
            logger.error(f"生成检测可视化失败: {e}")
            return image.copy()
    
    def enhance_mask(
        self,
        mask: np.ndarray,
        dilation_size: int = 5,
        blur_size: int = 5
    ) -> np.ndarray:
        """
        增强检测掩码

        Args:
            mask: 原始掩码
            dilation_size: 膨胀核大小
            blur_size: 模糊核大小

        Returns:
            增强后的掩码
        """
        import cv2

        # 膨胀操作，扩大水印区域
        kernel = np.ones((dilation_size, dilation_size), np.uint8)
        enhanced_mask = cv2.dilate(mask, kernel, iterations=1)

        # 高斯模糊，平滑边缘
        if blur_size > 0:
            enhanced_mask = cv2.GaussianBlur(enhanced_mask, (blur_size, blur_size), 0)

        return enhanced_mask

    def smart_enhance_mask(
        self,
        mask: np.ndarray,
        image_size: tuple,
        adaptive_dilation: bool = True,
        min_dilation: int = 5,
        max_dilation: int = 15,
        feather_size: int = 8,
        context_expansion_ratio: float = 0.12
    ) -> np.ndarray:
        """
        智能增强检测掩码，针对LAMA模型优化

        Args:
            mask: 原始掩码
            image_size: 图像尺寸 (width, height)
            adaptive_dilation: 是否自适应膨胀
            min_dilation: 最小膨胀大小
            max_dilation: 最大膨胀大小
            feather_size: 羽化大小
            context_expansion_ratio: 上下文扩展比例

        Returns:
            智能增强后的掩码
        """
        import cv2

        if np.sum(mask) == 0:
            return mask

        # 安全检查：如果原始掩码已经很大，使用保守参数
        original_mask_binary = (mask > 127).astype(np.uint8)
        original_mask_ratio = np.sum(original_mask_binary) / (mask.shape[0] * mask.shape[1])
        if original_mask_ratio > 0.4:  # 提高阈值到40%
            logger.warning(f"掩码覆盖率过高 ({original_mask_ratio:.1%})，使用保守增强")
            context_expansion_ratio = min(context_expansion_ratio, 0.05)
            max_dilation = min(max_dilation, 5)

        # 1. 分析水印区域大小，自适应调整膨胀参数
        contours, _ = cv2.findContours(mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        enhanced_mask = mask.copy()

        for contour in contours:
            # 获取边界框
            x, y, w, h = cv2.boundingRect(contour)
            area = w * h

            # 根据水印大小自适应调整膨胀大小
            if adaptive_dilation:
                # 基于水印面积计算膨胀大小
                image_area = image_size[0] * image_size[1]
                watermark_ratio = area / image_area

                if watermark_ratio < 0.01:  # 小水印
                    dilation_size = max_dilation
                elif watermark_ratio < 0.05:  # 中等水印
                    dilation_size = (min_dilation + max_dilation) // 2
                else:  # 大水印
                    dilation_size = min_dilation
            else:
                dilation_size = min_dilation

            # 2. 创建单个水印的掩码
            single_mask = np.zeros_like(mask)
            cv2.fillPoly(single_mask, [contour], 255)

            # 3. 膨胀操作 - 扩展水印边界
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (dilation_size, dilation_size))
            dilated_mask = cv2.dilate(single_mask, kernel, iterations=1)

            # 4. 上下文扩展 - 为LAMA提供更多上下文信息（简化版本）
            if context_expansion_ratio > 0:
                # 计算扩展大小，限制在合理范围内
                expand_w = min(int(w * context_expansion_ratio), max(w//3, min_dilation))
                expand_h = min(int(h * context_expansion_ratio), max(h//3, min_dilation))

                # 只使用椭圆膨胀，避免复杂的矩形操作
                if expand_w > 0 and expand_h > 0:
                    expand_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (expand_w, expand_h))
                    dilated_mask = cv2.dilate(dilated_mask, expand_kernel, iterations=1)

            # 5. 合并到总掩码
            enhanced_mask = cv2.bitwise_or(enhanced_mask, dilated_mask)

        # 6. 简单而有效的边缘平滑
        if feather_size > 0:
            # 第一步：形态学开运算平滑边缘
            morph_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
            enhanced_mask = cv2.morphologyEx(enhanced_mask, cv2.MORPH_OPEN, morph_kernel)

            # 第二步：高斯模糊创建平滑过渡
            blur_size = min(feather_size * 2, 11)  # 限制模糊大小
            if blur_size % 2 == 0:
                blur_size += 1
            enhanced_mask = cv2.GaussianBlur(enhanced_mask, (blur_size, blur_size), 0)

        # 最终安全检查：确保增强后的掩码不会过大
        final_mask_binary = (enhanced_mask > 127).astype(np.uint8)
        final_mask_ratio = np.sum(final_mask_binary) / (enhanced_mask.shape[0] * enhanced_mask.shape[1])
        if final_mask_ratio > 0.6:  # 提高阈值到60%
            logger.warning(f"增强后掩码过大 ({final_mask_ratio:.1%})，回退到传统增强")
            # 回退到简单的膨胀+模糊
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (min_dilation, min_dilation))
            enhanced_mask = cv2.dilate(original_mask_binary * 255, kernel, iterations=1)
            if feather_size > 0:
                enhanced_mask = cv2.GaussianBlur(enhanced_mask, (7, 7), 0)

        return enhanced_mask
    
    def __del__(self):
        """清理资源"""
        if hasattr(self, 'model') and self.model is not None:
            del self.model
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
