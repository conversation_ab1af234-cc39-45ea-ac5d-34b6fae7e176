# -*- coding: utf-8 -*-
"""
水印检测可视化模块
用于绘制检测结果和掩码
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import logging
from typing import List, Dict, Any, Union, Tuple

logger = logging.getLogger(__name__)


class WatermarkVisualization:
    """水印检测可视化工具"""
    
    def __init__(self):
        """初始化可视化工具"""
        self.colors = [
            (255, 0, 0),    # 红色
            (0, 255, 0),    # 绿色
            (0, 0, 255),    # 蓝色
            (255, 255, 0),  # 黄色
            (255, 0, 255),  # 紫色
            (0, 255, 255),  # 青色
        ]
    
    def draw_detections(
        self, 
        image: Union[Image.Image, np.ndarray], 
        detections: List[Dict[str, Any]],
        show_confidence: bool = True,
        line_thickness: int = 2
    ) -> Image.Image:
        """
        在图像上绘制检测结果
        
        Args:
            image: 输入图像
            detections: 检测结果列表
            show_confidence: 是否显示置信度
            line_thickness: 线条粗细
            
        Returns:
            绘制了检测框的图像
        """
        try:
            # 转换为PIL图像
            if isinstance(image, np.ndarray):
                if image.shape[2] == 3:  # BGR to RGB
                    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
                image = Image.fromarray(image)
            
            # 创建绘图对象
            draw = ImageDraw.Draw(image)
            
            # 尝试加载字体
            try:
                font = ImageFont.truetype("arial.ttf", 16)
            except:
                font = ImageFont.load_default()
            
            # 绘制每个检测结果
            for i, detection in enumerate(detections):
                # 获取边界框
                bbox = detection.get('bbox', detection.get('box', []))
                if len(bbox) != 4:
                    continue
                
                x1, y1, x2, y2 = bbox
                confidence = detection.get('confidence', detection.get('conf', 0.0))
                
                # 选择颜色
                color = self.colors[i % len(self.colors)]
                
                # 绘制边界框
                draw.rectangle([x1, y1, x2, y2], outline=color, width=line_thickness)
                
                # 绘制置信度标签
                if show_confidence:
                    label = f"Watermark {confidence:.2f}"
                    
                    # 计算文本大小
                    bbox_text = draw.textbbox((0, 0), label, font=font)
                    text_width = bbox_text[2] - bbox_text[0]
                    text_height = bbox_text[3] - bbox_text[1]
                    
                    # 绘制背景矩形
                    draw.rectangle(
                        [x1, y1 - text_height - 4, x1 + text_width + 4, y1],
                        fill=color
                    )
                    
                    # 绘制文本
                    draw.text((x1 + 2, y1 - text_height - 2), label, fill=(255, 255, 255), font=font)
            
            logger.info(f"绘制了 {len(detections)} 个检测结果")
            return image
            
        except Exception as e:
            logger.error(f"绘制检测结果失败: {e}")
            return image if isinstance(image, Image.Image) else Image.fromarray(image)
    
    def create_mask_from_detections(
        self, 
        image_size: Tuple[int, int], 
        detections: List[Dict[str, Any]],
        expansion_ratio: float = 0.1
    ) -> Image.Image:
        """
        从检测结果创建掩码
        
        Args:
            image_size: 图像尺寸 (width, height)
            detections: 检测结果列表
            expansion_ratio: 边界框扩展比例
            
        Returns:
            二值掩码图像
        """
        try:
            width, height = image_size
            
            # 创建空白掩码
            mask = Image.new('L', (width, height), 0)
            draw = ImageDraw.Draw(mask)
            
            # 为每个检测结果创建掩码区域
            for detection in detections:
                bbox = detection.get('bbox', detection.get('box', []))
                if len(bbox) != 4:
                    continue
                
                x1, y1, x2, y2 = bbox
                
                # 扩展边界框
                w = x2 - x1
                h = y2 - y1
                expand_w = w * expansion_ratio
                expand_h = h * expansion_ratio
                
                x1 = max(0, x1 - expand_w)
                y1 = max(0, y1 - expand_h)
                x2 = min(width, x2 + expand_w)
                y2 = min(height, y2 + expand_h)
                
                # 绘制填充矩形
                draw.rectangle([x1, y1, x2, y2], fill=255)
            
            logger.info(f"创建掩码完成，包含 {len(detections)} 个区域")
            return mask
            
        except Exception as e:
            logger.error(f"创建掩码失败: {e}")
            return Image.new('L', image_size, 0)
    
    def enhance_mask(self, mask: Image.Image, kernel_size: int = 5) -> Image.Image:
        """
        增强掩码，使边缘更平滑
        
        Args:
            mask: 输入掩码
            kernel_size: 形态学操作核大小
            
        Returns:
            增强后的掩码
        """
        try:
            # 转换为OpenCV格式
            mask_cv = np.array(mask)
            
            # 形态学操作：先闭运算再开运算
            kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (kernel_size, kernel_size))
            
            # 闭运算：填充小洞
            mask_cv = cv2.morphologyEx(mask_cv, cv2.MORPH_CLOSE, kernel)
            
            # 开运算：去除小噪点
            mask_cv = cv2.morphologyEx(mask_cv, cv2.MORPH_OPEN, kernel)
            
            # 高斯模糊：平滑边缘
            mask_cv = cv2.GaussianBlur(mask_cv, (3, 3), 0)
            
            # 转换回PIL图像
            enhanced_mask = Image.fromarray(mask_cv)
            
            logger.info("掩码增强完成")
            return enhanced_mask
            
        except Exception as e:
            logger.error(f"掩码增强失败: {e}")
            return mask
    
    def visualize_mask_overlay(
        self, 
        image: Image.Image, 
        mask: Image.Image, 
        alpha: float = 0.5,
        color: Tuple[int, int, int] = (255, 0, 0)
    ) -> Image.Image:
        """
        创建掩码覆盖可视化
        
        Args:
            image: 原始图像
            mask: 掩码图像
            alpha: 透明度
            color: 覆盖颜色
            
        Returns:
            覆盖了掩码的图像
        """
        try:
            # 确保图像尺寸一致
            if image.size != mask.size:
                mask = mask.resize(image.size, Image.LANCZOS)
            
            # 创建彩色掩码
            colored_mask = Image.new('RGB', image.size, color)
            
            # 将掩码转换为alpha通道
            mask_alpha = mask.convert('L')
            
            # 创建带透明度的掩码
            colored_mask.putalpha(mask_alpha)
            
            # 合成图像
            result = Image.alpha_composite(
                image.convert('RGBA'), 
                colored_mask.convert('RGBA')
            ).convert('RGB')
            
            return result
            
        except Exception as e:
            logger.error(f"创建掩码覆盖失败: {e}")
            return image
