# -*- coding: utf-8 -*-
"""
异步任务处理器
基于QThread实现的异步任务处理系统
"""

from PySide6.QtCore import QThread, Signal, QMutex, QWaitCondition, QTimer
from queue import Queue, Empty
import time
from typing import Optional, Callable

from src.models.task_model import Task, TaskStatus, ProcessingResult
from src.services.ai_service import ai_service
from src.utils.logger import get_logger, log_performance


class TaskWorker(QThread):
    """任务工作线程"""
    
    # 信号定义
    task_started = Signal(str)  # 任务开始
    task_progress = Signal(str, float, str)  # 任务进度 (task_id, progress, status)
    task_completed = Signal(str, dict)  # 任务完成 (task_id, result)
    task_failed = Signal(str, str)  # 任务失败 (task_id, error)
    
    def __init__(self, worker_id: str, task_queue: Queue):
        super().__init__()
        self.worker_id = worker_id
        self.task_queue = task_queue
        self.logger = get_logger(f"TaskWorker-{worker_id}")
        self.is_running = False
        self.current_task: Optional[Task] = None
        
    def run(self):
        """运行工作线程"""
        self.is_running = True
        self.logger.info(f"工作线程 {self.worker_id} 启动")
        
        while self.is_running:
            try:
                # 获取任务（超时1秒）
                task = self.task_queue.get(timeout=1.0)
                
                if task is None:  # 停止信号
                    break
                
                self.current_task = task
                self.process_task(task)
                self.current_task = None
                
                # 标记任务完成
                self.task_queue.task_done()
                
            except Empty:
                continue
            except Exception as e:
                self.logger.error(f"工作线程 {self.worker_id} 发生错误: {e}")
                if self.current_task:
                    self.task_failed.emit(self.current_task.id, str(e))
    
    @log_performance
    def process_task(self, task: Task):
        """处理单个任务"""
        try:
            self.logger.info(f"开始处理任务: {task.id}")
            self.task_started.emit(task.id)
            
            # 创建进度回调函数
            def progress_callback(progress: float):
                # 根据进度确定状态
                if progress < 0.2:
                    status = TaskStatus.LOADING.value
                elif progress < 0.4:
                    status = TaskStatus.DETECTING.value
                elif progress < 0.8:
                    status = TaskStatus.PROCESSING.value
                else:
                    status = TaskStatus.SAVING.value
                
                self.task_progress.emit(task.id, progress, status)
            
            # 调用AI服务处理图像
            result = ai_service.process_image(
                input_path=str(task.input_path),  # 确保传递字符串路径
                output_path=str(task.output_path),  # 确保传递字符串路径
                confidence_threshold=task.params.confidence_threshold,
                enhance_mask=task.params.enhance_mask,
                use_smart_enhancement=task.params.use_smart_enhancement,
                context_expansion_ratio=task.params.context_expansion_ratio,
                progress_callback=progress_callback
            )
            
            # 发送完成信号
            self.task_completed.emit(task.id, result)
            self.logger.info(f"任务处理完成: {task.id}")
            
        except Exception as e:
            self.logger.error(f"任务处理失败: {task.id}, 错误: {e}")
            self.task_failed.emit(task.id, str(e))
    
    def stop(self):
        """停止工作线程"""
        self.is_running = False
        self.logger.info(f"工作线程 {self.worker_id} 停止")


class TaskProcessor(QThread):
    """任务处理器主控制器"""
    
    # 信号定义
    task_started = Signal(str)
    task_progress = Signal(str, float, str)
    task_completed = Signal(str, dict)
    task_failed = Signal(str, str)
    all_tasks_completed = Signal()
    
    def __init__(self, max_workers: int = 4):
        super().__init__()
        self.max_workers = max_workers
        self.task_queue = Queue()
        self.workers = []
        self.is_running = False
        self.logger = get_logger("TaskProcessor")
        
        # 状态监控
        self.active_tasks = set()
        self.completed_tasks = set()
        self.failed_tasks = set()

        # 定时器用于检查任务完成状态（延迟初始化）
        self.status_timer = None
        self.init_status_timer()

    def init_status_timer(self):
        """初始化状态检查定时器（确保在主线程中）"""
        try:
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app and app.thread() == self.thread():
                # 在主线程中创建定时器
                self.status_timer = QTimer()
                self.status_timer.timeout.connect(self.check_completion_status)
                self.status_timer.start(1000)  # 每秒检查一次
            else:
                self.logger.warning("TaskProcessor不在主线程中，跳过定时器初始化")
        except Exception as e:
            self.logger.error(f"状态定时器初始化失败: {e}")

    def start_processing(self):
        """启动任务处理"""
        if self.is_running:
            return
        
        self.logger.info("启动任务处理器")
        self.is_running = True
        
        # 确保AI服务已加载
        if not ai_service.is_model_loaded():
            self.logger.info("正在加载AI模型...")
            if not ai_service.load_models():
                self.logger.error("AI模型加载失败")
                return False
        
        # 启动工作线程
        for i in range(self.max_workers):
            worker = TaskWorker(f"Worker-{i}", self.task_queue)
            
            # 连接信号
            worker.task_started.connect(self.on_task_started)
            worker.task_progress.connect(self.on_task_progress)
            worker.task_completed.connect(self.on_task_completed)
            worker.task_failed.connect(self.on_task_failed)
            
            worker.start()
            self.workers.append(worker)
        
        self.logger.info(f"启动了 {len(self.workers)} 个工作线程")
        return True
    
    def stop_processing(self):
        """停止任务处理"""
        if not self.is_running:
            return
        
        self.logger.info("停止任务处理器")
        self.is_running = False

        # 停止状态检查定时器
        if self.status_timer and self.status_timer.isActive():
            self.status_timer.stop()

        # 向队列添加停止信号
        for _ in range(self.max_workers):
            self.task_queue.put(None)
        
        # 等待所有工作线程结束
        for worker in self.workers:
            worker.stop()
            worker.wait(5000)  # 等待5秒
        
        self.workers.clear()
        self.logger.info("任务处理器已停止")
    
    def submit_task(self, task: Task) -> bool:
        """提交任务"""
        if not self.is_running:
            self.logger.warning("任务处理器未启动")
            return False
        
        self.task_queue.put(task)
        self.active_tasks.add(task.id)
        self.logger.info(f"提交任务: {task.id}")
        return True
    
    def on_task_started(self, task_id: str):
        """任务开始处理"""
        self.logger.info(f"任务开始: {task_id}")
        self.task_started.emit(task_id)
    
    def on_task_progress(self, task_id: str, progress: float, status: str):
        """任务进度更新"""
        self.task_progress.emit(task_id, progress, status)
    
    def on_task_completed(self, task_id: str, result: dict):
        """任务完成"""
        self.logger.info(f"任务完成: {task_id}")
        self.active_tasks.discard(task_id)
        self.completed_tasks.add(task_id)
        self.task_completed.emit(task_id, result)
    
    def on_task_failed(self, task_id: str, error: str):
        """任务失败"""
        self.logger.error(f"任务失败: {task_id}, 错误: {error}")
        self.active_tasks.discard(task_id)
        self.failed_tasks.add(task_id)
        self.task_failed.emit(task_id, error)
    
    def check_completion_status(self):
        """检查是否所有任务都已完成"""
        if self.is_running and len(self.active_tasks) == 0 and not self.task_queue.empty():
            # 如果没有活跃任务但队列不为空，可能需要处理
            pass
        elif self.is_running and len(self.active_tasks) == 0 and self.task_queue.empty():
            # 所有任务都已完成
            if len(self.completed_tasks) > 0 or len(self.failed_tasks) > 0:
                self.all_tasks_completed.emit()
                # 重置状态
                self.completed_tasks.clear()
                self.failed_tasks.clear()
                # 停止任务处理器
                self.stop_processing()
    
    def get_queue_size(self) -> int:
        """获取队列大小"""
        return self.task_queue.qsize()
    
    def get_active_task_count(self) -> int:
        """获取活跃任务数量"""
        return len(self.active_tasks)

    def reset_state(self):
        """重置任务处理器状态"""
        # 如果正在运行，先停止
        if self.is_running:
            self.stop_processing()

        self.active_tasks.clear()
        self.completed_tasks.clear()
        self.failed_tasks.clear()

        # 清空任务队列
        while not self.task_queue.empty():
            try:
                self.task_queue.get_nowait()
            except:
                break

        # 确保运行状态为False
        self.is_running = False

        self.logger.info("任务处理器状态已重置")


# 全局任务处理器实例
task_processor = TaskProcessor()
