# -*- coding: utf-8 -*-
"""
AI服务封装模块
基于实际的WatermarkRemovalPipeline，提供统一的AI服务接口
"""

import sys
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Callable, List, Union
import torch
from PIL import Image

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 暂时创建一个简化的AI服务，后续集成完整的管道
# from src.services.watermark_removal_pipeline import WatermarkRemovalPipeline

from src.utils.logger import get_logger
from src.utils.constants import YOLO_MODEL_PATH, LAMA_MODEL_PATH


class WatermarkAIService:
    """水印AI服务 - 封装WatermarkRemovalPipeline"""

    def __init__(self, models_dir: Optional[Path] = None):
        """
        初始化水印AI服务
        
        Args:
            models_dir: 模型目录，包含:
                - yolo/yolo11x-train28-best.pt (YOLO11检测模型)
                - lama/big-lama/models/best.ckpt (LAMA修复模型)
        """
        self.logger = get_logger(__name__)
        self.models_dir = models_dir or Path("models")
        self.pipeline = None
        self.is_loaded = False
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 模型路径配置
        self.detector_path = YOLO_MODEL_PATH
        self.inpainter_path = LAMA_MODEL_PATH
        
        self.logger.info(f"水印AI服务初始化 - 设备: {self.device}")

    def load_models(self) -> bool:
        """加载AI模型"""
        try:
            self.logger.info("正在加载水印检测和修复模型...")

            # 检查模型文件是否存在
            if not self.detector_path.exists():
                self.logger.error(f"YOLO检测模型不存在: {self.detector_path}")
                return False

            # LAMA模型检查（可选，因为我们有OpenCV备选方案）
            lama_available = self.inpainter_path.exists()
            if not lama_available:
                self.logger.warning(f"LAMA修复模型不存在: {self.inpainter_path}，将使用OpenCV修复")

            # 导入实际的水印去除管道
            from .watermark_removal_pipeline import WatermarkRemovalPipeline

            # 初始化水印去除管道
            self.pipeline = WatermarkRemovalPipeline(
                detector_model_path=str(self.detector_path),
                inpainter_model_path=str(self.inpainter_path) if lama_available else None,
                device=str(self.device)
            )

            self.is_loaded = True
            self.logger.info("✅ 水印去除管道加载成功")
            return True

        except Exception as e:
            self.logger.error(f"❌ AI模型加载失败: {e}")
            self.is_loaded = False
            return False

    def is_model_loaded(self) -> bool:
        """检查模型是否已加载"""
        return self.is_loaded

    def process_image(
        self,
        input_path: Union[Path, str],
        output_path: Union[Path, str],
        confidence_threshold: float = 0.3,
        enhance_mask: bool = True,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12,
        progress_callback: Optional[Callable[[float], None]] = None
    ) -> Dict[str, Any]:
        """
        处理图像去除水印
        
        Args:
            input_path: 输入图像路径
            output_path: 输出图像路径
            confidence_threshold: 检测置信度阈值
            enhance_mask: 是否增强掩码
            use_smart_enhancement: 是否使用智能增强
            context_expansion_ratio: 上下文扩展比例
            progress_callback: 进度回调函数
            
        Returns:
            处理结果字典
        """
        if not self.is_loaded:
            return {"success": False, "error": "AI模型未加载"}

        try:
            self.logger.info(f"开始处理图像: {input_path}")

            # 检查输入文件是否存在
            if not Path(input_path).exists():
                return {"success": False, "error": f"输入文件不存在: {input_path}"}

            # 确保输出目录存在
            output_dir = Path(output_path).parent
            output_dir.mkdir(parents=True, exist_ok=True)

            if progress_callback:
                progress_callback(0.1)

            # 调用实际的水印去除管道
            result = self.pipeline.remove_watermark(
                image=str(input_path),
                confidence_threshold=confidence_threshold,
                enhance_mask=enhance_mask,
                use_smart_enhancement=use_smart_enhancement,
                context_expansion_ratio=context_expansion_ratio,
                return_intermediate=True,
                progress_callback=progress_callback
            )

            # 处理结果
            if isinstance(result, dict):
                # 保存结果图像
                if 'result_image' in result and result['result_image'] is not None:
                    result['result_image'].save(str(output_path), 'JPEG', quality=95)

                    processing_result = {
                        "success": True,
                        "input_path": str(input_path),
                        "output_path": str(output_path),
                        "processing_time": result.get('processing_time', 0),
                        "detection_time": result.get('detection_time', 0),
                        "inpainting_time": result.get('inpainting_time', 0),
                        "watermark_count": len(result.get('detection_info', [])),
                        "confidence": result.get('max_confidence', 0),
                        "watermark_detected": result.get('watermark_detected', False),
                        "detection_info": result.get('detection_info', [])
                    }
                else:
                    # 如果没有结果图像，复制原图
                    import shutil
                    shutil.copy2(input_path, output_path)
                    processing_result = {
                        "success": False,
                        "input_path": str(input_path),
                        "output_path": str(output_path),
                        "error": "处理失败，未生成结果图像"
                    }
            else:
                # 简单的PIL图像结果
                if hasattr(result, 'save'):
                    result.save(str(output_path), 'JPEG', quality=95)
                    processing_result = {
                        "success": True,
                        "input_path": str(input_path),
                        "output_path": str(output_path),
                        "processing_time": 0,
                        "watermark_detected": False
                    }
                else:
                    processing_result = {
                        "success": False,
                        "input_path": str(input_path),
                        "output_path": str(output_path),
                        "error": "未知的结果格式"
                    }

            self.logger.info(f"图像处理完成: {output_path}")
            return processing_result
                
        except Exception as e:
            error_msg = str(e)
            self.logger.error(f"图像处理失败: {error_msg}")

            # 根据错误类型提供更具体的错误信息
            if "'Conv' object has no attribute 'bn'" in error_msg:
                error_msg = "水印检测失败: YOLO模型版本兼容性问题，请检查Ultralytics版本"
            elif "No such file or directory" in error_msg:
                error_msg = f"文件路径错误: {error_msg}"
            elif "CUDA" in error_msg:
                error_msg = "GPU处理失败，已切换到CPU模式"

            return {
                "success": False,
                "error": error_msg,
                "input_path": str(input_path),
                "output_path": str(output_path),
                "original_error": str(e)
            }

    def analyze_image(self, image_path: Union[Path, str]) -> Dict[str, Any]:
        """分析图像中的水印信息"""
        if not self.is_loaded:
            return {"success": False, "error": "AI模型未加载"}

        try:
            self.logger.info(f"开始分析图像: {image_path}")

            # 使用管道的分析功能
            analysis = self.pipeline.analyze_image(str(image_path))

            self.logger.info(f"图像分析完成: {image_path}")
            return {"success": True, **analysis}
        except Exception as e:
            self.logger.error(f"图像分析失败: {e}")
            return {"success": False, "error": str(e)}

    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "detector": {
                "name": "YOLO11 Watermark Detector",
                "model": "yolo11x-train28-best.pt",
                "path": str(self.detector_path),
                "exists": self.detector_path.exists()
            },
            "inpainter": {
                "name": "LAMA Inpainter", 
                "model": "best.ckpt",
                "path": str(self.inpainter_path),
                "exists": self.inpainter_path.exists()
            },
            "device": str(self.device),
            "is_loaded": self.is_loaded
        }

    def get_supported_formats(self) -> List[str]:
        """获取支持的图像格式"""
        return ['.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff']

    def cleanup(self):
        """清理资源"""
        if self.pipeline:
            # 清理GPU缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            self.pipeline = None
            self.is_loaded = False
            self.logger.info("AI服务资源已清理")


# 全局AI服务实例
ai_service = WatermarkAIService()
