"""
完整的水印去除管道
集成水印检测和图像修复功能
"""

import numpy as np
from PIL import Image
import logging
from typing import Tuple, Dict, Any, Optional, Union
from pathlib import Path
import time

from .watermark_detector import WatermarkDetector
from .lama_inpainter import LamaInpainter
from .real_lama_inpainter import RealLamaInpainter
from .visualization import WatermarkVisualization

logger = logging.getLogger(__name__)


class WatermarkRemovalPipeline:
    """完整的水印去除管道"""
    
    def __init__(
        self, 
        detector_model_path: Optional[str] = None,
        inpainter_model_path: Optional[str] = None,
        device: str = "auto"
    ):
        """
        初始化水印去除管道
        
        Args:
            detector_model_path: 检测模型路径
            inpainter_model_path: 修复模型路径
            device: 设备类型
        """
        self.device = device
        
        # 初始化检测器
        logger.info("正在初始化水印检测器...")
        self.detector = WatermarkDetector(
            model_path=detector_model_path,
            device=device
        )
        
        # 初始化修复器 - 优先使用真正的LAMA模型
        logger.info("正在初始化图像修复器...")

        # 尝试使用真正的LAMA模型
        try:
            self.inpainter = RealLamaInpainter(device=device)
            if self.inpainter.is_available():
                logger.info("✅ 真正的LAMA模型初始化成功")
            else:
                raise RuntimeError("LAMA模型不可用")
        except Exception as e:
            logger.warning(f"真正的LAMA模型初始化失败: {e}")
            logger.info("回退到简化实现...")
            self.inpainter = LamaInpainter(
                model_path=inpainter_model_path,
                device=device
            )

        # 初始化可视化器
        self.visualizer = WatermarkVisualization()

        logger.info("水印去除管道初始化完成")
    
    def remove_watermark(
        self,
        image: Union[Image.Image, str, Path],
        confidence_threshold: float = 0.5,
        enhance_mask: bool = True,
        mask_dilation_size: int = 5,
        mask_blur_size: int = 5,
        inpaint_radius: int = 3,
        return_intermediate: bool = False,
        use_smart_enhancement: bool = True,
        context_expansion_ratio: float = 0.12,
        progress_callback: Optional[callable] = None
    ) -> Union[Image.Image, Dict[str, Any]]:
        """
        完整的水印去除流程

        Args:
            image: 输入图像（PIL Image、文件路径或Path对象）
            confidence_threshold: 检测置信度阈值
            enhance_mask: 是否增强掩码
            mask_dilation_size: 掩码膨胀大小
            mask_blur_size: 掩码模糊大小
            inpaint_radius: 修复半径
            return_intermediate: 是否返回中间结果
            use_smart_enhancement: 是否使用智能掩码增强（针对LAMA优化）
            context_expansion_ratio: 上下文扩展比例

        Returns:
            修复后的图像或包含详细信息的字典
        """
        start_time = time.time()
        
        try:
            # 加载图像
            if isinstance(image, (str, Path)):
                image = Image.open(image).convert('RGB')
            elif not isinstance(image, Image.Image):
                raise ValueError("不支持的图像格式")
            
            original_image = image.copy()
            
            # 步骤1: 检测水印
            logger.info("正在检测水印...")
            if progress_callback:
                progress_callback(0.1)

            detection_start = time.time()

            original_mask, avg_confidence, detection_info = self.detector.detect(
                image=image,
                confidence_threshold=confidence_threshold
            )

            if progress_callback:
                progress_callback(0.4)

            detection_time = time.time() - detection_start
            logger.info(f"水印检测完成，耗时: {detection_time:.2f}秒")

            # 保存原始掩码的副本用于处理
            mask = original_mask.copy()
            
            # 检查是否检测到水印
            if avg_confidence < confidence_threshold:
                logger.info(f"未检测到明显水印 (置信度: {avg_confidence:.3f})")
                
                if return_intermediate:
                    return {
                        'result_image': original_image,
                        'original_image': original_image,
                        'original_mask': original_mask,
                        'enhanced_mask': original_mask,  # 没有增强时与原始掩码相同
                        'detection_info': detection_info,
                        'confidence': avg_confidence,
                        'processing_time': time.time() - start_time,
                        'detection_time': detection_time,
                        'inpainting_time': 0.0,
                        'watermark_detected': False
                    }
                else:
                    return original_image
            
            # 步骤2: 增强掩码（如果需要）
            if enhance_mask and np.any(mask > 0):
                if use_smart_enhancement:
                    logger.info("正在智能增强检测掩码（针对LAMA优化）...")
                    mask = self.detector.smart_enhance_mask(
                        mask=mask,
                        image_size=image.size,
                        adaptive_dilation=True,
                        min_dilation=max(5, mask_dilation_size),
                        max_dilation=max(15, mask_dilation_size * 3),
                        feather_size=max(8, mask_blur_size * 2),
                        context_expansion_ratio=context_expansion_ratio
                    )
                else:
                    logger.info("正在增强检测掩码...")
                    mask = self.detector.enhance_mask(
                        mask=mask,
                        dilation_size=mask_dilation_size,
                        blur_size=mask_blur_size
                    )
            
            # 步骤3: 图像修复
            logger.info("正在进行图像修复...")
            if progress_callback:
                progress_callback(0.6)

            inpainting_start = time.time()

            # 预处理掩码
            processed_mask = self.inpainter.preprocess_mask(mask)
            
            # 执行修复
            result_image = self.inpainter.inpaint(
                image=image,
                mask=processed_mask,
                inpaint_radius=inpaint_radius
            )
            
            if progress_callback:
                progress_callback(0.9)

            inpainting_time = time.time() - inpainting_start
            total_time = time.time() - start_time

            logger.info(f"图像修复完成，耗时: {inpainting_time:.2f}秒")
            logger.info(f"总处理时间: {total_time:.2f}秒")

            if progress_callback:
                progress_callback(1.0)
            
            if return_intermediate:
                return {
                    'result_image': result_image,
                    'original_image': original_image,
                    'original_mask': original_mask,
                    'enhanced_mask': mask,  # 增强后的掩码
                    'detection_info': detection_info,
                    'confidence': avg_confidence,
                    'processing_time': total_time,
                    'detection_time': detection_time,
                    'inpainting_time': inpainting_time,
                    'watermark_detected': True
                }
            else:
                return result_image
                
        except Exception as e:
            logger.error(f"水印去除失败: {e}")
            raise
    
    def batch_remove_watermarks(
        self,
        images: list,
        **kwargs
    ) -> list:
        """
        批量水印去除
        
        Args:
            images: 图像列表
            **kwargs: 其他参数
            
        Returns:
            处理结果列表
        """
        results = []
        total_images = len(images)
        
        logger.info(f"开始批量处理 {total_images} 张图像")
        
        for i, image in enumerate(images):
            try:
                logger.info(f"处理第 {i+1}/{total_images} 张图像")
                result = self.remove_watermark(image, **kwargs)
                results.append(result)
            except Exception as e:
                logger.error(f"处理第 {i+1} 张图像失败: {e}")
                # 添加原图作为备选
                if isinstance(image, Image.Image):
                    results.append(image.copy())
                else:
                    results.append(Image.open(image).convert('RGB'))
        
        logger.info(f"批量处理完成，成功处理 {len(results)} 张图像")
        return results
    
    def get_detection_visualization(
        self,
        image: Union[Image.Image, str, Path],
        confidence_threshold: float = 0.5
    ) -> Image.Image:
        """
        获取水印检测可视化图像
        
        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            
        Returns:
            带有检测框的图像
        """
        # 加载图像
        if isinstance(image, (str, Path)):
            image = Image.open(image).convert('RGB')
        
        return self.detector.get_detection_visualization(
            image=image,
            confidence_threshold=confidence_threshold
        )
    
    def analyze_image(
        self,
        image: Union[Image.Image, str, Path],
        confidence_threshold: float = 0.5
    ) -> Dict[str, Any]:
        """
        分析图像中的水印信息
        
        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            
        Returns:
            分析结果字典
        """
        # 加载图像
        if isinstance(image, (str, Path)):
            image = Image.open(image).convert('RGB')
        
        # 检测水印
        mask, avg_confidence, detection_info = self.detector.detect(
            image=image,
            confidence_threshold=confidence_threshold
        )
        
        # 计算统计信息
        total_pixels = image.width * image.height
        watermark_pixels = np.sum(mask > 0)
        watermark_ratio = watermark_pixels / total_pixels
        
        return {
            'image_size': (image.width, image.height),
            'watermark_detected': avg_confidence >= confidence_threshold,
            'confidence': avg_confidence,
            'detection_count': len(detection_info),
            'detection_info': detection_info,
            'watermark_pixels': int(watermark_pixels),
            'total_pixels': int(total_pixels),
            'watermark_ratio': float(watermark_ratio),
            'mask_shape': mask.shape
        }

    def create_detection_visualization(
        self,
        image: Union[Image.Image, str, Path],
        confidence_threshold: float = 0.5,
        save_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        创建水印检测可视化

        Args:
            image: 输入图像
            confidence_threshold: 置信度阈值
            save_path: 保存路径（可选）

        Returns:
            包含可视化结果的字典
        """
        # 加载图像
        if isinstance(image, (str, Path)):
            image = Image.open(image).convert('RGB')

        # 检测水印
        mask, avg_confidence, detection_info = self.detector.detect(
            image=image,
            confidence_threshold=confidence_threshold,
            return_detailed_info=True
        )

        # 创建各种可视化
        visualizations = {}

        # 1. 带检测框的图像
        visualizations['detection_boxes'] = self.visualizer.draw_detection_boxes(
            image, detection_info, show_confidence=True
        )

        # 2. 掩码叠加图像
        visualizations['mask_overlay'] = self.visualizer.create_mask_overlay(
            image, mask, alpha=0.4
        )

        # 3. 检测摘要图像
        visualizations['detection_summary'] = self.visualizer.create_detection_summary(
            image, detection_info, mask
        )

        # 4. 并排对比
        visualizations['side_by_side'] = self.visualizer.create_side_by_side_comparison(
            visualizations['detection_boxes'],
            visualizations['mask_overlay'],
            titles=("检测框", "检测掩码")
        )

        # 保存可视化结果
        if save_path:
            save_dir = Path(save_path)
            save_dir.mkdir(parents=True, exist_ok=True)

            for name, vis_image in visualizations.items():
                vis_path = save_dir / f"{name}.jpg"
                vis_image.save(str(vis_path), 'JPEG', quality=95)

        return {
            'visualizations': visualizations,
            'detection_info': detection_info,
            'mask': mask,
            'average_confidence': avg_confidence,
            'watermark_count': len(detection_info)
        }

    def analyze_image(self, image_path: str) -> Dict[str, Any]:
        """
        分析图像中的水印信息（仅检测，不修复）

        Args:
            image_path: 图像文件路径

        Returns:
            分析结果字典
        """
        try:
            # 加载图像
            image = Image.open(image_path).convert('RGB')

            # 执行检测
            mask, avg_confidence, detection_info = self.detector.detect(
                image=image,
                confidence_threshold=0.3  # 使用较低的阈值进行分析
            )

            # 返回分析结果
            return {
                "watermark_detected": avg_confidence > 0.3,
                "confidence": avg_confidence,
                "max_confidence": max([d.get('confidence', 0) for d in detection_info], default=0),
                "detection_count": len(detection_info),
                "detection_info": detection_info,
                "image_size": list(image.size),
                "analysis_time": 0.0  # 简化版本不计时
            }

        except Exception as e:
            logger.error(f"图像分析失败: {e}")
            return {
                "watermark_detected": False,
                "confidence": 0.0,
                "detection_count": 0,
                "detection_info": [],
                "error": str(e)
            }

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'detector'):
            del self.detector
        if hasattr(self, 'inpainter'):
            del self.inpainter
