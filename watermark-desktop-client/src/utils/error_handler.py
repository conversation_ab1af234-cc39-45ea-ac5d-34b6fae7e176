# -*- coding: utf-8 -*-
"""
错误处理模块
提供统一的错误处理、异常捕获和用户友好的错误信息
"""

import sys
import traceback
from typing import Optional, Callable, Any
from functools import wraps
from enum import Enum

from PySide6.QtWidgets import QMessageBox, QWidget
from PySide6.QtCore import QObject, Signal

from src.utils.logger import get_logger


class ErrorLevel(Enum):
    """错误级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ErrorCode(Enum):
    """错误代码"""
    # 通用错误
    UNKNOWN_ERROR = "E0001"
    INVALID_PARAMETER = "E0002"
    FILE_NOT_FOUND = "E0003"
    PERMISSION_DENIED = "E0004"
    
    # AI模型相关错误
    MODEL_LOAD_FAILED = "E1001"
    MODEL_NOT_LOADED = "E1002"
    INFERENCE_FAILED = "E1003"
    UNSUPPORTED_FORMAT = "E1004"
    
    # 文件处理错误
    FILE_READ_ERROR = "E2001"
    FILE_WRITE_ERROR = "E2002"
    INVALID_IMAGE = "E2003"
    DISK_SPACE_FULL = "E2004"
    
    # 界面相关错误
    UI_INIT_FAILED = "E3001"
    WIDGET_ERROR = "E3002"
    
    # 网络相关错误
    NETWORK_ERROR = "E4001"
    DOWNLOAD_FAILED = "E4002"


class AppError(Exception):
    """应用程序自定义异常"""
    
    def __init__(
        self, 
        message: str, 
        code: ErrorCode = ErrorCode.UNKNOWN_ERROR,
        level: ErrorLevel = ErrorLevel.ERROR,
        details: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.code = code
        self.level = level
        self.details = details
    
    def __str__(self):
        return f"[{self.code.value}] {self.message}"


class ErrorHandler(QObject):
    """错误处理器"""
    
    # 信号定义
    error_occurred = Signal(str, str, str)  # message, code, level
    
    def __init__(self, parent_widget: Optional[QWidget] = None):
        super().__init__()
        self.parent_widget = parent_widget
        self.logger = get_logger("ErrorHandler")
        
        # 错误消息映射
        self.error_messages = {
            ErrorCode.MODEL_LOAD_FAILED: "AI模型加载失败，请检查模型文件是否存在",
            ErrorCode.MODEL_NOT_LOADED: "AI模型未加载，请先加载模型",
            ErrorCode.INFERENCE_FAILED: "AI推理失败，请重试或检查输入图像",
            ErrorCode.UNSUPPORTED_FORMAT: "不支持的图像格式",
            ErrorCode.FILE_NOT_FOUND: "文件不存在",
            ErrorCode.FILE_READ_ERROR: "文件读取失败",
            ErrorCode.FILE_WRITE_ERROR: "文件写入失败，请检查磁盘空间和权限",
            ErrorCode.INVALID_IMAGE: "无效的图像文件",
            ErrorCode.DISK_SPACE_FULL: "磁盘空间不足",
            ErrorCode.PERMISSION_DENIED: "权限不足",
            ErrorCode.NETWORK_ERROR: "网络连接失败",
            ErrorCode.DOWNLOAD_FAILED: "下载失败",
        }
    
    def handle_error(
        self, 
        error: Exception, 
        show_dialog: bool = True,
        context: Optional[str] = None
    ):
        """处理错误"""
        if isinstance(error, AppError):
            self._handle_app_error(error, show_dialog, context)
        else:
            self._handle_generic_error(error, show_dialog, context)
    
    def _handle_app_error(
        self, 
        error: AppError, 
        show_dialog: bool,
        context: Optional[str]
    ):
        """处理应用程序自定义错误"""
        # 记录日志
        log_message = f"应用错误: {error}"
        if context:
            log_message = f"{context} - {log_message}"
        if error.details:
            log_message += f" 详情: {error.details}"
        
        if error.level == ErrorLevel.CRITICAL:
            self.logger.critical(log_message)
        elif error.level == ErrorLevel.ERROR:
            self.logger.error(log_message)
        elif error.level == ErrorLevel.WARNING:
            self.logger.warning(log_message)
        else:
            self.logger.info(log_message)
        
        # 发送信号
        self.error_occurred.emit(error.message, error.code.value, error.level.value)
        
        # 显示对话框
        if show_dialog and self.parent_widget:
            self._show_error_dialog(error.message, error.level)
    
    def _handle_generic_error(
        self, 
        error: Exception, 
        show_dialog: bool,
        context: Optional[str]
    ):
        """处理通用错误"""
        error_message = str(error)
        
        # 记录日志
        log_message = f"未处理的异常: {error_message}"
        if context:
            log_message = f"{context} - {log_message}"
        
        self.logger.error(log_message)
        self.logger.error(f"异常堆栈: {traceback.format_exc()}")
        
        # 发送信号
        self.error_occurred.emit(
            error_message, 
            ErrorCode.UNKNOWN_ERROR.value, 
            ErrorLevel.ERROR.value
        )
        
        # 显示对话框
        if show_dialog and self.parent_widget:
            self._show_error_dialog(f"发生未知错误: {error_message}", ErrorLevel.ERROR)
    
    def _show_error_dialog(self, message: str, level: ErrorLevel):
        """显示错误对话框"""
        if level == ErrorLevel.CRITICAL:
            icon = QMessageBox.Critical
            title = "严重错误"
        elif level == ErrorLevel.ERROR:
            icon = QMessageBox.Critical
            title = "错误"
        elif level == ErrorLevel.WARNING:
            icon = QMessageBox.Warning
            title = "警告"
        else:
            icon = QMessageBox.Information
            title = "信息"
        
        msg_box = QMessageBox(self.parent_widget)
        msg_box.setIcon(icon)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.exec()
    
    def get_user_friendly_message(self, code: ErrorCode) -> str:
        """获取用户友好的错误消息"""
        return self.error_messages.get(code, "发生未知错误")


def error_handler(
    show_dialog: bool = True,
    context: Optional[str] = None,
    reraise: bool = False
):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs) -> Any:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 获取错误处理器实例
                handler = ErrorHandler()
                handler.handle_error(e, show_dialog, context or func.__name__)
                
                if reraise:
                    raise
                
                return None
        return wrapper
    return decorator


def safe_execute(
    func: Callable,
    *args,
    default_return=None,
    show_dialog: bool = False,
    context: Optional[str] = None,
    **kwargs
) -> Any:
    """安全执行函数"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        handler = ErrorHandler()
        handler.handle_error(e, show_dialog, context or func.__name__)
        return default_return


def setup_exception_hook():
    """设置全局异常钩子"""
    def exception_hook(exc_type, exc_value, exc_traceback):
        """全局异常处理钩子"""
        if issubclass(exc_type, KeyboardInterrupt):
            # 允许Ctrl+C中断
            sys.__excepthook__(exc_type, exc_value, exc_traceback)
            return
        
        logger = get_logger("GlobalExceptionHandler")
        logger.critical(
            f"未捕获的异常: {exc_type.__name__}: {exc_value}",
            exc_info=(exc_type, exc_value, exc_traceback)
        )
        
        # 显示错误对话框
        error_msg = f"应用程序发生严重错误:\n{exc_type.__name__}: {exc_value}"
        
        try:
            msg_box = QMessageBox()
            msg_box.setIcon(QMessageBox.Critical)
            msg_box.setWindowTitle("严重错误")
            msg_box.setText(error_msg)
            msg_box.setDetailedText(
                ''.join(traceback.format_exception(exc_type, exc_value, exc_traceback))
            )
            msg_box.exec()
        except:
            # 如果GUI显示失败，至少打印到控制台
            print(f"严重错误: {error_msg}")
            traceback.print_exception(exc_type, exc_value, exc_traceback)
    
    sys.excepthook = exception_hook


# 全局错误处理器实例
global_error_handler = ErrorHandler()
