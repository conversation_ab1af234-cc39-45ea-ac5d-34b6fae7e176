# -*- coding: utf-8 -*-
"""
日志配置模块
提供统一的日志管理功能
"""

import logging
import logging.handlers
import sys
from pathlib import Path
from typing import Optional


def setup_logging(
    log_dir: Optional[Path] = None,
    log_level: str = "INFO",
    max_file_size: str = "10MB",
    backup_count: int = 5
) -> logging.Logger:
    """
    设置应用程序日志
    
    Args:
        log_dir: 日志目录
        log_level: 日志级别
        max_file_size: 最大文件大小
        backup_count: 备份文件数量
        
    Returns:
        配置好的日志器
    """
    if log_dir is None:
        log_dir = Path("logs")
    
    log_dir.mkdir(exist_ok=True)
    
    # 创建日志器
    logger = logging.getLogger("watermark_remover")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # 清除现有处理器
    logger.handlers.clear()
    
    # 控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)
    
    # 文件处理器（按日期轮转）
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_dir / "app.log",
        when='midnight',
        interval=1,
        backupCount=backup_count,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
    )
    file_handler.setFormatter(file_formatter)
    logger.addHandler(file_handler)
    
    # 错误文件处理器
    error_handler = logging.handlers.RotatingFileHandler(
        log_dir / "error.log",
        maxBytes=_parse_size(max_file_size),
        backupCount=backup_count,
        encoding='utf-8'
    )
    error_handler.setLevel(logging.ERROR)
    error_handler.setFormatter(file_formatter)
    logger.addHandler(error_handler)
    
    return logger


def _parse_size(size_str: str) -> int:
    """解析文件大小字符串"""
    size_str = size_str.upper()
    if size_str.endswith('KB'):
        return int(size_str[:-2]) * 1024
    elif size_str.endswith('MB'):
        return int(size_str[:-2]) * 1024 * 1024
    elif size_str.endswith('GB'):
        return int(size_str[:-2]) * 1024 * 1024 * 1024
    else:
        return int(size_str)


def get_logger(name: str = "watermark_remover") -> logging.Logger:
    """获取日志器"""
    return logging.getLogger(name)


# 性能日志装饰器
def log_performance(func):
    """性能日志装饰器"""
    import time
    import functools
    
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        logger = get_logger()
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            duration = end_time - start_time
            
            logger.info(f"{func.__name__} 执行完成，耗时: {duration:.3f}秒")
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            logger.error(f"{func.__name__} 执行失败，耗时: {duration:.3f}秒，错误: {e}")
            raise
    
    return wrapper
