# -*- coding: utf-8 -*-
"""
应用程序常量定义
"""

from enum import Enum
from pathlib import Path

# 应用程序信息
APP_NAME = "水印去除工具"
APP_VERSION = "1.0.0"
APP_AUTHOR = "Watermark Remover Team"
APP_DESCRIPTION = "基于AI的智能水印检测和去除工具"

# 文件路径
PROJECT_ROOT = Path(__file__).parent.parent.parent
CONFIG_DIR = PROJECT_ROOT / "config"
MODELS_DIR = PROJECT_ROOT / "models"
ASSETS_DIR = PROJECT_ROOT / "assets"
LOGS_DIR = PROJECT_ROOT / "logs"
OUTPUTS_DIR = PROJECT_ROOT / "outputs"
TEMP_DIR = PROJECT_ROOT / "temp"

# 模型文件路径
YOLO_MODEL_PATH = MODELS_DIR / "yolo" / "yolo11x-train28-best.pt"
LAMA_MODEL_PATH = MODELS_DIR / "lama" / "big-lama" / "models" / "best.ckpt"

# 支持的图像格式
SUPPORTED_IMAGE_FORMATS = [
    '.jpg', '.jpeg', '.png', '.webp', '.bmp', '.tiff', '.tif'
]

# 支持的图像格式（用于文件对话框）
IMAGE_FILTER = "图像文件 (*.jpg *.jpeg *.png *.webp *.bmp *.tiff *.tif)"

# 最大图像尺寸
MAX_IMAGE_SIZE = 2048
MIN_IMAGE_SIZE = 64

# 处理参数范围
CONFIDENCE_THRESHOLD_RANGE = (0.1, 0.9)
CONTEXT_EXPANSION_RANGE = (0.05, 0.3)
MAX_CONCURRENT_TASKS = 8


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"         # 等待处理
    LOADING = "loading"         # 加载模型
    DETECTING = "detecting"     # 检测水印
    PROCESSING = "processing"   # 图像修复
    SAVING = "saving"          # 保存结果
    COMPLETED = "completed"     # 处理完成
    FAILED = "failed"          # 处理失败
    CANCELLED = "cancelled"     # 用户取消


class TaskType(Enum):
    """任务类型枚举"""
    WATERMARK_REMOVAL = "watermark_removal"     # 水印去除
    WATERMARK_DETECTION = "watermark_detection" # 水印检测
    IMAGE_ENHANCEMENT = "image_enhancement"     # 图像增强


class ViewMode(Enum):
    """视图模式枚举"""
    TASK_LIST = "task_list"     # 任务列表视图
    RESULT_COMPARE = "result_compare"  # 结果对比视图
    SETTINGS = "settings"       # 设置视图
    HELP = "help"              # 帮助视图


class Theme(Enum):
    """主题枚举"""
    LIGHT = "light"            # 浅色主题
    DARK = "dark"              # 深色主题
    SYSTEM = "system"          # 跟随系统


class Language(Enum):
    """语言枚举"""
    ZH_CN = "zh_CN"            # 简体中文
    EN_US = "en_US"            # 英语


# 界面尺寸
DEFAULT_WINDOW_SIZE = (1200, 800)
MIN_WINDOW_SIZE = (800, 600)
PREVIEW_IMAGE_SIZE = (400, 300)
THUMBNAIL_SIZE = (150, 150)

# 颜色定义
COLORS = {
    'primary': '#2196F3',
    'secondary': '#FFC107',
    'success': '#4CAF50',
    'warning': '#FF9800',
    'error': '#F44336',
    'info': '#00BCD4',
    'light': '#F5F5F5',
    'dark': '#212121'
}

# 样式表文件
STYLE_SHEETS = {
    'light': 'styles/light_theme.qss',
    'dark': 'styles/dark_theme.qss'
}

# 图标文件
ICONS = {
    'app': 'icons/app.png',
    'add': 'icons/add.png',
    'remove': 'icons/remove.png',
    'play': 'icons/play.png',
    'pause': 'icons/pause.png',
    'stop': 'icons/stop.png',
    'settings': 'icons/settings.png',
    'help': 'icons/help.png',
    'folder': 'icons/folder.png',
    'image': 'icons/image.png',
    'success': 'icons/success.png',
    'error': 'icons/error.png',
    'warning': 'icons/warning.png'
}

# 快捷键
SHORTCUTS = {
    'open_files': 'Ctrl+O',
    'save_result': 'Ctrl+S',
    'start_processing': 'Space',
    'cancel_operation': 'Escape',
    'delete_selected': 'Delete',
    'show_help': 'F1',
    'show_settings': 'Ctrl+,',
    'quit_app': 'Ctrl+Q'
}

# 默认设置
DEFAULT_SETTINGS = {
    'confidence_threshold': 0.3,
    'enhance_mask': True,
    'use_smart_enhancement': True,
    'context_expansion_ratio': 0.12,
    'max_concurrent_tasks': 4,
    'use_gpu': True,
    'auto_save': True,
    'show_preview': True,
    'theme': 'system',
    'language': 'zh_CN'
}
