# -*- coding: utf-8 -*-
"""
应用程序初始化模块
负责初始化所有服务和组件
"""

import sys
from pathlib import Path
from typing import Optional

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTranslator, QLocale

from src.utils.logger import setup_logging, get_logger
from src.utils.config_manager import config_manager
from src.utils.error_handler import setup_exception_hook, global_error_handler
from src.utils.constants import PROJECT_ROOT, LOGS_DIR, OUTPUTS_DIR, TEMP_DIR
from src.services.ai_service import ai_service
from src.services.task_processor import task_processor


class AppInitializer:
    """应用程序初始化器"""
    
    def __init__(self):
        self.logger = None
        self.app = None
        self.translator = None
        self.is_initialized = False
    
    def initialize(self, app: Optional[QApplication] = None) -> bool:
        """初始化应用程序"""
        try:
            # 1. 设置全局异常处理
            setup_exception_hook()
            
            # 2. 创建必要的目录
            self._create_directories()
            
            # 3. 初始化日志系统
            self._setup_logging()
            
            # 4. 加载配置
            self._load_config()
            
            # 5. 初始化Qt应用程序
            if app is None:
                self.app = QApplication(sys.argv)
            else:
                self.app = app
            
            # 6. 设置应用程序信息
            self._setup_app_info()
            
            # 7. 设置国际化
            self._setup_internationalization()
            
            # 8. 初始化AI服务
            self._initialize_ai_service()
            
            # 9. 初始化任务处理器
            self._initialize_task_processor()
            
            self.is_initialized = True
            self.logger.info("✅ 应用程序初始化完成")
            return True
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"❌ 应用程序初始化失败: {e}")
            else:
                print(f"❌ 应用程序初始化失败: {e}")
            return False
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [LOGS_DIR, OUTPUTS_DIR, TEMP_DIR]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _setup_logging(self):
        """设置日志系统"""
        logging_config = config_manager.logging_config
        
        self.logger = setup_logging(
            log_dir=LOGS_DIR,
            log_level=logging_config.level,
            max_file_size=logging_config.max_file_size,
            backup_count=logging_config.backup_count
        )
        
        self.logger.info("🚀 应用程序启动")
        self.logger.info(f"项目根目录: {PROJECT_ROOT}")
    
    def _load_config(self):
        """加载配置"""
        if not config_manager.load_config():
            self.logger.warning("配置文件加载失败，使用默认配置")
        else:
            self.logger.info("配置文件加载成功")
    
    def _setup_app_info(self):
        """设置应用程序信息"""
        app_config = config_manager.get('app', {})
        
        self.app.setApplicationName(app_config.get('name', '水印去除工具'))
        self.app.setApplicationVersion(app_config.get('version', '1.0.0'))
        self.app.setOrganizationName(app_config.get('author', 'Watermark Remover Team'))
        
        self.logger.info(f"应用程序: {self.app.applicationName()} v{self.app.applicationVersion()}")
    
    def _setup_internationalization(self):
        """设置国际化"""
        ui_config = config_manager.ui_config
        
        if ui_config.language == 'zh_CN':
            # 加载中文翻译（如果有的话）
            self.translator = QTranslator()
            # translator_path = PROJECT_ROOT / "src/resources/translations/zh_CN.qm"
            # if translator_path.exists():
            #     self.translator.load(str(translator_path))
            #     self.app.installTranslator(self.translator)
            pass
        
        self.logger.info(f"语言设置: {ui_config.language}")
    
    def _initialize_ai_service(self):
        """初始化AI服务"""
        self.logger.info("正在初始化AI服务...")
        
        # 检查模型文件
        model_info = ai_service.get_model_info()
        
        detector_exists = model_info['detector']['exists']
        inpainter_exists = model_info['inpainter']['exists']
        
        if not detector_exists:
            self.logger.error(f"YOLO检测模型不存在: {model_info['detector']['path']}")
        
        if not inpainter_exists:
            self.logger.error(f"LAMA修复模型不存在: {model_info['inpainter']['path']}")
        
        if detector_exists and inpainter_exists:
            # 预加载模型
            processing_config = config_manager.processing_config
            if processing_config.use_gpu:
                self.logger.info("GPU加速已启用")
            else:
                self.logger.info("使用CPU处理")

            # 在后台加载模型
            self.logger.info("正在后台加载AI模型...")
            try:
                if ai_service.load_models():
                    self.logger.info("✅ AI模型预加载成功")
                else:
                    self.logger.warning("⚠️ AI模型预加载失败，将在首次使用时加载")
            except Exception as e:
                self.logger.warning(f"⚠️ AI模型预加载异常: {e}，将在首次使用时加载")
        else:
            self.logger.warning("部分AI模型文件缺失，某些功能可能不可用")
    
    def _initialize_task_processor(self):
        """初始化任务处理器"""
        self.logger.info("正在初始化任务处理器...")
        
        processing_config = config_manager.processing_config
        max_workers = processing_config.max_concurrent_tasks
        
        # 设置最大工作线程数
        task_processor.max_workers = max_workers
        
        self.logger.info(f"任务处理器配置: 最大并发任务数 = {max_workers}")
    
    def cleanup(self):
        """清理资源"""
        if not self.is_initialized:
            return
        
        self.logger.info("正在清理应用程序资源...")
        
        # 停止任务处理器
        if task_processor.is_running:
            task_processor.stop_processing()
        
        # 清理AI服务
        ai_service.cleanup()
        
        self.logger.info("✅ 应用程序资源清理完成")
    
    def get_app(self) -> QApplication:
        """获取Qt应用程序实例"""
        return self.app
    
    def is_ready(self) -> bool:
        """检查应用程序是否准备就绪"""
        return self.is_initialized


# 全局初始化器实例
app_initializer = AppInitializer()


def initialize_app(app: Optional[QApplication] = None) -> bool:
    """初始化应用程序的便捷函数"""
    return app_initializer.initialize(app)


def cleanup_app():
    """清理应用程序的便捷函数"""
    app_initializer.cleanup()


def get_qt_app() -> QApplication:
    """获取Qt应用程序实例的便捷函数"""
    return app_initializer.get_app()
