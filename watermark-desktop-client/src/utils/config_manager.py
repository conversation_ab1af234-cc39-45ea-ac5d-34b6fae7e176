# -*- coding: utf-8 -*-
"""
配置管理模块
负责加载和管理应用程序配置
"""

import json
from pathlib import Path
from typing import Any, Dict, Optional
from dataclasses import dataclass


@dataclass
class ModelConfig:
    """模型配置"""
    name: str = ""
    path: str = ""
    type: str = ""
    version: str = ""
    input_size: list = None
    description: str = ""
    confidence_threshold: Optional[float] = None

    def __post_init__(self):
        if self.input_size is None:
            self.input_size = [640, 640]


@dataclass
class ProcessingConfig:
    """处理配置"""
    default_confidence: float = 0.3
    enhance_mask: bool = True
    use_smart_enhancement: bool = True
    context_expansion_ratio: float = 0.12
    max_image_size: int = 2048
    max_concurrent_tasks: int = 4
    use_gpu: bool = True


@dataclass
class UIConfig:
    """界面配置"""
    theme: str = "system"
    language: str = "zh_CN"
    window: Dict[str, int] = None
    auto_save: bool = True
    show_preview: bool = True

    def __post_init__(self):
        if self.window is None:
            self.window = {"width": 1200, "height": 800}


@dataclass
class PathsConfig:
    """路径配置"""
    output_dir: str = "./outputs"
    temp_dir: str = "./temp"
    log_dir: str = "./logs"


@dataclass
class LoggingConfig:
    """日志配置"""
    level: str = "INFO"
    max_file_size: str = "10MB"
    backup_count: int = 5
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径
        """
        if config_file is None:
            config_file = Path("config/app_config.json")
        
        self.config_file = config_file
        self._config_data = {}
        self.load_config()
    
    def load_config(self) -> bool:
        """加载配置文件"""
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    content = f.read().strip()
                    if not content or content == '{}':
                        print(f"配置文件为空: {self.config_file}，将创建默认配置")
                        return self.create_default_config()

                    self._config_data = json.loads(content)
                return True
            else:
                print(f"配置文件不存在: {self.config_file}，将创建默认配置")
                return self.create_default_config()
        except Exception as e:
            print(f"加载配置文件失败: {e}，将创建默认配置")
            return self.create_default_config()
    
    def create_default_config(self) -> bool:
        """创建默认配置文件"""
        try:
            # 默认配置数据
            default_config = {
                "app": {
                    "name": "水印去除工具",
                    "version": "1.0.0",
                    "description": "基于AI的智能水印检测与去除工具"
                },
                "models": {
                    "detector": {
                        "name": "YOLOv8水印检测器",
                        "path": "models/yolo/watermark_detector.pt",
                        "type": "yolo",
                        "version": "1.0",
                        "input_size": [640, 640],
                        "description": "基于YOLOv8的水印检测模型",
                        "confidence_threshold": 0.3
                    },
                    "inpainter": {
                        "name": "LAMA修复器",
                        "path": "models/lama/big-lama.pt",
                        "type": "lama",
                        "version": "1.0",
                        "input_size": [512, 512],
                        "description": "基于LAMA的图像修复模型"
                    }
                },
                "processing": {
                    "default_confidence": 0.3,
                    "enhance_mask": True,
                    "use_smart_enhancement": True,
                    "context_expansion_ratio": 0.12,
                    "max_image_size": 2048,
                    "max_concurrent_tasks": 4,
                    "use_gpu": True
                },
                "ui": {
                    "theme": "modern",
                    "language": "zh_CN",
                    "window": {
                        "width": 1200,
                        "height": 800
                    },
                    "auto_save": True,
                    "show_preview": True
                },
                "paths": {
                    "output_dir": "./outputs",
                    "temp_dir": "./temp",
                    "log_dir": "./logs"
                },
                "logging": {
                    "level": "INFO",
                    "max_file_size": "10MB",
                    "backup_count": 5,
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
                }
            }

            self._config_data = default_config

            # 保存默认配置到文件
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=4, ensure_ascii=False)

            print(f"默认配置文件已创建: {self.config_file}")
            return True

        except Exception as e:
            print(f"创建默认配置文件失败: {e}")
            return False

    def save_config(self) -> bool:
        """保存配置文件"""
        try:
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self._config_data, f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存配置文件失败: {e}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self._config_data
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    @property
    def detector_config(self) -> ModelConfig:
        """获取检测器配置"""
        detector_data = self.get('models.detector', {})
        return ModelConfig(**detector_data)
    
    @property
    def inpainter_config(self) -> ModelConfig:
        """获取修复器配置"""
        inpainter_data = self.get('models.inpainter', {})
        return ModelConfig(**inpainter_data)
    
    @property
    def processing_config(self) -> ProcessingConfig:
        """获取处理配置"""
        processing_data = self.get('processing', {})
        return ProcessingConfig(**processing_data)
    
    @property
    def ui_config(self) -> UIConfig:
        """获取界面配置"""
        ui_data = self.get('ui', {})
        return UIConfig(**ui_data)
    
    @property
    def paths_config(self) -> PathsConfig:
        """获取路径配置"""
        paths_data = self.get('paths', {})
        return PathsConfig(**paths_data)
    
    @property
    def logging_config(self) -> LoggingConfig:
        """获取日志配置"""
        logging_data = self.get('logging', {})
        return LoggingConfig(**logging_data)


# 全局配置管理器实例
config_manager = ConfigManager()
