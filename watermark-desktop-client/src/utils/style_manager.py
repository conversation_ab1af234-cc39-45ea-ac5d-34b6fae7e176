# -*- coding: utf-8 -*-
"""
样式管理器
负责加载和应用现代化主题样式
"""

from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import QApplication, QWidget, QPushButton, QLabel, QFrame
from PySide6.QtCore import QObject, Signal

from src.utils.logger import get_logger
from src.utils.config_manager import config_manager


class StyleManager(QObject):
    """样式管理器"""

    # 信号定义
    theme_changed = Signal(str)  # 主题变更信号

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.current_theme = "modern"
        self.styles_dir = Path(__file__).parent.parent / "resources" / "styles"

        # 确保样式目录存在
        self.styles_dir.mkdir(parents=True, exist_ok=True)

    def load_theme(self, theme_name: str = "modern") -> str:
        """加载主题样式"""
        try:
            style_file = self.styles_dir / f"{theme_name}_theme.qss"

            if not style_file.exists():
                self.logger.warning(f"主题文件不存在: {style_file}")
                return ""

            with open(style_file, 'r', encoding='utf-8') as f:
                stylesheet = f.read()

            self.logger.info(f"成功加载主题: {theme_name}")
            return stylesheet

        except Exception as e:
            self.logger.error(f"加载主题失败: {e}")
            return ""

    def apply_theme(self, theme_name: str = "modern"):
        """应用主题到应用程序"""
        try:
            app = QApplication.instance()
            if not app:
                self.logger.error("无法获取QApplication实例")
                return False

            stylesheet = self.load_theme(theme_name)
            if not stylesheet:
                return False

            app.setStyleSheet(stylesheet)
            self.current_theme = theme_name
            self.theme_changed.emit(theme_name)

            self.logger.info(f"成功应用主题: {theme_name}")
            return True

        except Exception as e:
            self.logger.error(f"应用主题失败: {e}")
            return False

    def set_widget_class(self, widget, class_name: str):
        """为组件设置CSS类名"""
        try:
            # 检查是否是QWidget类型
            if hasattr(widget, 'setProperty'):
                widget.setProperty("class", class_name)

                # 只对QWidget类型的对象进行样式刷新
                if hasattr(widget, 'style') and hasattr(widget, 'update'):
                    widget.style().unpolish(widget)
                    widget.style().polish(widget)
                    widget.update()
            else:
                self.logger.warning(f"对象 {type(widget)} 不支持设置样式类名")

        except Exception as e:
            self.logger.error(f"设置组件类名失败: {e}")

    def apply_button_style(self, button: QPushButton, style_type: str = "default"):
        """应用按钮样式"""
        style_map = {
            "primary": "primary",
            "success": "success",
            "destructive": "destructive",
            "default": ""
        }

        class_name = style_map.get(style_type, "")
        if class_name:
            self.set_widget_class(button, class_name)

    def apply_label_style(self, label: QLabel, style_type: str = "default"):
        """应用标签样式"""
        style_map = {
            "title": "title",
            "subtitle": "subtitle",
            "muted": "muted",
            "small": "small",
            "default": ""
        }

        class_name = style_map.get(style_type, "")
        if class_name:
            self.set_widget_class(label, class_name)

    def apply_card_style(self, frame: QFrame, hoverable: bool = False):
        """应用卡片样式"""
        class_name = "card-hover" if hoverable else "card"
        self.set_widget_class(frame, class_name)

    def apply_task_item_style(self, frame: QFrame, selected: bool = False):
        """应用任务项样式"""
        self.set_widget_class(frame, "task-item")
        if selected:
            frame.setProperty("selected", "true")
        else:
            frame.setProperty("selected", "false")

        # 刷新样式（set_widget_class已经刷新过了，但这里需要再次刷新因为我们设置了selected属性）
        if hasattr(frame, 'style') and hasattr(frame, 'update'):
            frame.style().unpolish(frame)
            frame.style().polish(frame)
            frame.update()

    def apply_status_style(self, label: QLabel, status: str):
        """应用状态样式"""
        status_map = {
            "pending": "status-pending",
            "processing": "status-processing",
            "completed": "status-completed",
            "failed": "status-failed"
        }

        class_name = status_map.get(status, "status-pending")
        self.set_widget_class(label, class_name)

    def apply_drop_hint_style(self, label: QLabel):
        """应用拖拽提示样式"""
        self.set_widget_class(label, "drop-hint")

    def apply_image_display_style(self, label: QLabel):
        """应用图片显示样式"""
        self.set_widget_class(label, "image-display")

    def apply_info_panel_style(self, frame: QFrame):
        """应用信息面板样式"""
        self.set_widget_class(frame, "info-panel")

    def get_current_theme(self) -> str:
        """获取当前主题"""
        return self.current_theme

    def get_available_themes(self) -> list:
        """获取可用主题列表"""
        themes = []
        for style_file in self.styles_dir.glob("*_theme.qss"):
            theme_name = style_file.stem.replace("_theme", "")
            themes.append(theme_name)
        return themes

    def save_theme_preference(self, theme_name: str):
        """保存主题偏好设置"""
        try:
            config_manager.ui_config.theme = theme_name
            config_manager.save_config()
            self.logger.info(f"保存主题偏好: {theme_name}")

        except Exception as e:
            self.logger.error(f"保存主题偏好失败: {e}")

    def load_theme_preference(self) -> str:
        """加载主题偏好设置"""
        try:
            return config_manager.ui_config.theme
        except Exception as e:
            self.logger.error(f"加载主题偏好失败: {e}")
            return "modern"


# 全局样式管理器实例
style_manager = StyleManager()