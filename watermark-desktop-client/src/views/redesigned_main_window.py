# -*- coding: utf-8 -*-
"""
重新设计的主窗口界面
基于UI设计交互调整文档的全新界面实现
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QPushButton, QFrame, QScrollArea, QFileDialog, QMessageBox,
    QCheckBox, QProgressBar, QSplitter, QListWidget, QListWidgetItem,
    QApplication, QSizePolicy, QSpacerItem, QStackedWidget
)
from PySide6.QtCore import Qt, Signal, QSize, QTimer, QThread, QRect
from PySide6.QtGui import (QPixmap, QIcon, QFont, QPalette, QColor, QDragEnterEvent,
                          QDropEvent, QAction, QKeySequence, QPainter, QPen)
from pathlib import Path
from typing import List, Optional
from functools import partial
import os

from src.utils.logger import get_logger
from src.utils.config_manager import config_manager
from src.utils.constants import SHORTCUTS, APP_NAME, APP_VERSION
from src.views.login_widget import LoginWidget


class ImageItem:
    """图片项目数据类"""

    def __init__(self, file_path: Path):
        self.file_path = file_path
        self.file_name = file_path.name
        self.file_size = self._get_file_size()
        self.dimensions = self._get_dimensions()
        self.status = "READY"
        self.thumbnail = None
        self.processed_path = None

    def _get_file_size(self) -> str:
        """获取文件大小"""
        try:
            size = self.file_path.stat().st_size
            if size < 1024:
                return f"{size} B"
            elif size < 1024 * 1024:
                return f"{size / 1024:.1f} KB"
            else:
                return f"{size / (1024 * 1024):.1f} MB"
        except:
            return "未知"

    def _get_dimensions(self) -> str:
        """获取图片尺寸"""
        try:
            from PIL import Image
            with Image.open(self.file_path) as img:
                return f"{img.width} x {img.height} px"
        except:
            return "未知尺寸"


class DropZoneWidget(QFrame):
    """拖拽上传区域组件"""

    files_dropped = Signal(list)  # 文件拖拽信号

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.setup_ui()
        self.setup_drag_drop()

    def setup_ui(self):
        """设置UI"""
        self.setObjectName("dropZone")
        self.setMinimumHeight(400)  # 增加高度以适应新设计
        self.setAcceptDrops(True)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setObjectName("dropZoneLayout")
        layout.setAlignment(Qt.AlignCenter)
        layout.setSpacing(24)  # 适中的间距
        layout.setContentsMargins(40, 60, 40, 60)  # 适中的边距

        # 图标容器 - 使用简洁的线条风格图标
        icon_container = QFrame()
        icon_container.setObjectName("dropZoneIconContainer")
        icon_container.setFixedSize(60, 60)  # 恢复合适的尺寸
        icon_layout = QVBoxLayout(icon_container)
        icon_layout.setContentsMargins(8, 8, 8, 8)
        icon_layout.setAlignment(Qt.AlignCenter)
        icon_layout.setSpacing(4)

        # 使用更接近参考设计的图标组合
        # 山形/云朵图标
        icon_label = QLabel("🏔")  # 山形图标，更接近参考设计
        icon_label.setAlignment(Qt.AlignCenter)
        icon_label.setObjectName("dropZoneIcon")
        icon_layout.addWidget(icon_label)

        # 添加加号（位于右上角）
        plus_label = QLabel("+")
        plus_label.setAlignment(Qt.AlignCenter)
        plus_label.setObjectName("dropZonePlus")
        icon_layout.addWidget(plus_label)

        # 创建图标容器的包装器来确保居中
        icon_wrapper = QWidget()
        icon_wrapper_layout = QHBoxLayout(icon_wrapper)
        icon_wrapper_layout.setContentsMargins(0, 0, 0, 0)
        icon_wrapper_layout.setAlignment(Qt.AlignCenter)
        icon_wrapper_layout.addWidget(icon_container)

        layout.addWidget(icon_wrapper)

        # 主要文案 - 更简洁的描述
        main_text = QLabel("Drop your images anywhere on this page or select from your device")
        main_text.setAlignment(Qt.AlignCenter)
        main_text.setObjectName("dropZoneMainText")
        main_text.setWordWrap(True)
        layout.addWidget(main_text)

        # 行动召唤按钮 - 参考设计风格
        button_container = QWidget()
        button_layout = QHBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setAlignment(Qt.AlignCenter)

        self.select_button = QPushButton("Select up to 50 images")
        self.select_button.setObjectName("modernPrimaryButton")
        self.select_button.setFixedHeight(48)
        self.select_button.setMinimumWidth(220)
        self.select_button.setMaximumWidth(300)
        self.select_button.clicked.connect(self.select_files)
        button_layout.addWidget(self.select_button)

        layout.addWidget(button_container)

    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.setAcceptDrops(True)

    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            event.acceptProposedAction()
            self.setProperty("dragActive", True)
            self.style().polish(self)

    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.setProperty("dragActive", False)
        self.style().polish(self)

    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        self.setProperty("dragActive", False)
        self.style().polish(self)

        files = []
        for url in event.mimeData().urls():
            file_path = Path(url.toLocalFile())
            if file_path.is_file() and self._is_supported_image(file_path):
                files.append(file_path)

        if files:
            self.files_dropped.emit(files)
        else:
            QMessageBox.warning(self, "格式错误", "请选择支持的图片格式文件")

    def select_files(self):
        """选择文件"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("图片文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.tif)")

        if file_dialog.exec():
            files = [Path(f) for f in file_dialog.selectedFiles()]
            if len(files) > 50:
                QMessageBox.warning(self, "文件过多", "最多只能选择50张图片")
                files = files[:50]
            self.files_dropped.emit(files)

    def _is_supported_image(self, file_path: Path) -> bool:
        """检查是否为支持的图片格式"""
        supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        return file_path.suffix.lower() in supported_extensions


class ImageListWidget(QFrame):
    """图片列表组件"""

    item_selected = Signal(object)  # 图片项选择信号
    item_removed = Signal(object)   # 图片项移除信号

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.selected_item = None
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setObjectName("imageListWidget")

        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)  # 完全隐藏滚动条

        # 滚动内容
        self.scroll_content = QWidget()
        # 改回垂直列表布局，更适合文件管理场景
        self.scroll_layout = QVBoxLayout(self.scroll_content)
        self.scroll_layout.setContentsMargins(0, 0, 0, 0)  # 无边距，让列表项自己控制
        self.scroll_layout.setSpacing(2)  # 紧凑间距
        self.scroll_layout.setAlignment(Qt.AlignTop)  # 顶部对齐

        self.scroll_area.setWidget(self.scroll_content)
        layout.addWidget(self.scroll_area)



    def add_image(self, image_item: ImageItem):
        """添加图片项"""
        # 不在这里添加到image_items，由主窗口管理
        # self.image_items.append(image_item)  # 移除重复添加

        # 创建图片项组件
        item_widget = self._create_image_item_widget(image_item)

        # 添加到垂直布局
        self.scroll_layout.addWidget(item_widget)

        # 不自动选择第一个项目，保持预览区域空白

    def remove_image(self, image_item: ImageItem):
        """移除图片项"""
        # 移除对应的UI组件
        for i in range(self.scroll_layout.count()):
            widget = self.scroll_layout.itemAt(i).widget()
            if widget and hasattr(widget, 'image_item') and widget.image_item == image_item:
                widget.setParent(None)
                break

        # 如果移除的是选中项，清除选择
        if self.selected_item == image_item:
            self.selected_item = None

        # 通知主窗口移除图片项
        self.item_removed.emit(image_item)

    def clear_all(self):
        """清空所有图片"""
        self.selected_item = None

        # 清空UI
        while self.scroll_layout.count():
            child = self.scroll_layout.takeAt(0)
            if child.widget():
                child.widget().setParent(None)

        self.item_selected.emit(None)

    def refresh_from_main_list(self, image_items: List[ImageItem]):
        """从主窗口的图片列表刷新UI"""
        # 清空当前UI
        self.clear_all()

        # 重新添加所有图片项
        for image_item in image_items:
            item_widget = self._create_image_item_widget(image_item)
            # 添加到垂直布局
            self.scroll_layout.addWidget(item_widget)

    def select_item(self, image_item: ImageItem):
        """选择图片项"""
        self.selected_item = image_item

        # 更新UI选中状态
        for i in range(self.scroll_layout.count()):
            widget = self.scroll_layout.itemAt(i).widget()
            if widget and hasattr(widget, 'image_item'):
                is_selected = widget.image_item == image_item
                widget.setProperty("selected", is_selected)
                widget.style().polish(widget)

        self.item_selected.emit(image_item)

    def _create_image_item_widget(self, image_item: ImageItem) -> QWidget:
        """创建图片项组件（列表模式）"""
        widget = QFrame()
        widget.setObjectName("imageItem")
        widget.image_item = image_item
        widget.setMinimumHeight(80)  # 设置最小高度
        widget.setMaximumHeight(80)  # 固定高度
        widget.setCursor(Qt.PointingHandCursor)

        # 主布局（水平布局）
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(12, 8, 12, 8)
        layout.setSpacing(12)
        layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # 缩略图容器 - 紧凑设计
        thumbnail_container = QFrame()
        thumbnail_container.setObjectName("thumbnailContainer")
        thumbnail_container.setFixedSize(64, 64)  # 正方形缩略图

        # 缩略图布局
        thumb_layout = QVBoxLayout(thumbnail_container)
        thumb_layout.setContentsMargins(2, 2, 2, 2)

        # 缩略图标签
        thumbnail_label = QLabel()
        thumbnail_label.setObjectName("thumbnailLabel")
        thumbnail_label.setFixedSize(60, 60)
        thumbnail_label.setAlignment(Qt.AlignCenter)

        # 优化的图片加载和显示逻辑
        try:
            pixmap = QPixmap(str(image_item.file_path))
            if not pixmap.isNull():
                # 创建紧凑的缩略图
                thumbnail_pixmap = self._create_compact_thumbnail(pixmap, 60, 60)
                thumbnail_label.setPixmap(thumbnail_pixmap)
            else:
                self._set_compact_placeholder(thumbnail_label)
        except:
            self._set_compact_placeholder(thumbnail_label)

        thumb_layout.addWidget(thumbnail_label)
        layout.addWidget(thumbnail_container)

        # 文件信息容器（列表模式）
        info_container = QFrame()
        info_container.setObjectName("infoContainer")
        info_layout = QVBoxLayout(info_container)
        info_layout.setContentsMargins(0, 0, 0, 0)
        info_layout.setSpacing(2)
        info_layout.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)

        # 文件名（优化显示）
        name_label = QLabel()
        name_label.setObjectName("imageItemName")
        # 智能截断文件名，保留扩展名
        display_name = self._format_filename(image_item.file_name, 40)  # 列表模式可以显示更长的文件名
        name_label.setText(display_name)
        name_label.setWordWrap(False)
        name_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        name_label.setToolTip(image_item.file_name)  # 完整文件名作为提示
        info_layout.addWidget(name_label)

        # 文件详细信息（大小和尺寸）
        details_label = QLabel()
        details_label.setObjectName("imageItemDetails")
        details_text = image_item.file_size
        # 尝试获取图片尺寸
        try:
            pixmap = QPixmap(str(image_item.file_path))
            if not pixmap.isNull():
                dimensions = f"{pixmap.width()}×{pixmap.height()}"
                details_text = f"{image_item.file_size} • {dimensions}"
        except:
            pass
        details_label.setText(details_text)
        details_label.setAlignment(Qt.AlignLeft | Qt.AlignVCenter)
        info_layout.addWidget(details_label)

        layout.addWidget(info_container)

        # 弹性空间，将状态和按钮推到右侧
        layout.addStretch()

        # 状态指示器（badge风格）
        status_badge = QLabel()
        status_badge.setObjectName("statusBadge")
        status_badge.setFixedHeight(20)  # 固定高度，宽度自适应
        status_badge.setAlignment(Qt.AlignCenter)

        # 根据状态设置badge样式和文本
        if image_item.status == "READY":
            status_badge.setText("待处理")
            status_badge.setProperty("status", "ready")
        elif image_item.status == "PROCESSING":
            status_badge.setText("处理中")
            status_badge.setProperty("status", "processing")
        elif image_item.status == "COMPLETED":
            status_badge.setText("已完成")
            status_badge.setProperty("status", "completed")
        elif image_item.status == "FAILED":
            status_badge.setText("失败")
            status_badge.setProperty("status", "failed")

        layout.addWidget(status_badge)

        # 操作按钮容器（紧凑设计）
        button_container = QFrame()
        button_container.setFixedWidth(60)  # 固定宽度
        button_layout = QHBoxLayout(button_container)
        button_layout.setSpacing(4)
        button_layout.setContentsMargins(0, 0, 0, 0)

        # 下载按钮（始终显示，但根据状态启用/禁用）
        download_btn = QPushButton("⬇")
        download_btn.setObjectName("downloadButton")
        download_btn.setFixedSize(20, 20)  # 更小的按钮
        download_btn.setToolTip("下载处理结果")

        # 根据状态设置按钮可用性
        is_completed = image_item.status == "COMPLETED"
        has_processed_path = (
            image_item.processed_path is not None and
            str(image_item.processed_path).strip() != "" and
            Path(image_item.processed_path).exists() if image_item.processed_path else False
        )
        download_btn.setEnabled(is_completed and has_processed_path)
        download_btn.clicked.connect(partial(self.download_result, image_item))
        button_layout.addWidget(download_btn)

        # 删除按钮
        delete_btn = QPushButton("✕")
        delete_btn.setObjectName("deleteButton")
        delete_btn.setFixedSize(20, 20)  # 更小的按钮
        delete_btn.setToolTip("移除图片")
        delete_btn.clicked.connect(partial(self.remove_image, image_item))
        button_layout.addWidget(delete_btn)

        layout.addWidget(button_container)

        # 点击事件
        def mousePressEvent(event):
            if event.button() == Qt.LeftButton:
                self.select_item(image_item)

        widget.mousePressEvent = mousePressEvent

        return widget

    def _create_compact_thumbnail(self, source_pixmap: QPixmap, width: int, height: int) -> QPixmap:
        """创建紧凑的缩略图（列表模式）"""
        # 创建目标画布
        result = QPixmap(width, height)
        result.fill(QColor("#F8F9FA"))

        # 计算缩放后的尺寸，保持宽高比
        scaled_pixmap = source_pixmap.scaled(width, height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        # 计算居中位置
        x = (width - scaled_pixmap.width()) // 2
        y = (height - scaled_pixmap.height()) // 2

        # 绘制图片
        painter = QPainter(result)
        painter.setRenderHint(QPainter.Antialiasing)
        painter.drawPixmap(x, y, scaled_pixmap)
        painter.end()

        return result

    def _set_compact_placeholder(self, label: QLabel):
        """设置紧凑的占位符（列表模式）"""
        placeholder = QPixmap(60, 60)
        placeholder.fill(QColor("#F3F4F6"))

        painter = QPainter(placeholder)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制简单的图片图标
        painter.setPen(QPen(QColor("#9CA3AF"), 2))
        painter.drawRect(15, 15, 30, 20)
        painter.drawLine(20, 25, 25, 20)
        painter.drawLine(25, 20, 30, 25)
        painter.drawLine(30, 25, 40, 15)

        painter.end()
        label.setPixmap(placeholder)

    def _create_thumbnail_with_effects(self, source_pixmap: QPixmap, width: int, height: int) -> QPixmap:
        """创建带效果的缩略图"""
        # 创建目标画布
        result = QPixmap(width, height)
        result.fill(QColor("#FFFFFF"))

        # 计算缩放后的尺寸，保持宽高比
        scaled_pixmap = source_pixmap.scaled(width, height, Qt.KeepAspectRatio, Qt.SmoothTransformation)

        # 计算居中位置
        x = (width - scaled_pixmap.width()) // 2
        y = (height - scaled_pixmap.height()) // 2

        # 绘制图片
        painter = QPainter(result)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制阴影效果
        shadow_offset = 2
        shadow_color = QColor(0, 0, 0, 30)
        painter.fillRect(x + shadow_offset, y + shadow_offset,
                        scaled_pixmap.width(), scaled_pixmap.height(), shadow_color)

        # 绘制主图片
        painter.drawPixmap(x, y, scaled_pixmap)
        painter.end()

        return result

    def _set_placeholder_thumbnail(self, label: QLabel):
        """设置占位符缩略图"""
        label.setText("🖼️")
        label.setStyleSheet("""
            QLabel {
                font-size: 40px;
                color: #9E9E9E;
                background-color: #F8F9FA;
                border: 2px dashed #E0E0E0;
                border-radius: 8px;
            }
        """)

    def _format_filename(self, filename: str, max_length: int) -> str:
        """智能格式化文件名，保留扩展名"""
        if len(filename) <= max_length:
            return filename

        # 分离文件名和扩展名
        name_part = Path(filename).stem
        ext_part = Path(filename).suffix

        # 计算可用于文件名的长度
        available_length = max_length - len(ext_part) - 3  # 3个字符用于"..."

        if available_length > 0:
            return name_part[:available_length] + "..." + ext_part
        else:
            # 如果扩展名太长，只显示部分文件名
            return filename[:max_length-3] + "..."

    def download_result(self, image_item: ImageItem):
        """下载处理结果"""
        if not image_item.processed_path or not Path(image_item.processed_path).exists():
            QMessageBox.warning(self, "文件不存在", "处理结果文件不存在")
            return

        # 选择保存位置
        file_dialog = QFileDialog()
        file_dialog.setAcceptMode(QFileDialog.AcceptSave)
        file_dialog.setNameFilter("图片文件 (*.jpg *.png *.bmp *.tiff)")
        file_dialog.setDefaultSuffix("jpg")
        file_dialog.selectFile(f"processed_{image_item.file_name}")

        if file_dialog.exec():
            save_path = file_dialog.selectedFiles()[0]
            try:
                import shutil
                shutil.copy2(image_item.processed_path, save_path)
                QMessageBox.information(self, "保存成功", f"文件已保存到：\n{save_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存文件时发生错误：\n{str(e)}")


class ComparisonWidget(QFrame):
    """对比预览组件"""

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.original_pixmap = None
        self.processed_pixmap = None
        self.split_position = 0.5  # 分割线位置（0.0-1.0）
        self.is_dragging = False
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setObjectName("comparisonWidget")
        self.setMinimumSize(400, 300)
        self.setCursor(Qt.SplitHCursor)

    def set_images(self, original_path: str, processed_path: str):
        """设置对比图片"""
        try:
            # 加载原图
            original_image = QPixmap(original_path)
            if not original_image.isNull():
                self.original_pixmap = original_image

            # 加载处理后的图片
            if processed_path and Path(processed_path).exists():
                processed_image = QPixmap(processed_path)
                if not processed_image.isNull():
                    self.processed_pixmap = processed_image

            self.update()

        except Exception as e:
            self.logger.error(f"加载对比图片失败: {e}")

    def paintEvent(self, event):
        """绘制对比图片"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        rect = self.rect()

        if self.original_pixmap and self.processed_pixmap:
            # 计算缩放后的图片尺寸
            scaled_original = self.scale_pixmap_to_fit(self.original_pixmap, rect.size())
            scaled_processed = self.scale_pixmap_to_fit(self.processed_pixmap, rect.size())

            # 计算居中位置
            x = (rect.width() - scaled_original.width()) // 2
            y = (rect.height() - scaled_original.height()) // 2

            # 计算分割线位置
            split_x = int(x + scaled_original.width() * self.split_position)

            # 绘制原图（左侧）
            left_rect = QRect(x, y, split_x - x, scaled_original.height())
            painter.drawPixmap(left_rect, scaled_original,
                             QRect(0, 0, split_x - x, scaled_original.height()))

            # 绘制处理后图片（右侧）
            right_rect = QRect(split_x, y, x + scaled_original.width() - split_x, scaled_processed.height())
            painter.drawPixmap(right_rect, scaled_processed,
                             QRect(split_x - x, 0, x + scaled_processed.width() - split_x, scaled_processed.height()))

            # 绘制分割线
            painter.setPen(QPen(QColor(255, 255, 255), 2))
            painter.drawLine(split_x, y, split_x, y + scaled_original.height())

            # 绘制标签
            painter.setPen(QPen(QColor(0, 0, 0), 1))
            painter.setFont(QFont("Arial", 12, QFont.Bold))
            painter.drawText(x + 10, y + 25, "原图")
            painter.drawText(split_x + 10, y + 25, "处理后")

        elif self.original_pixmap:
            # 只显示原图
            scaled_pixmap = self.scale_pixmap_to_fit(self.original_pixmap, rect.size())
            x = (rect.width() - scaled_pixmap.width()) // 2
            y = (rect.height() - scaled_pixmap.height()) // 2
            painter.drawPixmap(x, y, scaled_pixmap)
        else:
            # 显示占位符
            painter.setPen(QPen(QColor(150, 150, 150), 1))
            painter.setFont(QFont("Arial", 16))
            painter.drawText(rect, Qt.AlignCenter, "选择已处理的图片查看对比效果")

    def scale_pixmap_to_fit(self, pixmap: QPixmap, size: QSize) -> QPixmap:
        """缩放图片以适应指定尺寸，保持宽高比"""
        return pixmap.scaled(size, Qt.KeepAspectRatio, Qt.SmoothTransformation)

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton and self.original_pixmap and self.processed_pixmap:
            self.is_dragging = True
            self.update_split_position(event.pos().x())

    def mouseMoveEvent(self, event):
        """鼠标移动事件"""
        if self.is_dragging and self.original_pixmap and self.processed_pixmap:
            self.update_split_position(event.pos().x())

    def mouseReleaseEvent(self, event):
        """鼠标释放事件"""
        if event.button() == Qt.LeftButton:
            self.is_dragging = False

    def update_split_position(self, x: int):
        """更新分割线位置"""
        if self.original_pixmap:
            rect = self.rect()
            scaled_pixmap = self.scale_pixmap_to_fit(self.original_pixmap, rect.size())
            image_x = (rect.width() - scaled_pixmap.width()) // 2
            image_width = scaled_pixmap.width()

            # 限制分割线在图片范围内
            relative_x = max(0, min(x - image_x, image_width))
            self.split_position = relative_x / image_width if image_width > 0 else 0.5
            self.update()


class PreviewWidget(QFrame):
    """图片预览组件"""

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.current_image_item = None
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setObjectName("previewWidget")
        self.setMinimumSize(400, 300)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # 创建堆叠组件
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)

        # 占位符标签
        self.placeholder_label = QLabel()
        self.placeholder_label.setAlignment(Qt.AlignCenter)
        self.placeholder_label.setMinimumSize(400, 300)
        self.stacked_widget.addWidget(self.placeholder_label)

        # 普通预览标签
        self.preview_label = QLabel()
        self.preview_label.setAlignment(Qt.AlignCenter)
        self.preview_label.setMinimumSize(400, 300)
        self.stacked_widget.addWidget(self.preview_label)

        # 对比预览组件
        self.comparison_widget = ComparisonWidget()
        self.stacked_widget.addWidget(self.comparison_widget)

        # 初始显示占位符
        self.show_placeholder()

    def show_placeholder(self):
        """显示占位符"""
        self.current_image_item = None
        self.placeholder_label.clear()
        self.placeholder_label.setText("")
        self.placeholder_label.setStyleSheet("""
            QLabel {
                background-color: #FFFFFF;
                border: none;
            }
        """)
        self.stacked_widget.setCurrentWidget(self.placeholder_label)

    def show_image(self, image_item: ImageItem):
        """显示图片预览"""
        if not image_item:
            self.show_placeholder()
            return

        self.current_image_item = image_item

        try:
            # 检查是否有处理结果且状态为COMPLETED
            if (image_item.status == "COMPLETED" and
                image_item.processed_path and
                Path(image_item.processed_path).exists()):

                # 显示对比预览
                self.comparison_widget.set_images(
                    str(image_item.file_path),
                    image_item.processed_path
                )
                self.stacked_widget.setCurrentWidget(self.comparison_widget)

            else:
                # 显示普通预览
                pixmap = QPixmap(str(image_item.file_path))
                if pixmap.isNull():
                    self.show_error("无法加载图片")
                    return

                # 缩放图片以适应预览区域
                scaled_pixmap = pixmap.scaled(
                    self.preview_label.size(),
                    Qt.KeepAspectRatio,
                    Qt.SmoothTransformation
                )

                self.preview_label.setPixmap(scaled_pixmap)
                self.preview_label.setStyleSheet("""
                    QLabel {
                        background-color: #FFFFFF;
                        border-right: 1px solid #E0E0E0;
                     
                    }
                """)
                self.stacked_widget.setCurrentWidget(self.preview_label)

        except Exception as e:
            self.logger.error(f"预览图片失败: {e}")
            self.show_error("预览失败")

    def show_error(self, message: str):
        """显示错误信息"""
        self.preview_label.setText(f"❌\n\n{message}")
        self.preview_label.setStyleSheet("""
            QLabel {
                color: #F44336;
                font-size: 16px;
                background-color: #FFFFFF;
                border: 2px dashed #F44336;
                border-radius: 8px;
            }
        """)


class ControlPanelWidget(QFrame):
    """底部控制面板组件"""

    process_requested = Signal()  # 处理请求信号
    settings_changed = Signal(dict)  # 设置变更信号

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.setup_ui()

    def setup_ui(self):
        """设置UI"""
        self.setObjectName("controlPanel")
        self.setFixedHeight(120)

        # 直接设置背景色，确保不被覆盖
        self.setStyleSheet("""
            QFrame#controlPanel {
                background-color: #F3F3F4;
                border-top: 1px solid #E0E0E0;
            }
        """)

        # 主布局 - 使用水平布局，左侧放内容，右侧放按钮
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(24, 16, 24, 16)
        main_layout.setSpacing(24)

        # 左侧内容区域
        left_content = QWidget()
        left_content.setObjectName("controlPanelLeftContent")  # 设置对象名用于样式
        left_layout = QVBoxLayout(left_content)
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(12)

        # 第一行：保存位置
        save_layout = QHBoxLayout()
        save_layout.setAlignment(Qt.AlignLeft)  # 确保左对齐

        save_label = QLabel("保存位置:")
        save_label.setObjectName("controlLabel")
        save_layout.addWidget(save_label)

        # 文件夹图标和路径输入框容器
        path_container = QHBoxLayout()
        path_container.setSpacing(8)

        # 文件夹图标
        folder_icon = QLabel("📁")
        path_container.addWidget(folder_icon)

        # 使用项目根目录下的outputs文件夹作为默认路径
        from src.utils.constants import OUTPUTS_DIR
        default_output_path = str(OUTPUTS_DIR)
        # 确保输出目录存在
        OUTPUTS_DIR.mkdir(parents=True, exist_ok=True)

        # 路径显示标签 - 限制宽度
        self.path_label = QLabel(default_output_path)
        self.path_label.setObjectName("pathLabel")
        self.path_label.setMaximumWidth(300)  # 限制路径显示宽度
        self.path_label.setMinimumWidth(200)  # 设置最小宽度
        path_container.addWidget(self.path_label)

        # 更改按钮（紧贴输入框右侧）
        change_btn = QPushButton("更改")
        change_btn.setObjectName("secondaryButton")
        change_btn.clicked.connect(self.change_save_path)
        path_container.addWidget(change_btn)

        # 打开按钮（紧贴更改按钮右侧）
        open_btn = QPushButton("打开")
        open_btn.setObjectName("secondaryButton")
        open_btn.clicked.connect(self.open_save_folder)
        path_container.addWidget(open_btn)

        # 添加弹性空间，防止路径容器拉伸到右侧
        path_container.addStretch()

        save_layout.addLayout(path_container)
        left_layout.addLayout(save_layout)

        # 第二行：处理选项
        options_layout = QHBoxLayout()

        # 处理选项
        self.keep_name_cb = QCheckBox("保留原始名称")
        self.keep_name_cb.setChecked(True)
        self.keep_name_cb.setObjectName("optionCheckbox")
        options_layout.addWidget(self.keep_name_cb)

        # 添加弹性空间
        options_layout.addStretch()

        left_layout.addLayout(options_layout)

        # 将左侧内容添加到主布局
        main_layout.addWidget(left_content)

        # 右侧：处理按钮 - 垂直居中
        button_container = QWidget()
        button_container.setObjectName("controlPanelButtonContainer")  # 设置对象名用于样式
        button_layout = QVBoxLayout(button_container)
        button_layout.setContentsMargins(0, 0, 0, 0)
        button_layout.setAlignment(Qt.AlignVCenter)

        # 处理按钮
        self.process_btn = QPushButton("▶ 开始处理")
        self.process_btn.setObjectName("primaryButton")
        self.process_btn.setMinimumWidth(120)  # 最小宽度
        self.process_btn.setMaximumWidth(150)  # 最大宽度，防止过宽
        self.process_btn.setFixedHeight(48)    # 固定按钮高度
        self.process_btn.setEnabled(False)     # 初始禁用
        self.process_btn.clicked.connect(self.process_requested.emit)
        button_layout.addWidget(self.process_btn)

        # 将按钮容器添加到主布局
        main_layout.addWidget(button_container)

    def change_save_path(self):
        """更改保存路径"""
        folder = QFileDialog.getExistingDirectory(
            self, "选择保存文件夹", self.path_label.text()
        )
        if folder:
            self.path_label.setText(folder)
            self.settings_changed.emit({"save_path": folder})

    def open_save_folder(self):
        """打开保存文件夹"""
        import subprocess
        import platform

        path = self.path_label.text()
        try:
            if platform.system() == "Windows":
                subprocess.run(["explorer", path])
            elif platform.system() == "Darwin":  # macOS
                subprocess.run(["open", path])
            else:  # Linux
                subprocess.run(["xdg-open", path])
        except Exception as e:
            self.logger.error(f"打开文件夹失败: {e}")
            QMessageBox.warning(self, "错误", "无法打开文件夹")

    def set_process_enabled(self, enabled: bool):
        """设置处理按钮状态"""
        self.process_btn.setEnabled(enabled)
        if enabled:
            self.process_btn.setText("▶ 开始处理")
        else:
            self.process_btn.setText("▶ 开始处理")

    def set_processing_state(self, processing: bool):
        """设置处理状态"""
        if processing:
            self.process_btn.setText("⏸ 处理中...")
            self.process_btn.setEnabled(False)
        else:
            self.process_btn.setText("▶ 开始处理")
            self.process_btn.setEnabled(True)


class RedesignedMainWindow(QMainWindow):
    """重新设计的主窗口"""

    # 信号定义
    files_selected = Signal(list)
    processing_requested = Signal(list, dict)

    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)

        # 状态变量
        self.image_items = []
        self.is_processing = False
        self.is_logged_in = False  # 登录状态

        # 设置窗口
        self.setup_window()
        self.create_menu_bar()
        self.setup_ui()
        self.apply_styles()  # 先应用主窗口样式
        self.setup_connections()
        self.apply_component_styles()  # 然后应用组件特定样式

        # 设置初始UI状态（仅在已登录时调用）
        if self.is_logged_in:
            self.update_ui_state()

    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle("水印去除工具")
        self.setMinimumSize(950, 600)
        self.resize(1000, 680)

        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("path/to/icon.png"))

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 打开文件
        open_action = QAction("打开文件(&O)", self)
        open_action.setShortcut(QKeySequence("Ctrl+O"))
        open_action.setStatusTip("选择要处理的图片文件")
        open_action.triggered.connect(self.open_files)
        file_menu.addAction(open_action)

        # 打开文件夹
        open_folder_action = QAction("打开文件夹(&D)", self)
        open_folder_action.setStatusTip("选择包含图片的文件夹")
        open_folder_action.triggered.connect(self.open_folder)
        file_menu.addAction(open_folder_action)

        file_menu.addSeparator()

        # 保存结果
        save_action = QAction("保存结果(&S)", self)
        save_action.setShortcut(QKeySequence("Ctrl+S"))
        save_action.setStatusTip("保存处理结果")
        save_action.triggered.connect(self.save_results)
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence("Ctrl+Q"))
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 处理菜单
        process_menu = menubar.addMenu("处理(&P)")

        # 开始处理
        self.start_action = QAction("开始处理(&S)", self)
        self.start_action.setShortcut(QKeySequence("F5"))
        self.start_action.setStatusTip("开始批量处理图片")
        self.start_action.triggered.connect(self.start_processing)
        process_menu.addAction(self.start_action)

        # 停止处理
        self.stop_action = QAction("停止处理(&T)", self)
        self.stop_action.setShortcut(QKeySequence("Escape"))
        self.stop_action.setStatusTip("停止当前处理")
        self.stop_action.setEnabled(False)
        self.stop_action.triggered.connect(self.stop_processing)
        process_menu.addAction(self.stop_action)

        process_menu.addSeparator()

        # 清除任务
        clear_action = QAction("清除任务(&C)", self)
        clear_action.setStatusTip("清除所有任务")
        clear_action.triggered.connect(self.clear_all_images)
        process_menu.addAction(clear_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")

        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut(QKeySequence("Ctrl+,"))
        settings_action.setStatusTip("打开应用程序设置")
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 帮助文档
        help_action = QAction("帮助文档(&H)", self)
        help_action.setShortcut(QKeySequence("F1"))
        help_action.setStatusTip("查看帮助文档")
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)

        help_menu.addSeparator()

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于此应用程序")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def setup_ui(self):
        """设置用户界面"""
        # 中央部件 - 使用QStackedWidget管理登录和主界面
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)

        # 创建登录界面
        self.login_widget = LoginWidget()
        self.login_widget.login_success.connect(self.handle_login_success)
        self.stacked_widget.addWidget(self.login_widget)

        # 创建主界面
        self.main_widget = self.create_main_widget()
        self.stacked_widget.addWidget(self.main_widget)

        # 根据登录状态显示对应界面
        if self.is_logged_in:
            self.stacked_widget.setCurrentWidget(self.main_widget)
        else:
            self.stacked_widget.setCurrentWidget(self.login_widget)

    def create_main_widget(self) -> QWidget:
        """创建主界面"""
        main_widget = QWidget()
        main_widget.setObjectName("mainWidget")

        # 主布局
        main_layout = QVBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # 头部区域
        self.header_widget = self.create_header()
        main_layout.addWidget(self.header_widget)

        # 内容区域
        content_widget = QWidget()
        content_widget.setObjectName("contentWidget")
        content_layout = QHBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)  # 移除填充
        content_layout.setSpacing(0)

        # 左侧：上传区域和图片列表
        self.left_widget = QWidget()
        self.left_widget.setObjectName("leftWidget")
        self.left_layout = QVBoxLayout(self.left_widget)
        self.left_layout.setContentsMargins(0, 0, 0, 0)  # 左、上、右（一半间距）、下边距
        self.left_layout.setSpacing(16)

        # 拖拽上传区域
        self.drop_zone = DropZoneWidget()
        self.left_layout.addWidget(self.drop_zone)

        # 图片列表
        self.image_list = ImageListWidget()
        self.image_list.setVisible(False)  # 初始隐藏
        self.left_layout.addWidget(self.image_list)

        content_layout.addWidget(self.left_widget)

        # 分割线
        separator = QFrame()
        separator.setObjectName("verticalSeparator")
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Plain)
        separator.setFixedWidth(1)
        separator.setStyleSheet("QFrame#verticalSeparator { background-color: #E5E7EB; border: none; }")
        content_layout.addWidget(separator)

        # 右侧：预览区域
        self.right_widget = QWidget()
        self.right_widget.setObjectName("rightWidget")
        right_layout = QVBoxLayout(self.right_widget)
        right_layout.setContentsMargins(0, 0, 0, 0)  # 左（一半间距）、上、右、下边距

        self.preview_widget = PreviewWidget()
        right_layout.addWidget(self.preview_widget)

        self.right_widget.setVisible(False)  # 初始状态隐藏整个右侧区域
        content_layout.addWidget(self.right_widget)

        main_layout.addWidget(content_widget)

        # 底部控制面板
        self.control_panel = ControlPanelWidget()
        main_layout.addWidget(self.control_panel)

        return main_widget

    def create_header(self) -> QWidget:
        """创建头部区域"""
        header = QFrame()
        header.setObjectName("headerWidget")
        header.setFixedHeight(60)

        layout = QHBoxLayout(header)
        layout.setContentsMargins(24, 12, 24, 12)
        layout.setSpacing(16)

        # 图片计数器
        self.count_label = QLabel("总共0张图片")
        self.count_label.setObjectName("countLabel")
        layout.addWidget(self.count_label)

        # 添加按钮
        self.add_btn = QPushButton("+ 添加")
        self.add_btn.setObjectName("headerSecondaryButton")
        self.add_btn.setFixedHeight(32)  # 固定高度
        self.add_btn.clicked.connect(self.add_more_images)
        self.add_btn.setVisible(False)  # 初始隐藏
        layout.addWidget(self.add_btn)

        # 清空按钮
        self.clear_btn = QPushButton("🗑️ 清空全部")
        self.clear_btn.setObjectName("headerDangerButton")
        self.clear_btn.setFixedHeight(32)  # 固定高度
        self.clear_btn.clicked.connect(self.clear_all_images)
        self.clear_btn.setVisible(False)  # 初始隐藏
        layout.addWidget(self.clear_btn)

        layout.addStretch()

        # 用户状态（占位符）
        status_label = QLabel("0积分剩余")
        status_label.setObjectName("statusLabel")
        layout.addWidget(status_label)

        # 登出按钮（占位符）
        logout_btn = QPushButton("登出")
        logout_btn.setObjectName("headerSecondaryButton")
        logout_btn.setFixedHeight(32)  # 固定高度
        layout.addWidget(logout_btn)

        return header

    def setup_connections(self):
        """设置信号连接"""
        # 拖拽上传
        self.drop_zone.files_dropped.connect(self.add_images)

        # 图片列表
        self.image_list.item_selected.connect(self.on_image_selected)
        self.image_list.item_removed.connect(self.on_image_removed)

        # 控制面板
        self.control_panel.process_requested.connect(self.start_processing)
        self.control_panel.settings_changed.connect(self.on_settings_changed)

    def apply_styles(self):
        """应用主窗口样式"""
        # 先应用自定义样式，确保不被覆盖
        self.setStyleSheet(self.get_custom_styles())

        # 然后应用现代化主题的部分样式（不覆盖自定义样式）
        # style_manager.apply_theme("modern")  # 暂时注释掉，避免覆盖自定义样式

    def apply_component_styles(self):
        """应用组件特定样式，确保不被主窗口样式覆盖"""
        # 重新应用控制面板样式
        if hasattr(self, 'control_panel'):
            self.control_panel.setStyleSheet("""
                QFrame#controlPanel {
                    background-color: #F3F3F4 !important;
                    border-top: 1px solid #E0E0E0 !important;
                }
            """)

    def get_custom_styles(self) -> str:
        """获取自定义样式"""
        return """
        /* 主窗口样式 */
        QMainWindow {
            background-color: #FFFFFF;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        /* 主要内容区域的背景色为白色 */
        #mainWidget, #contentWidget, #leftWidget, #rightWidget {
            background-color: #FFFFFF;
        }

        /* 修复QLabel背景色问题 */
        QLabel {
            background-color: transparent;
            border: none;
        }

        /* 特定QLabel保持原有背景色 */
        #pathLabel {
            background-color: #F8F9FA !important;
            border: 1px solid #E2E8F0 !important;
            border-radius: 4px !important;
            padding: 6px 8px !important;
            font-size: 12px !important;
            color: #374151 !important;
        }

        /* 缩略图容器保持背景色 */
        QLabel[objectName="thumbnailLabel"] {
            background-color: transparent;
        }

        /* 缩略图容器框架 */
        QFrame[objectName="thumbnailContainer"] {
            background-color: #F8F9FA !important;
            border: 1px solid #E2E8F0 !important;
            border-radius: 8px !important;
        }

        /* 头部样式 */
        #headerWidget {
            background-color: white !important;
            border-bottom: 1px solid #E0E0E0 !important;
        }

        /* 拖拽区域样式 - 参考现代设计 */
        #dropZone {
            background-color: #FFFFFF;
            border: none;
        }

        #dropZone[dragActive="true"] {
            border-color: #FF5722;
            background-color: #FFF3E0;
        }
        
        #dropZoneLayout {
            background-color: #FFFFFF;
        }

        /* 图标容器样式 */
        #dropZoneIconContainer {
            background-color: #FFFFFF;
            border: 2px solid #8C8D9C;
            border-radius: 8px;
            position:relative;
        }

        /* 图标样式 */
        #dropZoneIcon {
            font-size: 32px;
            color: #8C8D9C;
            margin: 0;
            padding: 0;
            line-height: 1;
        }

        #dropZonePlus {
            font-size: 16px;
            color: #8C8D9C;
            font-weight: 300;
            margin: 0;
            padding: 0;
            line-height: 1;
            position:absolute;
            right:0
            top:0;
            background:#ffffff;
        }

        /* 主要文案样式 */
        #dropZoneMainText {
            font-size: 16px;
            color: #8C8D9C;
            font-weight: 400;
            line-height: 1.5;
            min-width: 450px;
            max-width: 500px;
        }
        
        

        /* 现代主按钮样式 */
        #modernPrimaryButton {
            background-color: #FA4E18;
            color: #FFFFFF;
            border: none;
            border-radius: 8px;
            padding: 14px 32px;
            font-size: 15px;
            font-weight: 600;
            min-width: 200px;
            cursor: pointer;
        }

        #modernPrimaryButton:hover {
            background-color: #E8440F;
        }

        #modernPrimaryButton:pressed {
            background-color: #D63A06;
        }

        /* 按钮样式 */
        #primaryButton {
            background-color: #FF5722 !important;
            color: white !important;
            border: none !important;
            border-radius: 6px !important;
            padding: 12px 16px !important;
            font-size: 14px !important;
            font-weight: bold !important;
            min-width: 120px !important;
            max-width: 150px !important;
        }

        #primaryButton:hover {
            background-color: #E64A19 !important;
        }

        #primaryButton:pressed {
            background-color: #D84315 !important;
        }

        #primaryButton:disabled {
            background-color: #BDBDBD !important;
            color: #757575 !important;
        }

        #secondaryButton {
            background-color: #F5F5F5 !important;
            color: #424242 !important;
            border: 1px solid #E0E0E0 !important;
            border-radius: 6px !important;
            padding: 8px 16px !important;
            font-size: 14px !important;
        }

        #secondaryButton:hover {
            background-color: #EEEEEE !important;
        }

        #dangerButton {
            background-color: #F5F5F5;
            color: #F44336;
            border: 1px solid #FFCDD2;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
        }

        #dangerButton:hover {
            background-color: #FFEBEE;
        }

        /* 头部按钮样式（shadcn ui风格）*/
        #headerSecondaryButton {
            background-color: #FFFFFF !important;
            color: #374151 !important;
            border: 1px solid #D1D5DB !important;
            border-radius: 6px !important;
            padding: 6px 12px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            min-width: 80px !important;
        }

        #headerSecondaryButton:hover {
            background-color: #F9FAFB !important;
            border-color: #9CA3AF !important;
        }

        #headerSecondaryButton:pressed {
            background-color: #F3F4F6 !important;
        }

        #headerDangerButton {
            background-color: #FFFFFF !important;
            color: #DC2626 !important;
            border: 1px solid #FCA5A5 !important;
            border-radius: 6px !important;
            padding: 6px 12px !important;
            font-size: 13px !important;
            font-weight: 500 !important;
            min-width: 80px !important;
        }

        #headerDangerButton:hover {
            background-color: #FEF2F2 !important;
            border-color: #F87171 !important;
        }

        #headerDangerButton:pressed {
            background-color: #FEE2E2 !important;
        }

        /* 图片列表样式（列表模式）*/
        #imageListWidget {
            padding: 10px !important;
            background-color: #FFFFFF !important;
        }

        #imageItem {
            background-color: white !important;
            border: 1px solid #F3F4F6 !important;
            border-radius: 8px !important;
            margin: 1px 0 !important;
            padding: 0 !important;
        }

        #imageItem:hover {
            background-color: #F8FAFC !important;
            border-color: #E2E8F0 !important;
        }

        #imageItem[selected="true"] {
            background-color: #EEF2FF !important;
            border: 2px solid #6366F1 !important;
        }

        /* 缩略图容器样式（列表模式）*/
        #thumbnailContainer {
        
        }

        /* 信息容器样式 */
        #infoContainer {
            background-color: transparent !important;
        }

        #imageItemName {
            font-size: 13px !important;
            font-weight: 600 !important;
            color: #1F2937 !important;
            margin: 0 !important;
        }

        #imageItemDetails {
            font-size: 11px !important;
            font-weight: 400 !important;
            color: #6B7280 !important;
            margin: 0 !important;
        }

        /* 状态Badge样式（新设计）*/
        #statusBadge {
            border-radius: 10px;
            padding: 2px 8px;
            font-size: 10px;
            font-weight: 500;
            margin: 0;
            min-width: 50px;
        }

        #statusBadge[status="ready"] {
            background-color: #EFF6FF;
            color: #1D4ED8;
            border: 1px solid #DBEAFE;
        }

        #statusBadge[status="processing"] {
            background-color: #FFFBEB;
            color: #D97706;
            border: 1px solid #FED7AA;
        }

        #statusBadge[status="completed"] {
            background-color: #F0FDF4;
            color: #059669;
            border: 1px solid #BBF7D0;
        }

        #statusBadge[status="failed"] {
            background-color: #FEF2F2;
            color: #DC2626;
            border: 1px solid #FECACA;
        }

        /* 头部标签样式 */
        #countLabel {
            font-size: 13px;
            font-weight: 600;
            color: #374151;
        }

        #statusLabel {
            background-color: #E3F2FD;
            color: #1976D2;
            border-radius: 4px;
            padding: 4px 8px;
            font-size: 12px;
            font-weight: 500;
        }

        /* 控制面板容器样式 */
        #controlPanelLeftContent {
            background-color: #F5F5F5;
            border: none;
        }

        #controlPanelButtonContainer {
            background-color: #F5F5F5;
            border: none;
        }

        /* 路径标签样式 */
        #pathLabel {
            background-color: #F8F9FA;
            border: 1px solid #E2E8F0;
            border-radius: 4px;
            padding: 6px 8px;
            font-size: 12px;
            color: #374151;
        }

        /* 状态提示信息样式 */
        QLabel[class="status-message"] {
            background-color: #F8F9FA;
            border: 1px solid #E2E8F0;
            border-radius: 8px;
            padding: 12px 16px;
            font-size: 14px;
            color: #64748B;
            text-align: center;
        }

        QLabel[class="status-message-success"] {
            background-color: #F0FDF4;
            border-color: #BBF7D0;
            color: #166534;
        }

        QLabel[class="status-message-error"] {
            background-color: #FEF2F2;
            border-color: #FECACA;
            color: #DC2626;
        }

        QLabel[class="status-message-warning"] {
            background-color: #FFFBEB;
            border-color: #FED7AA;
            color: #D97706;
        }

        #downloadButton, #deleteButton {
            border: none;
            border-radius: 4px;
            font-size: 10px;
            font-weight: 600;
            padding: 0;
        }

        #downloadButton {
            background-color: #10B981;
            color: white;
            border: 1px solid #059669;
        }

        #downloadButton:hover {
            background-color: #059669;
            border-color: #047857;
        }

        #downloadButton:disabled {
            background-color: #F3F4F6;
            color: #9CA3AF;
            border-color: #D1D5DB;
        }

        #deleteButton {
            background-color: #EF4444;
            color: white;
            border: 1px solid #DC2626;
        }

        #deleteButton:hover {
            background-color: #DC2626;
            border-color: #B91C1C;
        }

        /* 预览区域样式 */
        #previewWidget {
            background-color: white;
       
        }

        /* 控制面板样式 */
        #controlPanel {
            background-color: #F3F3F4 !important;
            border-top: 1px solid #E0E0E0 !important;
        }

        #controlLabel {
            font-size: 14px !important;
            font-weight: 500 !important;
            color: #424242 !important;
        }

        #optionCheckbox {
            font-size: 14px !important;
            color: #424242 !important;
        }

        /* 计数标签样式 */
        #countLabel {
            font-size: 16px !important;
            font-weight: 600 !important;
            color: #374151 !important;
        }

        /* 状态标签样式 */
        #statusLabel {
            background-color: #E3F2FD !important;
            color: #1976D2 !important;
            border-radius: 4px !important;
            padding: 4px 8px !important;
            font-size: 12px !important;
            font-weight: 500 !important;
        }
        """

    def add_images(self, file_paths: List[Path]):
        """添加图片"""
        added_count = 0

        for file_path in file_paths:
            # 检查是否已存在
            if any(item.file_path == file_path for item in self.image_items):
                continue

            # 检查总数限制
            if len(self.image_items) >= 50:
                QMessageBox.warning(self, "文件过多", "最多只能添加50张图片")
                break

            # 创建图片项
            image_item = ImageItem(file_path)
            self.image_items.append(image_item)
            self.image_list.add_image(image_item)
            added_count += 1

        if added_count > 0:
            self.update_ui_state()
            self.files_selected.emit([item.file_path for item in self.image_items])

    def add_more_images(self):
        """添加更多图片"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("图片文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.tif)")

        if file_dialog.exec():
            files = [Path(f) for f in file_dialog.selectedFiles()]
            remaining_slots = 50 - len(self.image_items)

            if len(files) > remaining_slots:
                QMessageBox.warning(
                    self, "文件过多",
                    f"最多还能添加{remaining_slots}张图片"
                )
                files = files[:remaining_slots]

            self.add_images(files)

    def clear_all_images(self):
        """清空所有图片"""
        if not self.image_items:
            return

        reply = QMessageBox.question(
            self, "确认清空",
            f"确定要清空所有{len(self.image_items)}张图片吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            self.image_items.clear()
            self.image_list.clear_all()
            self.update_ui_state()

    def on_image_selected(self, image_item: ImageItem):
        """图片选择事件"""
        if image_item:
            # 确保预览模块可见
            self.right_widget.setVisible(True)
            # 显示选中的图片
            self.preview_widget.show_image(image_item)
        else:
            # 如果没有选中图片，显示空白预览
            self.preview_widget.show_placeholder()

    def on_image_removed(self, image_item: ImageItem):
        """图片移除事件"""
        if image_item in self.image_items:
            self.image_items.remove(image_item)
            self.update_ui_state()

    def update_ui_state(self):
        """更新UI状态"""
        has_images = len(self.image_items) > 0

        # 更新计数
        self.count_label.setText(f"总共{len(self.image_items)}张图片")

        # 显示/隐藏组件
        self.drop_zone.setVisible(not has_images)
        self.image_list.setVisible(has_images)
        self.add_btn.setVisible(has_images)
        self.clear_btn.setVisible(has_images)

        # 控制头部区域的显示/隐藏 - 只有添加图片后才显示
        self.header_widget.setVisible(has_images)

        # 控制预览模块的显示/隐藏
        self.right_widget.setVisible(has_images)

        # 动态调整左侧区域宽度
        if has_images:
            # 有图片时，增加左侧宽度以确保图片信息完整显示
            self.left_widget.setMinimumWidth(520)  # 进一步增加最小宽度
            self.left_widget.setMaximumWidth(650)  # 进一步增加最大宽度
        else:
            # 无图片时，左侧区域铺满整个窗口
            self.left_widget.setMinimumWidth(0)
            self.left_widget.setMaximumWidth(16777215)  # Qt的最大宽度值

        # 更新处理按钮状态
        self.control_panel.set_process_enabled(has_images and not self.is_processing)

        # 更新菜单状态
        self.update_menu_states()

        # 如果有图片但没有选中任何图片，显示空白预览
        if has_images and not self.image_list.selected_item:
            self.preview_widget.show_placeholder()

    def start_processing(self):
        """开始处理"""
        if not self.image_items or self.is_processing:
            return

        # 获取处理设置
        settings = {
            "keep_original_name": self.control_panel.keep_name_cb.isChecked(),
            "save_path": self.control_panel.path_label.text()
        }

        # 更新状态
        self.is_processing = True
        self.control_panel.set_processing_state(True)

        # 发送处理请求信号
        self.processing_requested.emit(self.image_items.copy(), settings)

    def on_processing_completed(self):
        """处理完成"""
        self.is_processing = False
        self.control_panel.set_processing_state(False)

        # 更新图片状态为已完成
        for item in self.image_items:
            item.status = "COMPLETED"

        # 刷新图片列表显示
        self.refresh_image_list()

        QMessageBox.information(self, "处理完成", "所有图片处理完成！")

    def on_processing_failed(self, error_message: str):
        """处理失败"""
        self.is_processing = False
        self.control_panel.set_processing_state(False)

        QMessageBox.critical(self, "处理失败", f"处理过程中发生错误：\n{error_message}")

    def on_settings_changed(self, settings: dict):
        """设置变更"""
        self.logger.info(f"设置已更改: {settings}")
        # 这里可以保存设置到配置文件

    def refresh_image_list(self):
        """刷新图片列表显示"""
        # 使用新的刷新方法
        self.image_list.refresh_from_main_list(self.image_items)
        # 更新UI状态
        self.update_ui_state()

    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_processing:
            reply = QMessageBox.question(
                self, "确认退出",
                "正在处理图片，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.No:
                event.ignore()
                return

        event.accept()

    # 菜单栏方法实现
    def open_files(self):
        """打开文件"""
        file_dialog = QFileDialog()
        file_dialog.setFileMode(QFileDialog.ExistingFiles)
        file_dialog.setNameFilter("图片文件 (*.jpg *.jpeg *.png *.bmp *.tiff *.tif)")

        if file_dialog.exec():
            files = [Path(f) for f in file_dialog.selectedFiles()]
            if len(files) > 50:
                QMessageBox.warning(self, "文件过多", "最多只能选择50张图片")
                files = files[:50]
            self.add_images(files)

    def open_folder(self):
        """打开文件夹"""
        folder = QFileDialog.getExistingDirectory(self, "选择包含图片的文件夹")
        if folder:
            folder_path = Path(folder)
            image_files = []

            # 支持的图片格式
            supported_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

            # 遍历文件夹中的图片文件
            for ext in supported_extensions:
                image_files.extend(folder_path.glob(f"*{ext}"))
                image_files.extend(folder_path.glob(f"*{ext.upper()}"))

            if image_files:
                if len(image_files) > 50:
                    QMessageBox.warning(self, "文件过多", f"文件夹中有{len(image_files)}张图片，最多只能处理50张")
                    image_files = image_files[:50]
                self.add_images(image_files)
            else:
                QMessageBox.information(self, "无图片文件", "所选文件夹中没有找到支持的图片文件")

    def save_results(self):
        """保存结果"""
        if not self.image_items:
            QMessageBox.information(self, "无结果", "没有可保存的处理结果")
            return

        # 检查是否有已处理的图片
        processed_items = [item for item in self.image_items if item.status == "COMPLETED" and item.processed_path]

        if not processed_items:
            QMessageBox.information(self, "无结果", "没有已完成处理的图片")
            return

        # 选择保存位置
        folder = QFileDialog.getExistingDirectory(self, "选择保存位置")
        if folder:
            try:
                import shutil
                save_path = Path(folder)

                for item in processed_items:
                    if item.processed_path and Path(item.processed_path).exists():
                        dest_path = save_path / f"processed_{item.file_name}"
                        shutil.copy2(item.processed_path, dest_path)

                QMessageBox.information(self, "保存成功", f"已保存{len(processed_items)}张处理结果到：\n{folder}")

            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存结果时发生错误：\n{str(e)}")

    def stop_processing(self):
        """停止处理"""
        if self.is_processing:
            reply = QMessageBox.question(
                self, "确认停止",
                "确定要停止当前处理吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.is_processing = False
                self.control_panel.set_processing_state(False)
                self.update_menu_states()
                QMessageBox.information(self, "已停止", "处理已停止")

    def show_settings(self):
        """显示设置对话框"""
        QMessageBox.information(self, "设置", "设置功能正在开发中...")

    def show_help(self):
        """显示帮助"""
        QMessageBox.information(
            self, "帮助",
            f"{APP_NAME} v{APP_VERSION}\n\n"
            "使用方法：\n"
            "1. 拖拽图片文件到上传区域或使用菜单打开文件\n"
            "2. 在图片列表中选择要预览的图片\n"
            "3. 配置保存位置和处理选项\n"
            "4. 点击\"开始处理\"按钮\n"
            "5. 处理完成后可以下载结果\n\n"
            "支持的格式：JPG、PNG、BMP、TIFF\n"
            "最多支持50张图片同时处理"
        )

    def show_about(self):
        """显示关于"""
        QMessageBox.about(
            self, "关于",
            f"<h3>{APP_NAME}</h3>"
            f"<p>版本: {APP_VERSION}</p>"
            f"<p>基于AI的智能水印检测与去除工具</p>"
            f"<p>使用先进的深度学习技术，自动检测并去除图片中的水印</p>"
            f"<p>© 2024 水印去除工具团队</p>"
        )

    def update_menu_states(self):
        """更新菜单状态"""
        has_images = len(self.image_items) > 0

        # 更新处理菜单状态
        self.start_action.setEnabled(has_images and not self.is_processing)
        self.stop_action.setEnabled(self.is_processing)

    def handle_login_success(self):
        """处理登录成功"""
        self.logger.info("用户登录成功")
        self.is_logged_in = True

        # 切换到主界面
        self.stacked_widget.setCurrentWidget(self.main_widget)

        # 初始化主界面状态
        self.update_ui_state()