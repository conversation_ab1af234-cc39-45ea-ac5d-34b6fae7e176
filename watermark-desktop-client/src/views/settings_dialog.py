# -*- coding: utf-8 -*-
"""
设置对话框
支持AI参数调整和应用配置
"""

from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QLabel, QSlider, QDoubleSpinBox, QSpinBox, QCheckBox,
    QComboBox, QGroupBox, QFormLayout, QPushButton,
    QDialogButtonBox, QFileDialog, QLineEdit, QMessageBox
)
from PySide6.QtCore import Qt, Signal
from pathlib import Path

from src.utils.config_manager import config_manager
from src.utils.logger import get_logger
from src.utils.constants import (
    CONFIDENCE_THRESHOLD_RANGE, CONTEXT_EXPANSION_RANGE, 
    MAX_CONCURRENT_TASKS, Theme, Language
)
from src.utils.error_handler import error_handler


class AIParametersWidget(QWidget):
    """AI参数设置组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 检测参数组
        detection_group = QGroupBox("水印检测参数")
        detection_layout = QFormLayout(detection_group)
        
        # 置信度阈值
        confidence_layout = QHBoxLayout()
        self.confidence_slider = QSlider(Qt.Horizontal)
        self.confidence_slider.setRange(
            int(CONFIDENCE_THRESHOLD_RANGE[0] * 100),
            int(CONFIDENCE_THRESHOLD_RANGE[1] * 100)
        )
        self.confidence_slider.setValue(30)  # 默认0.3
        
        self.confidence_spinbox = QDoubleSpinBox()
        self.confidence_spinbox.setRange(*CONFIDENCE_THRESHOLD_RANGE)
        self.confidence_spinbox.setSingleStep(0.01)
        self.confidence_spinbox.setDecimals(2)
        self.confidence_spinbox.setValue(0.3)
        
        # 连接滑块和数值框
        self.confidence_slider.valueChanged.connect(
            lambda v: self.confidence_spinbox.setValue(v / 100.0)
        )
        self.confidence_spinbox.valueChanged.connect(
            lambda v: self.confidence_slider.setValue(int(v * 100))
        )
        
        confidence_layout.addWidget(self.confidence_slider, 1)
        confidence_layout.addWidget(self.confidence_spinbox)
        detection_layout.addRow("置信度阈值:", confidence_layout)
        
        layout.addWidget(detection_group)
        
        # 处理参数组
        processing_group = QGroupBox("图像处理参数")
        processing_layout = QFormLayout(processing_group)
        
        # 增强掩码
        self.enhance_mask_cb = QCheckBox("增强检测掩码")
        self.enhance_mask_cb.setChecked(True)
        processing_layout.addRow(self.enhance_mask_cb)
        
        # 智能增强
        self.smart_enhancement_cb = QCheckBox("使用智能掩码增强")
        self.smart_enhancement_cb.setChecked(True)
        processing_layout.addRow(self.smart_enhancement_cb)
        
        # 上下文扩展比例
        context_layout = QHBoxLayout()
        self.context_slider = QSlider(Qt.Horizontal)
        self.context_slider.setRange(
            int(CONTEXT_EXPANSION_RANGE[0] * 100),
            int(CONTEXT_EXPANSION_RANGE[1] * 100)
        )
        self.context_slider.setValue(12)  # 默认0.12
        
        self.context_spinbox = QDoubleSpinBox()
        self.context_spinbox.setRange(*CONTEXT_EXPANSION_RANGE)
        self.context_spinbox.setSingleStep(0.01)
        self.context_spinbox.setDecimals(2)
        self.context_spinbox.setValue(0.12)
        
        # 连接滑块和数值框
        self.context_slider.valueChanged.connect(
            lambda v: self.context_spinbox.setValue(v / 100.0)
        )
        self.context_spinbox.valueChanged.connect(
            lambda v: self.context_slider.setValue(int(v * 100))
        )
        
        context_layout.addWidget(self.context_slider, 1)
        context_layout.addWidget(self.context_spinbox)
        processing_layout.addRow("上下文扩展比例:", context_layout)
        
        layout.addWidget(processing_group)
        
        # 性能参数组
        performance_group = QGroupBox("性能设置")
        performance_layout = QFormLayout(performance_group)
        
        # GPU加速
        self.use_gpu_cb = QCheckBox("启用GPU加速")
        self.use_gpu_cb.setChecked(True)
        performance_layout.addRow(self.use_gpu_cb)
        
        # 最大并发任务数
        self.max_tasks_spinbox = QSpinBox()
        self.max_tasks_spinbox.setRange(1, MAX_CONCURRENT_TASKS)
        self.max_tasks_spinbox.setValue(4)
        performance_layout.addRow("最大并发任务数:", self.max_tasks_spinbox)
        
        layout.addWidget(performance_group)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        processing_config = config_manager.processing_config
        
        self.confidence_spinbox.setValue(processing_config.default_confidence)
        self.enhance_mask_cb.setChecked(processing_config.enhance_mask)
        self.smart_enhancement_cb.setChecked(processing_config.use_smart_enhancement)
        self.context_spinbox.setValue(processing_config.context_expansion_ratio)
        self.use_gpu_cb.setChecked(processing_config.use_gpu)
        self.max_tasks_spinbox.setValue(processing_config.max_concurrent_tasks)
    
    def save_settings(self):
        """保存设置"""
        config_manager.set('processing.default_confidence', self.confidence_spinbox.value())
        config_manager.set('processing.enhance_mask', self.enhance_mask_cb.isChecked())
        config_manager.set('processing.use_smart_enhancement', self.smart_enhancement_cb.isChecked())
        config_manager.set('processing.context_expansion_ratio', self.context_spinbox.value())
        config_manager.set('processing.use_gpu', self.use_gpu_cb.isChecked())
        config_manager.set('processing.max_concurrent_tasks', self.max_tasks_spinbox.value())


class GeneralSettingsWidget(QWidget):
    """通用设置组件"""
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 界面设置组
        ui_group = QGroupBox("界面设置")
        ui_layout = QFormLayout(ui_group)
        
        # 主题选择
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["跟随系统", "浅色主题", "深色主题"])
        ui_layout.addRow("主题:", self.theme_combo)
        
        # 语言选择
        self.language_combo = QComboBox()
        self.language_combo.addItems(["简体中文", "English"])
        ui_layout.addRow("语言:", self.language_combo)
        
        # 自动保存
        self.auto_save_cb = QCheckBox("自动保存处理结果")
        self.auto_save_cb.setChecked(True)
        ui_layout.addRow(self.auto_save_cb)
        
        # 显示预览
        self.show_preview_cb = QCheckBox("显示图片预览")
        self.show_preview_cb.setChecked(True)
        ui_layout.addRow(self.show_preview_cb)
        
        layout.addWidget(ui_group)
        
        # 路径设置组
        paths_group = QGroupBox("路径设置")
        paths_layout = QFormLayout(paths_group)
        
        # 输出目录
        output_layout = QHBoxLayout()
        self.output_path_edit = QLineEdit()
        self.output_path_edit.setReadOnly(True)
        self.output_browse_btn = QPushButton("浏览...")
        self.output_browse_btn.clicked.connect(self.browse_output_dir)
        
        output_layout.addWidget(self.output_path_edit, 1)
        output_layout.addWidget(self.output_browse_btn)
        paths_layout.addRow("输出目录:", output_layout)
        
        # 临时目录
        temp_layout = QHBoxLayout()
        self.temp_path_edit = QLineEdit()
        self.temp_path_edit.setReadOnly(True)
        self.temp_browse_btn = QPushButton("浏览...")
        self.temp_browse_btn.clicked.connect(self.browse_temp_dir)
        
        temp_layout.addWidget(self.temp_path_edit, 1)
        temp_layout.addWidget(self.temp_browse_btn)
        paths_layout.addRow("临时目录:", temp_layout)
        
        layout.addWidget(paths_group)
        
        layout.addStretch()
    
    def load_settings(self):
        """加载设置"""
        ui_config = config_manager.ui_config
        paths_config = config_manager.paths_config
        
        # 主题设置
        theme_map = {"system": 0, "light": 1, "dark": 2}
        self.theme_combo.setCurrentIndex(theme_map.get(ui_config.theme, 0))
        
        # 语言设置
        language_map = {"zh_CN": 0, "en_US": 1}
        self.language_combo.setCurrentIndex(language_map.get(ui_config.language, 0))
        
        # 其他设置
        self.auto_save_cb.setChecked(ui_config.auto_save)
        self.show_preview_cb.setChecked(ui_config.show_preview)
        
        # 路径设置
        self.output_path_edit.setText(paths_config.output_dir)
        self.temp_path_edit.setText(paths_config.temp_dir)
    
    def save_settings(self):
        """保存设置"""
        # 主题设置
        theme_values = ["system", "light", "dark"]
        theme = theme_values[self.theme_combo.currentIndex()]
        config_manager.set('ui.theme', theme)
        
        # 语言设置
        language_values = ["zh_CN", "en_US"]
        language = language_values[self.language_combo.currentIndex()]
        config_manager.set('ui.language', language)
        
        # 其他设置
        config_manager.set('ui.auto_save', self.auto_save_cb.isChecked())
        config_manager.set('ui.show_preview', self.show_preview_cb.isChecked())
        
        # 路径设置
        config_manager.set('paths.output_dir', self.output_path_edit.text())
        config_manager.set('paths.temp_dir', self.temp_path_edit.text())
    
    @error_handler(show_dialog=True, context="选择输出目录")
    def browse_output_dir(self):
        """浏览输出目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择输出目录", self.output_path_edit.text()
        )
        if dir_path:
            self.output_path_edit.setText(dir_path)
    
    @error_handler(show_dialog=True, context="选择临时目录")
    def browse_temp_dir(self):
        """浏览临时目录"""
        dir_path = QFileDialog.getExistingDirectory(
            self, "选择临时目录", self.temp_path_edit.text()
        )
        if dir_path:
            self.temp_path_edit.setText(dir_path)


class SettingsDialog(QDialog):
    """设置对话框"""
    
    # 信号定义
    settings_changed = Signal()  # 设置已更改
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.setMinimumSize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建选项卡
        tab_widget = QTabWidget()
        
        # AI参数选项卡
        self.ai_params_widget = AIParametersWidget()
        tab_widget.addTab(self.ai_params_widget, "AI参数")
        
        # 通用设置选项卡
        self.general_widget = GeneralSettingsWidget()
        tab_widget.addTab(self.general_widget, "通用设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮区域
        button_box = QDialogButtonBox(
            QDialogButtonBox.Ok | QDialogButtonBox.Cancel | QDialogButtonBox.Apply
        )
        button_box.accepted.connect(self.accept_settings)
        button_box.rejected.connect(self.reject)
        button_box.button(QDialogButtonBox.Apply).clicked.connect(self.apply_settings)
        
        layout.addWidget(button_box)
    
    @error_handler(show_dialog=True, context="应用设置")
    def apply_settings(self):
        """应用设置"""
        # 保存所有设置
        self.ai_params_widget.save_settings()
        self.general_widget.save_settings()
        
        # 保存配置文件
        if config_manager.save_config():
            self.settings_changed.emit()
            self.logger.info("设置已保存")
        else:
            QMessageBox.warning(self, "警告", "设置保存失败")
    
    def accept_settings(self):
        """接受设置"""
        self.apply_settings()
        self.accept()
    
    def reset_to_defaults(self):
        """重置为默认值"""
        reply = QMessageBox.question(
            self, "确认", "确定要重置所有设置为默认值吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 重新加载默认设置
            self.ai_params_widget.load_settings()
            self.general_widget.load_settings()
            self.logger.info("设置已重置为默认值")
