# -*- coding: utf-8 -*-
"""
任务列表组件
支持文件拖拽、任务管理和进度显示
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, QListWidgetItem,
    QPushButton, QLabel, QProgressBar, QFrame, QMenu, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QMimeData, QUrl
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QPixmap, QIcon
from pathlib import Path
from typing import List, Optional

from src.models.task_model import Task, TaskStatus, task_manager
from src.utils.logger import get_logger
from src.utils.style_manager import style_manager
from src.utils.constants import SUPPORTED_IMAGE_FORMATS
from src.utils.error_handler import error_handler


class TaskItemWidget(QFrame):
    """任务项组件"""
    
    # 信号定义
    remove_requested = Signal(str)  # 请求移除任务
    retry_requested = Signal(str)   # 请求重试任务
    
    def __init__(self, task: Task):
        super().__init__()
        self.task = task
        self.logger = get_logger(__name__)
        
        self.setup_ui()
        self.update_display()
    
    def setup_ui(self):
        """设置现代化用户界面"""
        # 应用现代化卡片样式
        style_manager.apply_task_item_style(self)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)  # 增加内边距
        layout.setSpacing(12)  # 统一间距
        
        # 顶部：文件名和状态
        top_layout = QHBoxLayout()
        
        # 文件名
        self.file_label = QLabel()
        self.file_label.setWordWrap(True)
        top_layout.addWidget(self.file_label, 1)
        
        # 状态标签
        self.status_label = QLabel()
        self.status_label.setMinimumWidth(80)
        self.status_label.setAlignment(Qt.AlignCenter)
        top_layout.addWidget(self.status_label)
        
        layout.addLayout(top_layout)
        
        # 中部：进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setMaximum(100)
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 底部：详细信息
        self.detail_label = QLabel()
        self.detail_label.setStyleSheet("color: #666; font-size: 11px;")
        self.detail_label.setVisible(False)
        layout.addWidget(self.detail_label)
        
        # 设置右键菜单
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)
    
    def update_display(self):
        """更新显示内容"""
        # 更新文件名
        if self.task.input_path:
            file_name = Path(self.task.input_path).name
            self.file_label.setText(file_name)
        
        # 更新状态
        self.update_status()
        
        # 更新进度
        self.update_progress()
    
    def update_status(self):
        """更新状态显示"""
        status = self.task.status

        # 使用样式管理器应用状态样式
        status_style_map = {
            TaskStatus.PENDING: "pending",
            TaskStatus.LOADING: "processing",
            TaskStatus.DETECTING: "processing",
            TaskStatus.PROCESSING: "processing",
            TaskStatus.SAVING: "processing",
            TaskStatus.COMPLETED: "completed",
            TaskStatus.FAILED: "failed",
            TaskStatus.CANCELLED: "failed"
        }

        # 状态文本映射
        status_text_map = {
            TaskStatus.PENDING: "等待中",
            TaskStatus.LOADING: "加载中",
            TaskStatus.DETECTING: "检测中",
            TaskStatus.PROCESSING: "处理中",
            TaskStatus.SAVING: "保存中",
            TaskStatus.COMPLETED: "已完成",
            TaskStatus.FAILED: "失败",
            TaskStatus.CANCELLED: "已取消"
        }

        # 应用样式和文本
        status_type = status_style_map.get(status, "pending")
        style_manager.apply_status_style(self.status_label, status_type)
        self.status_label.setText(status_text_map.get(status, "未知"))
        
        # 显示/隐藏进度条
        show_progress = status in [
            TaskStatus.LOADING, TaskStatus.DETECTING, 
            TaskStatus.PROCESSING, TaskStatus.SAVING
        ]
        self.progress_bar.setVisible(show_progress)
        
        # 显示详细信息
        if status == TaskStatus.COMPLETED and self.task.result:
            result = self.task.result
            details = f"处理时间: {result.processing_time:.1f}s"
            if result.watermark_detected:
                details += f" | 检测到 {result.watermark_count} 个水印"
            self.detail_label.setText(details)
            self.detail_label.setVisible(True)
        elif status == TaskStatus.FAILED and self.task.error_message:
            self.detail_label.setText(f"错误: {self.task.error_message}")
            self.detail_label.setVisible(True)
        else:
            self.detail_label.setVisible(False)
    
    def update_progress(self):
        """更新进度"""
        progress = int(self.task.progress * 100)
        self.progress_bar.setValue(progress)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)
        
        # 重试操作（仅对失败或取消的任务）
        if self.task.status in [TaskStatus.FAILED, TaskStatus.CANCELLED]:
            retry_action = menu.addAction("重试")
            retry_action.triggered.connect(lambda: self.retry_requested.emit(self.task.id))
        
        # 移除操作
        remove_action = menu.addAction("移除")
        remove_action.triggered.connect(lambda: self.remove_requested.emit(self.task.id))
        
        # 显示菜单
        if not menu.isEmpty():
            menu.exec(self.mapToGlobal(position))


class TaskListWidget(QWidget):
    """任务列表组件"""
    
    # 信号定义
    files_dropped = Signal(list)        # 文件拖拽信号
    task_selected = Signal(str)         # 任务选择信号
    processing_requested = Signal()     # 请求开始处理
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.task_items = {}  # task_id -> TaskItemWidget
        
        self.setup_ui()
        self.setup_connections()
    
    def setup_ui(self):
        """设置现代化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(16, 16, 16, 16)  # 增加边距
        layout.setSpacing(16)  # 统一间距

        # 顶部工具栏
        toolbar_layout = QHBoxLayout()
        toolbar_layout.setSpacing(12)  # 按钮间距

        # 添加文件按钮
        self.add_files_btn = QPushButton("添加文件")
        self.add_files_btn.clicked.connect(self.add_files_clicked)
        toolbar_layout.addWidget(self.add_files_btn)

        # 清除按钮
        self.clear_btn = QPushButton("清除全部")
        self.clear_btn.clicked.connect(self.clear_all_tasks)
        toolbar_layout.addWidget(self.clear_btn)

        toolbar_layout.addStretch()

        # 开始处理按钮 - 应用主要样式
        self.process_btn = QPushButton("开始处理")
        style_manager.apply_button_style(self.process_btn, "primary")
        self.process_btn.clicked.connect(self.start_processing)
        toolbar_layout.addWidget(self.process_btn)

        layout.addLayout(toolbar_layout)
        
        # 任务列表
        self.task_list = QListWidget()
        self.task_list.setDragDropMode(QListWidget.NoDragDrop)
        self.task_list.itemSelectionChanged.connect(self.on_selection_changed)
        layout.addWidget(self.task_list)
        
        # 现代化拖拽提示
        self.drop_hint = QLabel("拖拽图片文件到此处\n或点击\"添加文件\"按钮")
        self.drop_hint.setAlignment(Qt.AlignCenter)
        style_manager.apply_drop_hint_style(self.drop_hint)
        layout.addWidget(self.drop_hint)
        
        # 启用拖拽
        self.setAcceptDrops(True)
        
        self.update_display()
    
    def setup_connections(self):
        """设置信号连接"""
        # 连接任务管理器的信号（如果有的话）
        pass
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            # 检查是否包含图片文件
            urls = event.mimeData().urls()
            has_images = any(
                Path(url.toLocalFile()).suffix.lower() in SUPPORTED_IMAGE_FORMATS
                for url in urls if url.isLocalFile()
            )
            
            if has_images:
                event.acceptProposedAction()
            else:
                event.ignore()
        else:
            event.ignore()
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        urls = event.mimeData().urls()
        file_paths = []
        
        for url in urls:
            if url.isLocalFile():
                file_path = Path(url.toLocalFile())
                if file_path.suffix.lower() in SUPPORTED_IMAGE_FORMATS:
                    file_paths.append(str(file_path))
        
        if file_paths:
            self.files_dropped.emit(file_paths)
            self.add_tasks_from_files(file_paths)
        
        event.acceptProposedAction()
    
    @error_handler(show_dialog=True, context="添加任务")
    def add_tasks_from_files(self, file_paths: List[str]):
        """从文件路径添加任务"""
        for file_path in file_paths:
            task = Task(input_path=Path(file_path))
            task_id = task_manager.add_task(task)
            self.add_task_item(task)
        
        self.update_display()
        self.logger.info(f"添加了 {len(file_paths)} 个任务")
    
    def add_task_item(self, task: Task):
        """添加任务项"""
        # 创建任务项组件
        task_item_widget = TaskItemWidget(task)
        task_item_widget.remove_requested.connect(self.remove_task)
        task_item_widget.retry_requested.connect(self.retry_task)
        
        # 创建列表项
        list_item = QListWidgetItem()
        list_item.setSizeHint(task_item_widget.sizeHint())
        
        # 添加到列表
        self.task_list.addItem(list_item)
        self.task_list.setItemWidget(list_item, task_item_widget)
        
        # 保存引用
        self.task_items[task.id] = task_item_widget
    
    def remove_task(self, task_id: str):
        """移除任务"""
        if task_id in self.task_items:
            # 从任务管理器移除
            task_manager.remove_task(task_id)
            
            # 从界面移除
            task_item_widget = self.task_items[task_id]
            for i in range(self.task_list.count()):
                item = self.task_list.item(i)
                if self.task_list.itemWidget(item) == task_item_widget:
                    self.task_list.takeItem(i)
                    break
            
            del self.task_items[task_id]
            self.update_display()
            
            self.logger.info(f"移除任务: {task_id}")
    
    def retry_task(self, task_id: str):
        """重试任务"""
        task = task_manager.get_task(task_id)
        if task:
            task.reset()
            if task_id in self.task_items:
                self.task_items[task_id].update_display()
            
            self.logger.info(f"重试任务: {task_id}")
    
    def clear_all_tasks(self):
        """清除所有任务"""
        if self.task_list.count() == 0:
            return
        
        reply = QMessageBox.question(
            self, "确认", "确定要清除所有任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 通过Presenter清除任务，确保状态正确重置
            from src.presenters.main_presenter import main_presenter
            try:
                main_presenter.clear_all_tasks()
                self.task_list.clear()
                self.task_items.clear()
                self.update_display()
                self.logger.info("清除了所有任务")
            except Exception as e:
                self.logger.error(f"清除任务失败: {e}")
                QMessageBox.warning(self, "错误", f"清除任务失败: {e}")
    
    def start_processing(self):
        """开始处理"""
        pending_tasks = task_manager.get_pending_tasks()
        if not pending_tasks:
            QMessageBox.information(self, "提示", "没有待处理的任务")
            return
        
        self.processing_requested.emit()
    
    def add_files_clicked(self):
        """添加文件按钮点击"""
        # 这个信号会被主窗口处理
        pass
    
    def on_selection_changed(self):
        """选择变化事件"""
        current_item = self.task_list.currentItem()
        if current_item:
            task_widget = self.task_list.itemWidget(current_item)
            if task_widget and hasattr(task_widget, 'task'):
                self.task_selected.emit(task_widget.task.id)
    
    def update_task_progress(self, task_id: str, progress: float):
        """更新任务进度"""
        task = task_manager.get_task(task_id)
        if task:
            task.update_progress(progress)
            if task_id in self.task_items:
                self.task_items[task_id].update_progress()
    
    def update_task_status(self, task_id: str, status: TaskStatus):
        """更新任务状态"""
        task = task_manager.get_task(task_id)
        if task:
            task.status = status
            if task_id in self.task_items:
                self.task_items[task_id].update_status()
    
    def update_display(self):
        """更新显示状态"""
        has_tasks = self.task_list.count() > 0
        
        # 显示/隐藏拖拽提示
        self.drop_hint.setVisible(not has_tasks)
        
        # 更新按钮状态
        self.clear_btn.setEnabled(has_tasks)
        
        pending_count = len(task_manager.get_pending_tasks())
        self.process_btn.setEnabled(pending_count > 0)
        self.process_btn.setText(f"开始处理 ({pending_count})")
    
    def get_task_count(self) -> int:
        """获取任务数量"""
        return self.task_list.count()
