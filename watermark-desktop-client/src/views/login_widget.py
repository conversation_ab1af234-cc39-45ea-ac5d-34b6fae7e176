#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录界面组件
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QPixmap, QPainter
from src.utils.logger import get_logger


class LoginWidget(QWidget):
    """登录界面组件"""
    
    login_success = Signal()  # 登录成功信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.setup_ui()
        self.apply_styles()
    
    def setup_ui(self):
        """设置UI"""
        self.setObjectName("loginWidget")
        
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        main_layout.setAlignment(Qt.AlignCenter)
        
        # 内容容器
        content_container = QWidget()
        content_container.setObjectName("loginContentContainer")
        content_container.setMaximumWidth(500)
        content_layout = QVBoxLayout(content_container)
        content_layout.setContentsMargins(40, 60, 40, 60)
        content_layout.setSpacing(0)  # 改为0，手动控制每个元素的间距
        content_layout.setAlignment(Qt.AlignCenter)
        
        # 标题区域 - 改为水平布局，让"Welcome to"和品牌在同一行
        title_container = QWidget()
        title_layout = QHBoxLayout(title_container)  # 改为水平布局
        title_layout.setObjectName("loginTitleLayout")
        title_layout.setContentsMargins(0, 0, 0, 0)
        title_layout.setSpacing(8)  # 减小间距
        title_layout.setAlignment(Qt.AlignCenter)

        # 主标题
        title_label = QLabel("Welcome to")
        title_label.setObjectName("loginTitle")
        title_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(title_label)

        # 图标
        icon_label = QLabel("🔶")  # 使用橙色菱形图标
        icon_label.setObjectName("loginIcon")
        icon_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(icon_label)

        # 品牌名称
        brand_label = QLabel("DeWatermark")
        brand_label.setObjectName("loginBrand")
        brand_label.setAlignment(Qt.AlignCenter)
        title_layout.addWidget(brand_label)

        content_layout.addWidget(title_container)

        # 标题和描述之间的小间距
        content_layout.addSpacing(16)

        # 描述文字
        desc_label = QLabel("Use your google or email to continue, signing up is free!")
        desc_label.setObjectName("loginDescription")
        desc_label.setAlignment(Qt.AlignCenter)
        desc_label.setWordWrap(False)  # 不换行显示
        content_layout.addWidget(desc_label)

        # 描述和按钮之间的大间距
        content_layout.addSpacing(40)

        # 登录按钮
        login_button = QPushButton("👤  Login with browser")
        login_button.setObjectName("loginButton")
        login_button.setFixedHeight(46)  # 减小按钮高度
        login_button.setFixedWidth(360)  # 减小按钮宽度
        login_button.clicked.connect(self.handle_login)
        content_layout.addWidget(login_button)

        # 按钮和法律条款之间的中等间距
        content_layout.addSpacing(32)

        # 法律条款
        terms_container = QWidget()
        terms_layout = QVBoxLayout(terms_container)
        terms_layout.setContentsMargins(0, 0, 0, 0)
        terms_layout.setSpacing(4)
        terms_layout.setAlignment(Qt.AlignCenter)

        terms_label = QLabel("By continuing up, you agree to our Terms of Service and our Privacy Policy.")
        terms_label.setObjectName("loginTerms")
        terms_label.setAlignment(Qt.AlignCenter)
        terms_label.setWordWrap(True)
        terms_layout.addWidget(terms_label)

        content_layout.addWidget(terms_container)
        
        # 将内容容器添加到主布局
        main_layout.addWidget(content_container)
    
    def handle_login(self):
        """处理登录"""
        self.logger.info("用户点击登录按钮")
        # 模拟登录成功
        self.login_success.emit()
    
    def apply_styles(self):
        """应用样式"""
        self.setStyleSheet("""
            /* 登录界面主容器 */
            #loginWidget {
                background-color: #FFFFFF;
            }
            
            /* 内容容器 */
            #loginContentContainer {
                background-color: transparent;
            }
            
            /* 标题布局样式 */
            #loginTitleLayout {
                background-color: transparent;
            }
            
            /* 主标题 */
            #loginTitle {
                font-size: 28px;
                font-weight: 400;
                color: #1F2937;
                margin: 0;
                padding: 0;
            }
            
            /* 图标 */
            #loginIcon {
                font-size: 28px;
                margin: 0;
                padding: 0;
            }
            
            /* 品牌名称 */
            #loginBrand {
                font-size: 28px;
                font-weight: 600;
                color: #1F2937;
                margin: 0;
                padding: 0;
            }
            
            /* 描述文字 */
            #loginDescription {
                font-size: 14px;
                font-weight: 400;
                color: #5C5D6B;
                line-height: 1.5;
                max-width: 450px;
            }
            
            /* 登录按钮 */
            #loginButton {
                background-color: #FFFFFF;
                color: #000000;
                border: 1px solid #D1D5DB;
                border-radius: 12px;
                padding: 10px 20px;
                font-size: 14px;
                font-weight: 500;
                margin:0 auto;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                cursor: pointer;
            }
            
            #loginButton:hover {
                background-color: #F9FAFB;
                border-color: #9CA3AF;
            }
            
            #loginButton:pressed {
                background-color: #F3F4F6;
            }
            
            /* 法律条款 */
            #loginTerms {
                font-size: 13px;
                font-weight: 400;
                color: #9CA3AF;
                line-height: 1.4;
                max-width: 500px;
            }
        """)
