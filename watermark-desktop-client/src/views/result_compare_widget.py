# -*- coding: utf-8 -*-
"""
结果对比组件
支持原图和处理后图片的对比显示
"""

from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QScrollArea,
    QPushButton, QFrame, QSplitter, QFileDialog, QMessageBox,
    QSlider, QCheckBox, QGroupBox
)
from PySide6.QtCore import Qt, Signal, QSize
from PySide6.QtGui import QPixmap, QPainter, QFont
from pathlib import Path
from typing import Optional

from src.models.task_model import Task, TaskStatus
from src.utils.logger import get_logger
from src.utils.constants import PREVIEW_IMAGE_SIZE
from src.utils.error_handler import error_handler


class ImageDisplayWidget(QLabel):
    """图像显示组件"""
    
    def __init__(self, title: str = ""):
        super().__init__()
        self.title = title
        self.original_pixmap = None
        self.scaled_pixmap = None
        self.scale_factor = 1.0
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        self.setMinimumSize(300, 200)
        self.setAlignment(Qt.AlignCenter)
        self.setStyleSheet("""
            QLabel {
                border: 2px solid #ddd;
                border-radius: 5px;
                background-color: #f9f9f9;
            }
        """)
        self.setText(f"{self.title}\n暂无图片")
    
    def set_image(self, image_path: str):
        """设置图片"""
        try:
            pixmap = QPixmap(image_path)
            if not pixmap.isNull():
                self.original_pixmap = pixmap
                self.update_display()
            else:
                self.setText(f"{self.title}\n图片加载失败")
        except Exception as e:
            self.setText(f"{self.title}\n图片加载错误")
    
    def clear_image(self):
        """清除图片"""
        self.original_pixmap = None
        self.scaled_pixmap = None
        self.setText(f"{self.title}\n暂无图片")
    
    def update_display(self):
        """更新显示"""
        if self.original_pixmap:
            # 计算缩放后的尺寸
            widget_size = self.size()
            scaled_pixmap = self.original_pixmap.scaled(
                widget_size,
                Qt.KeepAspectRatio,
                Qt.SmoothTransformation
            )
            self.scaled_pixmap = scaled_pixmap
            self.setPixmap(scaled_pixmap)
        else:
            self.setText(f"{self.title}\n暂无图片")
    
    def resizeEvent(self, event):
        """窗口大小变化事件"""
        super().resizeEvent(event)
        if self.original_pixmap:
            self.update_display()


class ResultCompareWidget(QWidget):
    """结果对比组件"""
    
    # 信号定义
    save_result_requested = Signal(str)  # 请求保存结果
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.current_task: Optional[Task] = None
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 顶部工具栏
        self.create_toolbar(layout)
        
        # 图片对比区域
        self.create_compare_area(layout)
        
        # 底部信息区域
        self.create_info_area(layout)
    
    def create_toolbar(self, parent_layout):
        """创建工具栏"""
        toolbar_frame = QFrame()
        toolbar_frame.setFrameStyle(QFrame.StyledPanel)
        toolbar_layout = QHBoxLayout(toolbar_frame)
        
        # 保存结果按钮
        self.save_btn = QPushButton("保存结果")
        self.save_btn.setEnabled(False)
        self.save_btn.clicked.connect(self.save_result)
        toolbar_layout.addWidget(self.save_btn)
        
        # 打开结果文件夹按钮
        self.open_folder_btn = QPushButton("打开文件夹")
        self.open_folder_btn.setEnabled(False)
        self.open_folder_btn.clicked.connect(self.open_result_folder)
        toolbar_layout.addWidget(self.open_folder_btn)
        
        toolbar_layout.addStretch()
        
        # 显示选项
        self.show_original_cb = QCheckBox("显示原图")
        self.show_original_cb.setChecked(True)
        self.show_original_cb.toggled.connect(self.toggle_original_display)
        toolbar_layout.addWidget(self.show_original_cb)
        
        parent_layout.addWidget(toolbar_frame)
    
    def create_compare_area(self, parent_layout):
        """创建对比区域"""
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        
        # 原图显示区域
        self.original_widget = ImageDisplayWidget("原图")
        splitter.addWidget(self.original_widget)
        
        # 结果图显示区域
        self.result_widget = ImageDisplayWidget("处理结果")
        splitter.addWidget(self.result_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
        parent_layout.addWidget(splitter, 1)
    
    def create_info_area(self, parent_layout):
        """创建信息区域"""
        info_frame = QFrame()
        info_frame.setFrameStyle(QFrame.StyledPanel)
        info_frame.setMaximumHeight(120)
        info_layout = QVBoxLayout(info_frame)
        
        # 任务信息
        task_info_layout = QHBoxLayout()
        
        # 左侧：基本信息
        left_info = QVBoxLayout()
        
        self.file_name_label = QLabel("文件名: 未选择")
        self.file_name_label.setFont(QFont("", 9))
        left_info.addWidget(self.file_name_label)
        
        self.status_label = QLabel("状态: 无任务")
        self.status_label.setFont(QFont("", 9))
        left_info.addWidget(self.status_label)
        
        task_info_layout.addLayout(left_info, 1)
        
        # 右侧：处理结果信息
        right_info = QVBoxLayout()
        
        self.processing_time_label = QLabel("处理时间: --")
        self.processing_time_label.setFont(QFont("", 9))
        right_info.addWidget(self.processing_time_label)
        
        self.detection_info_label = QLabel("检测结果: --")
        self.detection_info_label.setFont(QFont("", 9))
        right_info.addWidget(self.detection_info_label)
        
        task_info_layout.addLayout(right_info, 1)
        
        info_layout.addLayout(task_info_layout)
        
        # 错误信息（如果有）
        self.error_label = QLabel()
        self.error_label.setStyleSheet("color: red; font-weight: bold;")
        self.error_label.setVisible(False)
        self.error_label.setWordWrap(True)
        info_layout.addWidget(self.error_label)
        
        parent_layout.addWidget(info_frame)
    
    def set_task(self, task: Task):
        """设置当前任务"""
        self.current_task = task
        self.update_display()
    
    def clear_task(self):
        """清除当前任务"""
        self.current_task = None
        self.update_display()
    
    def update_display(self):
        """更新显示内容"""
        if not self.current_task:
            self.clear_all_displays()
            return
        
        task = self.current_task
        
        # 更新基本信息
        if task.input_path:
            file_name = Path(task.input_path).name
            self.file_name_label.setText(f"文件名: {file_name}")
            
            # 显示原图
            if self.show_original_cb.isChecked():
                self.original_widget.set_image(str(task.input_path))
            else:
                self.original_widget.clear_image()
        
        # 更新状态
        status_text = self.get_status_text(task.status)
        self.status_label.setText(f"状态: {status_text}")
        
        # 更新处理结果
        if task.result:
            result = task.result
            
            # 处理时间
            if result.processing_time > 0:
                self.processing_time_label.setText(f"处理时间: {result.processing_time:.1f}秒")
            
            # 检测信息
            if result.watermark_detected:
                detection_text = f"检测到 {result.watermark_count} 个水印"
                if result.confidence > 0:
                    detection_text += f" (置信度: {result.confidence:.2f})"
                self.detection_info_label.setText(f"检测结果: {detection_text}")
            else:
                self.detection_info_label.setText("检测结果: 未检测到水印")
            
            # 显示结果图片
            if result.success and result.output_path and Path(result.output_path).exists():
                self.result_widget.set_image(result.output_path)
                self.save_btn.setEnabled(True)
                self.open_folder_btn.setEnabled(True)
            else:
                self.result_widget.clear_image()
                self.save_btn.setEnabled(False)
                self.open_folder_btn.setEnabled(False)
            
            # 显示错误信息
            if not result.success and result.error:
                self.error_label.setText(f"错误: {result.error}")
                self.error_label.setVisible(True)
            else:
                self.error_label.setVisible(False)
        else:
            # 清除结果显示
            self.processing_time_label.setText("处理时间: --")
            self.detection_info_label.setText("检测结果: --")
            self.result_widget.clear_image()
            self.save_btn.setEnabled(False)
            self.open_folder_btn.setEnabled(False)
            self.error_label.setVisible(False)
        
        # 显示任务错误信息
        if task.error_message and task.status == TaskStatus.FAILED:
            self.error_label.setText(f"任务错误: {task.error_message}")
            self.error_label.setVisible(True)
    
    def clear_all_displays(self):
        """清除所有显示"""
        self.file_name_label.setText("文件名: 未选择")
        self.status_label.setText("状态: 无任务")
        self.processing_time_label.setText("处理时间: --")
        self.detection_info_label.setText("检测结果: --")
        
        self.original_widget.clear_image()
        self.result_widget.clear_image()
        
        self.save_btn.setEnabled(False)
        self.open_folder_btn.setEnabled(False)
        self.error_label.setVisible(False)
    
    def get_status_text(self, status: TaskStatus) -> str:
        """获取状态文本"""
        status_map = {
            TaskStatus.PENDING: "等待处理",
            TaskStatus.LOADING: "加载模型",
            TaskStatus.DETECTING: "检测水印",
            TaskStatus.PROCESSING: "图像修复",
            TaskStatus.SAVING: "保存结果",
            TaskStatus.COMPLETED: "处理完成",
            TaskStatus.FAILED: "处理失败",
            TaskStatus.CANCELLED: "已取消"
        }
        return status_map.get(status, "未知状态")
    
    def toggle_original_display(self, checked: bool):
        """切换原图显示"""
        if self.current_task and self.current_task.input_path:
            if checked:
                self.original_widget.set_image(str(self.current_task.input_path))
            else:
                self.original_widget.clear_image()
    
    @error_handler(show_dialog=True, context="保存结果")
    def save_result(self):
        """保存结果"""
        if not self.current_task or not self.current_task.result:
            return
        
        result = self.current_task.result
        if not result.success or not result.output_path:
            QMessageBox.warning(self, "警告", "没有可保存的结果")
            return
        
        # 选择保存位置
        source_path = Path(result.output_path)
        default_name = f"result_{source_path.name}"
        
        save_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存处理结果",
            default_name,
            "图片文件 (*.jpg *.png *.bmp)"
        )
        
        if save_path:
            try:
                import shutil
                shutil.copy2(result.output_path, save_path)
                QMessageBox.information(self, "成功", f"结果已保存到:\n{save_path}")
                self.logger.info(f"保存结果到: {save_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存失败:\n{str(e)}")
    
    @error_handler(show_dialog=True, context="打开文件夹")
    def open_result_folder(self):
        """打开结果文件夹"""
        if not self.current_task or not self.current_task.result:
            return
        
        result = self.current_task.result
        if not result.success or not result.output_path:
            return
        
        try:
            import subprocess
            import sys
            
            folder_path = Path(result.output_path).parent
            
            if sys.platform == "win32":
                subprocess.run(["explorer", str(folder_path)])
            elif sys.platform == "darwin":
                subprocess.run(["open", str(folder_path)])
            else:
                subprocess.run(["xdg-open", str(folder_path)])
                
        except Exception as e:
            QMessageBox.warning(self, "警告", f"无法打开文件夹:\n{str(e)}")
    
    def update_task_progress(self, progress: float):
        """更新任务进度（由外部调用）"""
        if self.current_task:
            self.current_task.update_progress(progress)
            # 可以在这里添加进度显示逻辑
    
    def update_task_result(self, result_data: dict):
        """更新任务结果（由外部调用）"""
        if self.current_task:
            from src.models.task_model import ProcessingResult
            result = ProcessingResult(**result_data)
            self.current_task.complete(result)
            self.update_display()
