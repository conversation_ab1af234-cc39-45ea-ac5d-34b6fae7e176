# -*- coding: utf-8 -*-
"""
主窗口界面
应用程序的主要用户界面
"""

from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QToolBar, QStatusBar, QLabel, QProgressBar,
    QFileDialog, QMessageBox, QApplication
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QIcon, QKeySequence, QPixmap, QAction
from pathlib import Path
from typing import List, Optional

from src.utils.logger import get_logger
from src.utils.config_manager import config_manager
from src.utils.style_manager import style_manager
from src.utils.constants import (
    APP_NAME, APP_VERSION, DEFAULT_WINDOW_SIZE, MIN_WINDOW_SIZE,
    SHORTCUTS, IMAGE_FILTER, SUPPORTED_IMAGE_FORMATS
)
from src.utils.error_handler import error_handler, AppError, ErrorCode


class MainWindow(QMainWindow):
    """主窗口类"""
    
    # 信号定义
    files_selected = Signal(list)  # 文件选择信号
    processing_started = Signal()  # 开始处理信号
    processing_stopped = Signal()  # 停止处理信号
    settings_requested = Signal()  # 设置请求信号
    help_requested = Signal()  # 帮助请求信号
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        
        # 状态变量
        self.is_processing = False
        self.current_files = []
        
        # 子组件（稍后初始化）
        self.task_list_widget = None
        self.result_compare_widget = None
        
        # 状态栏组件
        self.status_label = None
        self.progress_bar = None
        self.model_status_label = None
        
        self.setup_ui()
        self.setup_connections()
        self.load_settings()
        self.apply_modern_styles()

        self.logger.info("主窗口初始化完成")
    
    def setup_ui(self):
        """设置用户界面"""
        # 设置窗口属性
        self.setWindowTitle(f"{APP_NAME} v{APP_VERSION}")
        self.setMinimumSize(*MIN_WINDOW_SIZE)
        self.resize(*DEFAULT_WINDOW_SIZE)
        
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_tool_bar()
        
        # 创建中央部件
        self.create_central_widget()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 设置窗口图标（如果有的话）
        # self.setWindowIcon(QIcon("assets/icons/app.png"))
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开文件
        open_action = QAction("打开文件(&O)", self)
        open_action.setShortcut(QKeySequence(SHORTCUTS['open_files']))
        open_action.setStatusTip("选择要处理的图片文件")
        open_action.triggered.connect(self.open_files)
        file_menu.addAction(open_action)
        
        # 打开文件夹
        open_folder_action = QAction("打开文件夹(&D)", self)
        open_folder_action.setStatusTip("选择包含图片的文件夹")
        open_folder_action.triggered.connect(self.open_folder)
        file_menu.addAction(open_folder_action)
        
        file_menu.addSeparator()
        
        # 保存结果
        save_action = QAction("保存结果(&S)", self)
        save_action.setShortcut(QKeySequence(SHORTCUTS['save_result']))
        save_action.setStatusTip("保存处理结果")
        save_action.triggered.connect(self.save_results)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence(SHORTCUTS['quit_app']))
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 处理菜单
        process_menu = menubar.addMenu("处理(&P)")
        
        # 开始处理
        self.start_action = QAction("开始处理(&S)", self)
        self.start_action.setShortcut(QKeySequence(SHORTCUTS['start_processing']))
        self.start_action.setStatusTip("开始批量处理图片")
        self.start_action.triggered.connect(self.start_processing)
        process_menu.addAction(self.start_action)
        
        # 停止处理
        self.stop_action = QAction("停止处理(&T)", self)
        self.stop_action.setShortcut(QKeySequence(SHORTCUTS['cancel_operation']))
        self.stop_action.setStatusTip("停止当前处理")
        self.stop_action.setEnabled(False)
        self.stop_action.triggered.connect(self.stop_processing)
        process_menu.addAction(self.stop_action)
        
        process_menu.addSeparator()
        
        # 清除任务
        clear_action = QAction("清除任务(&C)", self)
        clear_action.setStatusTip("清除所有任务")
        clear_action.triggered.connect(self.clear_tasks)
        process_menu.addAction(clear_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 设置
        settings_action = QAction("设置(&S)", self)
        settings_action.setShortcut(QKeySequence(SHORTCUTS['show_settings']))
        settings_action.setStatusTip("打开应用程序设置")
        settings_action.triggered.connect(self.show_settings)
        tools_menu.addAction(settings_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 帮助文档
        help_action = QAction("帮助文档(&H)", self)
        help_action.setShortcut(QKeySequence(SHORTCUTS['show_help']))
        help_action.setStatusTip("查看帮助文档")
        help_action.triggered.connect(self.show_help)
        help_menu.addAction(help_action)
        
        help_menu.addSeparator()
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("关于此应用程序")
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_tool_bar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 打开文件按钮
        open_btn = toolbar.addAction("打开文件")
        open_btn.setStatusTip("选择要处理的图片文件")
        open_btn.triggered.connect(self.open_files)
        
        toolbar.addSeparator()
        
        # 开始处理按钮
        self.start_btn = toolbar.addAction("开始处理")
        self.start_btn.setStatusTip("开始批量处理图片")
        self.start_btn.triggered.connect(self.start_processing)
        
        # 停止处理按钮
        self.stop_btn = toolbar.addAction("停止处理")
        self.stop_btn.setStatusTip("停止当前处理")
        self.stop_btn.setEnabled(False)
        self.stop_btn.triggered.connect(self.stop_processing)
        
        toolbar.addSeparator()
        
        # 设置按钮
        settings_btn = toolbar.addAction("设置")
        settings_btn.setStatusTip("打开应用程序设置")
        settings_btn.triggered.connect(self.show_settings)
    
    def create_central_widget(self):
        """创建现代化的中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局 - 使用更大的边距
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(24, 24, 24, 24)  # 从5px增加到24px
        main_layout.setSpacing(24)  # 增加组件间距

        # 创建分割器 - 更细的分割线
        splitter = QSplitter(Qt.Horizontal)
        splitter.setHandleWidth(2)  # 更细的分割线
        main_layout.addWidget(splitter)
        
        # 左侧：任务列表区域（占位符）
        left_widget = QWidget()
        left_widget.setMinimumWidth(350)  # 增加最小宽度
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(16, 16, 16, 16)  # 增加内边距
        left_layout.setSpacing(16)  # 统一间距

        # 任务列表标题
        task_title = QLabel("任务列表")
        # 移除内联样式，将通过样式管理器应用
        left_layout.addWidget(task_title)

        # 任务列表组件占位符
        task_placeholder = QLabel("任务列表组件\n（待实现）")
        task_placeholder.setAlignment(Qt.AlignCenter)
        task_placeholder.setObjectName("placeholder")  # 设置对象名用于样式选择器
        left_layout.addWidget(task_placeholder)
        
        splitter.addWidget(left_widget)
        
        # 右侧：结果对比区域（占位符）
        right_widget = QWidget()
        right_widget.setMinimumWidth(450)  # 增加最小宽度
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(16, 16, 16, 16)  # 增加内边距
        right_layout.setSpacing(16)  # 统一间距

        # 结果对比标题
        result_title = QLabel("结果对比")
        # 移除内联样式，将通过样式管理器应用
        right_layout.addWidget(result_title)

        # 结果对比组件占位符
        result_placeholder = QLabel("结果对比组件\n（待实现）")
        result_placeholder.setAlignment(Qt.AlignCenter)
        result_placeholder.setObjectName("placeholder")  # 设置对象名用于样式选择器
        right_layout.addWidget(result_placeholder)
        
        splitter.addWidget(right_widget)
        
        # 设置分割器比例 - 适应新的最小宽度
        splitter.setSizes([450, 650])
    
    def create_status_bar(self):
        """创建状态栏"""
        status_bar = self.statusBar()
        
        # 主状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumWidth(200)
        status_bar.addPermanentWidget(self.progress_bar)
        
        # 模型状态标签
        self.model_status_label = QLabel("模型未加载")
        status_bar.addPermanentWidget(self.model_status_label)
    
    def setup_connections(self):
        """设置信号连接"""
        # 这里稍后连接各个组件的信号
        pass
    
    def load_settings(self):
        """加载设置"""
        ui_config = config_manager.ui_config

        # 恢复窗口大小
        if ui_config.window:
            width = ui_config.window.get('width', DEFAULT_WINDOW_SIZE[0])
            height = ui_config.window.get('height', DEFAULT_WINDOW_SIZE[1])
            self.resize(width, height)

    def apply_modern_styles(self):
        """应用现代化样式"""
        try:
            # 为关键按钮应用主要样式
            if hasattr(self, 'start_btn'):
                style_manager.apply_button_style(self.start_btn, "primary")

            # 为工具栏中的开始处理按钮应用样式
            toolbar_actions = self.findChildren(QAction)
            for action in toolbar_actions:
                if "开始处理" in action.text():
                    # 注意：QAction本身不能直接应用样式，需要通过工具栏按钮
                    pass

            # 应用标题样式
            title_labels = self.findChildren(QLabel)
            for label in title_labels:
                if "任务列表" in label.text() or "结果对比" in label.text():
                    style_manager.apply_label_style(label, "subtitle")

            self.logger.info("主窗口现代化样式应用完成")

        except Exception as e:
            self.logger.error(f"应用现代化样式失败: {e}")
    
    @error_handler(show_dialog=True, context="打开文件")
    def open_files(self):
        """打开文件对话框"""
        files, _ = QFileDialog.getOpenFileNames(
            self,
            "选择图片文件",
            "",
            IMAGE_FILTER
        )
        
        if files:
            self.current_files = files
            self.files_selected.emit(files)
            self.status_label.setText(f"已选择 {len(files)} 个文件")
            self.logger.info(f"用户选择了 {len(files)} 个文件")
    
    @error_handler(show_dialog=True, context="打开文件夹")
    def open_folder(self):
        """打开文件夹对话框"""
        folder = QFileDialog.getExistingDirectory(self, "选择图片文件夹")
        
        if folder:
            folder_path = Path(folder)
            image_files = []
            
            for ext in SUPPORTED_IMAGE_FORMATS:
                image_files.extend(folder_path.glob(f"*{ext}"))
                image_files.extend(folder_path.glob(f"*{ext.upper()}"))
            
            if image_files:
                file_paths = [str(f) for f in image_files]
                self.current_files = file_paths
                self.files_selected.emit(file_paths)
                self.status_label.setText(f"从文件夹加载了 {len(file_paths)} 个图片文件")
                self.logger.info(f"从文件夹加载了 {len(file_paths)} 个文件")
            else:
                QMessageBox.information(self, "提示", "所选文件夹中没有找到支持的图片文件")
    
    def start_processing(self):
        """开始处理"""
        if not self.current_files:
            QMessageBox.warning(self, "警告", "请先选择要处理的图片文件")
            return
        
        self.is_processing = True
        self.update_ui_state()
        self.processing_started.emit()
        self.status_label.setText("正在处理...")
        self.progress_bar.setVisible(True)
        
        self.logger.info("开始批量处理")
    
    def stop_processing(self):
        """停止处理"""
        self.is_processing = False
        self.update_ui_state()
        self.processing_stopped.emit()
        self.status_label.setText("处理已停止")
        self.progress_bar.setVisible(False)
        
        self.logger.info("停止批量处理")
    
    def clear_tasks(self):
        """清除任务"""
        reply = QMessageBox.question(
            self, "确认", "确定要清除所有任务吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # 通过Presenter清除任务，确保状态正确重置
            from src.presenters.main_presenter import main_presenter
            try:
                main_presenter.clear_all_tasks()
                self.current_files.clear()
                self.status_label.setText("任务已清除")
                self.logger.info("用户清除了所有任务")
            except Exception as e:
                self.logger.error(f"清除任务失败: {e}")
                QMessageBox.warning(self, "错误", f"清除任务失败: {e}")
    
    def save_results(self):
        """保存结果"""
        # TODO: 实现保存结果功能
        QMessageBox.information(self, "提示", "保存结果功能待实现")
    
    def show_settings(self):
        """显示设置对话框"""
        self.settings_requested.emit()
    
    def show_help(self):
        """显示帮助"""
        self.help_requested.emit()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            f"<h3>{APP_NAME}</h3>"
            f"<p>版本: {APP_VERSION}</p>"
            f"<p>基于AI的智能水印检测和去除工具</p>"
            f"<p>使用PySide6 + PyTorch开发</p>"
        )
    
    def update_ui_state(self):
        """更新界面状态"""
        # 更新按钮状态
        self.start_action.setEnabled(not self.is_processing)
        self.stop_action.setEnabled(self.is_processing)
        self.start_btn.setEnabled(not self.is_processing)
        self.stop_btn.setEnabled(self.is_processing)
    
    def update_progress(self, value: int):
        """更新进度条"""
        self.progress_bar.setValue(value)
    
    def update_model_status(self, status: str):
        """更新模型状态"""
        self.model_status_label.setText(status)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.is_processing:
            reply = QMessageBox.question(
                self, "确认退出", "正在处理任务，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.No:
                event.ignore()
                return
        
        # 保存窗口设置
        ui_config = config_manager.ui_config
        ui_config.window['width'] = self.width()
        ui_config.window['height'] = self.height()
        config_manager.save_config()
        
        self.logger.info("主窗口关闭")
        event.accept()
