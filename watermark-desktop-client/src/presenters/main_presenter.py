# -*- coding: utf-8 -*-
"""
主要Presenter
实现MVP架构中的Presenter层，连接视图和业务逻辑
"""

from PySide6.QtCore import QObject, Signal, QTimer
from pathlib import Path
from typing import List, Optional

from src.models.task_model import Task, TaskStatus, ProcessingParams, task_manager
from src.services.ai_service import ai_service
from src.services.task_processor import task_processor
from src.utils.logger import get_logger
from src.utils.config_manager import config_manager
from src.utils.error_handler import error_handler, AppError, ErrorCode


class MainPresenter(QObject):
    """主要Presenter类"""
    
    # 信号定义 - 用于通知视图更新
    task_added = Signal(str)                    # 任务已添加
    task_removed = Signal(str)                  # 任务已移除
    task_progress_updated = Signal(str, float, str)  # 任务进度更新
    task_completed = Signal(str, dict)          # 任务完成
    task_failed = Signal(str, str)              # 任务失败
    all_tasks_completed = Signal()              # 所有任务完成
    processing_started = Signal()               # 开始处理
    processing_stopped = Signal()               # 停止处理
    model_status_changed = Signal(str)          # 模型状态变化
    
    def __init__(self):
        super().__init__()
        self.logger = get_logger(__name__)
        self.is_processing = False
        self.status_timer = None

        # 连接任务处理器信号
        self.setup_task_processor_connections()

        # 延迟初始化定时器，确保在主线程中创建
        self.init_timer()
    
    def init_timer(self):
        """初始化定时器（确保在主线程中）"""
        try:
            from PySide6.QtWidgets import QApplication
            app = QApplication.instance()
            if app and app.thread() == self.thread():
                # 在主线程中创建定时器
                self.status_timer = QTimer()
                self.status_timer.timeout.connect(self.check_model_status)
                self.status_timer.start(5000)  # 每5秒检查一次
            else:
                self.logger.warning("不在主线程中，跳过定时器初始化")
        except Exception as e:
            self.logger.error(f"定时器初始化失败: {e}")

    def setup_task_processor_connections(self):
        """设置任务处理器信号连接"""
        task_processor.task_started.connect(self.on_task_started)
        task_processor.task_progress.connect(self.on_task_progress)
        task_processor.task_completed.connect(self.on_task_completed)
        task_processor.task_failed.connect(self.on_task_failed)
        task_processor.all_tasks_completed.connect(self.on_all_tasks_completed)
    
    # === 文件和任务管理 ===
    
    @error_handler(show_dialog=True, context="添加文件")
    def add_files(self, file_paths: List[str]) -> bool:
        """添加文件到任务列表"""
        if not file_paths:
            return False
        
        added_count = 0
        for file_path in file_paths:
            try:
                # 检查文件是否存在
                path = Path(file_path)
                if not path.exists():
                    self.logger.warning(f"文件不存在: {file_path}")
                    continue
                
                # 创建任务
                task = Task(input_path=path)
                
                # 获取当前处理参数
                processing_config = config_manager.processing_config
                task.params = ProcessingParams(
                    confidence_threshold=processing_config.default_confidence,
                    enhance_mask=processing_config.enhance_mask,
                    use_smart_enhancement=processing_config.use_smart_enhancement,
                    context_expansion_ratio=processing_config.context_expansion_ratio,
                    use_gpu=processing_config.use_gpu
                )
                
                # 添加到任务管理器
                task_id = task_manager.add_task(task)
                self.task_added.emit(task_id)
                added_count += 1
                
            except Exception as e:
                self.logger.error(f"添加文件失败 {file_path}: {e}")
        
        self.logger.info(f"成功添加 {added_count} 个任务")
        return added_count > 0
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if task_manager.remove_task(task_id):
            self.task_removed.emit(task_id)
            self.logger.info(f"移除任务: {task_id}")
            return True
        return False
    
    def clear_all_tasks(self) -> bool:
        """清除所有任务"""
        # 检查是否有活跃任务正在处理
        from src.services.task_processor import task_processor

        # 如果任务处理器没有活跃任务，强制重置处理状态
        if len(task_processor.active_tasks) == 0:
            self.is_processing = False
            self.logger.info("检测到无活跃任务，重置处理状态")

        # 再次检查处理状态
        if self.is_processing and len(task_processor.active_tasks) > 0:
            raise AppError("正在处理任务，无法清除", ErrorCode.INVALID_PARAMETER)

        # 清除任务管理器中的任务
        task_manager.clear_all_tasks()

        # 重置任务处理器状态
        task_processor.reset_state()

        # 确保处理状态正确重置
        self.is_processing = False

        self.logger.info("清除所有任务并重置状态")
        return True
    
    def retry_task(self, task_id: str) -> bool:
        """重试任务"""
        task = task_manager.get_task(task_id)
        if task:
            task.reset()
            self.logger.info(f"重试任务: {task_id}")
            return True
        return False
    
    # === 处理控制 ===
    
    @error_handler(show_dialog=True, context="开始处理")
    def start_processing(self) -> bool:
        """开始批量处理"""
        if self.is_processing:
            self.logger.warning("已在处理中")
            return False
        
        # 检查是否有待处理任务
        pending_tasks = task_manager.get_pending_tasks()
        if not pending_tasks:
            raise AppError("没有待处理的任务", ErrorCode.INVALID_PARAMETER)
        
        # 检查AI服务状态
        if not ai_service.is_model_loaded():
            self.logger.info("正在加载AI模型...")
            if not ai_service.load_models():
                raise AppError("AI模型加载失败", ErrorCode.MODEL_LOAD_FAILED)
        
        # 启动任务处理器
        if not task_processor.start_processing():
            raise AppError("任务处理器启动失败", ErrorCode.UNKNOWN_ERROR)
        
        # 提交所有待处理任务
        for task in pending_tasks:
            task.start()
            task_processor.submit_task(task)
        
        self.is_processing = True
        self.processing_started.emit()
        self.logger.info(f"开始处理 {len(pending_tasks)} 个任务")
        return True
    
    def stop_processing(self) -> bool:
        """停止处理"""
        if not self.is_processing:
            return False
        
        # 停止任务处理器
        task_processor.stop_processing()
        
        # 取消所有正在处理的任务
        processing_tasks = task_manager.get_processing_tasks()
        for task in processing_tasks:
            task.cancel()
        
        self.is_processing = False
        self.processing_stopped.emit()
        self.logger.info("停止处理")
        return True
    
    # === 任务处理器事件处理 ===
    
    def on_task_started(self, task_id: str):
        """任务开始处理"""
        task = task_manager.get_task(task_id)
        if task:
            task.status = TaskStatus.LOADING
            self.logger.info(f"任务开始: {task_id}")
    
    def on_task_progress(self, task_id: str, progress: float, status: str):
        """任务进度更新"""
        task = task_manager.get_task(task_id)
        if task:
            # 更新任务状态
            try:
                task_status = TaskStatus(status)
                task.update_progress(progress, task_status)
            except ValueError:
                task.update_progress(progress)
            
            # 发送进度更新信号
            self.task_progress_updated.emit(task_id, progress, status)
    
    def on_task_completed(self, task_id: str, result: dict):
        """任务完成"""
        task = task_manager.get_task(task_id)
        if task:
            from src.models.task_model import ProcessingResult
            processing_result = ProcessingResult(**result)
            task.complete(processing_result)

            self.task_completed.emit(task_id, result)
            self.logger.info(f"任务完成: {task_id}")

            # 检查是否所有任务都已完成，如果是则重置处理状态
            self._check_and_reset_processing_state()

    def on_task_failed(self, task_id: str, error: str):
        """任务失败"""
        task = task_manager.get_task(task_id)
        if task:
            task.fail(error)

            self.task_failed.emit(task_id, error)
            self.logger.error(f"任务失败: {task_id}, 错误: {error}")

            # 检查是否所有任务都已完成，如果是则重置处理状态
            self._check_and_reset_processing_state()

    def _check_and_reset_processing_state(self):
        """检查并重置处理状态"""
        from src.services.task_processor import task_processor

        # 如果没有活跃任务且没有待处理任务，重置处理状态
        pending_tasks = task_manager.get_pending_tasks()
        if len(task_processor.active_tasks) == 0 and len(pending_tasks) == 0:
            self.is_processing = False
            self.logger.info("所有任务已完成，重置处理状态")
    
    def on_all_tasks_completed(self):
        """所有任务完成"""
        self.is_processing = False
        self.all_tasks_completed.emit()
        
        # 统计结果
        completed_tasks = task_manager.get_completed_tasks()
        success_count = sum(1 for task in completed_tasks if task.result and task.result.success)
        failed_count = len(completed_tasks) - success_count
        
        self.logger.info(f"所有任务完成 - 成功: {success_count}, 失败: {failed_count}")
    
    # === 状态查询 ===
    
    def get_task_count(self) -> dict:
        """获取任务统计"""
        all_tasks = task_manager.get_all_tasks()
        return {
            'total': len(all_tasks),
            'pending': len(task_manager.get_pending_tasks()),
            'processing': len(task_manager.get_processing_tasks()),
            'completed': len(task_manager.get_completed_tasks())
        }
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return task_manager.get_task(task_id)
    
    def get_all_tasks(self) -> List[Task]:
        """获取所有任务"""
        return task_manager.get_all_tasks()
    
    def is_processing_active(self) -> bool:
        """是否正在处理"""
        return self.is_processing
    
    def check_model_status(self):
        """检查模型状态"""
        if ai_service.is_model_loaded():
            self.model_status_changed.emit("模型已加载")
        else:
            self.model_status_changed.emit("模型未加载")
    
    # === 设置管理 ===
    
    def update_processing_params(self, task_id: str, params: dict) -> bool:
        """更新任务处理参数"""
        task = task_manager.get_task(task_id)
        if task and task.status == TaskStatus.PENDING:
            # 更新参数
            for key, value in params.items():
                if hasattr(task.params, key):
                    setattr(task.params, key, value)
            
            self.logger.info(f"更新任务参数: {task_id}")
            return True
        return False
    
    def get_model_info(self) -> dict:
        """获取模型信息"""
        return ai_service.get_model_info()
    
    def reload_models(self) -> bool:
        """重新加载模型"""
        try:
            ai_service.cleanup()
            success = ai_service.load_models()
            
            if success:
                self.model_status_changed.emit("模型已重新加载")
            else:
                self.model_status_changed.emit("模型加载失败")
            
            return success
        except Exception as e:
            self.logger.error(f"重新加载模型失败: {e}")
            return False
    
    # === 清理资源 ===
    
    def cleanup(self):
        """清理资源"""
        if self.is_processing:
            self.stop_processing()
        
        # 停止定时器
        if self.status_timer and self.status_timer.isActive():
            self.status_timer.stop()
        
        self.logger.info("Presenter资源已清理")


# 全局Presenter实例
main_presenter = MainPresenter()
