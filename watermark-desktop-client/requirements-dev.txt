# 开发环境额外依赖
-r requirements.txt

# 代码质量工具
black>=23.0.0                   # 代码格式化
flake8>=6.0.0                   # 代码检查
isort>=5.12.0                   # 导入排序
mypy>=1.0.0                     # 类型检查

# 文档生成
sphinx>=6.0.0                   # 文档生成
sphinx-rtd-theme>=1.2.0         # 文档主题

# 调试工具
ipdb>=0.13.0                    # 调试器
memory-profiler>=0.60.0         # 内存分析

# Qt开发工具
qt6-tools>=6.5.0                # Qt Designer等工具（可选）
