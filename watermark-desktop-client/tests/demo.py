#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水印去除桌面客户端演示脚本
展示应用程序的核心功能
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def demo_ai_service():
    """演示AI服务功能"""
    print("🔍 演示AI服务功能")
    print("-" * 30)
    
    try:
        from src.services.ai_service import ai_service
        
        # 获取模型信息
        model_info = ai_service.get_model_info()
        print(f"检测器模型: {model_info['detector']['name']}")
        print(f"修复器模型: {model_info['inpainter']['name']}")
        print(f"设备: {model_info['device']}")
        print(f"模型已加载: {model_info['is_loaded']}")
        
        # 支持的格式
        formats = ai_service.get_supported_formats()
        print(f"支持格式: {', '.join(formats)}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务演示失败: {e}")
        return False

def demo_task_management():
    """演示任务管理功能"""
    print("\n🔍 演示任务管理功能")
    print("-" * 30)
    
    try:
        from src.models.task_model import Task, TaskManager, ProcessingParams
        from src.utils.constants import TaskStatus
        
        # 创建任务管理器
        task_manager = TaskManager()
        
        # 创建示例任务
        task1 = Task(input_path=Path("demo1.jpg"))
        task2 = Task(input_path=Path("demo2.jpg"))
        
        # 添加任务
        id1 = task_manager.add_task(task1)
        id2 = task_manager.add_task(task2)
        
        print(f"创建任务1: {id1[:8]}...")
        print(f"创建任务2: {id2[:8]}...")
        
        # 模拟任务状态变化
        task1.status = TaskStatus.PROCESSING
        task1.update_progress(0.5)
        
        task2.status = TaskStatus.COMPLETED
        task2.update_progress(1.0)
        
        # 获取任务统计
        all_tasks = task_manager.get_all_tasks()
        pending_tasks = task_manager.get_pending_tasks()
        processing_tasks = task_manager.get_processing_tasks()
        completed_tasks = task_manager.get_completed_tasks()
        
        print(f"总任务数: {len(all_tasks)}")
        print(f"待处理: {len(pending_tasks)}")
        print(f"处理中: {len(processing_tasks)}")
        print(f"已完成: {len(completed_tasks)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务管理演示失败: {e}")
        return False

def demo_config_system():
    """演示配置系统功能"""
    print("\n🔍 演示配置系统功能")
    print("-" * 30)
    
    try:
        from src.utils.config_manager import config_manager
        
        # 获取应用信息
        app_name = config_manager.get('app.name')
        app_version = config_manager.get('app.version')
        print(f"应用名称: {app_name}")
        print(f"应用版本: {app_version}")
        
        # 获取处理配置
        processing_config = config_manager.processing_config
        print(f"默认置信度: {processing_config.default_confidence}")
        print(f"使用GPU: {processing_config.use_gpu}")
        print(f"最大并发任务: {processing_config.max_concurrent_tasks}")
        
        # 获取界面配置
        ui_config = config_manager.ui_config
        print(f"主题: {ui_config.theme}")
        print(f"语言: {ui_config.language}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置系统演示失败: {e}")
        return False

def demo_presenter():
    """演示Presenter功能"""
    print("\n🔍 演示Presenter功能")
    print("-" * 30)
    
    try:
        from src.presenters.main_presenter import main_presenter
        
        # 获取任务统计
        stats = main_presenter.get_task_count()
        print(f"任务统计: {stats}")
        
        # 获取模型信息
        model_info = main_presenter.get_model_info()
        print(f"检测器存在: {model_info['detector']['exists']}")
        print(f"修复器存在: {model_info['inpainter']['exists']}")
        
        # 检查处理状态
        is_processing = main_presenter.is_processing_active()
        print(f"正在处理: {is_processing}")
        
        return True
        
    except Exception as e:
        print(f"❌ Presenter演示失败: {e}")
        return False

def demo_gui_components():
    """演示GUI组件功能"""
    print("\n🔍 演示GUI组件功能")
    print("-" * 30)
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.views.main_window import MainWindow
        from src.views.task_list_widget import TaskListWidget
        from src.views.result_compare_widget import ResultCompareWidget
        from src.views.settings_dialog import SettingsDialog
        
        # 创建应用程序实例（如果不存在）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("✅ 主窗口组件可创建")
        print("✅ 任务列表组件可创建")
        print("✅ 结果对比组件可创建")
        print("✅ 设置对话框可创建")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI组件演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("🚀 水印去除桌面客户端功能演示")
    print("=" * 50)
    
    demos = [
        ("AI服务", demo_ai_service),
        ("任务管理", demo_task_management),
        ("配置系统", demo_config_system),
        ("Presenter", demo_presenter),
        ("GUI组件", demo_gui_components)
    ]
    
    passed = 0
    total = len(demos)
    
    for name, demo_func in demos:
        try:
            if demo_func():
                passed += 1
                print(f"✅ {name}演示成功")
            else:
                print(f"❌ {name}演示失败")
        except Exception as e:
            print(f"❌ {name}演示异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 演示结果: {passed}/{total} 成功")
    
    if passed == total:
        print("🎉 所有功能演示成功！")
        print("\n💡 使用提示:")
        print("1. 运行 'python run.py' 启动完整应用")
        print("2. 拖拽图片文件到任务列表")
        print("3. 点击'开始处理'按钮")
        print("4. 在右侧查看处理结果")
        print("5. 通过菜单栏访问设置")
    else:
        print("⚠️ 部分功能演示失败，请检查相关模块")
    
    return 0 if passed == total else 1

if __name__ == "__main__":
    sys.exit(main())
