# -*- coding: utf-8 -*-
"""
预览模块可见性测试
测试初始状态下预览模块的隐藏和显示逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.views.redesigned_main_window import RedesignedMainWindow


def test_preview_module_visibility():
    """测试预览模块可见性"""
    app = QApplication(sys.argv)

    # 创建主窗口
    window = RedesignedMainWindow()

    # 显示窗口
    window.show()

    print("预览模块可见性测试窗口已启动")
    print("请验证以下行为：")
    print()
    print("初始状态:")
    print("   ✓ 应用程序启动时右侧预览模块应该完全隐藏")
    print("   ✓ 只显示左侧的拖拽上传区域")
    print("   ✓ 整个右侧区域不可见")
    print()
    print("添加图片后:")
    print("   ✓ 左侧显示图片列表")
    print("   ✓ 右侧预览模块仍然隐藏")
    print("   ✓ 只有点击选择图片时，右侧预览模块才显示")
    print()
    print("选择图片后:")
    print("   ✓ 右侧预览模块变为可见")
    print("   ✓ 显示选中图片的预览")
    print()
    print("删除所有图片后:")
    print("   ✓ 右侧预览模块重新隐藏")
    print("   ✓ 回到初始状态")
    print()
    print("测试步骤:")
    print("1. 检查初始状态 - 右侧预览模块应该完全隐藏")
    print("2. 添加图片文件 - 预览模块仍应隐藏")
    print("3. 点击图片列表中的图片 - 预览模块显示")
    print("4. 删除所有图片 - 预览模块重新隐藏")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_preview_module_visibility())