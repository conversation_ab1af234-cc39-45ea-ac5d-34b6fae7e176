# -*- coding: utf-8 -*-
"""
UI修正测试
测试黑色区块和菜单样式修正
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import (
    QApplication, QMainWindow, QVBoxLayout, QHBoxLayout, QWidget,
    QPushButton, QLabel, QFrame, QMenuBar, QMenu, QSplitter,
    QListWidget, QListWidgetItem, QScrollArea
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QAction

from src.utils.style_manager import style_manager


class UIFixTestWindow(QMainWindow):
    """UI修正测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("UI修正测试 - 黑色区块和菜单样式")
        self.setGeometry(100, 100, 1000, 700)

        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """设置用户界面"""
        # 创建菜单栏
        self.create_menu_bar()

        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(24, 24, 24, 24)
        main_layout.setSpacing(24)

        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        splitter.setHandleWidth(2)
        main_layout.addWidget(splitter)

        # 左侧区域
        left_widget = QWidget()
        left_widget.setMinimumWidth(350)
        left_layout = QVBoxLayout(left_widget)
        left_layout.setContentsMargins(16, 16, 16, 16)
        left_layout.setSpacing(16)

        # 左侧标题
        left_title = QLabel("左侧区域测试")
        left_layout.addWidget(left_title)

        # 列表组件测试
        test_list = QListWidget()
        for i in range(5):
            item = QListWidgetItem(f"测试项目 {i+1}")
            test_list.addItem(item)
        left_layout.addWidget(test_list)

        # 占位符测试
        placeholder = QLabel("这是一个占位符\n测试背景色是否正确")
        placeholder.setAlignment(Qt.AlignCenter)
        placeholder.setObjectName("placeholder")
        left_layout.addWidget(placeholder)

        splitter.addWidget(left_widget)

        # 右侧区域
        right_widget = QWidget()
        right_widget.setMinimumWidth(450)
        right_layout = QVBoxLayout(right_widget)
        right_layout.setContentsMargins(16, 16, 16, 16)
        right_layout.setSpacing(16)

        # 右侧标题
        right_title = QLabel("右侧区域测试")
        right_layout.addWidget(right_title)

        # 卡片容器测试
        card_frame = QFrame()
        style_manager.apply_card_style(card_frame)
        card_layout = QVBoxLayout(card_frame)
        card_layout.setContentsMargins(16, 16, 16, 16)

        card_title = QLabel("卡片标题")
        style_manager.apply_label_style(card_title, "subtitle")
        card_layout.addWidget(card_title)

        card_content = QLabel("这是卡片内容，测试背景色和样式是否正确显示。")
        card_content.setWordWrap(True)
        card_layout.addWidget(card_content)

        right_layout.addWidget(card_frame)

        # 按钮测试
        button_layout = QHBoxLayout()

        primary_btn = QPushButton("主要按钮")
        style_manager.apply_button_style(primary_btn, "primary")
        button_layout.addWidget(primary_btn)

        success_btn = QPushButton("成功按钮")
        style_manager.apply_button_style(success_btn, "success")
        button_layout.addWidget(success_btn)

        right_layout.addLayout(button_layout)

        splitter.addWidget(right_widget)

        # 设置分割器比例
        splitter.setSizes([450, 650])

    def create_menu_bar(self):
        """创建菜单栏测试"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        new_action = QAction("新建(&N)", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)

        open_action = QAction("打开(&O)", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        save_action = QAction("保存(&S)", self)
        save_action.setShortcut("Ctrl+S")
        file_menu.addAction(save_action)

        file_menu.addSeparator()

        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut("Ctrl+Z")
        edit_menu.addAction(undo_action)

        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut("Ctrl+Y")
        edit_menu.addAction(redo_action)

        edit_menu.addSeparator()

        copy_action = QAction("复制(&C)", self)
        copy_action.setShortcut("Ctrl+C")
        edit_menu.addAction(copy_action)

        paste_action = QAction("粘贴(&V)", self)
        paste_action.setShortcut("Ctrl+V")
        edit_menu.addAction(paste_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        about_action = QAction("关于(&A)", self)
        help_menu.addAction(about_action)

    def apply_styles(self):
        """应用样式"""
        # 应用现代化主题
        style_manager.apply_theme("modern")

        # 应用标题样式
        title_labels = self.findChildren(QLabel)
        for label in title_labels:
            if "区域测试" in label.text():
                style_manager.apply_label_style(label, "subtitle")


def test_ui_fixes():
    """测试UI修正"""
    app = QApplication(sys.argv)

    # 创建测试窗口
    window = UIFixTestWindow()
    window.show()

    print("UI修正测试窗口已启动")
    print("请检查以下内容：")
    print("1. 是否还有黑色区块？")
    print("2. 菜单下拉样式是否正常？")
    print("3. 背景色是否统一？")
    print("4. 占位符样式是否正确？")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_ui_fixes())