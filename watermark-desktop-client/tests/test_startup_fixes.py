# -*- coding: utf-8 -*-
"""
启动修正测试
测试配置文件和Qt警告修正
"""

import sys
import warnings
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

# 捕获警告
warnings.filterwarnings('error', category=DeprecationWarning)

def test_qt_warnings():
    """测试Qt警告修正"""
    print("🧪 测试Qt高DPI警告修正...")

    try:
        # 这些调用不应该产生DeprecationWarning
        app = QApplication([])

        # 测试高DPI设置（应该不会有警告）
        try:
            QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)
            print("✅ 高DPI设置成功，无警告")
        except AttributeError:
            print("⚠️ Qt版本不支持高DPI缩放策略设置")

        app.quit()
        return True

    except DeprecationWarning as e:
        print(f"❌ 仍有Qt弃用警告: {e}")
        return False
    except Exception as e:
        print(f"❌ Qt测试失败: {e}")
        return False

def test_config_creation():
    """测试配置文件自动创建"""
    print("🧪 测试配置文件自动创建...")

    try:
        from src.utils.config_manager import ConfigManager

        # 删除现有配置文件（如果存在）
        config_file = Path("config/app_config.json")
        if config_file.exists():
            config_file.unlink()
            print("删除现有配置文件")

        # 创建配置管理器（应该自动创建配置文件）
        config_manager = ConfigManager()

        # 检查配置文件是否被创建
        if config_file.exists():
            print("✅ 配置文件自动创建成功")

            # 检查配置内容
            app_name = config_manager.get("app.name", "")
            if app_name == "水印去除工具":
                print("✅ 配置内容正确")
                return True
            else:
                print(f"❌ 配置内容错误: {app_name}")
                return False
        else:
            print("❌ 配置文件未被创建")
            return False

    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_timer_creation():
    """测试定时器创建（主线程检查）"""
    print("🧪 测试定时器主线程创建...")

    try:
        from PySide6.QtCore import QTimer, QThread
        from PySide6.QtWidgets import QApplication

        app = QApplication.instance()
        if not app:
            app = QApplication([])

        # 检查当前线程是否为主线程
        main_thread = app.thread()
        current_thread = QThread.currentThread()

        if main_thread == current_thread:
            print("✅ 当前在主线程中")

            # 创建定时器应该不会有警告
            timer = QTimer()
            timer.start(1000)
            timer.stop()

            print("✅ 定时器创建成功，无警告")
            return True
        else:
            print("❌ 不在主线程中")
            return False

    except Exception as e:
        print(f"❌ 定时器测试失败: {e}")
        return False

def test_main_app_startup():
    """测试主应用程序启动"""
    print("🧪 测试主应用程序启动...")

    try:
        # 导入主应用程序类
        from main import WatermarkRemoverApp

        # 创建应用程序实例（不运行）
        app = WatermarkRemoverApp()

        print("✅ 主应用程序类创建成功")

        # 清理
        app.cleanup()

        return True

    except Exception as e:
        print(f"❌ 主应用程序测试失败: {e}")
        return False

def run_all_tests():
    """运行所有测试"""
    print("🚀 开始启动修正测试")
    print("=" * 50)

    tests = [
        ("Qt警告修正", test_qt_warnings),
        ("配置文件创建", test_config_creation),
        ("定时器创建", test_timer_creation),
        ("主应用程序启动", test_main_app_startup),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)

        try:
            result = test_func()
            results.append((test_name, result))

            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")

        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))

    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")

    print(f"\n总计: {passed}/{total} 测试通过")

    if passed == total:
        print("🎉 所有测试通过！启动修正成功！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步修正")
        return False

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)