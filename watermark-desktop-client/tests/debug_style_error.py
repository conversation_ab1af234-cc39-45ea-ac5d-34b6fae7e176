# -*- coding: utf-8 -*-
"""
调试样式错误脚本
专门用于调试QAction样式错误
"""

import sys
import traceback
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel, QMenuBar
from PySide6.QtCore import Qt
from PySide6.QtGui import QAction

from src.utils.style_manager import style_manager


class DebugWindow(QMainWindow):
    """调试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("样式错误调试")
        self.setGeometry(100, 100, 600, 400)

        self.setup_ui()
        self.test_styles()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 创建菜单栏
        menubar = self.menuBar()
        test_menu = menubar.addMenu("测试")

        # 创建QAction
        self.test_action = QAction("测试动作", self)
        test_menu.addAction(self.test_action)

        # 创建按钮
        self.test_button = QPushButton("测试按钮")
        layout.addWidget(self.test_button)

        # 创建标签
        self.test_label = QLabel("测试标签")
        layout.addWidget(self.test_label)

    def test_styles(self):
        """测试样式应用"""
        print("开始测试样式应用...")

        try:
            # 应用主题
            print("1. 应用主题...")
            style_manager.apply_theme("modern")
            print("✅ 主题应用成功")

            # 测试按钮样式
            print("2. 测试按钮样式...")
            style_manager.apply_button_style(self.test_button, "primary")
            print("✅ 按钮样式应用成功")

            # 测试标签样式
            print("3. 测试标签样式...")
            style_manager.apply_label_style(self.test_label, "title")
            print("✅ 标签样式应用成功")

            # 测试QAction样式（这里可能会出错）
            print("4. 测试QAction样式...")
            try:
                # 尝试直接对QAction应用样式（这应该会失败）
                style_manager.set_widget_class(self.test_action, "test-class")
                print("✅ QAction样式应用成功（意外）")
            except Exception as e:
                print(f"⚠️ QAction样式应用失败（预期）: {e}")

            # 测试所有findChildren的结果
            print("5. 测试findChildren结果...")
            all_actions = self.findChildren(QAction)
            print(f"找到 {len(all_actions)} 个QAction对象")

            for i, action in enumerate(all_actions):
                print(f"  Action {i}: {action.text()}, 类型: {type(action)}")
                try:
                    # 尝试对每个action应用样式
                    style_manager.set_widget_class(action, "test-class")
                    print(f"    ✅ 样式应用成功")
                except Exception as e:
                    print(f"    ⚠️ 样式应用失败: {e}")

            print("所有测试完成")

        except Exception as e:
            print(f"❌ 测试过程中出现错误: {e}")
            traceback.print_exc()


def debug_style_error():
    """调试样式错误"""
    app = QApplication(sys.argv)

    # 创建调试窗口
    window = DebugWindow()
    window.show()

    print("调试窗口已启动，检查控制台输出...")

    return app.exec()


if __name__ == "__main__":
    sys.exit(debug_style_error())