#!/usr/bin/env python3
"""
测试初始状态UI设计
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from src.views.redesigned_main_window import DropZoneWidget

class TestInitialStateWindow(QMainWindow):
    """测试初始状态窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("初始状态UI测试 - 参考现代设计")
        self.setGeometry(100, 100, 900, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加拖拽区域
        self.drop_zone = DropZoneWidget()
        layout.addWidget(self.drop_zone)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FFFFFF;
            }
        """)

def test_initial_state():
    """测试初始状态UI"""
    app = QApplication(sys.argv)
    
    window = TestInitialStateWindow()
    window.show()
    
    print("初始状态UI测试")
    print("参考设计特点:")
    print("1. ✅ 简洁的线条风格图标")
    print("2. ✅ 现代化的描述文字")
    print("3. ✅ 橙色的行动召唤按钮")
    print("4. ✅ 居中对称的布局")
    print("5. ✅ 简洁的背景和边框")
    print("6. ✅ 适当的间距和边距")
    print("7. ✅ 现代化的圆角设计")
    print("8. ✅ 悬停效果和交互反馈")
    
    return app.exec()

if __name__ == "__main__":
    test_initial_state()
