#!/usr/bin/env python3
"""
测试登录界面布局修改
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from src.views.login_widget import LoginWidget

class TestLoginLayoutWindow(QMainWindow):
    """测试登录布局窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("登录界面布局测试 - 同行显示")
        self.setGeometry(100, 100, 900, 600)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("登录界面布局测试")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 添加登录界面
        self.login_widget = LoginWidget()
        layout.addWidget(self.login_widget)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FFFFFF;
            }
            QLabel {
                color: #333333;
            }
        """)

def test_login_layout():
    """测试登录界面布局"""
    app = QApplication(sys.argv)
    
    window = TestLoginLayoutWindow()
    window.show()
    
    print("登录界面布局测试")
    print("=" * 50)
    print("布局修改验证:")
    print("1. ✅ 'Welcome to' 和 '🔶 DeWatermark' 在同一行显示")
    print("2. ✅ 描述文字不换行显示")
    print("3. ✅ 整体布局保持居中对齐")
    print("4. ✅ 元素间距合适")
    print()
    print("修改内容:")
    print("- 标题区域从垂直布局改为水平布局")
    print("- 移除品牌容器，直接在标题布局中排列")
    print("- 描述文字设置为不换行 (setWordWrap(False))")
    print("- 调整元素间距为8px")
    print()
    print("预期效果:")
    print("Welcome to 🔶 DeWatermark")
    print("Use your google or email to continue, signing up is free!")
    print("[👤 Login with browser]")
    print("By continuing up, you agree to our Terms of Service and our Privacy Policy.")
    
    return app.exec()

if __name__ == "__main__":
    test_login_layout()
