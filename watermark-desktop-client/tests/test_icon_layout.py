#!/usr/bin/env python3
"""
测试图标布局和居中效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from src.views.redesigned_main_window import DropZoneWidget

class TestIconLayoutWindow(QMainWindow):
    """测试图标布局窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图标布局测试 - 检查居中和比例")
        self.setGeometry(100, 100, 1000, 700)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("图标布局测试")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 添加拖拽区域
        self.drop_zone = DropZoneWidget()
        layout.addWidget(self.drop_zone)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FFFFFF;
            }
            QLabel {
                color: #333333;
            }
        """)

def test_icon_layout():
    """测试图标布局"""
    app = QApplication(sys.argv)
    
    window = TestIconLayoutWindow()
    window.show()
    
    print("图标布局测试")
    print("=" * 50)
    print("检查项目:")
    print("1. ✅ 图标容器是否为正方形 (120x120)")
    print("2. ✅ 图标是否未被压扁")
    print("3. ✅ 图标容器是否水平居中")
    print("4. ✅ 山形图标和加号是否垂直对齐")
    print("5. ✅ 文字描述是否居中")
    print("6. ✅ 按钮是否与图标垂直居中")
    print("7. ✅ 整体布局是否对称")
    print("8. ✅ 间距是否合适")
    print()
    print("修复内容:")
    print("- 图标容器改为120x120正方形")
    print("- 图标和加号设置固定高度")
    print("- 增加CSS flex布局支持")
    print("- 按钮使用独立容器居中")
    print("- 优化整体间距和边距")
    
    return app.exec()

if __name__ == "__main__":
    test_icon_layout()
