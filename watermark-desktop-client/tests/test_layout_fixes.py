# -*- coding: utf-8 -*-
"""
布局修复测试
测试初始状态布局和添加图片功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.views.redesigned_main_window import RedesignedMainWindow


def test_layout_fixes():
    """测试布局修复"""
    app = QApplication(sys.argv)

    # 创建主窗口
    window = RedesignedMainWindow()

    # 显示窗口
    window.show()

    print("布局修复测试窗口已启动")
    print("请验证以下修复：")
    print()
    print("修复1: 初始状态布局")
    print("   ✓ 左侧上传区域应该水平铺满整个窗口")
    print("   ✓ 右侧预览模块完全隐藏")
    print("   ✓ 上传区域占据全部可用空间")
    print()
    print("修复2: 添加图片功能")
    print("   ✓ 添加图片时不应出现partial错误")
    print("   ✓ 图片列表应该正常显示")
    print("   ✓ 删除和下载按钮应该正常工作")
    print()
    print("修复3: 布局动态调整")
    print("   ✓ 添加图片后左侧区域宽度应该限制为350-450px")
    print("   ✓ 右侧预览模块应该显示")
    print("   ✓ 删除所有图片后左侧区域重新铺满")
    print()
    print("测试步骤:")
    print("1. 检查初始状态 - 左侧上传区域铺满窗口")
    print("2. 添加图片文件 - 不应有错误，正常显示图片列表")
    print("3. 测试删除按钮 - 应该能正常删除图片")
    print("4. 删除所有图片 - 左侧区域重新铺满")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_layout_fixes())