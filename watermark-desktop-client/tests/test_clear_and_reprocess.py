#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试清除任务后重新处理的问题
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_clear_and_reprocess():
    """测试清除任务后重新处理"""
    print("🔍 测试清除任务后重新处理...")
    
    try:
        from src.models.task_model import Task, task_manager
        from src.services.task_processor import task_processor
        from src.presenters.main_presenter import main_presenter
        
        # 1. 创建测试任务
        print("\n1. 创建测试任务...")
        task1 = Task(input_path=Path("test1.jpg"))
        task2 = Task(input_path=Path("test2.jpg"))
        
        task_id1 = task_manager.add_task(task1)
        task_id2 = task_manager.add_task(task2)
        
        print(f"   创建任务: {task_id1[:8]}..., {task_id2[:8]}...")
        print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        
        # 2. 模拟处理完成
        print("\n2. 模拟任务处理完成...")
        task1.status = task1.status.__class__.COMPLETED
        task2.status = task2.status.__class__.COMPLETED
        
        # 模拟任务处理器状态
        task_processor.active_tasks.add(task_id1)
        task_processor.active_tasks.add(task_id2)
        task_processor.completed_tasks.add(task_id1)
        task_processor.completed_tasks.add(task_id2)
        
        print(f"   活跃任务: {len(task_processor.active_tasks)}")
        print(f"   完成任务: {len(task_processor.completed_tasks)}")
        
        # 3. 清除所有任务
        print("\n3. 清除所有任务...")
        main_presenter.clear_all_tasks()
        
        print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        print(f"   活跃任务: {len(task_processor.active_tasks)}")
        print(f"   完成任务: {len(task_processor.completed_tasks)}")
        print(f"   处理状态: {main_presenter.is_processing}")
        
        # 4. 添加新任务
        print("\n4. 添加新任务...")
        task3 = Task(input_path=Path("test3.jpg"))
        task_id3 = task_manager.add_task(task3)
        
        print(f"   新任务: {task_id3[:8]}...")
        print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        
        # 5. 检查是否可以开始处理
        print("\n5. 检查处理状态...")
        pending_tasks = task_manager.get_pending_tasks()
        print(f"   待处理任务: {len(pending_tasks)}")
        print(f"   处理器运行状态: {task_processor.is_running}")
        print(f"   Presenter处理状态: {main_presenter.is_processing}")
        print(f"   工作线程数量: {len(task_processor.workers)}")
        print(f"   任务队列大小: {task_processor.get_queue_size()}")

        # 6. 尝试开始处理
        print("\n6. 尝试开始处理...")
        try:
            result = main_presenter.start_processing()
            print(f"   开始处理结果: {result}")
            print(f"   处理器运行状态: {task_processor.is_running}")
            print(f"   Presenter处理状态: {main_presenter.is_processing}")
            print(f"   工作线程数量: {len(task_processor.workers)}")
            print(f"   任务队列大小: {task_processor.get_queue_size()}")

            # 等待一下看看任务是否被提交
            time.sleep(2)
            print(f"   2秒后活跃任务: {len(task_processor.active_tasks)}")

        except Exception as e:
            print(f"   开始处理失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_clear_and_reprocess()
