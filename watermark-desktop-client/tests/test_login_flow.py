#!/usr/bin/env python3
"""
测试完整的登录流程
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from src.views.redesigned_main_window import RedesignedMainWindow

def test_login_flow():
    """测试完整的登录流程"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = RedesignedMainWindow()
    window.show()
    
    print("完整登录流程测试")
    print("=" * 50)
    
    def check_initial_state():
        print("1. 初始状态检查:")
        print(f"   - 当前界面: {'登录界面' if window.stacked_widget.currentWidget() == window.login_widget else '主界面'}")
        print(f"   - 登录状态: {'已登录' if window.is_logged_in else '未登录'}")
        print(f"   - 登录界面是否显示: {window.login_widget.isVisible()}")
        print()
        
        # 3秒后模拟点击登录按钮
        QTimer.singleShot(3000, simulate_login)
    
    def simulate_login():
        print("2. 模拟用户点击登录按钮...")
        # 触发登录按钮点击
        window.login_widget.handle_login()
        
        # 500ms后检查登录后状态
        QTimer.singleShot(500, check_after_login)
    
    def check_after_login():
        print("3. 登录后状态检查:")
        print(f"   - 当前界面: {'登录界面' if window.stacked_widget.currentWidget() == window.login_widget else '主界面'}")
        print(f"   - 登录状态: {'已登录' if window.is_logged_in else '未登录'}")
        print(f"   - 主界面是否显示: {window.main_widget.isVisible()}")
        print(f"   - 头部区域是否隐藏: {not window.header_widget.isVisible()}")
        print(f"   - 拖拽区域是否显示: {window.drop_zone.isVisible()}")
        print()
        
        if window.is_logged_in and window.stacked_widget.currentWidget() == window.main_widget:
            print("✅ 登录流程测试成功！")
            print("   - 登录界面正确显示")
            print("   - 点击登录按钮成功切换到主界面")
            print("   - 主界面初始状态正确")
        else:
            print("❌ 登录流程测试失败！")
        
        # 2秒后关闭应用
        QTimer.singleShot(2000, app.quit)
    
    # 1秒后开始测试
    QTimer.singleShot(1000, check_initial_state)
    
    return app.exec()

if __name__ == "__main__":
    test_login_flow()
