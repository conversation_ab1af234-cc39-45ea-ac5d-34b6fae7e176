# -*- coding: utf-8 -*-
"""
UI Bug修复测试
测试三个具体问题的修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.views.redesigned_main_window import RedesignedMainWindow


def test_ui_bug_fixes():
    """测试UI Bug修复"""
    app = QApplication(sys.argv)

    # 创建主窗口
    window = RedesignedMainWindow()

    # 显示窗口
    window.show()

    print("UI Bug修复测试窗口已启动")
    print("请验证以下三个修复：")
    print()
    print("修复1: 初始状态预览区域")
    print("   ✓ 应用程序启动时右侧预览区域应该完全空白")
    print("   ✓ 不应显示任何占位符内容")
    print()
    print("修复2: 图片预览和删除功能")
    print("   ✓ 添加图片后，预览区域应保持空白")
    print("   ✓ 只有点击选择图片列表中的图片时才显示预览")
    print("   ✓ 图片列表中的删除按钮（X）应该能正常工作")
    print()
    print("修复3: 处理功能导入错误")
    print("   ✓ 点击'开始处理'按钮不应出现TaskType导入错误")
    print("   ✓ 处理功能应该能正常启动")
    print()
    print("测试步骤:")
    print("1. 检查初始状态 - 右侧预览区域应该空白")
    print("2. 添加图片文件 - 预览区域仍应保持空白")
    print("3. 点击图片列表中的图片 - 此时才显示预览")
    print("4. 点击图片项的X按钮 - 应该能删除图片")
    print("5. 添加图片后点击'开始处理' - 不应有错误")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_ui_bug_fixes())