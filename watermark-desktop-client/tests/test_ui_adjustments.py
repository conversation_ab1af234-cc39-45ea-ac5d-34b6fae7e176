# -*- coding: utf-8 -*-
"""
UI调整测试
测试四个具体调整的实现效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.views.redesigned_main_window import RedesignedMainWindow


def test_ui_adjustments():
    """测试UI调整"""
    app = QApplication(sys.argv)

    # 创建主窗口
    window = RedesignedMainWindow()

    # 显示窗口
    window.show()

    print("UI调整测试窗口已启动")
    print("请验证以下四个调整：")
    print()
    print("1. 菜单栏功能:")
    print("   - 检查是否有完整的菜单栏（文件、处理、工具、帮助）")
    print("   - 测试菜单项功能（打开文件、设置、帮助等）")
    print("   - 验证快捷键是否工作")
    print()
    print("2. 初始状态预览区域:")
    print("   - 右侧预览区域应显示合适的初始内容")
    print("   - 不应显示'选择图片在此预览'的占位符")
    print("   - 应显示应用程序相关的欢迎信息")
    print()
    print("3. 移除文字移除功能:")
    print("   - 底部控制面板不应有'启用文字移除'复选框")
    print("   - 只应有'保留原始名称'选项")
    print()
    print("4. 真实水印去除处理:")
    print("   - 添加图片后点击'开始处理'")
    print("   - 应调用真实的AI服务进行处理")
    print("   - 处理完成后应能下载结果")
    print()
    print("测试步骤:")
    print("1. 拖拽或选择图片文件")
    print("2. 查看图片列表和状态")
    print("3. 点击开始处理按钮")
    print("4. 观察处理过程和结果")
    print("5. 测试菜单栏各项功能")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_ui_adjustments())