#!/usr/bin/env python3
"""
测试登录界面UI
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from src.views.redesigned_main_window import RedesignedMainWindow

def test_login_ui():
    """测试登录界面UI"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = RedesignedMainWindow()
    window.show()
    
    print("登录界面UI测试")
    print("=" * 50)
    print("测试内容:")
    print("1. ✅ 启动时显示登录界面")
    print("2. ✅ 复刻参考图片的UI设计")
    print("3. ✅ Welcome to DeWatermark 标题")
    print("4. ✅ 橙色品牌图标")
    print("5. ✅ 描述文字居中显示")
    print("6. ✅ Login with browser 按钮")
    print("7. ✅ 法律条款文字")
    print("8. ✅ 点击登录按钮切换到主界面")
    print()
    print("UI特点:")
    print("- 简洁的白色背景")
    print("- 居中对称的布局")
    print("- 现代化的按钮设计")
    print("- 清晰的文字层次")
    print("- 符合参考设计的视觉效果")
    print()
    print("操作说明:")
    print("- 点击 'Login with browser' 按钮模拟登录")
    print("- 登录成功后会自动切换到主界面")
    
    def check_initial_state():
        print(f"\n当前界面: {'登录界面' if window.stacked_widget.currentWidget() == window.login_widget else '主界面'}")
        print(f"登录状态: {'已登录' if window.is_logged_in else '未登录'}")
    
    # 1秒后检查初始状态
    QTimer.singleShot(1000, check_initial_state)
    
    return app.exec()

if __name__ == "__main__":
    test_login_ui()
