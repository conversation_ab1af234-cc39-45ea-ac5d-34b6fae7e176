#!/usr/bin/env python3
"""
测试背景色修复效果
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from src.views.redesigned_main_window import RedesignedMainWindow

def test_background_fix():
    """测试背景色修复效果"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = RedesignedMainWindow()
    
    # 模拟登录状态，直接显示主界面
    window.is_logged_in = True
    window.stacked_widget.setCurrentWidget(window.main_widget)
    window.update_ui_state()
    
    window.show()
    
    print("背景色修复测试")
    print("=" * 50)
    print("问题分析:")
    print("🔍 发现 #F9FBFC 背景色的根本原因:")
    print("   - modern_theme.qss 中设置了全局 QWidget 背景色为 #F8FAFC")
    print("   - 这个颜色非常接近您看到的 #F9FBFC")
    print("   - 主题文件的全局样式覆盖了我们的局部设置")
    print()
    print("修复方案:")
    print("✅ 在主窗口样式中添加强制覆盖:")
    print("   QWidget { background-color: #FFFFFF; }")
    print("✅ 这将覆盖主题文件的全局设置")
    print("✅ 确保所有区域都是纯白背景")
    print()
    print("技术细节:")
    print("- modern_theme.qss: QWidget { background-color: #F8FAFC; }")
    print("- 主窗口样式: QWidget { background-color: #FFFFFF; } (优先级更高)")
    print("- CSS优先级: 后加载的样式覆盖先加载的样式")
    print()
    
    def check_background_colors():
        print("背景色检查:")
        print(f"- 主窗口背景: {window.styleSheet()}")
        print(f"- 拖拽区域对象名: {window.drop_zone.objectName()}")
        print(f"- 左侧区域对象名: {window.left_widget.objectName()}")
        print()
        print("预期效果:")
        print("- 整个界面应该是纯白色背景")
        print("- 不再有 #F9FBFC 或 #F8FAFC 灰色背景")
        print("- 拖拽区域完全是白色")
    
    # 2秒后检查背景色
    QTimer.singleShot(2000, check_background_colors)
    
    return app.exec()

if __name__ == "__main__":
    test_background_fix()
