#!/usr/bin/env python3
"""
测试初始状态头部是否隐藏
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from src.views.redesigned_main_window import RedesignedMainWindow

def test_initial_header_state():
    """测试初始状态头部是否隐藏"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = RedesignedMainWindow()
    window.show()
    
    def check_initial_state():
        print("初始状态检查:")
        print("=" * 30)
        print(f"图片数量: {len(window.image_items)}")
        print(f"头部区域是否隐藏: {not window.header_widget.isVisible()}")
        print(f"拖拽区域是否显示: {window.drop_zone.isVisible()}")
        print(f"图片列表是否隐藏: {not window.image_list.isVisible()}")
        print(f"预览区域是否隐藏: {not window.right_widget.isVisible()}")
        
        if not window.header_widget.isVisible():
            print("✅ 初始状态正确：头部区域已隐藏")
        else:
            print("❌ 初始状态错误：头部区域仍然显示")
        
        # 1秒后关闭应用
        QTimer.singleShot(1000, app.quit)
    
    # 500ms后检查状态，确保初始化完成
    QTimer.singleShot(500, check_initial_state)
    
    return app.exec()

if __name__ == "__main__":
    test_initial_header_state()
