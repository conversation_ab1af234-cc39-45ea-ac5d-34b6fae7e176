#!/usr/bin/env python3
"""
测试底部控制面板按钮布局
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from src.views.redesigned_main_window import ControlPanelWidget

class TestWindow(QMainWindow):
    """测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("控制面板按钮布局测试")
        self.setGeometry(100, 100, 800, 200)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 添加控制面板
        self.control_panel = ControlPanelWidget()
        layout.addWidget(self.control_panel)
        
        # 启用处理按钮以便测试
        self.control_panel.set_process_enabled(True)

        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FFFFFF;
            }
        """)

def test_button_layout():
    """测试按钮布局"""
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    print("控制面板按钮布局测试")
    print("检查项目:")
    print("1. 开始处理按钮是否在右侧")
    print("2. 按钮是否垂直居中")
    print("3. 按钮是否完整显示")
    print("4. 按钮尺寸是否合适")
    print("5. 左侧内容区域背景色是否透明")
    print("6. 右侧按钮容器背景色是否透明")
    print("7. 保存位置是否左对齐")
    print("8. 路径显示宽度是否受限")

    return app.exec()

if __name__ == "__main__":
    test_button_layout()
