#!/usr/bin/env python3
"""
测试UI改进效果
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from src.views.redesigned_main_window import RedesignedMainWindow, ImageItem

def test_ui_improvements():
    """测试UI改进效果"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = RedesignedMainWindow()
    window.show()
    
    # 模拟添加一些图片来测试列表显示
    def add_test_images():
        # 查找一些测试图片
        test_images = []
        outputs_dir = project_root / "outputs"
        if outputs_dir.exists():
            for img_file in outputs_dir.glob("*.jpg"):
                if len(test_images) < 5:  # 只添加5张图片用于测试
                    test_images.append(img_file)
            
            for img_file in outputs_dir.glob("*.jpeg"):
                if len(test_images) < 5:
                    test_images.append(img_file)
        
        # 如果没有找到图片，创建一些虚拟的图片项
        if not test_images:
            print("没有找到测试图片，创建虚拟图片项...")
            for i in range(3):
                # 创建虚拟图片项用于测试UI
                fake_path = project_root / f"test_image_{i+1}.jpg"
                image_item = ImageItem(fake_path)
                image_item.file_size = f"{50 + i*10}.2 KB"
                image_item.status = ["READY", "PROCESSING", "COMPLETED"][i % 3]
                window.image_items.append(image_item)
        else:
            # 使用真实图片
            for img_path in test_images:
                image_item = ImageItem(img_path)
                image_item.status = "READY"
                window.image_items.append(image_item)
        
        # 刷新UI
        window.refresh_image_list()
        print(f"已添加 {len(window.image_items)} 张图片用于测试UI")
    
    # 延迟添加图片，让窗口先显示
    QTimer.singleShot(1000, add_test_images)
    
    print("UI改进测试启动...")
    print("测试内容:")
    print("1. 图片列表与预览区域之间的1像素分割线")
    print("2. 增加的左侧区域宽度")
    print("3. 优化的状态badge样式")
    print("4. 改进的头部按钮样式")
    print("5. 底部控制面板按钮位置调整")
    
    return app.exec()

if __name__ == "__main__":
    test_ui_improvements()
