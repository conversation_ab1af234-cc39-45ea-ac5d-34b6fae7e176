#!/usr/bin/env python3
"""
测试登录界面间距优化
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel
from src.views.login_widget import LoginWidget

class TestLoginSpacingWindow(QMainWindow):
    """测试登录间距窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("登录界面间距优化测试")
        self.setGeometry(100, 100, 1000, 680)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title = QLabel("登录界面间距优化测试")
        title.setStyleSheet("font-size: 18px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 添加登录界面
        self.login_widget = LoginWidget()
        layout.addWidget(self.login_widget)
        
        # 设置窗口样式
        self.setStyleSheet("""
            QMainWindow {
                background-color: #FFFFFF;
            }
            QLabel {
                color: #333333;
            }
        """)

def test_login_spacing():
    """测试登录界面间距优化"""
    app = QApplication(sys.argv)
    
    window = TestLoginSpacingWindow()
    window.show()
    
    print("登录界面间距优化测试")
    print("=" * 60)
    print("🎨 UI设计原则应用:")
    print()
    print("1. ✅ 视觉层次优化")
    print("   - 标题和描述: 16px 间距 (紧密关联)")
    print("   - 描述和按钮: 40px 间距 (功能分离)")
    print("   - 按钮和条款: 32px 间距 (适中分离)")
    print()
    print("2. ✅ 按钮尺寸优化")
    print("   - 高度: 48px → 44px (更紧凑)")
    print("   - 宽度: 300px → 280px (更合适)")
    print("   - 内边距: 12px 24px → 10px 20px")
    print("   - 字体: 15px → 14px")
    print()
    print("3. ✅ 间距层次设计")
    print("   - 小间距 (16px): 相关内容之间")
    print("   - 中间距 (32px): 不同功能区域")
    print("   - 大间距 (40px): 主要操作分离")
    print()
    print("📐 间距设计理念:")
    print("- 标题 ↓ 16px ↓ 描述 (内容组)")
    print("- 描述 ↓ 40px ↓ 按钮 (操作分离)")
    print("- 按钮 ↓ 32px ↓ 条款 (补充信息)")
    print()
    print("🎯 用户体验改进:")
    print("- 更清晰的视觉层次")
    print("- 更合理的信息分组")
    print("- 更舒适的操作体验")
    print("- 更专业的界面设计")
    
    return app.exec()

if __name__ == "__main__":
    test_login_spacing()
