#!/usr/bin/env python3
"""
测试按钮颜色修改
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from src.views.redesigned_main_window import RedesignedMainWindow

class TestButtonColorWindow(QMainWindow):
    """测试按钮颜色窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("按钮颜色测试")
        self.setGeometry(100, 100, 1000, 680)
        
        # 中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 布局
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(40, 40, 40, 40)
        layout.setSpacing(30)
        
        # 标题
        title = QLabel("modernPrimaryButton 颜色测试")
        title.setStyleSheet("font-size: 24px; font-weight: bold; color: #333333; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 颜色说明
        color_info = QLabel("新颜色规范: #FA4E18 (主色) + 白色文字")
        color_info.setStyleSheet("font-size: 16px; color: #666666; margin-bottom: 30px;")
        layout.addWidget(color_info)
        
        # 测试按钮
        test_button = QPushButton("Select up to 50 images")
        test_button.setObjectName("modernPrimaryButton")
        layout.addWidget(test_button)
        
        # 颜色对比展示
        color_demo = QWidget()
        color_demo_layout = QVBoxLayout(color_demo)
        color_demo_layout.setSpacing(15)
        
        # 主色展示
        main_color = QLabel("#FA4E18 - 主按钮颜色")
        main_color.setStyleSheet("""
            background-color: #FA4E18;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        """)
        color_demo_layout.addWidget(main_color)
        
        # 悬停色展示
        hover_color = QLabel("#E8440F - 悬停状态颜色")
        hover_color.setStyleSheet("""
            background-color: #E8440F;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        """)
        color_demo_layout.addWidget(hover_color)
        
        # 按下色展示
        pressed_color = QLabel("#D63A06 - 按下状态颜色")
        pressed_color.setStyleSheet("""
            background-color: #D63A06;
            color: white;
            padding: 15px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
        """)
        color_demo_layout.addWidget(pressed_color)
        
        layout.addWidget(color_demo)
        
        # 应用主窗口样式
        main_window = RedesignedMainWindow()
        self.setStyleSheet(main_window.get_custom_styles())

def test_button_color():
    """测试按钮颜色修改"""
    app = QApplication(sys.argv)
    
    window = TestButtonColorWindow()
    window.show()
    
    print("modernPrimaryButton 颜色测试")
    print("=" * 50)
    print("🎨 颜色规范更新:")
    print()
    print("✅ 主按钮颜色:")
    print("   - 背景色: #FA4E18 (橙红色)")
    print("   - 文字色: #FFFFFF (纯白色)")
    print("   - 边框: 无边框")
    print()
    print("✅ 交互状态颜色:")
    print("   - 悬停状态: #E8440F (稍深)")
    print("   - 按下状态: #D63A06 (更深)")
    print()
    print("🎯 设计特点:")
    print("   - 高对比度: 橙红背景 + 白色文字")
    print("   - 视觉突出: 鲜明的品牌色调")
    print("   - 交互反馈: 清晰的状态变化")
    print("   - 现代感: 符合当前设计趋势")
    print()
    print("📐 技术规格:")
    print("   - 圆角: 8px")
    print("   - 内边距: 14px 32px")
    print("   - 字体: 15px, 600 weight")
    print("   - 最小宽度: 450px")
    print()
    print("🧪 测试内容:")
    print("   - 按钮颜色显示正确")
    print("   - 文字颜色为白色")
    print("   - 悬停效果正常")
    print("   - 按下效果正常")
    
    return app.exec()

if __name__ == "__main__":
    test_button_color()
