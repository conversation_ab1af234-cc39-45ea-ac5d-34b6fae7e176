#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型加载状态
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_model_status():
    """测试模型状态"""
    print("🔍 测试AI模型状态...")
    
    try:
        from src.services.ai_service import ai_service
        
        print(f"模型是否已加载: {ai_service.is_model_loaded()}")
        
        # 尝试加载模型
        if not ai_service.is_model_loaded():
            print("正在加载模型...")
            success = ai_service.load_models()
            print(f"模型加载结果: {success}")
            print(f"加载后状态: {ai_service.is_model_loaded()}")
        
        # 获取模型信息
        model_info = ai_service.get_model_info()
        print("\n模型信息:")
        for key, value in model_info.items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_model_status()
