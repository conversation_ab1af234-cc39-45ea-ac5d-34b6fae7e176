#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Presenter状态管理
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_presenter_state():
    """测试Presenter状态管理"""
    print("🔍 测试Presenter状态管理...")
    
    try:
        from src.models.task_model import Task, task_manager
        from src.services.task_processor import task_processor
        from src.presenters.main_presenter import main_presenter
        
        # 1. 初始状态
        print("\n1. 初始状态...")
        print(f"   Presenter处理状态: {main_presenter.is_processing}")
        print(f"   任务处理器运行状态: {task_processor.is_running}")
        print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        
        # 2. 添加任务
        print("\n2. 添加任务...")
        task1 = Task(input_path=Path("test1.jpg"))
        task_id1 = task_manager.add_task(task1)
        print(f"   添加任务: {task_id1[:8]}...")
        print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        
        # 3. 模拟开始处理
        print("\n3. 模拟开始处理...")
        main_presenter.is_processing = True  # 模拟处理状态
        task_processor.is_running = True     # 模拟处理器运行
        print(f"   Presenter处理状态: {main_presenter.is_processing}")
        print(f"   任务处理器运行状态: {task_processor.is_running}")
        
        # 4. 尝试再次开始处理（应该被拒绝）
        print("\n4. 尝试再次开始处理...")
        try:
            result = main_presenter.start_processing()
            print(f"   开始处理结果: {result}")
        except Exception as e:
            print(f"   开始处理异常: {e}")
        
        # 5. 清除所有任务
        print("\n5. 清除所有任务...")
        try:
            # 先重置处理状态，允许清除
            main_presenter.is_processing = False
            result = main_presenter.clear_all_tasks()
            print(f"   清除结果: {result}")
            print(f"   Presenter处理状态: {main_presenter.is_processing}")
            print(f"   任务处理器运行状态: {task_processor.is_running}")
            print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        except Exception as e:
            print(f"   清除失败: {e}")
        
        # 6. 添加新任务
        print("\n6. 添加新任务...")
        task2 = Task(input_path=Path("test2.jpg"))
        task_id2 = task_manager.add_task(task2)
        print(f"   添加任务: {task_id2[:8]}...")
        print(f"   任务数量: {len(task_manager.get_all_tasks())}")
        
        # 7. 尝试开始处理
        print("\n7. 尝试开始处理...")
        try:
            result = main_presenter.start_processing()
            print(f"   开始处理结果: {result}")
            print(f"   Presenter处理状态: {main_presenter.is_processing}")
            print(f"   任务处理器运行状态: {task_processor.is_running}")
        except Exception as e:
            print(f"   开始处理失败: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_presenter_state()
