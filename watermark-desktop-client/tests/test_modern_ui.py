# -*- coding: utf-8 -*-
"""
现代化UI测试
测试样式系统和现代化界面组件
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLabel
from PySide6.QtCore import Qt

from src.utils.style_manager import style_manager


class ModernUITestWindow(QMainWindow):
    """现代化UI测试窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("现代化UI测试")
        self.setGeometry(100, 100, 800, 600)

        self.setup_ui()
        self.apply_styles()

    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(24, 24, 24, 24)
        layout.setSpacing(16)

        # 标题
        self.title_label = QLabel("现代化UI样式测试")
        self.title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(self.title_label)

        # 不同类型的按钮
        self.primary_btn = QPushButton("主要按钮")
        layout.addWidget(self.primary_btn)

        self.success_btn = QPushButton("成功按钮")
        layout.addWidget(self.success_btn)

        self.destructive_btn = QPushButton("危险按钮")
        layout.addWidget(self.destructive_btn)

        self.default_btn = QPushButton("默认按钮")
        layout.addWidget(self.default_btn)

        # 不同类型的标签
        self.subtitle_label = QLabel("这是一个副标题")
        layout.addWidget(self.subtitle_label)

        self.muted_label = QLabel("这是一个静音标签")
        layout.addWidget(self.muted_label)

        self.small_label = QLabel("这是一个小标签")
        layout.addWidget(self.small_label)

    def apply_styles(self):
        """应用现代化样式"""
        # 应用主题
        style_manager.apply_theme("modern")

        # 应用按钮样式
        style_manager.apply_button_style(self.primary_btn, "primary")
        style_manager.apply_button_style(self.success_btn, "success")
        style_manager.apply_button_style(self.destructive_btn, "destructive")
        # default_btn 使用默认样式

        # 应用标签样式
        style_manager.apply_label_style(self.title_label, "title")
        style_manager.apply_label_style(self.subtitle_label, "subtitle")
        style_manager.apply_label_style(self.muted_label, "muted")
        style_manager.apply_label_style(self.small_label, "small")


def test_modern_ui():
    """测试现代化UI"""
    app = QApplication(sys.argv)

    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建测试窗口
    window = ModernUITestWindow()
    window.show()

    print("现代化UI测试窗口已启动")
    print("请检查以下内容：")
    print("1. 主题样式是否正确加载")
    print("2. 按钮样式是否符合现代化设计")
    print("3. 标签样式是否正确应用")
    print("4. 布局间距是否合理")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_modern_ui())