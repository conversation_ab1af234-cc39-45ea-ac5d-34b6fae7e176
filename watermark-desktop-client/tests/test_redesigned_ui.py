# -*- coding: utf-8 -*-
"""
重新设计的UI测试
测试新的界面设计和交互
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.views.redesigned_main_window import RedesignedMainWindow
from src.utils.style_manager import style_manager


def test_redesigned_ui():
    """测试重新设计的UI"""
    app = QApplication(sys.argv)

    # 创建主窗口
    window = RedesignedMainWindow()

    # 连接信号（用于测试）
    def on_files_selected(files):
        print(f"选择了 {len(files)} 个文件:")
        for file in files:
            print(f"  - {file}")

    def on_processing_requested(items, settings):
        print(f"请求处理 {len(items)} 个图片项目")
        print(f"设置: {settings}")

        # 模拟处理完成
        window.on_processing_completed()

    window.files_selected.connect(on_files_selected)
    window.processing_requested.connect(on_processing_requested)

    # 显示窗口
    window.show()

    print("重新设计的UI测试窗口已启动")
    print("请测试以下功能：")
    print("1. 拖拽图片到上传区域")
    print("2. 点击选择图片按钮")
    print("3. 查看图片列表和预览")
    print("4. 测试添加、删除、清空功能")
    print("5. 测试处理设置和开始处理")
    print("6. 检查界面响应式布局")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_redesigned_ui())