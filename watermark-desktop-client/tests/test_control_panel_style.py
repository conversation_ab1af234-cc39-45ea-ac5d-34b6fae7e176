#!/usr/bin/env python3
"""
测试控制面板背景色的简单脚本
"""

import sys
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QFrame, QLabel
from PySide6.QtCore import Qt

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.views.redesigned_main_window import ControlPanelWidget

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("控制面板背景色测试")
        self.setGeometry(100, 100, 800, 400)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        layout = QVBoxLayout(central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 添加一些内容区域（白色背景）
        content_area = QFrame()
        content_area.setStyleSheet("background-color: white; border: 1px solid red;")
        content_label = QLabel("内容区域（应该是白色背景）")
        content_label.setAlignment(Qt.AlignCenter)
        content_layout = QVBoxLayout(content_area)
        content_layout.addWidget(content_label)
        layout.addWidget(content_area)
        
        # 添加控制面板
        self.control_panel = ControlPanelWidget()
        layout.addWidget(self.control_panel)
        
        # 打印控制面板的样式信息
        print("控制面板对象名:", self.control_panel.objectName())
        print("控制面板样式表:", self.control_panel.styleSheet())
        
        # 尝试强制设置背景色
        self.control_panel.setStyleSheet("""
            QFrame#controlPanel {
                background-color: #F3F3F4 !important;
                border: 2px solid blue !important;
            }
        """)
        print("强制设置后的样式表:", self.control_panel.styleSheet())

def main():
    app = QApplication(sys.argv)
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
