#!/usr/bin/env python3
"""
测试头部区域显示/隐藏逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QTimer
from src.views.redesigned_main_window import RedesignedMainWindow, ImageItem

def test_header_visibility():
    """测试头部区域显示/隐藏逻辑"""
    app = QApplication(sys.argv)
    
    # 创建主窗口
    window = RedesignedMainWindow()
    window.show()
    
    print("头部区域显示/隐藏测试")
    print("=" * 40)
    
    # 检查初始状态
    def check_initial_state():
        print("1. 检查初始状态:")
        print(f"   - 头部区域是否隐藏: {not window.header_widget.isVisible()}")
        print(f"   - 拖拽区域是否显示: {window.drop_zone.isVisible()}")
        print(f"   - 图片列表是否隐藏: {not window.image_list.isVisible()}")
        print()
        
        # 2秒后添加图片
        QTimer.singleShot(2000, add_test_images)
    
    def add_test_images():
        print("2. 添加测试图片:")
        # 查找测试图片
        outputs_dir = project_root / "outputs"
        test_images = []
        if outputs_dir.exists():
            for img_file in outputs_dir.glob("*.jpg"):
                if len(test_images) < 3:
                    test_images.append(img_file)
        
        if test_images:
            for img_path in test_images:
                image_item = ImageItem(img_path)
                window.image_items.append(image_item)
        else:
            # 创建虚拟图片项
            for i in range(2):
                fake_path = project_root / f"test_image_{i+1}.jpg"
                image_item = ImageItem(fake_path)
                window.image_items.append(image_item)
        
        # 刷新UI
        window.refresh_image_list()
        
        print(f"   - 已添加 {len(window.image_items)} 张图片")
        print(f"   - 头部区域是否显示: {window.header_widget.isVisible()}")
        print(f"   - 拖拽区域是否隐藏: {not window.drop_zone.isVisible()}")
        print(f"   - 图片列表是否显示: {window.image_list.isVisible()}")
        print()
        
        # 3秒后清空图片
        QTimer.singleShot(3000, clear_all_images)
    
    def clear_all_images():
        print("3. 清空所有图片:")
        window.image_items.clear()
        window.image_list.clear_all()
        window.update_ui_state()
        
        print(f"   - 图片数量: {len(window.image_items)}")
        print(f"   - 头部区域是否隐藏: {not window.header_widget.isVisible()}")
        print(f"   - 拖拽区域是否显示: {window.drop_zone.isVisible()}")
        print(f"   - 图片列表是否隐藏: {not window.image_list.isVisible()}")
        print()
        print("✅ 测试完成！头部区域显示/隐藏逻辑正常工作")
    
    # 1秒后开始测试
    QTimer.singleShot(1000, check_initial_state)
    
    return app.exec()

if __name__ == "__main__":
    test_header_visibility()
