# -*- coding: utf-8 -*-
"""
处理功能修复测试
测试开始处理按钮的功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt

from src.views.redesigned_main_window import RedesignedMainWindow


def test_processing_fix():
    """测试处理功能修复"""
    app = QApplication(sys.argv)

    # 创建主窗口
    window = RedesignedMainWindow()

    # 显示窗口
    window.show()

    print("处理功能修复测试窗口已启动")
    print("请验证以下修复：")
    print()
    print("修复: TaskProcessor方法调用错误")
    print("   ✓ 点击'开始处理'按钮不应出现'add_task'错误")
    print("   ✓ 应该正确调用'submit_task'方法")
    print("   ✓ 任务处理器应该正常启动")
    print("   ✓ 任务应该能正常提交到处理队列")
    print()
    print("测试步骤:")
    print("1. 添加一张或多张图片")
    print("2. 点击'开始处理'按钮")
    print("3. 观察是否有错误信息")
    print("4. 检查任务是否正常开始处理")
    print()
    print("预期结果:")
    print("- 不应出现'add_task'相关错误")
    print("- 应该看到任务开始处理的日志")
    print("- 图片状态应该变为'PROCESSING'")
    print("- 处理完成后状态变为'COMPLETED'")

    return app.exec()


if __name__ == "__main__":
    sys.exit(test_processing_fix())