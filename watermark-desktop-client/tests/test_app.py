#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序测试脚本
用于测试桌面客户端是否能正常启动和运行
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        # 测试PySide6导入
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6导入成功")
        
        # 测试项目模块导入
        from src.utils.config_manager import config_manager
        print("✅ 配置管理器导入成功")
        
        from src.utils.logger import setup_logging
        print("✅ 日志模块导入成功")
        
        from src.models.task_model import Task, TaskManager
        print("✅ 任务模型导入成功")
        
        from src.services.ai_service import ai_service
        print("✅ AI服务导入成功")
        
        from src.views.main_window import MainWindow
        print("✅ 主窗口导入成功")
        
        from src.presenters.main_presenter import main_presenter
        print("✅ Presenter导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_config():
    """测试配置系统"""
    print("\n🔍 测试配置系统...")
    
    try:
        from src.utils.config_manager import config_manager
        
        # 测试配置加载
        config_loaded = config_manager.load_config()
        print(f"✅ 配置加载: {'成功' if config_loaded else '使用默认配置'}")
        
        # 测试配置访问
        app_name = config_manager.get('app.name', '未知应用')
        print(f"✅ 应用名称: {app_name}")
        
        processing_config = config_manager.processing_config
        print(f"✅ 处理配置: 置信度={processing_config.default_confidence}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_ai_service():
    """测试AI服务"""
    print("\n🔍 测试AI服务...")
    
    try:
        from src.services.ai_service import ai_service
        
        # 测试模型信息获取
        model_info = ai_service.get_model_info()
        print(f"✅ 检测器模型存在: {model_info['detector']['exists']}")
        print(f"✅ 修复器模型存在: {model_info['inpainter']['exists']}")
        print(f"✅ 设备: {model_info['device']}")
        
        return True
        
    except Exception as e:
        print(f"❌ AI服务测试失败: {e}")
        return False

def test_task_model():
    """测试任务模型"""
    print("\n🔍 测试任务模型...")
    
    try:
        from src.models.task_model import Task, TaskManager, ProcessingParams
        from pathlib import Path
        
        # 创建测试任务
        task = Task(input_path=Path("test.jpg"))
        print(f"✅ 任务创建成功: {task.id}")
        
        # 测试任务管理器
        task_manager = TaskManager()
        task_id = task_manager.add_task(task)
        print(f"✅ 任务添加成功: {task_id}")
        
        # 测试任务获取
        retrieved_task = task_manager.get_task(task_id)
        print(f"✅ 任务获取成功: {retrieved_task is not None}")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务模型测试失败: {e}")
        return False

def test_gui_creation():
    """测试GUI创建"""
    print("\n🔍 测试GUI创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        from src.views.main_window import MainWindow
        from src.views.task_list_widget import TaskListWidget
        from src.views.result_compare_widget import ResultCompareWidget
        
        # 创建应用程序实例
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建主窗口
        main_window = MainWindow()
        print("✅ 主窗口创建成功")
        
        # 创建组件
        task_list = TaskListWidget()
        print("✅ 任务列表组件创建成功")
        
        result_compare = ResultCompareWidget()
        print("✅ 结果对比组件创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试水印去除桌面客户端")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_ai_service,
        test_task_model,
        test_gui_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！应用程序准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
