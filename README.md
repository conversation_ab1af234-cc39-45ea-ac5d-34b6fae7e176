# 🚀 自动水印去除系统

一个基于AI的端到端水印去除系统，结合了先进的水印检测和图像修复技术，实现全自动的图片水印去除。

## ✨ 特性

- 🔍 **智能水印检测**: 基于JoyCaption的YOLOv11模型，精确检测图片中的水印位置
- 🎨 **高质量修复**: 集成LAMA模型，实现高质量的图像修复
- ⚡ **全自动流程**: 无需手动标记，从检测到去除完全自动化
- 🔄 **批量处理**: 支持批量图片处理，提高工作效率
- 📊 **性能监控**: 实时监控系统性能和内存使用情况
- 🛡️ **错误处理**: 完善的错误处理和日志记录系统
- 🌐 **RESTful API**: 提供完整的HTTP API接口

### 🆕 最新增强功能

- 🚀 **真正的LAMA模型**: 196MB big-lama.pt模型，专业级修复质量
- 🧠 **智能掩码增强**: 2.4倍扩展效果，自适应上下文扩展
- 🎨 **RGB通道自动修复**: 自动检测并修复颜色通道问题
- ⚡ **详细性能监控**: 检测时间、修复时间、掩码扩展倍数等详细指标
- 🔧 **统一处理接口**: 简化的API调用，支持高级配置参数

### 🎯 核心功能

- 🎯 **可视化系统**: 检测框绘制、掩码叠加、并排对比等多种可视化方式
- 🔬 **验证框架**: 全面的LAMA模型性能评估，支持单个和多个水印场景
- 🛠️ **调试工具**: 深入的性能分析和问题诊断，自动生成优化建议
- 📈 **质量指标**: SSIM、PSNR、水印移除完整性等多种评估指标
- 🔍 **问题隔离**: 分阶段调试，精确定位检测vs修复阶段的问题

## 🏗️ 技术架构

### 核心组件

1. **水印检测器** (`WatermarkDetector`)
   - 基于YOLOv11的水印检测模型
   - 来源：fancyfeast/joycaption-watermark-detection
   - 支持多种置信度阈值和参数调整

2. **图像修复器** (`LamaInpainter`)
   - 基于LAMA的高质量图像修复
   - 支持大面积掩码修复
   - 自动回退到OpenCV inpaint

3. **水印去除管道** (`WatermarkRemovalPipeline`)
   - 集成检测和修复功能
   - 自动数据流转换
   - 性能优化和内存管理

### 系统架构

```
用户请求 → FastAPI → 水印检测 → 掩码增强 → 图像修复 → 结果返回
    ↓           ↓         ↓         ↓         ↓         ↓
  验证参数   异步处理   YOLO模型   形态学操作  LAMA模型   文件保存
```

## 📁 项目结构

```
watermark-detection/
├── app.py                 # FastAPI主应用
├── models/                # 核心模型模块
│   ├── watermark_detector.py      # 水印检测器
│   ├── lama_inpainter.py          # LAMA修复器
│   ├── real_lama_inpainter.py     # 真正的LAMA模型
│   └── watermark_removal_pipeline.py  # 统一处理管道
├── tests/                 # 测试代码
│   ├── integration/       # 集成测试
│   ├── unit/             # 单元测试
│   ├── performance/      # 性能测试
│   ├── examples/         # 示例和调试工具
│   └── run_tests.py      # 测试运行器
├── docs/                 # 项目文档
│   ├── README.md         # 文档索引
│   ├── API.md           # API文档
│   ├── QUICK_START_ENHANCED.md  # 快速开始
│   └── ...              # 其他技术文档
├── images/              # 测试图像
├── outputs/             # 输出结果
└── requirements.txt     # 依赖列表
```

## 🚀 快速开始

### 环境要求

- Python 3.8+
- CUDA 11.0+ (可选，用于GPU加速)
- 8GB+ RAM (推荐16GB+)

### 安装

1. **克隆仓库**
```bash
git clone <repository-url>
cd watermark-detection
```

2. **下载模型文件**
```bash
# 检查模型状态
python download_models.py --check

# 下载所有模型（需要先配置下载URL）
python download_models.py --all

# 或者手动下载模型文件到对应目录
# models/yolo/yolo11x-train28-best.pt (109MB)
# models/lama/big-lama/models/best.ckpt (391MB)
```

3. **安装依赖**
```bash
pip install -r requirements.txt
```

4. **启动服务**
```bash
python app.py
```

服务将在 `http://localhost:8000` 启动。

### Docker部署

```bash
# 构建镜像
docker build -t watermark-removal .

# 运行容器
docker run -p 8000:8000 watermark-removal
```

## 🧪 运行测试

### 使用测试运行器
```bash
# 运行所有测试
python tests/run_tests.py

# 运行特定类型的测试
python tests/run_tests.py --type unit        # 单元测试
python tests/run_tests.py --type integration # 集成测试
python tests/run_tests.py --type performance # 性能测试
python tests/run_tests.py --type app         # API测试
python tests/run_tests.py --type examples    # 示例代码
```

### 手动运行测试
```bash
# 测试真正的LAMA模型
python tests/integration/test_real_lama.py --image images/test_image2.jpeg

# 测试智能掩码增强
python tests/performance/enhanced_effect_test.py --image images/test_image2.jpeg

# 测试API集成（需要先启动app.py）
python tests/integration/test_app_integration.py
```

## 📖 API文档

### 核心接口

#### 1. 同步水印去除
```http
POST /remove-watermark-sync
Content-Type: application/json

{
    "image_url": "https://example.com/image.jpg",
    "confidence_threshold": 0.5,
    "enhance_mask": true
}
```

#### 2. 异步水印去除
```http
POST /remove-watermark
Content-Type: application/json

{
    "image_url": "https://example.com/image.jpg",
    "confidence_threshold": 0.5,
    "enhance_mask": true
}
```

#### 3. 批量处理
```http
POST /remove-watermark-batch
Content-Type: application/json

{
    "image_urls": [
        "https://example.com/image1.jpg",
        "https://example.com/image2.jpg"
    ],
    "confidence_threshold": 0.5,
    "enhance_mask": true
}
```

#### 4. 任务状态查询
```http
GET /task/{task_id}
```

#### 5. 批量状态查询
```http
GET /batch/{batch_id}
```

### 监控接口

#### 健康检查
```http
GET /health
```

#### 性能统计
```http
GET /performance
```

#### 内存清理
```http
POST /performance/cleanup
```

## 🎯 增强功能使用

### 可视化和验证

```python
from models import WatermarkRemovalPipeline, LamaValidationFramework, WatermarkDebuggingTool

# 初始化组件
pipeline = WatermarkRemovalPipeline()
validator = LamaValidationFramework(pipeline.detector, pipeline.inpainter)
debugger = WatermarkDebuggingTool(pipeline.detector, pipeline.inpainter)

# 创建可视化
vis_result = pipeline.create_detection_visualization(
    "input_image.jpg",
    save_path="./visualizations"
)

# 执行验证
validation_result = validator.validate_single_watermark_removal(
    "input_image.jpg",
    save_results=True,
    output_dir="./validation"
)

# 运行调试
debug_result = debugger.debug_full_pipeline(
    "input_image.jpg",
    save_debug_results=True,
    output_dir="./debugging"
)
```

### 快速体验增强功能

```bash
# 运行完整分析示例
python examples/enhanced_watermark_analysis.py --image test_image.jpg --output ./results

# 查看结果
ls ./results/
# visualization/  validation/  debugging/
```

## 📖 文档

- [API文档](docs/API.md) - 详细的API接口说明
- [部署指南](docs/DEPLOYMENT.md) - 生产环境部署指南
- [增强功能文档](docs/ENHANCED_FEATURES.md) - 新增功能详细说明
- [快速开始指南](docs/QUICK_START_ENHANCED.md) - 增强功能快速上手
- [功能总结](docs/ENHANCEMENT_SUMMARY.md) - 增强功能完整总结

## 🔧 配置说明

### 环境变量

```bash
# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# 模型配置
WATERMARK_DETECTION_MODEL=models/watermark_detector
LAMA_MODEL_PATH=models/lama

# 服务配置
MAX_FILE_SIZE=52428800  # 50MB
CONFIDENCE_THRESHOLD=0.3
```

### 配置文件

主要配置在 `app.py` 中的 `Config` 类：

```python
class Config:
    UPLOAD_DIR = "uploads"
    OUTPUT_DIR = "outputs"
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.webp'}
    CONFIDENCE_THRESHOLD = 0.3
```

## 🧪 测试

### 运行测试

```bash
# 运行所有测试
pytest

# 运行单元测试
pytest -m unit

# 运行集成测试
pytest -m integration

# 生成覆盖率报告
pytest --cov=models --cov=utils --cov=app --cov-report=html
```

### 测试覆盖率

目标覆盖率：80%+

- 单元测试：模型组件、工具函数
- 集成测试：API接口、端到端流程
- 性能测试：内存使用、处理速度

## 📊 性能优化

### 内存管理

- 自动GPU内存清理
- 模型缓存管理
- 定期垃圾回收

### 性能监控

- 实时内存使用监控
- 处理时间统计
- 系统资源监控

### 优化建议

1. **GPU使用**: 启用CUDA加速可显著提升处理速度
2. **批量处理**: 使用批量接口处理多张图片
3. **内存管理**: 定期调用清理接口释放内存
4. **参数调优**: 根据图片类型调整置信度阈值

## 🔍 故障排除

### 常见问题

1. **模型下载失败**
   - 检查网络连接
   - 手动下载模型文件到指定目录

2. **内存不足**
   - 降低批处理大小
   - 启用自动内存清理
   - 使用CPU模式

3. **检测效果不佳**
   - 调整置信度阈值
   - 启用掩码增强
   - 检查图片质量

### 日志分析

日志文件位置：
- 应用日志：`logs/app.log`
- 错误日志：`logs/error.log`
- 性能日志：`logs/performance.log`

## 🔧 Git配置

### 大文件处理
项目包含大型模型文件（~500MB），已配置Git忽略这些文件：

```bash
# 检查忽略的文件
git status --ignored

# 查看Git配置
cat .gitignore
cat .gitattributes
```

### 推荐工作流
```bash
# 1. 克隆代码（不包含模型）
git clone <repository-url>
cd watermark-detection

# 2. 下载模型文件
python download_models.py --all

# 3. 正常开发和提交
git add .
git commit -m "feat: 添加新功能"
git push
```

### Git LFS（可选）
如需版本控制模型文件：
```bash
# 安装并配置Git LFS
git lfs install
git lfs track "*.pt" "*.ckpt"
git add .gitattributes
```

详细配置请参考：[Git配置指南](docs/GIT_SETUP.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [JoyCaption](https://huggingface.co/spaces/fancyfeast/joycaption-watermark-detection) - 水印检测模型
- [LAMA](https://github.com/advimman/lama) - 图像修复模型
- [Ultralytics](https://github.com/ultralytics/ultralytics) - YOLO实现

## 📞 支持

如有问题或建议，请：
- 提交 [Issue](../../issues)
- 发送邮件至：<EMAIL>
- 查看 [Wiki](../../wiki) 获取更多文档
