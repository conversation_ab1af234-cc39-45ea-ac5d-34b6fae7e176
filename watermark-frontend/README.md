# 🚀 智能水印去除工具 - 前端界面

基于 Next.js + shadcn/ui 构建的现代化水印去除工具前端界面，为用户提供直观、高效的图像处理体验。

## ✨ 特性

- 🎨 **现代化UI设计** - 基于 shadcn/ui 的精美界面
- 📱 **响应式布局** - 完美适配桌面和移动设备
- 🔄 **实时处理进度** - 可视化处理状态和进度
- 📊 **结果对比展示** - 原图与处理结果的直观对比
- 📝 **历史记录管理** - 本地存储处理历史
- ⚙️ **参数自定义** - 灵活的处理参数配置
- 🌐 **多语言支持** - 中文界面，易于使用
- 🔒 **类型安全** - 完整的 TypeScript 支持

## 🏗️ 技术栈

- **框架**: Next.js 14+ (App Router)
- **UI组件**: shadcn/ui + Radix UI
- **样式**: Tailwind CSS
- **语言**: TypeScript
- **状态管理**: React Context + useReducer
- **图标**: Lucide React
- **字体**: Inter

## 🚀 快速开始

### 环境要求

- Node.js 18+
- npm 或 yarn

### 安装依赖

```bash
# 使用 npm
npm install

# 或使用 yarn
yarn install
```

### 环境配置

复制环境配置模板并根据需要修改：

```bash
# 复制环境配置模板
cp .env.example .env.local

# 编辑配置文件
nano .env.local
```

主要配置项：

```env
# 后端 API 地址
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# 应用配置
NEXT_PUBLIC_APP_NAME=智能水印去除工具
NEXT_PUBLIC_APP_VERSION=1.0.0

# 功能开关 (开发环境)
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true
```

> 📖 详细的环境配置说明请参考 [环境配置指南](docs/ENVIRONMENT_CONFIG.md)

### 启动开发服务器

```bash
npm run dev
# or
yarn dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 📖 核心功能

### 1. 图片上传和处理

- **拖拽上传**: 支持拖拽文件到上传区域
- **URL输入**: 支持通过图片URL进行处理
- **格式支持**: JPG、PNG、WebP 等主流格式
- **大小限制**: 最大支持 50MB 文件

### 2. 实时处理状态

- **进度显示**: 实时显示处理进度百分比
- **状态更新**: 显示当前处理阶段
- **错误处理**: 友好的错误提示和重试机制
- **取消功能**: 支持取消正在进行的处理

### 3. 结果展示

- **对比视图**: 原图与处理结果的并排对比
- **中间结果**: 显示检测可视化和处理掩码
- **统计信息**: 处理时间、置信度等详细信息
- **下载功能**: 一键下载处理结果

### 4. 参数配置

- **置信度阈值**: 控制水印检测敏感度
- **掩码增强**: 优化检测区域
- **智能增强**: AI优化的处理算法
- **上下文扩展**: 控制修复区域范围

### 5. 历史记录

- **本地存储**: 自动保存处理历史
- **搜索过滤**: 快速查找历史记录
- **详细信息**: 查看处理参数和结果
- **批量管理**: 支持删除和导出

## 🔧 API 集成

### API 客户端

项目包含完整的 API 客户端，支持：

- **同步处理**: 直接返回处理结果
- **异步处理**: 轮询任务状态
- **批量处理**: 多图片批量处理
- **错误处理**: 统一的错误处理机制

### 使用示例

```typescript
import { watermarkApi } from '@/lib/api';

// 同步处理
const result = await watermarkApi.removeSync({
  image_url: 'https://example.com/image.jpg',
  confidence_threshold: 0.5,
  enhance_mask: true
});

// 异步处理
const taskResult = await watermarkApi.removeAsync({
  image_url: 'https://example.com/image.jpg'
});

// 轮询状态
await watermarkApi.pollTask(taskResult.task_id, (task) => {
  console.log(`进度: ${task.progress}%`);
});
```

## 🚀 部署

### Vercel 部署

1. 推送代码到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 自动部署

### 其他平台

项目支持部署到任何支持 Next.js 的平台：

- Netlify
- AWS Amplify
- Railway
- 自托管

## 📄 许可证

MIT License

## 🙏 致谢

- [Next.js](https://nextjs.org/) - React 框架
- [shadcn/ui](https://ui.shadcn.com/) - UI 组件库
- [Tailwind CSS](https://tailwindcss.com/) - CSS 框架
- [Lucide](https://lucide.dev/) - 图标库
