# 🔧 环境配置指南

## 概述

本文档详细说明了智能水印去除工具前端项目的环境配置，包括不同环境的配置文件、环境变量说明和最佳实践。

## 📁 配置文件结构

```
watermark-frontend/
├── .env.example        # 环境变量模板文件 (可提交到版本控制)
├── .env.local          # 本地开发环境配置 (不提交)
├── .env.production     # 生产环境配置 (不提交)
└── .gitignore          # 保护敏感环境变量
```

## 🚀 快速开始

### 1. 复制环境配置模板

```bash
# 复制模板文件到本地配置
cp .env.example .env.local

# 根据需要修改配置
nano .env.local
```

### 2. 配置后端API地址

```bash
# 本地开发 (默认)
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# 如果后端运行在不同端口
NEXT_PUBLIC_API_BASE_URL=http://localhost:8001
```

## 📋 环境变量详解

### 🔗 API 配置

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `NEXT_PUBLIC_API_BASE_URL` | 后端API基础地址 | `http://localhost:8000` | `https://api.example.com` |

### 📱 应用配置

| 变量名 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `NEXT_PUBLIC_APP_NAME` | 应用名称 | `智能水印去除工具` | `Watermark Remover` |
| `NEXT_PUBLIC_APP_VERSION` | 应用版本 | `1.0.0` | `2.1.0` |
| `NEXT_PUBLIC_APP_DESCRIPTION` | 应用描述 | `基于AI的自动水印检测...` | 自定义描述 |

### 🎛️ 功能开关

| 变量名 | 说明 | 开发环境 | 生产环境 |
|--------|------|----------|----------|
| `NEXT_PUBLIC_DEBUG_MODE` | 调试模式 | `true` | `false` |
| `NEXT_PUBLIC_SHOW_DEV_TOOLS` | 显示开发工具 | `true` | `false` |
| `NEXT_PUBLIC_ENABLE_ANALYTICS` | 启用分析 | `false` | `true` |

### 📤 上传配置

| 变量名 | 说明 | 默认值 | 备注 |
|--------|------|--------|------|
| `NEXT_PUBLIC_MAX_FILE_SIZE` | 最大文件大小(字节) | `52428800` | 50MB |
| `NEXT_PUBLIC_ALLOWED_FORMATS` | 支持的文件格式 | `jpg,jpeg,png,webp,gif` | 逗号分隔 |

### 🎨 UI 配置

| 变量名 | 说明 | 可选值 | 默认值 |
|--------|------|--------|--------|
| `NEXT_PUBLIC_DEFAULT_THEME` | 默认主题 | `light/dark/system` | `system` |
| `NEXT_PUBLIC_ENABLE_ANIMATIONS` | 启用动画 | `true/false` | `true` |

### 📊 监控配置

| 变量名 | 说明 | 用途 | 示例 |
|--------|------|------|------|
| `NEXT_PUBLIC_SENTRY_DSN` | Sentry错误监控 | 错误追踪 | `https://<EMAIL>/xxx` |
| `NEXT_PUBLIC_GA_MEASUREMENT_ID` | Google Analytics | 用户分析 | `G-XXXXXXXXXX` |

### 🌐 CDN 配置

| 变量名 | 说明 | 用途 | 示例 |
|--------|------|------|------|
| `NEXT_PUBLIC_IMAGE_CDN_URL` | 图片CDN地址 | 图片加速 | `https://cdn.example.com/images` |
| `NEXT_PUBLIC_STATIC_CDN_URL` | 静态资源CDN | 资源加速 | `https://cdn.example.com/static` |

## 🏗️ 不同环境配置

### 开发环境 (.env.local)

```bash
# API配置
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000

# 功能开关
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_SHOW_DEV_TOOLS=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false

# 性能监控
NEXT_PUBLIC_SHOW_PERFORMANCE=true
```

### 生产环境 (.env.production)

```bash
# API配置
NEXT_PUBLIC_API_BASE_URL=https://api.watermark-removal.com

# 功能开关
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_SHOW_DEV_TOOLS=false
NEXT_PUBLIC_ENABLE_ANALYTICS=true

# 监控配置
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

### 测试环境 (.env.test)

```bash
# API配置
NEXT_PUBLIC_API_BASE_URL=https://test-api.watermark-removal.com

# 功能开关
NEXT_PUBLIC_DEBUG_MODE=true
NEXT_PUBLIC_ENABLE_ANALYTICS=false
```

## 🔒 安全最佳实践

### 1. 环境变量命名规则

- **公开变量**: 使用 `NEXT_PUBLIC_` 前缀，会暴露给客户端
- **私有变量**: 不使用前缀，仅在服务器端可用

```bash
# ✅ 正确 - 公开配置
NEXT_PUBLIC_API_BASE_URL=https://api.example.com

# ❌ 错误 - 敏感信息不应使用 NEXT_PUBLIC_
NEXT_PUBLIC_API_SECRET_KEY=secret-key

# ✅ 正确 - 敏感信息
API_SECRET_KEY=secret-key
```

### 2. 文件保护

```bash
# .gitignore 配置
.env
.env.local
.env.development
.env.production
.env.test
# 允许模板文件
!.env.example
```

### 3. 生产环境注意事项

- 不要在生产环境启用调试模式
- 确保API地址使用HTTPS
- 配置适当的监控和分析工具
- 定期更新敏感配置

## 🛠️ 使用示例

### 在代码中使用环境变量

```typescript
// API客户端配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

// 功能开关
const isDebugMode = process.env.NEXT_PUBLIC_DEBUG_MODE === 'true'

// 上传配置
const maxFileSize = parseInt(process.env.NEXT_PUBLIC_MAX_FILE_SIZE || '52428800')
```

### 条件渲染

```tsx
// 开发工具组件
{process.env.NEXT_PUBLIC_SHOW_DEV_TOOLS === 'true' && (
  <DevTools />
)}

// 分析组件
{process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true' && (
  <Analytics measurementId={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID} />
)}
```

## 🚨 故障排除

### 常见问题

1. **环境变量不生效**
   - 确保变量名以 `NEXT_PUBLIC_` 开头（客户端使用）
   - 重启开发服务器
   - 检查 `.env.local` 文件是否存在

2. **API连接失败**
   - 检查 `NEXT_PUBLIC_API_BASE_URL` 配置
   - 确保后端服务正在运行
   - 检查CORS配置

3. **构建时环境变量丢失**
   - 确保生产环境配置正确
   - 检查部署平台的环境变量设置

### 调试命令

```bash
# 查看当前环境变量
npm run dev -- --debug

# 检查构建时的环境变量
npm run build -- --debug
```

## 📚 相关文档

- [Next.js 环境变量文档](https://nextjs.org/docs/basic-features/environment-variables)
- [部署指南](./DEPLOYMENT.md)
- [API集成指南](./API_INTEGRATION.md)
