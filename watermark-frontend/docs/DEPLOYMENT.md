# 🚀 部署指南

## 概述

本文档详细说明智能水印去除工具前端的部署流程，包括不同平台的部署方案、环境配置、性能优化等。

## 部署前准备

### 环境要求

- Node.js 18.0+
- npm 或 yarn
- Git

### 构建配置

```json
{
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "analyze": "ANALYZE=true next build"
  }
}
```

### 环境变量配置

创建对应环境的配置文件：

```bash
# .env.local (本地开发)
NEXT_PUBLIC_API_BASE_URL=http://localhost:8000
NEXT_PUBLIC_APP_NAME=智能水印去除工具
NEXT_PUBLIC_APP_VERSION=1.0.0

# .env.production (生产环境)
NEXT_PUBLIC_API_BASE_URL=https://api.watermark-removal.com
NEXT_PUBLIC_APP_NAME=智能水印去除工具
NEXT_PUBLIC_APP_VERSION=1.0.0
NEXT_PUBLIC_ENABLE_ANALYTICS=true
```

## Vercel 部署

### 自动部署

1. **连接 GitHub**
   ```bash
   # 推送代码到 GitHub
   git add .
   git commit -m "feat: 完成前端开发"
   git push origin main
   ```

2. **导入项目到 Vercel**
   - 访问 [vercel.com](https://vercel.com)
   - 点击 "New Project"
   - 选择 GitHub 仓库
   - 配置项目设置

3. **环境变量配置**
   ```
   NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
   NEXT_PUBLIC_APP_NAME=智能水印去除工具
   NEXT_PUBLIC_APP_VERSION=1.0.0
   ```

4. **部署设置**
   ```json
   {
     "framework": "nextjs",
     "buildCommand": "npm run build",
     "outputDirectory": ".next",
     "installCommand": "npm install"
   }
   ```

### 手动部署

```bash
# 安装 Vercel CLI
npm i -g vercel

# 登录 Vercel
vercel login

# 部署项目
vercel --prod
```

### 自定义域名

```bash
# 添加域名
vercel domains add your-domain.com

# 配置 DNS
# A记录: @ -> ***********
# CNAME记录: www -> cname.vercel-dns.com
```

## Netlify 部署

### 通过 Git 部署

1. **连接仓库**
   - 登录 Netlify
   - 点击 "New site from Git"
   - 选择 GitHub 仓库

2. **构建设置**
   ```
   Build command: npm run build
   Publish directory: out
   ```

3. **环境变量**
   ```
   NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
   NEXT_PUBLIC_APP_NAME=智能水印去除工具
   ```

### 静态导出配置

```typescript
// next.config.ts
/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

module.exports = nextConfig
```

### 手动部署

```bash
# 安装 Netlify CLI
npm install -g netlify-cli

# 构建项目
npm run build

# 部署
netlify deploy --prod --dir=out
```

## AWS Amplify 部署

### 配置文件

```yaml
# amplify.yml
version: 1
frontend:
  phases:
    preBuild:
      commands:
        - npm ci
    build:
      commands:
        - npm run build
  artifacts:
    baseDirectory: .next
    files:
      - '**/*'
  cache:
    paths:
      - node_modules/**/*
```

### 环境变量

```bash
NEXT_PUBLIC_API_BASE_URL=https://your-api-domain.com
NEXT_PUBLIC_APP_NAME=智能水印去除工具
AMPLIFY_MONOREPO_APP_ROOT=watermark-frontend
```

## Docker 部署

### Dockerfile

```dockerfile
# 多阶段构建
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json yarn.lock* package-lock.json* pnpm-lock.yaml* ./
RUN \
  if [ -f yarn.lock ]; then yarn --frozen-lockfile; \
  elif [ -f package-lock.json ]; then npm ci; \
  elif [ -f pnpm-lock.yaml ]; then yarn global add pnpm && pnpm i --frozen-lockfile; \
  else echo "Lockfile not found." && exit 1; \
  fi

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

ENV NEXT_TELEMETRY_DISABLED 1

RUN npm run build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
ENV NEXT_TELEMETRY_DISABLED 1

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'

services:
  watermark-frontend:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_BASE_URL=http://watermark-backend:8000
    depends_on:
      - watermark-backend
    networks:
      - watermark-network

  watermark-backend:
    image: watermark-backend:latest
    ports:
      - "8000:8000"
    networks:
      - watermark-network

networks:
  watermark-network:
    driver: bridge
```

### 构建和运行

```bash
# 构建镜像
docker build -t watermark-frontend .

# 运行容器
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_API_BASE_URL=http://localhost:8000 \
  watermark-frontend

# 使用 Docker Compose
docker-compose up -d
```

## 自托管部署

### PM2 部署

```bash
# 安装 PM2
npm install -g pm2

# 构建项目
npm run build

# PM2 配置文件
# ecosystem.config.js
module.exports = {
  apps: [{
    name: 'watermark-frontend',
    script: 'npm',
    args: 'start',
    cwd: '/path/to/watermark-frontend',
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      NEXT_PUBLIC_API_BASE_URL: 'https://api.example.com'
    },
    instances: 'max',
    exec_mode: 'cluster',
    watch: false,
    max_memory_restart: '1G'
  }]
}

# 启动应用
pm2 start ecosystem.config.js
pm2 save
pm2 startup
```

### Nginx 反向代理

```nginx
# /etc/nginx/sites-available/watermark-frontend
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到 HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL 配置
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/key.pem;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains";
    
    # 静态文件缓存
    location /_next/static/ {
        alias /path/to/watermark-frontend/.next/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 代理到 Next.js
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

## 性能优化

### 构建优化

```typescript
// next.config.ts
const nextConfig = {
  // 压缩
  compress: true,
  
  // 图片优化
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
  },
  
  // Webpack 配置
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // 生产环境优化
      config.optimization.splitChunks = {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      };
    }
    return config;
  },
}
```

### CDN 配置

```typescript
// next.config.ts
const nextConfig = {
  assetPrefix: process.env.NODE_ENV === 'production' 
    ? 'https://cdn.example.com' 
    : '',
}
```

### 缓存策略

```typescript
// next.config.ts
const nextConfig = {
  async headers() {
    return [
      {
        source: '/_next/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/images/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
    ];
  },
}
```

## 监控和日志

### 错误监控

```typescript
// lib/monitoring.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: 1.0,
});

export { Sentry };
```

### 性能监控

```typescript
// lib/analytics.ts
export const trackEvent = (eventName: string, properties?: Record<string, any>) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', eventName, properties);
  }
};

export const trackPageView = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('config', 'GA_MEASUREMENT_ID', {
      page_path: url,
    });
  }
};
```

## 安全配置

### 内容安全策略

```typescript
// next.config.ts
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: [
              "default-src 'self'",
              "script-src 'self' 'unsafe-eval' 'unsafe-inline'",
              "style-src 'self' 'unsafe-inline'",
              "img-src 'self' data: https:",
              "connect-src 'self' https://api.example.com",
            ].join('; '),
          },
        ],
      },
    ];
  },
}
```

### 环境变量安全

```bash
# 生产环境变量
NEXT_PUBLIC_API_BASE_URL=https://api.production.com
SENTRY_DSN=your-sentry-dsn
GA_MEASUREMENT_ID=your-ga-id

# 敏感信息不要使用 NEXT_PUBLIC_ 前缀
DATABASE_URL=your-database-url
API_SECRET_KEY=your-secret-key
```

## 故障排除

### 常见问题

1. **构建失败**
   ```bash
   # 清理缓存
   rm -rf .next node_modules
   npm install
   npm run build
   ```

2. **环境变量不生效**
   ```bash
   # 检查变量名是否以 NEXT_PUBLIC_ 开头
   # 重启开发服务器
   ```

3. **静态导出问题**
   ```typescript
   // 确保没有使用服务端功能
   // 配置 next.config.ts
   const nextConfig = {
     output: 'export',
     images: { unoptimized: true }
   }
   ```

### 调试工具

```bash
# 分析包大小
npm run analyze

# 类型检查
npm run type-check

# 代码检查
npm run lint

# 性能分析
npm run build -- --profile
```
