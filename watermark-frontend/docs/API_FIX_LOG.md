# API 修复日志

## 修复时间
2025-07-31

## 问题描述

前端与后端 API 的数据格式不匹配，导致以下错误：

1. **文件上传错误**: `ApiError: 文件上传失败`
2. **任务提交错误**: `WatermarkApiError: 任务提交失败`

## 根本原因

前端 API 客户端期望的响应格式与后端实际返回的格式不一致：

### 前端期望格式
```json
{
  "success": true,
  "data": {
    // 实际数据
  }
}
```

### 后端实际格式
```json
{
  "success": true,
  "message": "操作成功",
  // 直接包含数据字段，不包装在 data 中
  "file_url": "/uploads/...",
  "task_id": "...",
  // 其他字段...
}
```

## 修复内容

### 1. 文件上传 API (`uploadFile`)

**修复前问题**:
- 期望 `{ success: true, data: { file_url, file_size, file_type } }`
- 实际收到 `{ success: true, file_url, filename, size }`

**修复方案**:
```typescript
// 后端返回的格式是 { success: true, file_url: "...", filename: "...", size: ... }
// 我们需要将其转换为前端期望的格式
if (data.success === true && data.file_url) {
  return {
    file_url: data.file_url,
    file_size: data.size || 0,
    file_type: data.filename ? data.filename.split('.').pop() || 'unknown' : 'unknown'
  }
}
```

### 2. 任务提交 API (`submitTask`)

**修复前问题**:
- 期望 `{ success: true, data: TaskStatus }`
- 实际收到 `{ success: true, task_id, message, ... }`

**修复方案**:
```typescript
// 后端直接返回任务状态数据，不是包装在 data 字段中
if (data.success === true && data.task_id) {
  return {
    task_id: data.task_id,
    status: 'pending' as const,
    progress: 0,
    message: data.message,
    result_url: data.result_url,
    // ... 其他字段映射
  }
}
```

### 3. 任务状态查询 API (`getTaskStatus`)

**修复前问题**:
- 期望 `{ success: true, data: TaskStatus }`
- 实际收到直接的任务状态数据

**修复方案**:
```typescript
// 后端直接返回任务状态数据
if (data.task_id) {
  return {
    task_id: data.task_id,
    status: data.status || 'pending',
    progress: data.progress || 0,
    // ... 完整的字段映射
  }
}
```

## 技术改进

### 1. 直接使用 fetch API
- 不再依赖通用的 `request` 函数
- 更好的错误处理和调试支持
- 针对每个端点的特定处理逻辑

### 2. 增强的调试功能
- 添加了 `console.log` 来查看后端返回的原始数据
- 更详细的错误信息
- 更好的错误分类

### 3. 改进的错误处理
```typescript
// 检查错误响应
if (data.success === false) {
  throw new ApiError(data.error || data.message || '操作失败')
}

// 检查意外的响应格式
if (!expectedCondition) {
  throw new ApiError(`意外的响应格式: ${JSON.stringify(data)}`)
}
```

## 测试验证

### 1. 文件上传测试
- ✅ 上传成功后正确解析 `file_url`
- ✅ 正确映射 `size` 到 `file_size`
- ✅ 从 `filename` 提取 `file_type`

### 2. 任务提交测试
- ✅ 成功提交任务并获取 `task_id`
- ✅ 正确设置初始状态为 `pending`
- ✅ 保留所有后端返回的字段

### 3. 任务状态查询测试
- ✅ 正确查询任务状态
- ✅ 完整映射所有状态字段
- ✅ 处理不同的任务状态

## 后续建议

### 1. API 规范化
建议后端统一响应格式，要么都使用：
```json
{ "success": true, "data": {...} }
```
要么都直接返回数据。

### 2. 类型定义完善
- 为每个 API 端点定义精确的响应类型
- 使用 TypeScript 的联合类型处理不同的响应格式

### 3. 测试覆盖
- 添加单元测试覆盖所有 API 方法
- 模拟不同的响应格式进行测试

## 修复文件列表

- `src/lib/api.ts` - 主要的 API 客户端修复
- `src/app/api-test/page.tsx` - 添加调试日志
- `docs/API_FIX_LOG.md` - 本文档

## 验证方法

1. 访问 http://localhost:3000 测试完整流程
2. 访问 http://localhost:3000/api-test 测试单个 API
3. 查看浏览器控制台的调试信息
4. 确认后端日志显示成功响应
