# 🏗️ 架构设计文档

## 概述

智能水印去除工具前端采用现代化的 React 生态系统构建，基于 Next.js 14+ App Router 架构，提供类型安全、高性能、可维护的用户界面。

## 技术架构

### 核心技术栈

```
┌─────────────────────────────────────────────────────────────┐
│                        用户界面层                              │
├─────────────────────────────────────────────────────────────┤
│  React Components + shadcn/ui + Tailwind CSS              │
├─────────────────────────────────────────────────────────────┤
│                        状态管理层                              │
├─────────────────────────────────────────────────────────────┤
│  React Context + useReducer + Custom Hooks               │
├─────────────────────────────────────────────────────────────┤
│                        业务逻辑层                              │
├─────────────────────────────────────────────────────────────┤
│  API Client + Type Definitions + Utility Functions       │
├─────────────────────────────────────────────────────────────┤
│                        框架层                                 │
├─────────────────────────────────────────────────────────────┤
│  Next.js 14+ (App Router) + TypeScript                   │
└─────────────────────────────────────────────────────────────┘
```

### 目录结构设计

```
src/
├── app/                    # Next.js App Router 页面
│   ├── layout.tsx         # 根布局，包含全局提供者
│   ├── page.tsx           # 主页面，核心功能入口
│   ├── history/           # 历史记录管理
│   └── settings/          # 系统设置
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 基础组件
│   ├── layout/           # 布局相关组件
│   ├── ImageUploader.tsx # 图片上传核心组件
│   ├── ProcessingStatus.tsx # 处理状态展示
│   ├── ResultViewer.tsx  # 结果对比展示
│   └── ParameterPanel.tsx # 参数配置面板
├── contexts/             # React Context 状态管理
│   └── AppContext.tsx    # 全局应用状态
├── hooks/                # 自定义 React Hooks
│   ├── useWatermarkRemoval.ts # 水印去除业务逻辑
│   └── useLocalStorage.ts # 本地存储抽象
└── lib/                  # 核心工具库
    ├── api.ts           # API 客户端封装
    ├── types.ts         # TypeScript 类型定义
    └── utils.ts         # 通用工具函数
```

## 组件架构

### 组件层次结构

```
App (layout.tsx)
├── AppProvider (全局状态)
├── Header (导航栏)
├── Main Content
│   ├── HomePage
│   │   ├── ImageUploader
│   │   ├── ParameterPanel
│   │   ├── ProcessingStatus
│   │   └── ResultViewer
│   ├── HistoryPage
│   └── SettingsPage
└── Footer
```

### 组件设计原则

1. **单一职责**: 每个组件只负责一个特定功能
2. **可复用性**: 组件设计考虑复用场景
3. **类型安全**: 严格的 TypeScript 类型定义
4. **可测试性**: 组件逻辑与UI分离，便于测试

## 状态管理

### 状态架构

```typescript
interface AppState {
  settings: UserSettings;      // 用户配置
  history: HistoryItem[];      // 处理历史
  currentTask?: TaskStatus;    // 当前任务
  isProcessing: boolean;       // 处理状态
}
```

### 状态流转

```
用户操作 → Action → Reducer → State → UI更新
    ↓
本地存储同步 ← State变化监听
```

### Context 设计

- **AppContext**: 全局应用状态管理
- **分离关注点**: 设置、历史、任务状态独立管理
- **性能优化**: 使用 useCallback 和 useMemo 优化渲染

## API 集成架构

### API 客户端设计

```typescript
class WatermarkApiClient {
  // 基础请求方法
  private async request<T>(endpoint: string, options?: RequestInit): Promise<T>
  
  // 业务方法
  async removeWatermarkSync(request: WatermarkRemovalRequest): Promise<WatermarkRemovalResponse>
  async removeWatermarkAsync(request: WatermarkRemovalRequest): Promise<WatermarkRemovalResponse>
  async pollTaskStatus(taskId: string, onProgress?: (task: TaskStatus) => void): Promise<TaskStatus>
}
```

### 错误处理策略

1. **统一错误类型**: WatermarkApiError 封装所有API错误
2. **错误边界**: React Error Boundary 捕获组件错误
3. **用户友好**: 错误信息本地化和用户友好提示
4. **重试机制**: 网络错误自动重试

## 数据流架构

### 处理流程

```
图片上传 → 参数配置 → API调用 → 状态轮询 → 结果展示 → 历史保存
    ↓           ↓         ↓         ↓         ↓         ↓
  验证文件   → 构建请求 → 发送请求 → 更新进度 → 显示结果 → 本地存储
```

### 数据持久化

- **本地存储**: localStorage 保存用户设置和历史记录
- **会话存储**: sessionStorage 保存临时状态
- **数据同步**: 状态变化自动同步到存储

## 性能优化

### 代码分割

```typescript
// 路由级别分割
const HistoryPage = lazy(() => import('./history/page'));
const SettingsPage = lazy(() => import('./settings/page'));

// 组件级别分割
const ResultViewer = lazy(() => import('@/components/ResultViewer'));
```

### 图片优化

- **懒加载**: 图片懒加载减少初始加载时间
- **压缩预览**: 上传前压缩图片预览
- **缓存策略**: 浏览器缓存优化

### 渲染优化

- **React.memo**: 防止不必要的重渲染
- **useCallback**: 稳定函数引用
- **useMemo**: 缓存计算结果

## 类型系统

### 类型定义层次

```typescript
// 基础类型
interface WatermarkRemovalRequest { ... }
interface TaskStatus { ... }

// UI组件类型
interface ImageUploaderProps { ... }
interface ProcessingStatusProps { ... }

// 状态管理类型
interface AppState { ... }
type AppAction = { ... }

// 工具函数类型
type ValidationResult = { valid: boolean; error?: string }
```

### 类型安全策略

1. **严格模式**: 启用 TypeScript 严格模式
2. **类型守卫**: 运行时类型检查
3. **泛型约束**: 灵活且安全的泛型使用
4. **类型推导**: 充分利用 TypeScript 类型推导

## 安全考虑

### 输入验证

- **文件类型**: 严格验证上传文件类型
- **文件大小**: 限制文件大小防止滥用
- **URL验证**: 验证图片URL格式和安全性

### 数据保护

- **敏感信息**: 不在前端存储敏感信息
- **HTTPS**: 强制使用HTTPS通信
- **CSP**: 内容安全策略防止XSS

## 可维护性

### 代码规范

- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **TypeScript**: 类型检查
- **Husky**: Git hooks 自动化

### 文档化

- **组件文档**: JSDoc 注释
- **API文档**: 详细的接口说明
- **架构文档**: 系统设计说明
- **部署文档**: 部署和配置指南

## 扩展性

### 功能扩展

- **插件系统**: 支持功能插件扩展
- **主题系统**: 支持自定义主题
- **国际化**: 多语言支持框架
- **API版本**: 支持多版本API

### 技术扩展

- **状态管理**: 可升级到 Redux Toolkit
- **路由**: 支持复杂路由需求
- **测试**: 完整的测试框架
- **监控**: 性能和错误监控

## 部署架构

### 构建优化

```json
{
  "scripts": {
    "build": "next build",
    "start": "next start",
    "analyze": "ANALYZE=true next build"
  }
}
```

### 部署策略

- **静态导出**: 支持静态站点部署
- **服务端渲染**: 支持SSR部署
- **CDN优化**: 静态资源CDN分发
- **环境配置**: 多环境配置管理

## 监控和调试

### 开发工具

- **React DevTools**: 组件调试
- **Redux DevTools**: 状态调试
- **Network Tab**: API调试
- **Performance Tab**: 性能分析

### 生产监控

- **错误监控**: Sentry 错误追踪
- **性能监控**: Web Vitals 监控
- **用户行为**: 用户行为分析
- **API监控**: API调用监控
