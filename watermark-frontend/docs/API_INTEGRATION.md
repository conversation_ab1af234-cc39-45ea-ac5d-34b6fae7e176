# 🔌 API 集成指南

## 概述

本文档详细说明前端如何与水印去除后端API进行集成，包括API客户端设计、错误处理、状态管理等。

## API 客户端架构

### 核心设计

```typescript
// API 客户端类
export class WatermarkApiClient {
  private baseUrl: string;
  
  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }
  
  // 通用请求方法
  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T>
  
  // 业务方法
  async removeWatermarkSync(request: WatermarkRemovalRequest): Promise<WatermarkRemovalResponse>
  async removeWatermarkAsync(request: WatermarkRemovalRequest): Promise<WatermarkRemovalResponse>
  async getTaskStatus(taskId: string): Promise<TaskStatus>
  async pollTaskStatus(taskId: string, onProgress?: (task: TaskStatus) => void): Promise<TaskStatus>
}
```

### 环境配置

```typescript
// 环境变量配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

// API 端点常量
export const API_ENDPOINTS = {
  REMOVE_WATERMARK_SYNC: '/remove-watermark-sync',
  REMOVE_WATERMARK: '/remove-watermark',
  TASK_STATUS: '/task',
  HEALTH: '/health',
} as const;
```

## API 接口详解

### 1. 同步水印去除

**端点**: `POST /remove-watermark-sync`

```typescript
// 请求类型
interface WatermarkRemovalRequest {
  image_url: string;
  confidence_threshold?: number;
  enhance_mask?: boolean;
  use_smart_enhancement?: boolean;
  context_expansion_ratio?: number;
  return_intermediate?: boolean;
}

// 响应类型
interface WatermarkRemovalResponse {
  success: boolean;
  message: string;
  task_id: string;
  result_url?: string;
  processing_time?: number;
  detection_confidence?: number;
  watermark_detected?: boolean;
  detection_info?: DetectionInfo;
  intermediate_results?: IntermediateResults;
}

// 使用示例
const result = await apiClient.removeWatermarkSync({
  image_url: 'https://example.com/image.jpg',
  confidence_threshold: 0.5,
  enhance_mask: true,
  use_smart_enhancement: true,
  context_expansion_ratio: 0.12,
  return_intermediate: true
});
```

### 2. 异步水印去除

**端点**: `POST /remove-watermark`

```typescript
// 提交任务
const submitResult = await apiClient.removeWatermarkAsync({
  image_url: 'https://example.com/image.jpg',
  confidence_threshold: 0.3
});

// 轮询任务状态
const finalResult = await apiClient.pollTaskStatus(
  submitResult.task_id,
  (task) => {
    console.log(`进度: ${task.progress}%`);
    console.log(`状态: ${task.status}`);
  }
);
```

### 3. 任务状态查询

**端点**: `GET /task/{task_id}`

```typescript
// 任务状态类型
interface TaskStatus {
  task_id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  message: string;
  result_url?: string;
  error?: string;
  created_at: string;
  completed_at?: string;
  processing_time?: number;
  detection_confidence?: number;
  watermark_detected?: boolean;
  detection_info?: DetectionInfo;
  intermediate_results?: IntermediateResults;
}

// 查询示例
const taskStatus = await apiClient.getTaskStatus('task-id-123');
```

## 错误处理

### 错误类型定义

```typescript
export class WatermarkApiError extends Error {
  public status?: number;
  public code?: string;
  public details?: any;

  constructor(message: string, status?: number, code?: string, details?: any) {
    super(message);
    this.name = 'WatermarkApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}
```

### 错误处理策略

```typescript
private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  try {
    const response = await fetch(`${this.baseUrl}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
    });
    
    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      let errorDetails;

      try {
        const errorData = await response.json();
        errorMessage = errorData.detail || errorData.message || errorMessage;
        errorDetails = errorData;
      } catch {
        // 无法解析错误响应，使用默认错误消息
      }

      throw new WatermarkApiError(
        errorMessage,
        response.status,
        response.status.toString(),
        errorDetails
      );
    }

    return await response.json();
  } catch (error) {
    if (error instanceof WatermarkApiError) {
      throw error;
    }

    // 网络错误或其他错误
    throw new WatermarkApiError(
      error instanceof Error ? error.message : '网络请求失败',
      0,
      'NETWORK_ERROR',
      error
    );
  }
}
```

## 状态轮询机制

### 轮询实现

```typescript
async pollTaskStatus(
  taskId: string,
  onProgress?: (task: TaskStatus) => void,
  interval: number = 1000,
  maxAttempts: number = 300 // 5分钟超时
): Promise<TaskStatus> {
  let attempts = 0;

  return new Promise((resolve, reject) => {
    const poll = async () => {
      try {
        attempts++;
        const task = await this.getTaskStatus(taskId);
        
        onProgress?.(task);

        if (task.status === 'completed') {
          resolve(task);
          return;
        }

        if (task.status === 'failed' || task.status === 'cancelled') {
          reject(new WatermarkApiError(
            task.error || `任务${task.status}`,
            0,
            task.status.toUpperCase()
          ));
          return;
        }

        if (attempts >= maxAttempts) {
          reject(new WatermarkApiError('任务超时', 0, 'TIMEOUT'));
          return;
        }

        // 继续轮询
        setTimeout(poll, interval);
      } catch (error) {
        reject(error);
      }
    };

    poll();
  });
}
```

### 轮询优化

```typescript
// 自适应轮询间隔
const getPollingInterval = (attempts: number): number => {
  if (attempts < 10) return 1000;      // 前10次每秒轮询
  if (attempts < 30) return 2000;      // 接下来20次每2秒轮询
  return 5000;                         // 之后每5秒轮询
};

// 指数退避
const getBackoffInterval = (attempts: number): number => {
  return Math.min(1000 * Math.pow(2, attempts), 30000);
};
```

## React Hook 集成

### useWatermarkRemoval Hook

```typescript
export function useWatermarkRemoval(options: UseWatermarkRemovalOptions = {}) {
  const { onSuccess, onError, onProgress } = options;
  
  const [state, setState] = useState<ProcessingState>({
    isProcessing: false,
    progress: 0,
  });

  const removeWatermarkSync = useCallback(async (request: WatermarkRemovalRequest) => {
    setState({ isProcessing: true, progress: 0, error: undefined });

    try {
      const result = await watermarkApi.removeSync(request);
      
      if (result.success) {
        const taskStatus: TaskStatus = {
          task_id: result.task_id,
          status: 'completed',
          progress: 100,
          message: result.message,
          result_url: result.result_url,
          // ... 其他字段
        };

        setState({
          isProcessing: false,
          currentTask: taskStatus,
          progress: 100,
        });

        onSuccess?.(taskStatus);
        return taskStatus;
      }
    } catch (error) {
      const apiError = error instanceof WatermarkApiError 
        ? error 
        : new WatermarkApiError(error instanceof Error ? error.message : '未知错误');

      setState({
        isProcessing: false,
        progress: 0,
        error: apiError.message,
      });

      onError?.(apiError);
      throw apiError;
    }
  }, [onSuccess, onError]);

  return {
    isProcessing: state.isProcessing,
    progress: state.progress,
    currentTask: state.currentTask,
    error: state.error,
    removeWatermarkSync,
    // ... 其他方法
  };
}
```

## 类型安全

### 严格类型定义

```typescript
// 请求参数验证
const validateRequest = (request: WatermarkRemovalRequest): void => {
  if (!request.image_url) {
    throw new Error('image_url is required');
  }
  
  if (request.confidence_threshold !== undefined) {
    if (request.confidence_threshold < 0 || request.confidence_threshold > 1) {
      throw new Error('confidence_threshold must be between 0 and 1');
    }
  }
  
  if (request.context_expansion_ratio !== undefined) {
    if (request.context_expansion_ratio < 0 || request.context_expansion_ratio > 1) {
      throw new Error('context_expansion_ratio must be between 0 and 1');
    }
  }
};

// 响应数据验证
const validateResponse = (response: any): WatermarkRemovalResponse => {
  if (typeof response.success !== 'boolean') {
    throw new Error('Invalid response: success must be boolean');
  }
  
  if (typeof response.message !== 'string') {
    throw new Error('Invalid response: message must be string');
  }
  
  return response as WatermarkRemovalResponse;
};
```

## 性能优化

### 请求缓存

```typescript
class CachedApiClient extends WatermarkApiClient {
  private cache = new Map<string, any>();
  private cacheTimeout = 5 * 60 * 1000; // 5分钟

  async getTaskStatus(taskId: string): Promise<TaskStatus> {
    const cacheKey = `task-${taskId}`;
    const cached = this.cache.get(cacheKey);
    
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    
    const result = await super.getTaskStatus(taskId);
    
    // 只缓存已完成的任务
    if (result.status === 'completed' || result.status === 'failed') {
      this.cache.set(cacheKey, {
        data: result,
        timestamp: Date.now()
      });
    }
    
    return result;
  }
}
```

### 请求去重

```typescript
class DeduplicatedApiClient extends WatermarkApiClient {
  private pendingRequests = new Map<string, Promise<any>>();

  async removeWatermarkSync(request: WatermarkRemovalRequest): Promise<WatermarkRemovalResponse> {
    const requestKey = JSON.stringify(request);
    
    if (this.pendingRequests.has(requestKey)) {
      return this.pendingRequests.get(requestKey)!;
    }
    
    const promise = super.removeWatermarkSync(request);
    this.pendingRequests.set(requestKey, promise);
    
    try {
      const result = await promise;
      return result;
    } finally {
      this.pendingRequests.delete(requestKey);
    }
  }
}
```

## 测试策略

### API 客户端测试

```typescript
describe('WatermarkApiClient', () => {
  let client: WatermarkApiClient;
  
  beforeEach(() => {
    client = new WatermarkApiClient('http://test-api.com');
  });

  it('should handle successful sync removal', async () => {
    const mockResponse = {
      success: true,
      message: 'Success',
      task_id: 'test-task',
      result_url: 'http://example.com/result.jpg'
    };
    
    global.fetch = jest.fn().mockResolvedValue({
      ok: true,
      json: () => Promise.resolve(mockResponse)
    });

    const result = await client.removeWatermarkSync({
      image_url: 'http://example.com/test.jpg'
    });

    expect(result).toEqual(mockResponse);
  });

  it('should handle API errors', async () => {
    global.fetch = jest.fn().mockResolvedValue({
      ok: false,
      status: 400,
      statusText: 'Bad Request',
      json: () => Promise.resolve({ detail: 'Invalid image URL' })
    });

    await expect(client.removeWatermarkSync({
      image_url: 'invalid-url'
    })).rejects.toThrow(WatermarkApiError);
  });
});
```

## 最佳实践

### 1. 错误边界

```typescript
class ApiErrorBoundary extends React.Component {
  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    if (error instanceof WatermarkApiError) {
      // 记录API错误
      console.error('API Error:', error);
    }
  }
}
```

### 2. 加载状态管理

```typescript
const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

const setLoading = (key: string, loading: boolean) => {
  setLoadingStates(prev => ({ ...prev, [key]: loading }));
};
```

### 3. 重试机制

```typescript
const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  for (let i = 0; i < maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      if (i === maxRetries - 1) throw error;
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }
  throw new Error('Max retries exceeded');
};
```
