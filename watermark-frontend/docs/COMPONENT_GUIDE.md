# 📦 组件使用指南

## 概述

本文档详细介绍智能水印去除工具前端的核心组件，包括使用方法、属性说明、最佳实践等。

## 核心组件

### 1. ImageUploader - 图片上传组件

用于处理图片上传和URL输入的核心组件。

#### 基本用法

```typescript
import { ImageUploader } from '@/components/ImageUploader';

function MyComponent() {
  const handleImageSelect = (file: File | string) => {
    console.log('选择的图片:', file);
  };

  const handleImagePreview = (previewUrl: string) => {
    console.log('预览URL:', previewUrl);
  };

  return (
    <ImageUploader
      onImageSelect={handleImageSelect}
      onImagePreview={handleImagePreview}
      isProcessing={false}
    />
  );
}
```

#### 属性说明

```typescript
interface ImageUploaderProps {
  onImageSelect: (file: File | string) => void;  // 图片选择回调
  onImagePreview?: (previewUrl: string) => void; // 预览回调
  isProcessing?: boolean;                        // 是否正在处理
  className?: string;                            // 自定义样式
}
```

#### 特性

- ✅ 拖拽上传支持
- ✅ URL输入支持
- ✅ 文件类型验证
- ✅ 文件大小限制
- ✅ 实时预览
- ✅ 错误处理

### 2. ProcessingStatus - 处理状态组件

显示水印去除任务的处理状态和进度。

#### 基本用法

```typescript
import { ProcessingStatus } from '@/components/ProcessingStatus';

function MyComponent() {
  const [currentTask, setCurrentTask] = useState<TaskStatus | undefined>();

  const handleCancel = () => {
    console.log('取消处理');
  };

  const handleRetry = () => {
    console.log('重试处理');
  };

  return (
    <ProcessingStatus
      task={currentTask}
      onCancel={handleCancel}
      onRetry={handleRetry}
    />
  );
}
```

#### 属性说明

```typescript
interface ProcessingStatusProps {
  task?: TaskStatus;           // 任务状态
  onCancel?: () => void;       // 取消回调
  onRetry?: () => void;        // 重试回调
  className?: string;          // 自定义样式
}
```

#### 状态类型

```typescript
type TaskStatusType = 
  | 'pending'     // 等待处理
  | 'processing'  // 正在处理
  | 'completed'   // 处理完成
  | 'failed'      // 处理失败
  | 'cancelled';  // 已取消
```

### 3. ResultViewer - 结果展示组件

展示原图与处理结果的对比，支持下载和分享。

#### 基本用法

```typescript
import { ResultViewer } from '@/components/ResultViewer';

function MyComponent() {
  const [originalImageUrl, setOriginalImageUrl] = useState('');
  const [task, setTask] = useState<TaskStatus | undefined>();

  const handleDownload = () => {
    console.log('下载结果');
  };

  const handleShare = () => {
    console.log('分享结果');
  };

  return (
    <ResultViewer
      originalImageUrl={originalImageUrl}
      task={task}
      onDownload={handleDownload}
      onShare={handleShare}
    />
  );
}
```

#### 属性说明

```typescript
interface ResultViewerProps {
  originalImageUrl: string;    // 原图URL
  task?: TaskStatus;           // 任务状态
  onDownload?: () => void;     // 下载回调
  onShare?: () => void;        // 分享回调
  className?: string;          // 自定义样式
}
```

#### 特性

- ✅ 原图与结果对比
- ✅ 中间结果展示
- ✅ 处理统计信息
- ✅ 下载功能
- ✅ 分享功能
- ✅ 全屏查看

### 4. ParameterPanel - 参数配置组件

提供水印检测和处理参数的配置界面。

#### 基本用法

```typescript
import { ParameterPanel } from '@/components/ParameterPanel';
import { useSettings } from '@/contexts/AppContext';

function MyComponent() {
  const { settings, updateSettings } = useSettings();

  return (
    <ParameterPanel
      settings={settings}
      onSettingsChange={updateSettings}
      disabled={false}
    />
  );
}
```

#### 属性说明

```typescript
interface ParameterPanelProps {
  settings: UserSettings;                           // 用户设置
  onSettingsChange: (settings: Partial<UserSettings>) => void; // 设置变更回调
  disabled?: boolean;                               // 是否禁用
  className?: string;                               // 自定义样式
}
```

#### 配置项

```typescript
interface UserSettings {
  defaultConfidenceThreshold: number;    // 置信度阈值 (0.0-1.0)
  defaultEnhanceMask: boolean;           // 掩码增强
  defaultUseSmartEnhancement: boolean;   // 智能增强
  defaultContextExpansionRatio: number;  // 上下文扩展比例
  autoDownload: boolean;                 // 自动下载
  showIntermediateResults: boolean;      // 显示中间结果
}
```

## 布局组件

### MainLayout - 主布局

提供页面的基础布局结构。

```typescript
import { MainLayout } from '@/components/layout/MainLayout';

function MyPage() {
  return (
    <MainLayout>
      <div>页面内容</div>
    </MainLayout>
  );
}
```

### Header - 页面头部

```typescript
import { Header } from '@/components/layout/Header';

// 自动包含在 MainLayout 中，通常不需要单独使用
```

### Footer - 页面底部

```typescript
import { Footer } from '@/components/layout/Footer';

// 自动包含在 MainLayout 中，可通过 showFooter 属性控制
<MainLayout showFooter={false}>
  <div>不显示底部的页面</div>
</MainLayout>
```

## 自定义 Hooks

### useWatermarkRemoval - 水印去除逻辑

处理水印去除的核心业务逻辑。

```typescript
import { useWatermarkRemoval } from '@/hooks/useWatermarkRemoval';

function MyComponent() {
  const {
    isProcessing,
    progress,
    currentTask,
    error,
    removeWatermarkSync,
    removeWatermarkAsync,
    cancel,
    reset
  } = useWatermarkRemoval({
    onSuccess: (result) => {
      console.log('处理成功:', result);
    },
    onError: (error) => {
      console.error('处理失败:', error);
    },
    onProgress: (task) => {
      console.log('进度更新:', task.progress);
    }
  });

  const handleProcess = async () => {
    try {
      await removeWatermarkSync({
        image_url: 'https://example.com/image.jpg',
        confidence_threshold: 0.5,
        enhance_mask: true
      });
    } catch (error) {
      console.error('处理失败:', error);
    }
  };

  return (
    <div>
      <button onClick={handleProcess} disabled={isProcessing}>
        {isProcessing ? '处理中...' : '开始处理'}
      </button>
      {isProcessing && <div>进度: {progress}%</div>}
      {error && <div>错误: {error}</div>}
    </div>
  );
}
```

### useLocalStorage - 本地存储

简化本地存储操作的Hook。

```typescript
import { useLocalStorage } from '@/hooks/useLocalStorage';

function MyComponent() {
  const [value, setValue, removeValue] = useLocalStorage('my-key', 'default-value');

  return (
    <div>
      <input 
        value={value} 
        onChange={(e) => setValue(e.target.value)} 
      />
      <button onClick={removeValue}>清除</button>
    </div>
  );
}
```

## Context 使用

### AppContext - 全局状态

```typescript
import { useApp, useSettings, useHistory, useCurrentTask } from '@/contexts/AppContext';

function MyComponent() {
  // 完整的应用状态
  const { state, updateSettings, addHistoryItem } = useApp();
  
  // 只使用设置
  const { settings, updateSettings } = useSettings();
  
  // 只使用历史记录
  const { history, addHistoryItem, removeHistoryItem, clearHistory } = useHistory();
  
  // 只使用当前任务
  const { currentTask, isProcessing, setCurrentTask, setProcessing } = useCurrentTask();

  return <div>组件内容</div>;
}
```

## 样式自定义

### 使用 Tailwind CSS

```typescript
import { cn } from '@/lib/utils';

function MyComponent({ className }: { className?: string }) {
  return (
    <div className={cn(
      "default-styles",
      "more-default-styles",
      className
    )}>
      内容
    </div>
  );
}
```

### 主题自定义

```css
/* globals.css */
:root {
  --primary: 210 40% 98%;
  --primary-foreground: 222.2 84% 4.9%;
  /* 更多 CSS 变量 */
}

.dark {
  --primary: 222.2 84% 4.9%;
  --primary-foreground: 210 40% 98%;
}
```

## 最佳实践

### 1. 组件组合

```typescript
// 推荐：组合使用组件
function WatermarkRemovalPage() {
  return (
    <MainLayout>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <ImageUploader onImageSelect={handleImageSelect} />
          <ProcessingStatus task={currentTask} />
          <ResultViewer originalImageUrl={originalUrl} task={currentTask} />
        </div>
        <div>
          <ParameterPanel settings={settings} onSettingsChange={updateSettings} />
        </div>
      </div>
    </MainLayout>
  );
}
```

### 2. 错误处理

```typescript
function MyComponent() {
  const [error, setError] = useState<string | null>(null);

  const handleError = (error: Error) => {
    setError(error.message);
    console.error('组件错误:', error);
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return <div>正常内容</div>;
}
```

### 3. 加载状态

```typescript
function MyComponent() {
  const [isLoading, setIsLoading] = useState(false);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">加载中...</span>
      </div>
    );
  }

  return <div>内容</div>;
}
```

### 4. 响应式设计

```typescript
function ResponsiveComponent() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div className="col-span-1 md:col-span-2 lg:col-span-1">
        移动端全宽，平板端占2列，桌面端占1列
      </div>
    </div>
  );
}
```

### 5. 性能优化

```typescript
import { memo, useCallback, useMemo } from 'react';

const OptimizedComponent = memo(function MyComponent({ data, onUpdate }) {
  const processedData = useMemo(() => {
    return data.map(item => ({ ...item, processed: true }));
  }, [data]);

  const handleUpdate = useCallback((id: string) => {
    onUpdate(id);
  }, [onUpdate]);

  return (
    <div>
      {processedData.map(item => (
        <div key={item.id} onClick={() => handleUpdate(item.id)}>
          {item.name}
        </div>
      ))}
    </div>
  );
});
```

## 测试组件

### 单元测试示例

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { ImageUploader } from '@/components/ImageUploader';

describe('ImageUploader', () => {
  it('should call onImageSelect when file is selected', () => {
    const mockOnImageSelect = jest.fn();
    
    render(<ImageUploader onImageSelect={mockOnImageSelect} />);
    
    const fileInput = screen.getByRole('button', { name: /选择文件/ });
    const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    fireEvent.change(fileInput, { target: { files: [file] } });
    
    expect(mockOnImageSelect).toHaveBeenCalledWith(file);
  });
});
```
