'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

export default function TestStyles() {
  return (
    <div className="min-h-screen bg-background p-8">
      <div className="max-w-4xl mx-auto space-y-8">
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold text-foreground">样式测试页面</h1>
          <p className="text-xl text-muted-foreground">验证Tailwind CSS是否正确加载</p>
        </div>

        {/* 颜色测试 */}
        <Card>
          <CardHeader>
            <CardTitle>颜色系统测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="p-4 bg-primary text-primary-foreground rounded-lg text-center">
                Primary
              </div>
              <div className="p-4 bg-secondary text-secondary-foreground rounded-lg text-center">
                Secondary
              </div>
              <div className="p-4 bg-muted text-muted-foreground rounded-lg text-center">
                Muted
              </div>
              <div className="p-4 bg-accent text-accent-foreground rounded-lg text-center">
                Accent
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 按钮测试 */}
        <Card>
          <CardHeader>
            <CardTitle>按钮测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Button>Default Button</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
              <Button variant="ghost">Ghost</Button>
              <Button variant="destructive">Destructive</Button>
            </div>
          </CardContent>
        </Card>

        {/* 徽章测试 */}
        <Card>
          <CardHeader>
            <CardTitle>徽章测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex flex-wrap gap-4">
              <Badge>Default</Badge>
              <Badge variant="secondary">Secondary</Badge>
              <Badge variant="outline">Outline</Badge>
              <Badge variant="destructive">Destructive</Badge>
            </div>
          </CardContent>
        </Card>

        {/* 动画测试 */}
        <Card>
          <CardHeader>
            <CardTitle>动画测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-primary/10 rounded-lg text-center hover:scale-105 transition-transform duration-200">
                Hover Scale
              </div>
              <div className="p-4 bg-secondary/10 rounded-lg text-center animate-pulse">
                Pulse Animation
              </div>
              <div className="p-4 bg-accent/10 rounded-lg text-center animate-bounce">
                Bounce Animation
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 渐变测试 */}
        <Card>
          <CardHeader>
            <CardTitle>渐变测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-8 bg-gradient-to-r from-primary to-purple-600 rounded-lg text-center">
              <h3 className="text-2xl font-bold text-white">渐变背景</h3>
            </div>
            <div className="text-center">
              <h3 className="text-3xl font-bold text-gradient">渐变文字</h3>
            </div>
          </CardContent>
        </Card>

        {/* 阴影测试 */}
        <Card>
          <CardHeader>
            <CardTitle>阴影测试</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="p-4 bg-card rounded-lg shadow-sm text-center">
                Shadow SM
              </div>
              <div className="p-4 bg-card rounded-lg shadow-md text-center">
                Shadow MD
              </div>
              <div className="p-4 bg-card rounded-lg shadow-lg text-center">
                Shadow LG
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-center text-muted-foreground">
          <p>如果您能看到上述样式正确显示，说明Tailwind CSS已正确加载！</p>
        </div>
      </div>
    </div>
  );
}
