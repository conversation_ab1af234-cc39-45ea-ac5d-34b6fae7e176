/**
 * 历史记录页面
 */

'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  History as HistoryIcon,
  Search,
  Download,
  Trash2,
  Eye,
  Calendar,
  Clock,
  Target,
  Zap,
  Filter,
  X
} from 'lucide-react';
import { useHistory } from '@/contexts/AppContext';
import { HistoryItem } from '@/lib/types';
import { formatDateTime, formatDuration, downloadFile } from '@/lib/utils';

export default function HistoryPage() {
  const { history, removeHistoryItem, clearHistory } = useHistory();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<HistoryItem | null>(null);

  // 过滤历史记录
  const filteredHistory = history.filter(item => {
    if (!searchTerm) return true;
    
    const searchLower = searchTerm.toLowerCase();
    return (
      item.id.toLowerCase().includes(searchLower) ||
      item.originalImageUrl.toLowerCase().includes(searchLower) ||
      formatDateTime(item.timestamp).toLowerCase().includes(searchLower)
    );
  });

  const handleDownload = (item: HistoryItem) => {
    if (item.resultImageUrl) {
      downloadFile(item.resultImageUrl, `watermark-removed-${item.id}.jpg`);
    }
  };

  const handleDelete = (id: string) => {
    removeHistoryItem(id);
    if (selectedItem?.id === id) {
      setSelectedItem(null);
    }
  };

  const handleClearAll = () => {
    if (confirm('确定要清空所有历史记录吗？此操作不可撤销。')) {
      clearHistory();
      setSelectedItem(null);
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <HistoryIcon className="h-6 w-6" />
            <h1 className="text-2xl font-bold">处理历史</h1>
            <Badge variant="secondary">{history.length} 条记录</Badge>
          </div>
          
          {history.length > 0 && (
            <Button 
              variant="destructive" 
              onClick={handleClearAll}
              className="flex items-center space-x-2"
            >
              <Trash2 className="h-4 w-4" />
              <span>清空历史</span>
            </Button>
          )}
        </div>

        {/* 搜索和过滤 */}
        {history.length > 0 && (
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center space-x-4">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索历史记录..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                  {searchTerm && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setSearchTerm('')}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 历史记录列表 */}
        {filteredHistory.length === 0 ? (
          <Card>
            <CardContent className="p-12 text-center">
              <HistoryIcon className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">
                {history.length === 0 ? '暂无历史记录' : '未找到匹配的记录'}
              </h3>
              <p className="text-muted-foreground">
                {history.length === 0 
                  ? '开始处理图片后，历史记录将显示在这里' 
                  : '尝试使用不同的搜索关键词'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* 历史记录列表 */}
            <div className="space-y-4">
              {filteredHistory.map((item) => (
                <Card 
                  key={item.id}
                  className={`cursor-pointer transition-colors ${
                    selectedItem?.id === item.id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => setSelectedItem(item)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-4">
                      {/* 缩略图 */}
                      <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                        <img
                          src={item.originalImageUrl}
                          alt="原图缩略图"
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                          <Eye className="h-4 w-4 text-white" />
                        </div>
                      </div>

                      {/* 信息 */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-2">
                          <Badge 
                            variant={item.status === 'completed' ? 'default' : 'destructive'}
                            className="text-xs"
                          >
                            {item.status === 'completed' ? '已完成' : '失败'}
                          </Badge>
                          <span className="text-xs text-muted-foreground">
                            {formatDateTime(item.timestamp)}
                          </span>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-xs text-muted-foreground">
                          <div className="flex items-center">
                            <Target className="h-3 w-3 mr-1" />
                            置信度: {Math.round((item.confidence || 0) * 100)}%
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            {item.processingTime ? formatDuration(item.processingTime) : 'N/A'}
                          </div>
                        </div>

                        <div className="flex items-center justify-between mt-2">
                          <span className="text-xs font-mono text-muted-foreground truncate">
                            ID: {item.id}
                          </span>
                          
                          <div className="flex items-center space-x-1">
                            {item.resultImageUrl && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  handleDownload(item);
                                }}
                                className="h-6 w-6 p-0"
                              >
                                <Download className="h-3 w-3" />
                              </Button>
                            )}
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDelete(item.id);
                              }}
                              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* 详细信息面板 */}
            <div className="lg:sticky lg:top-6">
              {selectedItem ? (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center justify-between">
                      <span>处理详情</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedItem(null)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {/* 图片对比 */}
                    <div className="space-y-4">
                      <h4 className="font-medium">图片对比</h4>
                      
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm text-muted-foreground mb-2">原图</p>
                          <div className="rounded-lg overflow-hidden border">
                            <img
                              src={selectedItem.originalImageUrl}
                              alt="原图"
                              className="w-full h-auto object-contain"
                            />
                          </div>
                        </div>

                        {selectedItem.resultImageUrl && (
                          <div>
                            <p className="text-sm text-muted-foreground mb-2">处理结果</p>
                            <div className="rounded-lg overflow-hidden border">
                              <img
                                src={selectedItem.resultImageUrl}
                                alt="处理结果"
                                className="w-full h-auto object-contain"
                              />
                            </div>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* 处理参数 */}
                    <div className="space-y-3">
                      <h4 className="font-medium">处理参数</h4>
                      <div className="grid grid-cols-2 gap-3 text-sm">
                        <div>
                          <span className="text-muted-foreground">置信度阈值:</span>
                          <span className="ml-2 font-medium">
                            {Math.round(selectedItem.parameters.confidence_threshold! * 100)}%
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">掩码增强:</span>
                          <span className="ml-2 font-medium">
                            {selectedItem.parameters.enhance_mask ? '开启' : '关闭'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">智能增强:</span>
                          <span className="ml-2 font-medium">
                            {selectedItem.parameters.use_smart_enhancement ? '开启' : '关闭'}
                          </span>
                        </div>
                        <div>
                          <span className="text-muted-foreground">扩展比例:</span>
                          <span className="ml-2 font-medium">
                            {Math.round((selectedItem.parameters.context_expansion_ratio || 0) * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* 操作按钮 */}
                    <div className="flex space-x-2">
                      {selectedItem.resultImageUrl && (
                        <Button 
                          onClick={() => handleDownload(selectedItem)}
                          className="flex-1"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          下载结果
                        </Button>
                      )}
                      
                      <Button 
                        variant="destructive"
                        onClick={() => handleDelete(selectedItem.id)}
                        className="flex-1"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        删除记录
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card>
                  <CardContent className="p-12 text-center">
                    <Eye className="h-8 w-8 mx-auto text-muted-foreground mb-3" />
                    <p className="text-muted-foreground">
                      选择一条历史记录查看详细信息
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  );
}
