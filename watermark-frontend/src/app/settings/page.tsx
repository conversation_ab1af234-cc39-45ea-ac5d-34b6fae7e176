/**
 * 设置页面
 */

'use client';

import React, { useState } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { ParameterPanel } from '@/components/ParameterPanel';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Settings as SettingsIcon,
  Save,
  RotateCcw,
  CheckCircle,
  Info,
  Database,
  Trash2,
  Download,
  Upload
} from 'lucide-react';
import { useSettings, useHistory, useApp } from '@/contexts/AppContext';
import { DEFAULT_SETTINGS } from '@/lib/types';

export default function SettingsPage() {
  const { settings, updateSettings } = useSettings();
  const { history, clearHistory } = useHistory();
  const { resetState } = useApp();
  const [saveStatus, setSaveStatus] = useState<'idle' | 'saving' | 'saved'>('idle');

  const handleSave = () => {
    setSaveStatus('saving');
    
    // 模拟保存过程
    setTimeout(() => {
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 2000);
    }, 500);
  };

  const handleReset = () => {
    if (confirm('确定要重置所有设置为默认值吗？')) {
      updateSettings(DEFAULT_SETTINGS);
      setSaveStatus('saved');
      setTimeout(() => setSaveStatus('idle'), 2000);
    }
  };

  const handleExportData = () => {
    const data = {
      settings,
      history,
      exportTime: new Date().toISOString(),
      version: '1.0.0'
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { 
      type: 'application/json' 
    });
    
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `watermark-tool-data-${Date.now()}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleImportData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        
        if (data.settings) {
          updateSettings(data.settings);
        }
        
        // 这里可以添加导入历史记录的逻辑
        // 但需要注意不要覆盖现有数据
        
        alert('数据导入成功！');
      } catch (error) {
        alert('数据格式错误，导入失败！');
      }
    };
    reader.readAsText(file);
  };

  const handleClearAllData = () => {
    if (confirm('确定要清空所有数据吗？这将删除所有设置和历史记录，此操作不可撤销。')) {
      resetState();
      alert('所有数据已清空！');
    }
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* 页面标题 */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <SettingsIcon className="h-6 w-6" />
            <h1 className="text-2xl font-bold">系统设置</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              onClick={handleReset}
              className="flex items-center space-x-2"
            >
              <RotateCcw className="h-4 w-4" />
              <span>重置默认</span>
            </Button>
            
            <Button 
              onClick={handleSave}
              disabled={saveStatus === 'saving'}
              className="flex items-center space-x-2"
            >
              {saveStatus === 'saving' ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  <span>保存中...</span>
                </>
              ) : saveStatus === 'saved' ? (
                <>
                  <CheckCircle className="h-4 w-4" />
                  <span>已保存</span>
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  <span>保存设置</span>
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 保存状态提示 */}
        {saveStatus === 'saved' && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              设置已成功保存！
            </AlertDescription>
          </Alert>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：参数设置 */}
          <div className="lg:col-span-2">
            <ParameterPanel
              settings={settings}
              onSettingsChange={updateSettings}
            />
          </div>

          {/* 右侧：其他设置 */}
          <div className="space-y-6">
            {/* 数据管理 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Database className="h-5 w-5" />
                  <span>数据管理</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">历史记录</span>
                    <Badge variant="secondary">{history.length} 条</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm">存储大小</span>
                    <Badge variant="secondary">
                      {Math.round(JSON.stringify({ settings, history }).length / 1024 * 10) / 10} KB
                    </Badge>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Button 
                    variant="outline" 
                    onClick={handleExportData}
                    className="w-full flex items-center space-x-2"
                  >
                    <Download className="h-4 w-4" />
                    <span>导出数据</span>
                  </Button>
                  
                  <div className="relative">
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleImportData}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    />
                    <Button 
                      variant="outline" 
                      className="w-full flex items-center space-x-2"
                    >
                      <Upload className="h-4 w-4" />
                      <span>导入数据</span>
                    </Button>
                  </div>
                  
                  <Button 
                    variant="destructive" 
                    onClick={handleClearAllData}
                    className="w-full flex items-center space-x-2"
                  >
                    <Trash2 className="h-4 w-4" />
                    <span>清空所有数据</span>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 系统信息 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Info className="h-5 w-5" />
                  <span>系统信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="grid grid-cols-1 gap-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">应用版本:</span>
                    <span>1.0.0</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">API地址:</span>
                    <span className="font-mono text-xs">
                      {process.env.NEXT_PUBLIC_API_BASE_URL || 'localhost:8000'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">浏览器:</span>
                    <span className="text-xs">
                      {typeof window !== 'undefined' ? navigator.userAgent.split(' ')[0] : 'Unknown'}
                    </span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">本地存储:</span>
                    <span>
                      {typeof window !== 'undefined' && 'localStorage' in window ? '支持' : '不支持'}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 使用说明 */}
            <Card>
              <CardHeader>
                <CardTitle>使用说明</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm text-muted-foreground">
                <p>
                  • <strong>置信度阈值</strong>：控制水印检测的敏感度，较低的值会检测更多可能的水印区域
                </p>
                <p>
                  • <strong>掩码增强</strong>：扩展检测区域以获得更好的修复效果
                </p>
                <p>
                  • <strong>智能增强</strong>：使用AI优化的掩码增强算法
                </p>
                <p>
                  • <strong>上下文扩展</strong>：控制掩码扩展的程度，较高的值可能修复更多区域
                </p>
                <p>
                  • 所有设置都会自动保存到本地存储中
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
