'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle, XCircle, Upload, Globe, AlertCircle } from "lucide-react"
import { watermarkApi } from '@/lib/api'

/**
 * API 测试页面
 * 用于测试 API 连接和功能
 */
export default function ApiTestPage() {
  const [testResults, setTestResults] = useState<Record<string, any>>({})
  const [isLoading, setIsLoading] = useState<Record<string, boolean>>({})
  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [testUrl, setTestUrl] = useState('https://via.placeholder.com/400x300.jpg')

  const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000'

  // 测试 API 连接
  const testConnection = async () => {
    setIsLoading(prev => ({ ...prev, connection: true }))
    try {
      const result = await watermarkApi.healthCheck()
      setTestResults(prev => ({ 
        ...prev, 
        connection: { success: true, data: result } 
      }))
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        connection: { success: false, error: error instanceof Error ? error.message : '连接失败' } 
      }))
    } finally {
      setIsLoading(prev => ({ ...prev, connection: false }))
    }
  }

  // 测试文件上传
  const testFileUpload = async () => {
    if (!selectedFile) {
      alert('请先选择一个文件')
      return
    }

    setIsLoading(prev => ({ ...prev, upload: true }))
    try {
      console.log('开始上传文件:', selectedFile.name, selectedFile.size, 'bytes')
      const result = await watermarkApi.uploadFile(selectedFile)
      console.log('上传成功，结果:', result)
      setTestResults(prev => ({
        ...prev,
        upload: { success: true, data: result }
      }))
    } catch (error) {
      console.error('上传失败:', error)
      setTestResults(prev => ({
        ...prev,
        upload: { success: false, error: error instanceof Error ? error.message : '上传失败' }
      }))
    } finally {
      setIsLoading(prev => ({ ...prev, upload: false }))
    }
  }

  // 测试水印去除任务提交
  const testTaskSubmit = async () => {
    if (!testUrl) {
      alert('请输入测试图片URL')
      return
    }

    setIsLoading(prev => ({ ...prev, task: true }))
    try {
      console.log('开始提交任务，图片URL:', testUrl)
      const result = await watermarkApi.submitTask({
        image_url: testUrl,
        confidence_threshold: 0.5,
        enhance_mask: true,
        use_smart_enhancement: true,
        context_expansion_ratio: 0.1,
        return_intermediate: true,
      })
      console.log('任务提交成功，结果:', result)
      setTestResults(prev => ({
        ...prev,
        task: { success: true, data: result }
      }))
    } catch (error) {
      console.error('任务提交失败:', error)
      setTestResults(prev => ({
        ...prev,
        task: { success: false, error: error instanceof Error ? error.message : '任务提交失败' }
      }))
    } finally {
      setIsLoading(prev => ({ ...prev, task: false }))
    }
  }

  const getStatusIcon = (success: boolean) => {
    return success ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  }

  const getStatusBadge = (success: boolean) => {
    return success ? (
      <Badge variant="default" className="bg-green-100 text-green-800">成功</Badge>
    ) : (
      <Badge variant="destructive">失败</Badge>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">API 测试工具</h1>
          <p className="text-muted-foreground">
            测试与后端 API 的连接和各项功能。当前 API 地址: <code className="bg-muted px-2 py-1 rounded">{apiBaseUrl}</code>
          </p>
        </div>

        <div className="grid gap-6">
          {/* API 连接测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                API 连接测试
              </CardTitle>
              <CardDescription>
                测试与后端服务器的基本连接
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Button 
                  onClick={testConnection}
                  disabled={isLoading.connection}
                  className="w-full"
                >
                  {isLoading.connection ? '测试中...' : '测试连接'}
                </Button>
                
                {testResults.connection && (
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(testResults.connection.success)}
                      {getStatusBadge(testResults.connection.success)}
                    </div>
                    <pre className="text-sm bg-background p-2 rounded overflow-auto">
                      {JSON.stringify(testResults.connection, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 文件上传测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                文件上传测试
              </CardTitle>
              <CardDescription>
                测试图片文件上传功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="file-upload">选择图片文件</Label>
                  <Input
                    id="file-upload"
                    type="file"
                    accept="image/*"
                    onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                    className="mt-1"
                  />
                  {selectedFile && (
                    <p className="text-sm text-muted-foreground mt-1">
                      已选择: {selectedFile.name} ({Math.round(selectedFile.size / 1024)}KB)
                    </p>
                  )}
                </div>
                
                <Button 
                  onClick={testFileUpload}
                  disabled={isLoading.upload || !selectedFile}
                  className="w-full"
                >
                  {isLoading.upload ? '上传中...' : '测试上传'}
                </Button>
                
                {testResults.upload && (
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(testResults.upload.success)}
                      {getStatusBadge(testResults.upload.success)}
                    </div>
                    <pre className="text-sm bg-background p-2 rounded overflow-auto">
                      {JSON.stringify(testResults.upload, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* 任务提交测试 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-5 w-5" />
                任务提交测试
              </CardTitle>
              <CardDescription>
                测试水印去除任务提交功能
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="test-url">测试图片URL</Label>
                  <Input
                    id="test-url"
                    type="url"
                    value={testUrl}
                    onChange={(e) => setTestUrl(e.target.value)}
                    placeholder="输入图片URL"
                    className="mt-1"
                  />
                </div>
                
                <Button 
                  onClick={testTaskSubmit}
                  disabled={isLoading.task || !testUrl}
                  className="w-full"
                >
                  {isLoading.task ? '提交中...' : '测试任务提交'}
                </Button>
                
                {testResults.task && (
                  <div className="p-4 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      {getStatusIcon(testResults.task.success)}
                      {getStatusBadge(testResults.task.success)}
                    </div>
                    <pre className="text-sm bg-background p-2 rounded overflow-auto">
                      {JSON.stringify(testResults.task, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        <Alert className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <strong>注意:</strong> 此页面仅用于开发和测试目的。请确保后端服务器正在运行，并且 API 地址配置正确。
          </AlertDescription>
        </Alert>
      </div>
    </div>
  )
}
