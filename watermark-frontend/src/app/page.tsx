'use client';

import React, { useState, useCallback, useRef } from 'react';
import { MainLayout } from '@/components/layout/MainLayout';
import { HeroSection } from '@/components/sections/HeroSection';
import { BeforeAfterShowcase } from '@/components/sections/BeforeAfterShowcase';
import { HowItWorksSection } from '@/components/sections/HowItWorksSection';
import { FeaturesSection } from '@/components/sections/FeaturesSection';
import { TestimonialsSection } from '@/components/sections/TestimonialsSection';
import { FAQSection } from '@/components/sections/FAQSection';
import { ModernImageUploader } from '@/components/upload/ModernImageUploader';
import { ProcessFlow } from '@/components/process/ProcessFlow';
import { ImageComparison } from '@/components/result/ImageComparison';
import { ParameterPanel } from '@/components/ParameterPanel';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Sparkles,
  Zap,
  Brain,
  ArrowRight,
  CheckCircle,
  RotateCcw
} from 'lucide-react';
import { useWatermarkRemoval } from '@/hooks/useWatermarkRemoval';
import { useSettings, useHistory } from '@/contexts/AppContext';
import { WatermarkRemovalRequest, HistoryItem } from '@/lib/types';
import { generateId } from '@/lib/utils';
import { watermarkApi } from '@/lib/api';


export default function NewHome() {
  const [selectedImage, setSelectedImage] = useState<File | string | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(1);
  const [showMainApp, setShowMainApp] = useState(false);
  
  const uploadSectionRef = useRef<HTMLDivElement>(null);

  const { settings, updateSettings } = useSettings();
  const { addHistoryItem } = useHistory();

  const {
    isProcessing,
    currentTask,
    error,
    removeWatermarkSync,
    reset
  } = useWatermarkRemoval({
    onSuccess: (result) => {
      // 添加到历史记录
      if (previewUrl && result.result_url) {
        const historyItem: HistoryItem = {
          id: generateId(),
          originalImageUrl: previewUrl,
          resultImageUrl: result.result_url,
          timestamp: new Date().toISOString(),
          status: 'completed',
          processingTime: result.processing_time,
          confidence: result.detection_confidence,
          parameters: {
            image_url: typeof selectedImage === 'string' ? selectedImage : previewUrl,
            confidence_threshold: settings.defaultConfidenceThreshold,
            enhance_mask: settings.defaultEnhanceMask,
            use_smart_enhancement: settings.defaultUseSmartEnhancement,
            context_expansion_ratio: settings.defaultContextExpansionRatio,
            return_intermediate: settings.showIntermediateResults,
          },
        };
        addHistoryItem(historyItem);
      }
      setCurrentStep(5);
    },
    onError: (error) => {
      console.error('水印去除失败:', error);
    },
  });

  const handleGetStarted = useCallback(() => {
    setShowMainApp(true);
    setTimeout(() => {
      uploadSectionRef.current?.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }, 100);
  }, []);

  const handleImageSelect = useCallback((image: File | string) => {
    setSelectedImage(image);
    setCurrentStep(2);
    reset(); // 重置之前的处理状态
  }, [reset]);

  const handleImagePreview = useCallback((url: string) => {
    setPreviewUrl(url);
  }, []);

  const handleStartProcessing = useCallback(async () => {
    if (!selectedImage || !previewUrl) return;

    setCurrentStep(3);

    try {
      let imageUrl: string;

      // 如果是文件，先上传到服务器
      if (selectedImage instanceof File) {
        const uploadResult = await watermarkApi.uploadFile(selectedImage);
        // 使用环境变量中的API基础地址
        const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';
        imageUrl = `${apiBaseUrl}${uploadResult.file_url}`;
      } else {
        // 如果是URL，直接使用
        imageUrl = selectedImage;
      }

      setCurrentStep(4);

      const request: WatermarkRemovalRequest = {
        image_url: imageUrl,
        confidence_threshold: settings.defaultConfidenceThreshold,
        enhance_mask: settings.defaultEnhanceMask,
        use_smart_enhancement: settings.defaultUseSmartEnhancement,
        context_expansion_ratio: settings.defaultContextExpansionRatio,
        return_intermediate: settings.showIntermediateResults,
      };

      // 使用同步处理以获得更好的用户体验
      await removeWatermarkSync(request);
    } catch (error) {
      console.error('处理失败:', error);
    }
  }, [
    selectedImage, 
    previewUrl, 
    settings, 
    removeWatermarkSync
  ]);

  const handleDownload = useCallback(() => {
    if (currentTask?.result_url) {
      const link = document.createElement('a');
      link.href = currentTask.result_url;
      link.download = `watermark-removed-${Date.now()}.jpg`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }, [currentTask]);

  const handleShare = useCallback(() => {
    if (navigator.share && currentTask?.result_url) {
      navigator.share({
        title: '智能水印去除结果',
        text: '看看我用 AI 去除水印的效果！',
        url: currentTask.result_url,
      });
    }
  }, [currentTask]);

  const handleReset = useCallback(() => {
    setSelectedImage(null);
    setPreviewUrl(null);
    setCurrentStep(1);
    reset();
  }, [reset]);

  return (
    <MainLayout>
      {/* Hero Section */}
      {!showMainApp && (
        <>
          <HeroSection
            onGetStarted={handleGetStarted}
            onImageSelect={handleImageSelect}
            onImagePreview={handleImagePreview}
          />
          <BeforeAfterShowcase />
          <HowItWorksSection />
          <FeaturesSection />
          <TestimonialsSection />
          <FAQSection />
        </>
      )}

      {/* Main Application */}
      {showMainApp && (
        <div ref={uploadSectionRef} className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
          <div className="container mx-auto px-6 py-12">
            <div className="max-w-7xl mx-auto space-y-12">
              {/* 页面标题 */}
              <div className="text-center space-y-6 animate-in fade-in slide-in-from-bottom-4 duration-700">
                <Badge className="bg-blue-100 text-blue-700 border-blue-200 px-4 py-2">
                  <Sparkles className="w-4 h-4 mr-2" />
                  AI 智能水印去除
                </Badge>
                <h1 className="text-4xl md:text-5xl font-bold text-slate-900">
                  <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">开始处理</span>您的图片
                </h1>
                <p className="text-xl text-slate-600 max-w-2xl mx-auto">
                  按照以下步骤，轻松去除图片中的水印
                </p>
              </div>

              {/* 处理流程指示器 */}
              <ProcessFlow 
                currentStep={currentStep}
                task={currentTask}
                isProcessing={isProcessing}
                error={typeof error === 'string' ? error : error?.message}
                onRetry={handleStartProcessing}
                className="animate-in fade-in slide-in-from-bottom-4 duration-700 delay-200"
              />

              {/* 主要内容区域 */}
              <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
                {/* 左侧：上传和结果 */}
                <div className="xl:col-span-3 space-y-8">
                  {/* 图片上传 */}
                  {currentStep <= 2 && (
                    <Card className="bg-white border border-slate-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 animate-in fade-in slide-in-from-bottom-4 delay-300">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-900">
                          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <span className="text-sm font-bold text-white">1</span>
                          </div>
                          <span className="text-xl">选择图片</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <ModernImageUploader
                          onImageSelect={handleImageSelect}
                          onImagePreview={handleImagePreview}
                          disabled={isProcessing}
                        />
                      </CardContent>
                    </Card>
                  )}

                  {/* 处理结果 */}
                  {currentStep === 5 && currentTask?.result_url && previewUrl && (
                    <Card className="bg-white border border-slate-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 animate-in zoom-in-50">
                      <CardHeader className="pb-4">
                        <div className="flex items-center justify-between">
                          <CardTitle className="flex items-center gap-3 text-slate-900">
                            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center shadow-lg">
                              <CheckCircle className="w-5 h-5 text-white" />
                            </div>
                            <span className="text-xl">处理完成</span>
                          </CardTitle>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={handleReset}
                            className="border-slate-300 text-slate-600 hover:bg-slate-50 hover:scale-105 transition-all duration-200"
                          >
                            <RotateCcw className="w-4 h-4 mr-2" />
                            重新开始
                          </Button>
                        </div>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <ImageComparison
                          originalUrl={previewUrl}
                          resultUrl={currentTask.result_url}
                          task={currentTask}
                          onDownload={handleDownload}
                          onShare={handleShare}
                        />
                      </CardContent>
                    </Card>
                  )}
                </div>

                {/* 右侧：参数配置 */}
                <div className="xl:col-span-1">
                  <div className="sticky top-6 space-y-6">
                    {/* 参数配置面板 */}
                    <Card className="bg-white border border-slate-200 rounded-2xl shadow-xl hover:shadow-2xl transition-all duration-500 animate-in fade-in slide-in-from-bottom-4 delay-500">
                      <CardHeader className="pb-4">
                        <CardTitle className="flex items-center gap-3 text-slate-900">
                          <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center shadow-lg">
                            <span className="text-sm font-bold text-white">2</span>
                          </div>
                          <span className="text-xl">参数配置</span>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <ParameterPanel
                          settings={settings}
                          onSettingsChange={updateSettings}
                          disabled={isProcessing}
                        />
                      </CardContent>
                    </Card>

                    {/* 开始处理按钮 */}
                    {selectedImage && currentStep >= 2 && currentStep < 5 && (
                      <Button
                        size="lg"
                        className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 animate-in zoom-in-50"
                        onClick={handleStartProcessing}
                        disabled={isProcessing}
                      >
                        {isProcessing ? (
                          <>
                            <Brain className="w-5 h-5 mr-2 animate-pulse" />
                            AI 处理中...
                          </>
                        ) : (
                          <>
                            <Zap className="w-5 h-5 mr-2" />
                            开始去除水印
                            <ArrowRight className="w-5 h-5 ml-2" />
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  );
}
