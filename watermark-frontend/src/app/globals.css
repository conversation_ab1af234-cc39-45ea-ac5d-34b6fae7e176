@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* 现代化色彩系统 */
  --background: 0 0% 100%;
  --foreground: 224 71.4% 4.1%;
  --card: 0 0% 100%;
  --card-foreground: 224 71.4% 4.1%;
  --popover: 0 0% 100%;
  --popover-foreground: 224 71.4% 4.1%;

  /* 主色调 - 现代蓝紫色 */
  --primary: 262.1 83.3% 57.8%;
  --primary-foreground: 210 20% 98%;

  /* 辅助色彩 */
  --secondary: 220 14.3% 95.9%;
  --secondary-foreground: 220.9 39.3% 11%;
  --muted: 220 14.3% 95.9%;
  --muted-foreground: 220 8.9% 46.1%;
  --accent: 220 14.3% 95.9%;
  --accent-foreground: 220.9 39.3% 11%;

  /* 功能色彩 */
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 20% 98%;
  --success: 142.1 76.2% 36.3%;
  --success-foreground: 355.7 100% 97.3%;
  --warning: 32.5 94.6% 43.7%;
  --warning-foreground: 355.7 100% 97.3%;

  /* 边框和输入 */
  --border: 220 13% 91%;
  --input: 220 13% 91%;
  --ring: 262.1 83.3% 57.8%;

  /* 圆角 */
  --radius: 0.75rem;
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* 现代化色彩系统 - 使用蓝紫渐变主题 */
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);

  /* 主色调 - 现代蓝紫色 */
  --primary: oklch(0.6 0.25 270);
  --primary-foreground: oklch(0.98 0 0);
  --primary-light: oklch(0.7 0.25 270);
  --primary-dark: oklch(0.5 0.25 270);

  /* 辅助色彩 */
  --secondary: oklch(0.96 0.01 270);
  --secondary-foreground: oklch(0.2 0 0);
  --muted: oklch(0.96 0.01 270);
  --muted-foreground: oklch(0.5 0 0);
  --accent: oklch(0.96 0.01 270);
  --accent-foreground: oklch(0.2 0 0);

  /* 功能色彩 */
  --destructive: oklch(0.65 0.25 25);
  --destructive-foreground: oklch(0.98 0 0);
  --success: oklch(0.6 0.2 140);
  --success-foreground: oklch(0.98 0 0);
  --warning: oklch(0.7 0.2 60);
  --warning-foreground: oklch(0.98 0 0);

  /* 边框和输入 */
  --border: oklch(0.9 0.01 270);
  --input: oklch(0.9 0.01 270);
  --ring: oklch(0.6 0.25 270);

  /* 图表色彩 */
  --chart-1: oklch(0.6 0.25 270);
  --chart-2: oklch(0.65 0.2 200);
  --chart-3: oklch(0.7 0.15 140);
  --chart-4: oklch(0.75 0.2 60);
  --chart-5: oklch(0.8 0.15 25);

  /* 侧边栏 */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.6 0.25 270);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.96 0.01 270);
  --sidebar-accent-foreground: oklch(0.2 0 0);
  --sidebar-border: oklch(0.9 0.01 270);
  --sidebar-ring: oklch(0.6 0.25 270);

  /* 自定义设计变量 */
  --gradient-primary: linear-gradient(135deg, oklch(0.6 0.25 270) 0%, oklch(0.65 0.25 250) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.96 0.01 270) 0%, oklch(0.94 0.02 260) 100%);
  --shadow-glow: 0 0 20px oklch(0.6 0.25 270 / 0.3);
  --shadow-soft: 0 4px 20px oklch(0 0 0 / 0.08);
  --shadow-medium: 0 8px 30px oklch(0 0 0 / 0.12);
  --shadow-strong: 0 20px 40px oklch(0 0 0 / 0.15);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground antialiased;
    font-family: var(--font-sans), ui-sans-serif, system-ui, sans-serif;
    font-feature-settings: "cv02", "cv03", "cv04", "cv11";
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* 简化的组件样式 */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(262.1 83.3% 67.8%) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}

@layer utilities {
  /* 简化的工具类 */
  .hover-scale {
    transition: transform 0.2s ease;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }
}
