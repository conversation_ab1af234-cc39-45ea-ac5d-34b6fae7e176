'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function TestPage() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('https://picsum.photos/300/200');

  const testHealthCheck = async () => {
    setLoading(true);
    setResult('正在测试健康检查...');
    
    try {
      const response = await fetch('http://localhost:8001/health');
      const data = await response.json();
      setResult(`✅ 健康检查成功:\n${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      setResult(`❌ 健康检查失败: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  const testWatermarkRemoval = async () => {
    setLoading(true);
    setResult('正在测试水印去除...');
    
    try {
      const requestData = {
        image_url: imageUrl,
        confidence_threshold: 0.5,
        enhance_mask: true,
        use_smart_enhancement: true,
        context_expansion_ratio: 0.12,
        return_intermediate: false
      };

      console.log('发送请求:', requestData);

      const response = await fetch('http://localhost:8001/remove-watermark-sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      console.log('响应状态:', response.status, response.statusText);

      if (!response.ok) {
        const errorText = await response.text();
        console.log('错误响应:', errorText);
        setResult(`❌ 请求失败 (${response.status}): ${errorText}`);
        return;
      }

      const data = await response.json();
      console.log('成功响应:', data);
      setResult(`✅ 水印去除成功:\n${JSON.stringify(data, null, 2)}`);
    } catch (error) {
      console.error('请求异常:', error);
      setResult(`❌ 请求异常: ${error}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>API 连接测试</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <label htmlFor="imageUrl">测试图片URL:</label>
            <Input
              id="imageUrl"
              value={imageUrl}
              onChange={(e) => setImageUrl(e.target.value)}
              placeholder="输入图片URL"
            />
          </div>

          <div className="flex space-x-4">
            <Button onClick={testHealthCheck} disabled={loading}>
              测试健康检查
            </Button>
            <Button onClick={testWatermarkRemoval} disabled={loading}>
              测试水印去除
            </Button>
          </div>

          {result && (
            <div className="mt-4">
              <h3 className="font-semibold mb-2">测试结果:</h3>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-96">
                {result}
              </pre>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
