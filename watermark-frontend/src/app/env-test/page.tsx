'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { CheckCircle, XCircle, AlertCircle } from "lucide-react"

/**
 * 环境变量测试页面
 * 用于验证环境配置是否正确加载
 */
export default function EnvTestPage() {
  // 获取所有环境变量
  const envVars = {
    // API 配置
    API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    
    // 应用配置
    APP_NAME: process.env.NEXT_PUBLIC_APP_NAME,
    APP_VERSION: process.env.NEXT_PUBLIC_APP_VERSION,
    APP_DESCRIPTION: process.env.NEXT_PUBLIC_APP_DESCRIPTION,
    
    // 功能开关
    DEBUG_MODE: process.env.NEXT_PUBLIC_DEBUG_MODE,
    SHOW_DEV_TOOLS: process.env.NEXT_PUBLIC_SHOW_DEV_TOOLS,
    ENABLE_ANALYTICS: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS,
    
    // 上传配置
    MAX_FILE_SIZE: process.env.NEXT_PUBLIC_MAX_FILE_SIZE,
    ALLOWED_FORMATS: process.env.NEXT_PUBLIC_ALLOWED_FORMATS,
    
    // UI 配置
    DEFAULT_THEME: process.env.NEXT_PUBLIC_DEFAULT_THEME,
    ENABLE_ANIMATIONS: process.env.NEXT_PUBLIC_ENABLE_ANIMATIONS,
    
    // 开发配置
    HOT_RELOAD: process.env.NEXT_PUBLIC_HOT_RELOAD,
    SHOW_PERFORMANCE: process.env.NEXT_PUBLIC_SHOW_PERFORMANCE,
    
    // 其他配置
    DEFAULT_LOCALE: process.env.NEXT_PUBLIC_DEFAULT_LOCALE,
    ENABLE_PWA: process.env.NEXT_PUBLIC_ENABLE_PWA,
  }

  // 检查环境变量状态
  const getStatus = (value: string | undefined) => {
    if (value === undefined) return 'missing'
    if (value === '') return 'empty'
    return 'ok'
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ok':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'empty':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />
      case 'missing':
        return <XCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'ok':
        return <Badge variant="default" className="bg-green-100 text-green-800">已配置</Badge>
      case 'empty':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">空值</Badge>
      case 'missing':
        return <Badge variant="destructive">缺失</Badge>
      default:
        return null
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">环境变量测试</h1>
          <p className="text-muted-foreground">
            此页面用于验证环境配置是否正确加载。仅在开发环境中可见。
          </p>
        </div>

        <div className="grid gap-6">
          {/* API 配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🔗 API 配置
              </CardTitle>
              <CardDescription>
                后端API相关的配置项
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(getStatus(envVars.API_BASE_URL))}
                    <span className="font-medium">API_BASE_URL</span>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(getStatus(envVars.API_BASE_URL))}
                    <code className="text-sm bg-background px-2 py-1 rounded">
                      {envVars.API_BASE_URL || '未设置'}
                    </code>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 应用配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📱 应用配置
              </CardTitle>
              <CardDescription>
                应用基本信息配置
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { key: 'APP_NAME', value: envVars.APP_NAME, label: '应用名称' },
                  { key: 'APP_VERSION', value: envVars.APP_VERSION, label: '应用版本' },
                  { key: 'APP_DESCRIPTION', value: envVars.APP_DESCRIPTION, label: '应用描述' },
                ].map(({ key, value, label }) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(getStatus(value))}
                      <span className="font-medium">{label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(getStatus(value))}
                      <code className="text-sm bg-background px-2 py-1 rounded max-w-xs truncate">
                        {value || '未设置'}
                      </code>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 功能开关 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                🎛️ 功能开关
              </CardTitle>
              <CardDescription>
                应用功能的开关配置
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { key: 'DEBUG_MODE', value: envVars.DEBUG_MODE, label: '调试模式' },
                  { key: 'SHOW_DEV_TOOLS', value: envVars.SHOW_DEV_TOOLS, label: '显示开发工具' },
                  { key: 'ENABLE_ANALYTICS', value: envVars.ENABLE_ANALYTICS, label: '启用分析' },
                ].map(({ key, value, label }) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(getStatus(value))}
                      <span className="font-medium">{label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(getStatus(value))}
                      <Badge variant={value === 'true' ? 'default' : 'secondary'}>
                        {value === 'true' ? '启用' : value === 'false' ? '禁用' : '未设置'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 上传配置 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                📤 上传配置
              </CardTitle>
              <CardDescription>
                文件上传相关的配置项
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[
                  { 
                    key: 'MAX_FILE_SIZE', 
                    value: envVars.MAX_FILE_SIZE, 
                    label: '最大文件大小',
                    display: envVars.MAX_FILE_SIZE ? `${Math.round(parseInt(envVars.MAX_FILE_SIZE) / 1024 / 1024)}MB` : '未设置'
                  },
                  { key: 'ALLOWED_FORMATS', value: envVars.ALLOWED_FORMATS, label: '支持格式' },
                ].map(({ key, value, label, display }) => (
                  <div key={key} className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {getStatusIcon(getStatus(value))}
                      <span className="font-medium">{label}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      {getStatusBadge(getStatus(value))}
                      <code className="text-sm bg-background px-2 py-1 rounded">
                        {display || value || '未设置'}
                      </code>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* 环境信息 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                ℹ️ 环境信息
              </CardTitle>
              <CardDescription>
                当前运行环境的基本信息
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <span className="font-medium">Node 环境</span>
                  <Badge variant="outline">{process.env.NODE_ENV || 'development'}</Badge>
                </div>
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <span className="font-medium">构建时间</span>
                  <code className="text-sm bg-background px-2 py-1 rounded">
                    {new Date().toLocaleString('zh-CN')}
                  </code>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
          <p className="text-sm text-blue-800 dark:text-blue-200">
            💡 <strong>提示:</strong> 此页面仅用于开发环境测试。在生产环境中，请确保移除此页面或限制访问权限。
          </p>
        </div>
      </div>
    </div>
  )
}
