/**
 * 参数配置面板组件
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  RotateCcw, 
  Info,
  Sliders,
  Zap,
  Eye,
  Target
} from 'lucide-react';
import { UserSettings } from '@/lib/types';
import { cn } from '@/lib/utils';

interface ParameterPanelProps {
  settings: UserSettings;
  onSettingsChange: (settings: Partial<UserSettings>) => void;
  disabled?: boolean;
  className?: string;
}

export function ParameterPanel({ 
  settings, 
  onSettingsChange, 
  disabled = false,
  className 
}: ParameterPanelProps) {
  const handleReset = () => {
    onSettingsChange({
      defaultConfidenceThreshold: 0.3,
      defaultEnhanceMask: true,
      defaultUseSmartEnhancement: true,
      defaultContextExpansionRatio: 0.12,
      autoDownload: false,
      showIntermediateResults: false,
    });
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>处理参数</span>
          </div>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            disabled={disabled}
          >
            <RotateCcw className="h-4 w-4 mr-1" />
            重置
          </Button>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 检测参数 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <h3 className="font-medium">检测参数</h3>
          </div>

          {/* 置信度阈值 */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <Label htmlFor="confidence-threshold" className="text-sm">
                置信度阈值
              </Label>
              <Badge variant="outline">
                {Math.round(settings.defaultConfidenceThreshold * 100)}%
              </Badge>
            </div>
            
            <div className="space-y-2">
              <input
                id="confidence-threshold"
                type="range"
                min="0.1"
                max="0.9"
                step="0.05"
                value={settings.defaultConfidenceThreshold}
                onChange={(e) => onSettingsChange({
                  defaultConfidenceThreshold: parseFloat(e.target.value)
                })}
                disabled={disabled}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
              />
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>低敏感度 (10%)</span>
                <span>高敏感度 (90%)</span>
              </div>
            </div>
            
            <div className="text-xs text-muted-foreground flex items-start space-x-1">
              <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
              <span>
                较低的阈值会检测更多可能的水印区域，较高的阈值只检测明显的水印
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* 处理参数 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Sliders className="h-4 w-4" />
            <h3 className="font-medium">处理参数</h3>
          </div>

          {/* 掩码增强 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm">掩码增强</Label>
              <p className="text-xs text-muted-foreground">
                扩展检测区域以获得更好的修复效果
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.defaultEnhanceMask}
                onChange={(e) => onSettingsChange({
                  defaultEnhanceMask: e.target.checked
                })}
                disabled={disabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* 智能增强 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm flex items-center">
                <Zap className="h-3 w-3 mr-1" />
                智能增强
              </Label>
              <p className="text-xs text-muted-foreground">
                使用AI优化的掩码增强算法
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.defaultUseSmartEnhancement}
                onChange={(e) => onSettingsChange({
                  defaultUseSmartEnhancement: e.target.checked
                })}
                disabled={disabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* 上下文扩展比例 */}
          {settings.defaultUseSmartEnhancement && (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <Label htmlFor="context-expansion" className="text-sm">
                  上下文扩展比例
                </Label>
                <Badge variant="outline">
                  {Math.round(settings.defaultContextExpansionRatio * 100)}%
                </Badge>
              </div>
              
              <div className="space-y-2">
                <input
                  id="context-expansion"
                  type="range"
                  min="0.05"
                  max="0.3"
                  step="0.01"
                  value={settings.defaultContextExpansionRatio}
                  onChange={(e) => onSettingsChange({
                    defaultContextExpansionRatio: parseFloat(e.target.value)
                  })}
                  disabled={disabled}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>保守 (5%)</span>
                  <span>激进 (30%)</span>
                </div>
              </div>
              
              <div className="text-xs text-muted-foreground flex items-start space-x-1">
                <Info className="h-3 w-3 mt-0.5 flex-shrink-0" />
                <span>
                  控制掩码扩展的程度，较高的值可能修复更多区域但也可能影响周围内容
                </span>
              </div>
            </div>
          )}
        </div>

        <Separator />

        {/* 界面设置 */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Eye className="h-4 w-4" />
            <h3 className="font-medium">界面设置</h3>
          </div>

          {/* 自动下载 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm">自动下载结果</Label>
              <p className="text-xs text-muted-foreground">
                处理完成后自动下载结果图片
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.autoDownload}
                onChange={(e) => onSettingsChange({
                  autoDownload: e.target.checked
                })}
                disabled={disabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>

          {/* 显示中间结果 */}
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-sm">显示中间结果</Label>
              <p className="text-xs text-muted-foreground">
                显示检测可视化和处理掩码
              </p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.showIntermediateResults}
                onChange={(e) => onSettingsChange({
                  showIntermediateResults: e.target.checked
                })}
                disabled={disabled}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
            </label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
