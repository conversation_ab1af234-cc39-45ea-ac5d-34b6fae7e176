/**
 * 图片上传和URL输入组件
 */

'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  Link as LinkIcon, 
  Image as ImageIcon,
  X,
  AlertCircle,
  Check
} from 'lucide-react';
import { cn, validateImageFile, validateImageUrl, fileToDataUrl } from '@/lib/utils';

interface ImageUploaderProps {
  onImageSelect: (file: File | string) => void;
  onImagePreview?: (previewUrl: string) => void;
  isProcessing?: boolean;
  className?: string;
}

export function ImageUploader({ 
  onImageSelect, 
  onImagePreview,
  isProcessing = false,
  className 
}: ImageUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState('');
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [urlError, setUrlError] = useState<string | null>(null);
  
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    setError(null);
    
    const validation = validateImageFile(file);
    if (!validation.valid) {
      setError(validation.error!);
      return;
    }

    try {
      const dataUrl = await fileToDataUrl(file);
      setSelectedFile(file);
      setPreviewUrl(dataUrl);
      onImagePreview?.(dataUrl);
      onImageSelect(file);
    } catch (err) {
      setError('文件读取失败，请重试');
    }
  }, [onImageSelect, onImagePreview]);

  // 处理拖拽
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (isProcessing) return;

    const files = e.dataTransfer.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect, isProcessing]);

  // 处理文件输入
  const handleFileInput = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  // 处理URL输入
  const handleUrlSubmit = useCallback(() => {
    setUrlError(null);
    
    if (!imageUrl.trim()) {
      setUrlError('请输入图片URL');
      return;
    }

    const validation = validateImageUrl(imageUrl.trim());
    if (!validation.valid) {
      setUrlError(validation.error!);
      return;
    }

    setPreviewUrl(imageUrl.trim());
    onImagePreview?.(imageUrl.trim());
    onImageSelect(imageUrl.trim());
  }, [imageUrl, onImageSelect, onImagePreview]);

  // 清除选择
  const handleClear = useCallback(() => {
    setSelectedFile(null);
    setImageUrl('');
    setPreviewUrl(null);
    setError(null);
    setUrlError(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <Card className={cn("w-full", className)}>
      <CardContent className="p-6">
        <Tabs defaultValue="upload" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="upload" disabled={isProcessing}>
              <Upload className="h-4 w-4 mr-2" />
              上传文件
            </TabsTrigger>
            <TabsTrigger value="url" disabled={isProcessing}>
              <LinkIcon className="h-4 w-4 mr-2" />
              URL链接
            </TabsTrigger>
          </TabsList>

          {/* 文件上传 */}
          <TabsContent value="upload" className="space-y-4">
            <div
              className={cn(
                "relative border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                dragActive ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                isProcessing ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-primary/50"
              )}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
              onClick={() => !isProcessing && fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={handleFileInput}
                className="hidden"
                disabled={isProcessing}
              />
              
              <div className="space-y-4">
                <div className="mx-auto w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center">
                  <Upload className="h-6 w-6 text-primary" />
                </div>
                
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    拖拽图片到此处或点击上传
                  </p>
                  <p className="text-sm text-muted-foreground">
                    支持 JPG、PNG、WebP 格式，最大 50MB
                  </p>
                </div>
                
                <Button variant="outline" disabled={isProcessing}>
                  选择文件
                </Button>
              </div>
            </div>

            {error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}
          </TabsContent>

          {/* URL输入 */}
          <TabsContent value="url" className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="image-url">图片URL地址</Label>
              <div className="flex space-x-2">
                <Input
                  id="image-url"
                  type="url"
                  placeholder="https://example.com/image.jpg"
                  value={imageUrl}
                  onChange={(e) => setImageUrl(e.target.value)}
                  disabled={isProcessing}
                  onKeyDown={(e) => e.key === 'Enter' && handleUrlSubmit()}
                />
                <Button 
                  onClick={handleUrlSubmit}
                  disabled={isProcessing || !imageUrl.trim()}
                >
                  <Check className="h-4 w-4" />
                </Button>
              </div>
            </div>

            {urlError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{urlError}</AlertDescription>
              </Alert>
            )}
          </TabsContent>
        </Tabs>

        {/* 预览区域 */}
        {previewUrl && (
          <div className="mt-6 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-medium flex items-center">
                <ImageIcon className="h-5 w-5 mr-2" />
                图片预览
              </h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClear}
                disabled={isProcessing}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="relative rounded-lg overflow-hidden border">
              <img
                src={previewUrl}
                alt="预览图片"
                className="w-full h-auto max-h-96 object-contain"
                onError={() => {
                  setError('图片加载失败，请检查URL是否正确');
                  setPreviewUrl(null);
                }}
              />
            </div>
            
            {selectedFile && (
              <div className="text-sm text-muted-foreground">
                文件名: {selectedFile.name} | 大小: {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
