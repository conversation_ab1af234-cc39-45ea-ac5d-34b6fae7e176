/**
 * 结果展示和对比组件
 */

'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download, 
  Share2, 
  Eye,
  EyeOff,
  Maximize2,
  Copy,
  ExternalLink,
  ImageIcon,
  Layers
} from 'lucide-react';
import { TaskStatus, DetectionInfo, IntermediateResults } from '@/lib/types';
import { downloadFile, copyToClipboard } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface ResultViewerProps {
  originalImageUrl: string;
  task?: TaskStatus;
  onDownload?: () => void;
  onShare?: () => void;
  className?: string;
}

export function ResultViewer({ 
  originalImageUrl,
  task,
  onDownload,
  onShare,
  className 
}: ResultViewerProps) {
  const [showComparison, setShowComparison] = useState(true);
  const [activeTab, setActiveTab] = useState('result');

  if (!task || task.status !== 'completed' || !task.result_url) {
    return null;
  }

  const handleDownload = () => {
    if (task.result_url) {
      downloadFile(task.result_url, `watermark-removed-${Date.now()}.jpg`);
      onDownload?.();
    }
  };

  const handleCopyUrl = async () => {
    if (task.result_url) {
      const success = await copyToClipboard(task.result_url);
      if (success) {
        // 这里可以添加成功提示
      }
    }
  };

  const handleShare = () => {
    if (navigator.share && task.result_url) {
      navigator.share({
        title: '水印去除结果',
        text: '查看我的水印去除结果',
        url: task.result_url,
      });
    } else {
      onShare?.();
    }
  };

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <ImageIcon className="h-5 w-5" />
            <span>处理结果</span>
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              完成
            </Badge>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowComparison(!showComparison)}
            >
              {showComparison ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              {showComparison ? '隐藏对比' : '显示对比'}
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleCopyUrl}>
              <Copy className="h-4 w-4" />
            </Button>
            
            <Button variant="ghost" size="sm" onClick={handleShare}>
              <Share2 className="h-4 w-4" />
            </Button>
            
            <Button onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              下载
            </Button>
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* 图片对比区域 */}
        {showComparison ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 原图 */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">原图</h3>
              <div className="relative rounded-lg overflow-hidden border">
                <img
                  src={originalImageUrl}
                  alt="原图"
                  className="w-full h-auto object-contain"
                />
                <div className="absolute top-2 left-2">
                  <Badge variant="secondary">原图</Badge>
                </div>
              </div>
            </div>

            {/* 处理结果 */}
            <div className="space-y-2">
              <h3 className="text-sm font-medium text-muted-foreground">处理结果</h3>
              <div className="relative rounded-lg overflow-hidden border">
                <img
                  src={task.result_url}
                  alt="处理结果"
                  className="w-full h-auto object-contain"
                />
                <div className="absolute top-2 left-2">
                  <Badge className="bg-green-500">已去除水印</Badge>
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* 单独显示结果 */
          <div className="space-y-2">
            <h3 className="text-sm font-medium text-muted-foreground">处理结果</h3>
            <div className="relative rounded-lg overflow-hidden border">
              <img
                src={task.result_url}
                alt="处理结果"
                className="w-full h-auto object-contain"
              />
            </div>
          </div>
        )}

        {/* 中间结果展示 */}
        {task.intermediate_results && (
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="result">最终结果</TabsTrigger>
              <TabsTrigger value="detection">检测可视化</TabsTrigger>
              <TabsTrigger value="masks">处理掩码</TabsTrigger>
            </TabsList>

            <TabsContent value="result" className="mt-4">
              <div className="text-center text-muted-foreground">
                最终处理结果已在上方显示
              </div>
            </TabsContent>

            <TabsContent value="detection" className="mt-4">
              {task.intermediate_results.detection_visualization_url ? (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">水印检测可视化</h4>
                  <div className="rounded-lg overflow-hidden border">
                    <img
                      src={task.intermediate_results.detection_visualization_url}
                      alt="检测可视化"
                      className="w-full h-auto object-contain"
                    />
                  </div>
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  检测可视化不可用
                </div>
              )}
            </TabsContent>

            <TabsContent value="masks" className="mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {task.intermediate_results.original_mask && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">原始掩码</h4>
                    <div className="rounded-lg overflow-hidden border">
                      <img
                        src={task.intermediate_results.original_mask}
                        alt="原始掩码"
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>
                )}

                {task.intermediate_results.enhanced_mask && (
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium">增强掩码</h4>
                    <div className="rounded-lg overflow-hidden border">
                      <img
                        src={task.intermediate_results.enhanced_mask}
                        alt="增强掩码"
                        className="w-full h-auto object-contain"
                      />
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        )}

        {/* 处理统计信息 */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">
              {task.watermark_detected ? '✓' : '✗'}
            </p>
            <p className="text-xs text-muted-foreground">水印检测</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">
              {Math.round((task.detection_confidence || 0) * 10) / 10}%
            </p>
            <p className="text-xs text-muted-foreground">检测置信度</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">
              {task.detection_info?.boxes.length || 0}
            </p>
            <p className="text-xs text-muted-foreground">检测区域</p>
          </div>
          
          <div className="text-center">
            <p className="text-2xl font-bold text-primary">
              {task.processing_time ? Math.round(task.processing_time * 10) / 10 : 0}s
            </p>
            <p className="text-xs text-muted-foreground">处理时间</p>
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-wrap gap-2 justify-center">
          <Button variant="outline" onClick={handleDownload}>
            <Download className="h-4 w-4 mr-2" />
            下载结果
          </Button>
          
          <Button variant="outline" onClick={handleCopyUrl}>
            <Copy className="h-4 w-4 mr-2" />
            复制链接
          </Button>
          
          <Button variant="outline" onClick={handleShare}>
            <Share2 className="h-4 w-4 mr-2" />
            分享结果
          </Button>
          
          <Button variant="outline" asChild>
            <a href={task.result_url} target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              新窗口打开
            </a>
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
