'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Upload,
  Brain,
  Edit3,
  Download,
  ArrowRight,
  Sparkles,
  Zap,
  CheckCircle
} from 'lucide-react';

const steps = [
  {
    id: 1,
    icon: Upload,
    title: "上传带有水印的图片",
    description: "您可以将图片拖放到文本框中，或通过点击上传按钮从设备中选择文件。支持 JPG、PNG、WebP 等格式。",
    image: "/api/placeholder/300/200",
    color: "from-blue-500 to-blue-600",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200"
  },
  {
    id: 2,
    icon: Brain,
    title: "自动去除水印",
    description: "我们的 AI 将在几秒钟内处理您的图片，自动检测并去除图片中的版权标记，保持原始质量。",
    image: "/api/placeholder/300/200",
    color: "from-purple-500 to-purple-600",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200"
  },
  {
    id: 3,
    icon: Edit3,
    title: "使用手动画笔完成编辑",
    description: "在某些情况下，AI 不能完全去除水印的所有细节，用户可以继续使用手动画笔完成过程。",
    image: "/api/placeholder/300/200",
    color: "from-green-500 to-green-600",
    bgColor: "bg-green-50",
    borderColor: "border-green-200"
  },
  {
    id: 4,
    icon: Download,
    title: "下载无水印的高清图片",
    description: "现在您可以选择下载与原版一样的图片质量，或下载高清质量的图片。完全免费，无需注册。",
    image: "/api/placeholder/300/200",
    color: "from-orange-500 to-orange-600",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200"
  }
];

export function HowItWorksSection() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center space-y-6 mb-20">
            <Badge className="bg-slate-100 text-slate-700 border-slate-200 px-4 py-2">
              <Sparkles className="w-4 h-4 mr-2" />
              简单四步
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900">
              如何使用智能水印去除工具
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              通过以下四个简单步骤，轻松去除图片上的复杂水印，让您的图片焕然一新
            </p>
          </div>

          {/* 步骤展示 */}
          <div className="space-y-20">
            {steps.map((step, index) => (
              <div key={step.id} className="relative">
                {/* 连接线 */}
                {index < steps.length - 1 && (
                  <div className="absolute left-1/2 -bottom-10 transform -translate-x-1/2 z-10">
                    <div className="w-px h-20 bg-gradient-to-b from-slate-300 to-transparent"></div>
                    <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                      <div className="w-8 h-8 bg-white border-2 border-slate-300 rounded-full flex items-center justify-center">
                        <ArrowRight className="w-4 h-4 text-slate-400 rotate-90" />
                      </div>
                    </div>
                  </div>
                )}

                <Card className={`overflow-hidden border-2 ${step.borderColor} hover:shadow-2xl transition-all duration-500 hover:scale-[1.02]`}>
                  <div className={`grid grid-cols-1 lg:grid-cols-2 ${index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''}`}>
                    {/* 图片区域 */}
                    <div className={`relative ${step.bgColor} p-8 flex items-center justify-center ${index % 2 === 1 ? 'lg:col-start-2' : ''}`}>
                      <div className="relative">
                        {/* 主图片 */}
                        <div className="w-80 h-60 bg-white rounded-2xl shadow-xl overflow-hidden border border-white/50">
                          <div className="w-full h-full bg-gradient-to-br from-slate-200 to-slate-300 flex items-center justify-center">
                            <div className="text-center space-y-3">
                              <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-xl mx-auto flex items-center justify-center shadow-lg`}>
                                <step.icon className="w-8 h-8 text-white" />
                              </div>
                              <p className="text-slate-600 font-medium">步骤 {step.id}</p>
                            </div>
                          </div>
                        </div>

                        {/* 装饰元素 */}
                        <div className={`absolute -top-4 -right-4 w-8 h-8 bg-gradient-to-br ${step.color} rounded-full animate-pulse`}></div>
                        <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-white rounded-full shadow-lg border-2 border-slate-200"></div>
                        
                        {/* 步骤编号 */}
                        <div className="absolute -top-6 -left-6">
                          <div className={`w-12 h-12 bg-gradient-to-br ${step.color} rounded-full flex items-center justify-center shadow-lg`}>
                            <span className="text-white font-bold text-lg">{step.id}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* 内容区域 */}
                    <div className="p-8 lg:p-12 flex items-center">
                      <div className="space-y-6">
                        {/* 图标和标题 */}
                        <div className="space-y-4">
                          <div className={`w-16 h-16 bg-gradient-to-br ${step.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                            <step.icon className="w-8 h-8 text-white" />
                          </div>
                          <h3 className="text-2xl lg:text-3xl font-bold text-slate-900">
                            {step.title}
                          </h3>
                        </div>

                        {/* 描述 */}
                        <p className="text-lg text-slate-600 leading-relaxed">
                          {step.description}
                        </p>

                        {/* 特性亮点 */}
                        <div className="space-y-3">
                          {step.id === 1 && (
                            <>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">支持多种格式：JPG、PNG、WebP</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">最大支持 50MB 文件</span>
                              </div>
                            </>
                          )}
                          {step.id === 2 && (
                            <>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">AI 智能识别，99.5% 准确率</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">平均处理时间少于 30 秒</span>
                              </div>
                            </>
                          )}
                          {step.id === 3 && (
                            <>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">精细化手动编辑工具</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">实时预览编辑效果</span>
                              </div>
                            </>
                          )}
                          {step.id === 4 && (
                            <>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">保持原始图片质量</span>
                              </div>
                              <div className="flex items-center gap-3">
                                <CheckCircle className="w-5 h-5 text-green-500" />
                                <span className="text-slate-700">完全免费，无需注册</span>
                              </div>
                            </>
                          )}
                        </div>

                        {/* 处理时间指示 */}
                        <div className="flex items-center gap-2 text-sm text-slate-500">
                          <Zap className="w-4 h-4" />
                          <span>
                            {step.id === 1 && "即时上传"}
                            {step.id === 2 && "AI 处理：15-30秒"}
                            {step.id === 3 && "可选步骤"}
                            {step.id === 4 && "即时下载"}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </Card>
              </div>
            ))}
          </div>

          {/* 底部 CTA */}
          <div className="text-center mt-20">
            <div className="inline-flex items-center gap-2 px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 cursor-pointer">
              <Sparkles className="w-5 h-5" />
              <span className="font-medium">准备开始了吗？立即上传您的图片</span>
              <ArrowRight className="w-5 h-5" />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
