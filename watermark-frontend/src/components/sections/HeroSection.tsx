'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON><PERSON>,
  Zap,
  Shield,
  Brain,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Clock,
  Upload,
  Image as ImageIcon
} from 'lucide-react';

interface HeroSectionProps {
  onGetStarted: () => void;
  onImageSelect?: (file: File | string) => void;
  onImagePreview?: (url: string) => void;
}

export function HeroSection({ onGetStarted, onImageSelect, onImagePreview }: HeroSectionProps) {
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && onImageSelect) {
      onImageSelect(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        onImagePreview?.(url);
      };
      reader.readAsDataURL(file);
      onGetStarted();
    }
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files?.[0];
    if (file && onImageSelect) {
      onImageSelect(file);
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        onImagePreview?.(url);
      };
      reader.readAsDataURL(file);
      onGetStarted();
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  return (
    <section className="relative min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-100/20 via-white to-purple-100/20" />

      <div className="container mx-auto px-6 relative z-10 pt-20">
        <div className="max-w-6xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center space-y-8 mb-16 animate-in fade-in slide-in-from-bottom-4 duration-700">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold tracking-tight">
                <span className="block text-slate-900">使用 AI 在线去除</span>
                <span className="block bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">图片水印</span>
              </h1>
              <p className="text-xl md:text-2xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
                用专业图像提升您的产品。快速、准确、保持原始质量。
              </p>
            </div>
          </div>

          {/* 主要上传区域 */}
          <div className="max-w-4xl mx-auto mb-20">
            <div
              className="relative group"
              onDrop={handleDrop}
              onDragOver={handleDragOver}
            >
              {/* 主上传卡片 */}
              <div className="bg-white rounded-3xl shadow-2xl border border-slate-200/50 p-12 text-center hover:shadow-3xl transition-all duration-500 group-hover:scale-[1.02]">
                <div className="space-y-8">
                  {/* 上传图标 */}
                  <div className="flex justify-center">
                    <div className="relative">
                      <div className="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                        <Upload className="w-12 h-12 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                        <Sparkles className="w-4 h-4 text-white" />
                      </div>
                    </div>
                  </div>

                  {/* 上传文本 */}
                  <div className="space-y-4">
                    <h3 className="text-2xl font-semibold text-slate-900">
                      拖放图片到这里
                    </h3>
                    <p className="text-slate-500 text-lg">
                      或直接拖放到这里 / Ctrl + V 粘贴图像
                    </p>
                  </div>

                  {/* 上传按钮 */}
                  <div className="space-y-4">
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleFileSelect}
                      className="hidden"
                      id="file-upload"
                    />
                    <label htmlFor="file-upload">
                      <Button
                        size="lg"
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg font-medium rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                        asChild
                      >
                        <span className="cursor-pointer">
                          <ImageIcon className="w-5 h-5 mr-2" />
                          上传图像
                        </span>
                      </Button>
                    </label>

                    {/* 支持格式 */}
                    <div className="flex justify-center gap-3 text-sm text-slate-400">
                      <span className="px-3 py-1 bg-slate-100 rounded-full">JPG</span>
                      <span className="px-3 py-1 bg-slate-100 rounded-full">PNG</span>
                      <span className="px-3 py-1 bg-slate-100 rounded-full">WebP</span>
                      <span className="px-3 py-1 bg-slate-100 rounded-full">最大 50MB</span>
                    </div>
                  </div>
                </div>
              </div>

              {/* 装饰性元素 */}
              <div className="absolute -top-4 -left-4 w-8 h-8 bg-blue-500 rounded-full opacity-20 animate-pulse"></div>
              <div className="absolute -bottom-4 -right-4 w-12 h-12 bg-purple-500 rounded-full opacity-20 animate-pulse delay-1000"></div>
            </div>

            {/* 示例图片 */}
            <div className="mt-12 text-center">
              <p className="text-slate-500 mb-6">没有图像？试试这些</p>
              <div className="flex justify-center gap-4 flex-wrap">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="w-20 h-20 bg-gradient-to-br from-slate-200 to-slate-300 rounded-xl flex items-center justify-center cursor-pointer hover:scale-110 transition-transform duration-200 shadow-md hover:shadow-lg"
                  >
                    <ImageIcon className="w-8 h-8 text-slate-500" />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 特性展示 */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 max-w-4xl mx-auto animate-in fade-in slide-in-from-bottom-4 duration-700 delay-300">
            {[
              { icon: Zap, title: "快速处理", desc: "平均 30 秒" },
              { icon: Brain, title: "AI 智能", desc: "精准识别" },
              { icon: Shield, title: "隐私安全", desc: "本地处理" },
              { icon: CheckCircle, title: "免费使用", desc: "无需注册" }
            ].map((feature, index) => (
              <div key={index} className="text-center space-y-3">
                <div className="w-12 h-12 bg-white rounded-xl shadow-lg flex items-center justify-center mx-auto">
                  <feature.icon className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-semibold text-slate-900">{feature.title}</h4>
                <p className="text-sm text-slate-500">{feature.desc}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
}
