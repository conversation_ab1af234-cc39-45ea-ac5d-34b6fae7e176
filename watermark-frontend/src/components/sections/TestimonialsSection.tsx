'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Star,
  Quote,
  User,
  Calendar,
  Sparkles
} from 'lucide-react';

const testimonials = [
  {
    id: 1,
    name: "张小明",
    role: "电商运营",
    date: "2024年1月15日",
    rating: 5,
    content: "我经常需要在不同平台上重复使用许多图片，这个工具在加速流程方面帮助我极大。AI识别准确，处理速度很快！",
    avatar: "ZXM"
  },
  {
    id: 2,
    name: "李设计师",
    role: "UI/UX 设计师",
    date: "2024年1月20日",
    rating: 5,
    content: "这个去除水印工具令人难以置信的快速。我以为这个工具和最近几个月制作的其他工具一样质量差，但它在实际操作中表现出色。",
    avatar: "LSJ"
  },
  {
    id: 3,
    name: "王创业者",
    role: "创业公司CEO",
    date: "2024年1月25日",
    rating: 5,
    content: "过去几个月我一直使用这个网站去除图片上的标志，这大大节省了时间和精力，而不需要手动编辑。AI 快速检测版权标记，您只需一键即可去除它们！",
    avatar: "WCY"
  },
  {
    id: 4,
    name: "陈摄影师",
    role: "专业摄影师",
    date: "2024年2月1日",
    rating: 5,
    content: "作为摄影师，我对图片质量要求很高。这个工具不仅能完美去除水印，还能保持原始图片的质量，真的很棒！",
    avatar: "CSY"
  },
  {
    id: 5,
    name: "刘营销",
    role: "数字营销专家",
    date: "2024年2月5日",
    rating: 5,
    content: "团队每天需要处理大量的图片素材，这个工具大大提高了我们的工作效率。界面简洁，操作简单，效果出色。",
    avatar: "LYX"
  },
  {
    id: 6,
    name: "赵学生",
    role: "大学生",
    date: "2024年2月10日",
    rating: 5,
    content: "免费使用，无需注册，对学生党来说太友好了！处理效果很好，完全满足我的需求。强烈推荐！",
    avatar: "ZXS"
  }
];

export function TestimonialsSection() {
  return (
    <section className="py-20 bg-gradient-to-b from-slate-50 to-white">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center space-y-6 mb-16">
            <Badge className="bg-yellow-100 text-yellow-700 border-yellow-200 px-4 py-2">
              <Star className="w-4 h-4 mr-2" />
              用户好评
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900">
              用户对我们的评价
            </h2>
            <div className="flex items-center justify-center gap-2">
              <div className="flex items-center gap-1">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star key={star} className="w-6 h-6 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              <span className="text-2xl font-bold text-slate-900">4.9/5</span>
              <span className="text-slate-600">基于 1,200+ 条评论</span>
            </div>
          </div>

          {/* 评价网格 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card 
                key={testimonial.id}
                className={`p-6 hover:shadow-xl transition-all duration-500 hover:scale-105 border border-slate-200 ${
                  index % 3 === 0 ? 'lg:transform lg:translate-y-4' : 
                  index % 3 === 2 ? 'lg:transform lg:-translate-y-4' : ''
                }`}
              >
                <div className="space-y-4">
                  {/* 引用图标 */}
                  <div className="flex justify-between items-start">
                    <Quote className="w-8 h-8 text-blue-500 opacity-50" />
                    <div className="flex items-center gap-1">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star 
                          key={star} 
                          className={`w-4 h-4 ${
                            star <= testimonial.rating 
                              ? 'fill-yellow-400 text-yellow-400' 
                              : 'text-slate-300'
                          }`} 
                        />
                      ))}
                    </div>
                  </div>

                  {/* 评价内容 */}
                  <p className="text-slate-700 leading-relaxed">
                    "{testimonial.content}"
                  </p>

                  {/* 用户信息 */}
                  <div className="flex items-center gap-3 pt-4 border-t border-slate-100">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-semibold">
                      {testimonial.avatar}
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-slate-900">{testimonial.name}</h4>
                      <p className="text-sm text-slate-500">{testimonial.role}</p>
                    </div>
                    <div className="text-xs text-slate-400">
                      <Calendar className="w-3 h-3 inline mr-1" />
                      {testimonial.date}
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* 统计信息 */}
          <div className="mt-16 grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">50K+</div>
              <div className="text-slate-600">满意用户</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">1M+</div>
              <div className="text-slate-600">处理图片</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">99.5%</div>
              <div className="text-slate-600">成功率</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">24/7</div>
              <div className="text-slate-600">在线服务</div>
            </div>
          </div>

          {/* 信任标识 */}
          <div className="mt-16 text-center">
            <div className="inline-flex items-center gap-4 px-8 py-4 bg-white rounded-2xl shadow-lg border border-slate-200">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <Sparkles className="w-4 h-4 text-green-600" />
                </div>
                <span className="text-slate-700 font-medium">100% 安全</span>
              </div>
              <div className="w-px h-6 bg-slate-200"></div>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <Star className="w-4 h-4 text-blue-600" />
                </div>
                <span className="text-slate-700 font-medium">免费使用</span>
              </div>
              <div className="w-px h-6 bg-slate-200"></div>
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-purple-600" />
                </div>
                <span className="text-slate-700 font-medium">无需注册</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
