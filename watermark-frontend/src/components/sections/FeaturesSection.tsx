'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Brain, 
  Zap, 
  Shield, 
  Sparkles,
  Target,
  Clock,
  Palette,
  Download,
  ArrowRight,
  CheckCircle,
  Star,
  Users,
  Award
} from 'lucide-react';
import { cn } from '@/lib/utils';

const features = [
  {
    icon: Brain,
    title: 'AI 智能检测',
    description: '基于 YOLO 深度学习模型，精确识别各种类型的水印和不需要的元素',
    highlights: ['99.5% 检测准确率', '支持复杂水印', '多目标识别'],
    color: 'text-blue-500',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
  },
  {
    icon: Sparkles,
    title: 'LAMA 智能修复',
    description: '采用先进的 LAMA 算法，智能填充被去除的区域，保持图像自然效果',
    highlights: ['无损画质', '智能填充', '边缘平滑'],
    color: 'text-purple-500',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
  },
  {
    icon: Zap,
    title: '极速处理',
    description: '优化的算法和模型，大多数图片在 30 秒内完成处理',
    highlights: ['平均 <30s', '批量处理', '实时预览'],
    color: 'text-yellow-500',
    bgColor: 'bg-yellow-50',
    borderColor: 'border-yellow-200',
  },
  {
    icon: Shield,
    title: '隐私保护',
    description: '本地处理，图片不会上传到第三方服务器，确保您的隐私安全',
    highlights: ['本地处理', '数据安全', '无存储'],
    color: 'text-green-500',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
  },
];

const useCases = [
  {
    title: '电商产品图',
    description: '去除产品图片上的水印，用于商品展示',
    image: '/images/usecase-ecommerce.jpg',
    tags: ['电商', '产品', '营销'],
  },
  {
    title: '社交媒体',
    description: '清理社交媒体图片，提升内容质量',
    image: '/images/usecase-social.jpg',
    tags: ['社交', '内容', '分享'],
  },
  {
    title: '设计素材',
    description: '处理设计素材，获得干净的背景图',
    image: '/images/usecase-design.jpg',
    tags: ['设计', '素材', '创意'],
  },
];

const stats = [
  { icon: Users, value: '50K+', label: '用户信赖' },
  { icon: Target, value: '99.5%', label: '成功率' },
  { icon: Clock, value: '<30s', label: '平均处理时间' },
  { icon: Award, value: '4.9/5', label: '用户评分' },
];

export function FeaturesSection() {
  return (
    <section className="py-20 bg-gradient-to-b from-background to-muted/20">
      <div className="container mx-auto px-6">
        <div className="max-w-6xl mx-auto space-y-20">
          {/* 标题区域 */}
          <div className="text-center space-y-4 animate-in fade-in slide-in-from-bottom-4 duration-700">
            <Badge variant="secondary" className="px-4 py-2 text-sm font-medium">
              <Star className="w-4 h-4 mr-2" />
              核心功能
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold">
              <span className="text-gradient">AI 驱动</span>的智能水印去除
            </h2>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              结合最新的深度学习技术，为您提供专业级的图像处理体验
            </p>
          </div>

          {/* 核心功能 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <Card 
                  key={feature.title}
                  className={cn(
                    "p-6 hover:scale-105 transition-all duration-300 bg-card border border-border rounded-xl shadow-lg hover:shadow-xl animate-in fade-in slide-in-from-bottom-4",
                    `delay-${(index + 1) * 100}`
                  )}
                >
                  <div className="space-y-4">
                    <div className="flex items-center gap-4">
                      <div className={cn(
                        "w-12 h-12 rounded-lg flex items-center justify-center",
                        feature.bgColor
                      )}>
                        <Icon className={cn("w-6 h-6", feature.color)} />
                      </div>
                      <h3 className="text-xl font-semibold">{feature.title}</h3>
                    </div>
                    
                    <p className="text-muted-foreground leading-relaxed">
                      {feature.description}
                    </p>
                    
                    <div className="flex flex-wrap gap-2">
                      {feature.highlights.map((highlight) => (
                        <Badge 
                          key={highlight}
                          variant="outline" 
                          className={cn("text-xs", feature.borderColor)}
                        >
                          <CheckCircle className="w-3 h-3 mr-1" />
                          {highlight}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Card>
              );
            })}
          </div>

          {/* 统计数据 */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 animate-in fade-in slide-in-from-bottom-4 duration-700">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <Card 
                  key={stat.label}
                  className={cn(
                    "p-6 text-center hover:scale-105 transition-transform duration-200 bg-card border border-border rounded-xl shadow-lg hover:shadow-xl",
                    `delay-${(index + 1) * 100}`
                  )}
                >
                  <div className="space-y-2">
                    <div className="w-12 h-12 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                      <Icon className="w-6 h-6 text-primary" />
                    </div>
                    <div className="text-2xl font-bold text-primary">{stat.value}</div>
                    <div className="text-sm text-muted-foreground">{stat.label}</div>
                  </div>
                </Card>
              );
            })}
          </div>

          {/* 使用场景 */}
          <div className="space-y-12 animate-in fade-in slide-in-from-bottom-4 duration-700">
            <div className="text-center space-y-4">
              <h3 className="text-3xl font-bold">适用场景</h3>
              <p className="text-lg text-muted-foreground">
                无论是个人使用还是商业需求，都能找到合适的应用场景
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {useCases.map((useCase, index) => (
                <Card 
                  key={useCase.title}
                  className={cn(
                    "overflow-hidden hover:scale-105 transition-transform duration-200 bg-card border border-border rounded-xl shadow-lg hover:shadow-xl group",
                    `delay-${(index + 1) * 200}`
                  )}
                >
                  <div className="aspect-video bg-gradient-to-br from-primary/10 to-secondary/10 flex items-center justify-center">
                    <div className="text-center space-y-2">
                      <Palette className="w-12 h-12 mx-auto text-primary" />
                      <p className="text-sm text-muted-foreground">示例图片</p>
                    </div>
                  </div>
                  <div className="p-6 space-y-4">
                    <h4 className="text-lg font-semibold">{useCase.title}</h4>
                    <p className="text-muted-foreground text-sm">{useCase.description}</p>
                    <div className="flex flex-wrap gap-2">
                      {useCase.tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </div>

          {/* CTA 区域 */}
          <Card className="p-8 text-center bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20 animate-in fade-in slide-in-from-bottom-4 duration-700">
            <div className="space-y-6">
              <div className="space-y-2">
                <h3 className="text-2xl font-bold">准备好体验 AI 的强大能力了吗？</h3>
                <p className="text-muted-foreground">
                  立即上传您的图片，感受智能水印去除的神奇效果
                </p>
              </div>
              <Button size="lg" className="bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-105 transition-all duration-200">
                <Zap className="w-5 h-5 mr-2" />
                立即开始使用
                <ArrowRight className="w-5 h-5 ml-2" />
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </section>
  );
}
