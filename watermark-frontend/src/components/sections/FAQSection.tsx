'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  ChevronDown,
  ChevronUp,
  HelpCircle,
  CheckCircle,
  Shield,
  Zap,
  Users
} from 'lucide-react';

const faqs = [
  {
    id: 1,
    question: "如何去除图片中的水印？",
    answer: "使用我们的AI智能水印去除工具非常简单：1) 上传您的图片（支持拖拽或点击上传）；2) AI会自动检测并去除水印；3) 如需要，可使用手动画笔进行精细调整；4) 下载处理后的高清图片。整个过程通常在30秒内完成。"
  },
  {
    id: 2,
    question: "支持哪些文件类型？",
    answer: "我们支持所有主流图片格式，包括 JPG、JPEG、PNG、WebP、GIF 等。最大文件大小支持 50MB，能够处理高分辨率图片而不损失质量。"
  },
  {
    id: 3,
    question: "可以手动去除水印吗？",
    answer: "是的！除了AI自动处理外，我们还提供手动画笔工具。您可以精确标记需要去除的区域，特别适用于复杂水印或AI未能完全识别的部分。手动工具同样采用智能算法，确保处理效果自然。"
  },
  {
    id: 4,
    question: "如何在手机上使用？",
    answer: "我们的工具完全支持移动设备！您可以直接在手机浏览器中访问我们的网站，界面会自动适配移动端。支持触摸操作，包括拖拽上传、手动编辑等功能。"
  },
  {
    id: 5,
    question: "此工具收费吗？",
    answer: "我们提供免费版本，包含基础的AI水印去除功能。免费版每天可处理一定数量的图片。如需更多处理次数、批量处理或高级功能，可升级到专业版。"
  },
  {
    id: 6,
    question: "使用去除水印工具是否合法？",
    answer: "请注意版权法律！我们的工具仅应用于您拥有版权或有权编辑的图片。去除他人的版权水印可能涉及法律问题。建议仅用于去除您自己添加的水印或处理您有权使用的图片。"
  },
  {
    id: 7,
    question: "能同时处理多张图片吗？",
    answer: "目前免费版支持单张图片处理。专业版提供批量处理功能，可以同时上传多张图片进行批量水印去除，大大提高工作效率。"
  },
  {
    id: 8,
    question: "处理后的图片质量如何？",
    answer: "我们使用先进的AI算法，确保在去除水印的同时保持原始图片质量。在大多数情况下，处理后的图片质量与原图相同，甚至在某些情况下还能提升图片质量。"
  },
  {
    id: 9,
    question: "我的图片数据安全吗？",
    answer: "绝对安全！我们采用端到端加密传输，图片处理完成后会自动删除，不会存储在我们的服务器上。您的隐私和数据安全是我们的首要考虑。"
  },
  {
    id: 10,
    question: "如果AI无法完全去除水印怎么办？",
    answer: "如果AI自动处理效果不理想，您可以：1) 使用我们的手动画笔工具进行精细调整；2) 调整处理参数重新处理；3) 联系我们的客服获得人工协助。我们致力于为每位用户提供满意的处理效果。"
  }
];

export function FAQSection() {
  const [openItems, setOpenItems] = useState<number[]>([1]); // 默认展开第一个

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="max-w-4xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center space-y-6 mb-16">
            <Badge className="bg-green-100 text-green-700 border-green-200 px-4 py-2">
              <HelpCircle className="w-4 h-4 mr-2" />
              常见问题
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900">
              常见问题解答
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              解答关于在线去除水印的相关问题，帮助您更好地使用我们的服务
            </p>
          </div>

          {/* FAQ 列表 */}
          <div className="space-y-4">
            {faqs.map((faq) => (
              <Card 
                key={faq.id}
                className="overflow-hidden border border-slate-200 hover:border-blue-300 transition-all duration-300"
              >
                <button
                  onClick={() => toggleItem(faq.id)}
                  className="w-full p-6 text-left hover:bg-slate-50 transition-colors duration-200"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-slate-900 pr-4">
                      {faq.question}
                    </h3>
                    <div className="flex-shrink-0">
                      {openItems.includes(faq.id) ? (
                        <ChevronUp className="w-5 h-5 text-blue-600" />
                      ) : (
                        <ChevronDown className="w-5 h-5 text-slate-400" />
                      )}
                    </div>
                  </div>
                </button>
                
                {openItems.includes(faq.id) && (
                  <div className="px-6 pb-6">
                    <div className="pt-4 border-t border-slate-100">
                      <p className="text-slate-600 leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                )}
              </Card>
            ))}
          </div>

          {/* 底部帮助信息 */}
          <div className="mt-16 text-center">
            <Card className="p-8 bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
              <div className="space-y-4">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
                  <HelpCircle className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-slate-900">
                  还有其他问题？
                </h3>
                <p className="text-slate-600 max-w-md mx-auto">
                  如果您没有找到想要的答案，请随时联系我们的客服团队，我们将竭诚为您服务。
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center pt-4">
                  <div className="flex items-center gap-2 text-sm text-slate-600">
                    <CheckCircle className="w-4 h-4 text-green-500" />
                    <span>24/7 在线客服</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-slate-600">
                    <Zap className="w-4 h-4 text-blue-500" />
                    <span>快速响应</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-slate-600">
                    <Users className="w-4 h-4 text-purple-500" />
                    <span>专业团队</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>
    </section>
  );
}
