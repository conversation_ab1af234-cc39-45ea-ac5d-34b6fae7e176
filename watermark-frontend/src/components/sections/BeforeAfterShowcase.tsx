'use client';

import React, { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowRight,
  Sparkles,
  Zap,
  CheckCircle,
  Star
} from 'lucide-react';

const showcaseImages = [
  {
    id: 1,
    before: "/api/placeholder/400/300",
    after: "/api/placeholder/400/300",
    title: "复杂水印去除",
    description: "多层复杂水印，与图片颜色融合"
  },
  {
    id: 2,
    before: "/api/placeholder/400/300", 
    after: "/api/placeholder/400/300",
    title: "文字水印处理",
    description: "半透明文字水印精准识别"
  },
  {
    id: 3,
    before: "/api/placeholder/400/300",
    after: "/api/placeholder/400/300", 
    title: "Logo标志去除",
    description: "保持图片质量的同时完美去除"
  }
];

export function BeforeAfterShowcase() {
  const [activeImage, setActiveImage] = useState(0);

  return (
    <section className="py-20 bg-gradient-to-b from-white to-slate-50">
      <div className="container mx-auto px-6">
        <div className="max-w-7xl mx-auto">
          {/* 标题区域 */}
          <div className="text-center space-y-6 mb-16">
            <Badge className="bg-blue-100 text-blue-700 border-blue-200 px-4 py-2">
              <Sparkles className="w-4 h-4 mr-2" />
              AI 技术展示
            </Badge>
            <h2 className="text-4xl md:text-5xl font-bold text-slate-900">
              看看 AI 的神奇效果
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              我们的 AI 技术能够精准识别并去除各种类型的水印，同时保持图片的原始质量
            </p>
          </div>

          {/* 主要展示区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            {/* 左侧：对比图片 */}
            <div className="space-y-8">
              {/* 主要对比图 */}
              <div className="relative">
                <div className="bg-white rounded-2xl shadow-2xl p-8 border border-slate-200/50">
                  <div className="grid grid-cols-2 gap-6">
                    {/* 处理前 */}
                    <div className="space-y-4">
                      <div className="relative">
                        <div className="aspect-[4/3] bg-gradient-to-br from-slate-200 to-slate-300 rounded-xl overflow-hidden">
                          <div className="w-full h-full flex items-center justify-center">
                            <div className="text-center space-y-2">
                              <div className="w-16 h-16 bg-slate-400 rounded-lg mx-auto opacity-50"></div>
                              <p className="text-slate-500 text-sm">带水印图片</p>
                            </div>
                          </div>
                        </div>
                        <Badge className="absolute top-3 left-3 bg-red-100 text-red-700 border-red-200">
                          处理前
                        </Badge>
                      </div>
                    </div>

                    {/* 处理后 */}
                    <div className="space-y-4">
                      <div className="relative">
                        <div className="aspect-[4/3] bg-gradient-to-br from-green-100 to-blue-100 rounded-xl overflow-hidden">
                          <div className="w-full h-full flex items-center justify-center">
                            <div className="text-center space-y-2">
                              <div className="w-16 h-16 bg-green-500 rounded-lg mx-auto flex items-center justify-center">
                                <CheckCircle className="w-8 h-8 text-white" />
                              </div>
                              <p className="text-slate-700 text-sm font-medium">完美去除</p>
                            </div>
                          </div>
                        </div>
                        <Badge className="absolute top-3 left-3 bg-green-100 text-green-700 border-green-200">
                          处理后
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* 箭头指示 */}
                  <div className="flex justify-center mt-6">
                    <div className="flex items-center gap-3 px-4 py-2 bg-blue-50 rounded-full">
                      <Zap className="w-4 h-4 text-blue-600" />
                      <span className="text-sm font-medium text-blue-700">AI 智能处理</span>
                      <ArrowRight className="w-4 h-4 text-blue-600" />
                    </div>
                  </div>
                </div>

                {/* 装饰元素 */}
                <div className="absolute -top-4 -right-4 w-8 h-8 bg-blue-500 rounded-full animate-pulse"></div>
                <div className="absolute -bottom-4 -left-4 w-6 h-6 bg-purple-500 rounded-full animate-pulse delay-500"></div>
              </div>

              {/* 缩略图选择器 */}
              <div className="flex gap-4 justify-center">
                {showcaseImages.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setActiveImage(index)}
                    className={`relative w-20 h-16 rounded-lg overflow-hidden transition-all duration-300 ${
                      activeImage === index 
                        ? 'ring-2 ring-blue-500 scale-110' 
                        : 'hover:scale-105 opacity-70 hover:opacity-100'
                    }`}
                  >
                    <div className="w-full h-full bg-gradient-to-br from-slate-200 to-slate-300"></div>
                    {activeImage === index && (
                      <div className="absolute inset-0 bg-blue-500/20 flex items-center justify-center">
                        <CheckCircle className="w-4 h-4 text-blue-600" />
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* 右侧：特性说明 */}
            <div className="space-y-8">
              <div className="space-y-6">
                <h3 className="text-3xl font-bold text-slate-900">
                  {showcaseImages[activeImage].title}
                </h3>
                <p className="text-lg text-slate-600 leading-relaxed">
                  {showcaseImages[activeImage].description}。我们的 AI 技术能够：
                </p>
              </div>

              {/* 特性列表 */}
              <div className="space-y-4">
                {[
                  "精准识别各种类型的水印",
                  "保持图片原始质量不变",
                  "智能填充被去除的区域",
                  "处理复杂背景和纹理"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-6 h-6 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                    </div>
                    <span className="text-slate-700">{feature}</span>
                  </div>
                ))}
              </div>

              {/* 统计数据 */}
              <div className="grid grid-cols-3 gap-6 pt-6 border-t border-slate-200">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">99.5%</div>
                  <div className="text-sm text-slate-500">成功率</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">&lt;30s</div>
                  <div className="text-sm text-slate-500">处理时间</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">4.9★</div>
                  <div className="text-sm text-slate-500">用户评分</div>
                </div>
              </div>

              {/* CTA 按钮 */}
              <div className="pt-6">
                <Button 
                  size="lg" 
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105"
                >
                  <Zap className="w-5 h-5 mr-2" />
                  立即体验
                  <ArrowRight className="w-5 h-5 ml-2" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
