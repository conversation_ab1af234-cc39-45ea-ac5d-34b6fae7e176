'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  Share2, 
  RotateCcw, 
  ZoomIn, 
  ZoomOut,
  Move,
  Eye,
  EyeOff,
  Maximize2,
  Star,
  Clock,
  Target
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { TaskStatus } from '@/lib/types';

interface ImageComparisonProps {
  originalUrl: string;
  resultUrl: string;
  task: TaskStatus;
  onDownload?: () => void;
  onShare?: () => void;
  className?: string;
}

export function ImageComparison({ 
  originalUrl, 
  resultUrl, 
  task,
  onDownload,
  onShare,
  className 
}: ImageComparisonProps) {
  const [sliderPosition, setSliderPosition] = useState(50);
  const [isDragging, setIsDragging] = useState(false);
  const [showOverlay, setShowOverlay] = useState(true);
  const [zoom, setZoom] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  
  const containerRef = useRef<HTMLDivElement>(null);
  const sliderRef = useRef<HTMLDivElement>(null);

  // 处理滑块拖拽
  const handleMouseDown = useCallback((e: React.MouseEvent) => {
    setIsDragging(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e: MouseEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  const handleMouseUp = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isDragging, handleMouseMove, handleMouseUp]);

  // 处理触摸事件
  const handleTouchMove = useCallback((e: TouchEvent) => {
    if (!isDragging || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = e.touches[0].clientX - rect.left;
    const percentage = Math.max(0, Math.min(100, (x / rect.width) * 100));
    setSliderPosition(percentage);
  }, [isDragging]);

  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setIsDragging(true);
    e.preventDefault();
  }, []);

  const handleTouchEnd = useCallback(() => {
    setIsDragging(false);
  }, []);

  useEffect(() => {
    if (isDragging) {
      document.addEventListener('touchmove', handleTouchMove);
      document.addEventListener('touchend', handleTouchEnd);
      return () => {
        document.removeEventListener('touchmove', handleTouchMove);
        document.removeEventListener('touchend', handleTouchEnd);
      };
    }
  }, [isDragging, handleTouchMove, handleTouchEnd]);

  return (
    <div className={cn("space-y-6", className)}>
      {/* 对比图片区域 */}
      <Card className="overflow-hidden bg-card border border-border rounded-xl shadow-lg hover:shadow-xl transition-all duration-300">
        <div className="relative bg-gradient-to-br from-muted/30 to-muted/10">
          {/* 图片容器 */}
          <div 
            ref={containerRef}
            className={cn(
              "relative overflow-hidden cursor-col-resize select-none",
              isFullscreen ? "h-screen" : "aspect-video"
            )}
            style={{ transform: `scale(${zoom})`, transformOrigin: 'center' }}
          >
            {/* 原图 */}
            <div className="absolute inset-0">
              <img
                src={originalUrl}
                alt="原图"
                className="w-full h-full object-contain"
                draggable={false}
              />
              {showOverlay && (
                <div className="absolute top-4 left-4">
                  <Badge variant="secondary" className="bg-black/50 text-white">
                    原图
                  </Badge>
                </div>
              )}
            </div>
            
            {/* 处理后图片 */}
            <div 
              className="absolute inset-0 overflow-hidden"
              style={{ clipPath: `inset(0 ${100 - sliderPosition}% 0 0)` }}
            >
              <img
                src={resultUrl}
                alt="处理后"
                className="w-full h-full object-contain"
                draggable={false}
              />
              {showOverlay && (
                <div className="absolute top-4 right-4">
                  <Badge variant="default" className="bg-primary text-primary-foreground">
                    处理后
                  </Badge>
                </div>
              )}
            </div>
            
            {/* 分割线 */}
            <div
              ref={sliderRef}
              className="absolute top-0 bottom-0 w-1 bg-white shadow-lg cursor-col-resize z-10 group"
              style={{ left: `${sliderPosition}%` }}
              onMouseDown={handleMouseDown}
              onTouchStart={handleTouchStart}
            >
              {/* 分割线手柄 */}
              <div className="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-white rounded-full shadow-lg flex items-center justify-center group-hover:scale-110 transition-transform">
                <Move className="w-4 h-4 text-muted-foreground" />
              </div>
            </div>
          </div>
          
          {/* 控制栏 */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex items-center gap-2 bg-black/50 backdrop-blur-sm rounded-lg p-2">
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={() => setShowOverlay(!showOverlay)}
            >
              {showOverlay ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={() => setZoom(Math.max(0.5, zoom - 0.25))}
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={() => setZoom(Math.min(3, zoom + 0.25))}
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={() => setZoom(1)}
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="ghost"
              className="text-white hover:bg-white/20"
              onClick={() => setIsFullscreen(!isFullscreen)}
            >
              <Maximize2 className="w-4 h-4" />
            </Button>
          </div>
        </div>
      </Card>
      
      {/* 处理结果统计 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 text-center hover:scale-105 transition-transform duration-200">
          <div className="flex items-center justify-center mb-2">
            <Star className="w-5 h-5 text-primary mr-2" />
            <span className="text-2xl font-bold text-primary">
              {Math.round((task.detection_confidence || 0) * 100)}%
            </span>
          </div>
          <p className="text-sm text-muted-foreground">检测置信度</p>
        </Card>
        
        <Card className="p-4 text-center hover:scale-105 transition-transform duration-200">
          <div className="flex items-center justify-center mb-2">
            <Target className="w-5 h-5 text-success mr-2" />
            <span className="text-2xl font-bold text-success">
              {task.detection_info?.boxes?.length || 0}
            </span>
          </div>
          <p className="text-sm text-muted-foreground">检测区域</p>
        </Card>

        <Card className="p-4 text-center hover:scale-105 transition-transform duration-200">
          <div className="flex items-center justify-center mb-2">
            <Clock className="w-5 h-5 text-warning mr-2" />
            <span className="text-2xl font-bold text-warning">
              {task.processing_time ? Math.round(task.processing_time * 10) / 10 : 0}s
            </span>
          </div>
          <p className="text-sm text-muted-foreground">处理时间</p>
        </Card>

        <Card className="p-4 text-center hover:scale-105 transition-transform duration-200">
          <div className="flex items-center justify-center mb-2">
            <Badge variant="outline" className="text-lg font-bold px-3 py-1">
              AI
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">LAMA 模型</p>
        </Card>
      </div>
      
      {/* 操作按钮 */}
      <div className="flex flex-col sm:flex-row gap-4 justify-center">
        <Button 
          size="lg" 
          className="bg-primary text-primary-foreground hover:bg-primary/90 flex-1 sm:flex-none hover:scale-105 transition-all duration-200"
          onClick={onDownload}
        >
          <Download className="w-5 h-5 mr-2" />
          下载处理后图片
        </Button>
        <Button 
          size="lg" 
          variant="outline" 
          className="bg-secondary text-secondary-foreground hover:bg-secondary/80 border border-border flex-1 sm:flex-none hover:scale-105 transition-all duration-200"
          onClick={onShare}
        >
          <Share2 className="w-5 h-5 mr-2" />
          分享结果
        </Button>
      </div>
      
      {/* 使用提示 */}
      <Card className="p-4 bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20">
        <div className="text-center space-y-2">
          <p className="text-sm font-medium">💡 使用提示</p>
          <p className="text-xs text-muted-foreground">
            拖拽中间的分割线可以对比处理前后的效果，使用底部控制栏可以缩放和切换显示模式
          </p>
        </div>
      </Card>
    </div>
  );
}
