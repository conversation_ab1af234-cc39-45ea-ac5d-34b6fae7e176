'use client';

import React, { useState, useCallback, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Upload, 
  Image as ImageIcon, 
  Link, 
  X, 
  Check,
  AlertCircle,
  Loader2,
  Camera,
  FileImage
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface ModernImageUploaderProps {
  onImageSelect: (file: File | string) => void;
  onImagePreview?: (url: string) => void;
  disabled?: boolean;
  className?: string;
}

export function ModernImageUploader({ 
  onImageSelect, 
  onImagePreview, 
  disabled = false,
  className 
}: ModernImageUploaderProps) {
  const [dragActive, setDragActive] = useState(false);
  const [uploadMode, setUploadMode] = useState<'file' | 'url'>('file');
  const [urlInput, setUrlInput] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [urlValid, setUrlValid] = useState<boolean | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [isUploading, setIsUploading] = useState(false);
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);

  // 文件验证
  const validateFile = (file: File) => {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif'];
    
    if (!allowedTypes.includes(file.type)) {
      throw new Error('不支持的文件格式。请选择 JPG、PNG、WebP 或 GIF 格式的图片。');
    }
    
    if (file.size > maxSize) {
      throw new Error('文件过大。请选择小于 50MB 的图片。');
    }
  };

  // URL验证
  const validateUrl = useCallback(async (url: string) => {
    if (!url) {
      setUrlValid(null);
      return;
    }
    
    setIsValidating(true);
    try {
      const response = await fetch(url, { method: 'HEAD' });
      const contentType = response.headers.get('content-type');
      const isImage = contentType?.startsWith('image/');
      setUrlValid(isImage || false);
    } catch {
      setUrlValid(false);
    } finally {
      setIsValidating(false);
    }
  }, []);

  // 处理文件选择
  const handleFileSelect = useCallback(async (file: File) => {
    try {
      validateFile(file);
      setIsUploading(true);
      setUploadProgress(0);
      
      // 模拟上传进度
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return 90;
          }
          return prev + 10;
        });
      }, 100);
      
      // 创建预览
      const reader = new FileReader();
      reader.onload = (e) => {
        const url = e.target?.result as string;
        setPreviewUrl(url);
        onImagePreview?.(url);
        setUploadProgress(100);
        setTimeout(() => {
          setIsUploading(false);
          clearInterval(progressInterval);
        }, 500);
      };
      reader.readAsDataURL(file);
      
      onImageSelect(file);
    } catch (error) {
      setIsUploading(false);
      setUploadProgress(0);
      console.error('文件选择失败:', error);
    }
  }, [onImageSelect, onImagePreview]);

  // 处理拖拽
  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (disabled) return;
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [disabled, handleFileSelect]);

  // 处理URL输入
  const handleUrlSubmit = useCallback(() => {
    if (urlValid && urlInput) {
      setPreviewUrl(urlInput);
      onImagePreview?.(urlInput);
      onImageSelect(urlInput);
    }
  }, [urlValid, urlInput, onImageSelect, onImagePreview]);

  // 清除选择
  const handleClear = useCallback(() => {
    setPreviewUrl(null);
    setUrlInput('');
    setUrlValid(null);
    setUploadProgress(0);
    setIsUploading(false);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, []);

  return (
    <div className={cn("space-y-6", className)}>
      {/* 模式切换 */}
      <div className="flex justify-center">
        <div className="flex bg-secondary rounded-lg p-1">
          <Button
            variant={uploadMode === 'file' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setUploadMode('file')}
            className="rounded-md"
          >
            <FileImage className="w-4 h-4 mr-2" />
            上传文件
          </Button>
          <Button
            variant={uploadMode === 'url' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setUploadMode('url')}
            className="rounded-md"
          >
            <Link className="w-4 h-4 mr-2" />
            图片链接
          </Button>
        </div>
      </div>

      {uploadMode === 'file' ? (
        /* 文件上传区域 */
        <Card className="relative overflow-hidden">
          <div
            ref={dropZoneRef}
            className={cn(
              "relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-300",
              dragActive 
                ? "border-primary bg-primary/5 scale-105" 
                : "border-border hover:border-primary/50 hover:bg-accent/50",
              disabled && "opacity-50 cursor-not-allowed",
              "hover:scale-105 transition-transform duration-200"
            )}
            onDragEnter={handleDrag}
            onDragLeave={handleDrag}
            onDragOver={handleDrag}
            onDrop={handleDrop}
          >
            {isUploading ? (
              <div className="space-y-4 animate-in zoom-in-50 duration-300">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <Loader2 className="w-8 h-8 text-primary animate-spin" />
                </div>
                <div className="space-y-2">
                  <p className="text-lg font-medium">上传中...</p>
                  <div className="w-full bg-secondary rounded-full h-2">
                    <div 
                      className="bg-primary h-2 rounded-full transition-all duration-300"
                      style={{ width: `${uploadProgress}%` }}
                    />
                  </div>
                  <p className="text-sm text-muted-foreground">{uploadProgress}%</p>
                </div>
              </div>
            ) : previewUrl ? (
              <div className="space-y-4 animate-in zoom-in-50 duration-300">
                <div className="relative w-32 h-32 mx-auto rounded-lg overflow-hidden">
                  <img 
                    src={previewUrl} 
                    alt="预览" 
                    className="w-full h-full object-cover"
                  />
                  <Button
                    size="sm"
                    variant="destructive"
                    className="absolute top-2 right-2 w-6 h-6 p-0"
                    onClick={handleClear}
                  >
                    <X className="w-3 h-3" />
                  </Button>
                </div>
                <div className="flex items-center justify-center gap-2">
                  <Check className="w-5 h-5 text-success" />
                  <span className="text-sm font-medium text-success">图片已选择</span>
                </div>
              </div>
            ) : (
              <div className="space-y-4 animate-in fade-in slide-in-from-bottom-4 duration-500">
                <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                  <Upload className="w-8 h-8 text-primary" />
                </div>
                <div className="space-y-2">
                  <p className="text-lg font-medium">拖拽图片到这里</p>
                  <p className="text-sm text-muted-foreground">
                    或者 
                    <Button 
                      variant="link" 
                      className="p-0 h-auto text-primary"
                      onClick={() => fileInputRef.current?.click()}
                    >
                      点击选择文件
                    </Button>
                  </p>
                </div>
                <div className="flex flex-wrap justify-center gap-2">
                  <Badge variant="secondary">JPG</Badge>
                  <Badge variant="secondary">PNG</Badge>
                  <Badge variant="secondary">WebP</Badge>
                  <Badge variant="secondary">GIF</Badge>
                </div>
              </div>
            )}
            
            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) handleFileSelect(file);
              }}
              className="hidden"
              disabled={disabled}
            />
          </div>
        </Card>
      ) : (
        /* URL输入区域 */
        <Card className="p-6 space-y-4 animate-in fade-in slide-in-from-bottom-4 duration-500">
          <div className="space-y-2">
            <label className="text-sm font-medium">图片链接</label>
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Input
                  placeholder="请输入图片URL地址..."
                  value={urlInput}
                  onChange={(e) => {
                    setUrlInput(e.target.value);
                    validateUrl(e.target.value);
                  }}
                  className="pr-10"
                  disabled={disabled}
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  {isValidating ? (
                    <Loader2 className="w-4 h-4 animate-spin text-muted-foreground" />
                  ) : urlValid === true ? (
                    <Check className="w-4 h-4 text-success" />
                  ) : urlValid === false ? (
                    <AlertCircle className="w-4 h-4 text-destructive" />
                  ) : null}
                </div>
              </div>
              <Button 
                onClick={handleUrlSubmit}
                disabled={!urlValid || disabled}
                className="btn-primary"
              >
                确认
              </Button>
            </div>
            {urlValid === false && (
              <p className="text-sm text-destructive">无效的图片链接</p>
            )}
          </div>
          
          {previewUrl && (
            <div className="space-y-4 animate-in zoom-in-50 duration-300">
              <div className="relative w-full max-w-md mx-auto rounded-lg overflow-hidden">
                <img 
                  src={previewUrl} 
                  alt="预览" 
                  className="w-full h-auto object-cover"
                />
                <Button
                  size="sm"
                  variant="destructive"
                  className="absolute top-2 right-2 w-6 h-6 p-0"
                  onClick={handleClear}
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
}
