/**
 * 主布局组件
 */

'use client';

import React from 'react';
import { Header } from './Header';
import { Footer } from './Footer';
import { cn } from '@/lib/utils';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
  showFooter?: boolean;
}

export function MainLayout({ 
  children, 
  className,
  showFooter = true 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className={cn(
        "flex-1   py-6 mx-auto",
        className
      )}>
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
}

/**
 * 全屏布局组件（不包含容器限制）
 */
export function FullScreenLayout({ 
  children, 
  className,
  showFooter = true 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className={cn(
        "flex-1",
        className
      )}>
        {children}
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
}

/**
 * 居中布局组件
 */
interface CenteredLayoutProps extends MainLayoutProps {
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export function CenteredLayout({ 
  children, 
  className,
  maxWidth = 'lg',
  showFooter = true 
}: CenteredLayoutProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md', 
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      
      <main className="flex-1 flex items-center justify-center p-6">
        <div className={cn(
          "w-full",
          maxWidthClasses[maxWidth],
          className
        )}>
          {children}
        </div>
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
}
