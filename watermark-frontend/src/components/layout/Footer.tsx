/**
 * 页面底部组件
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Github, 
  Mail, 
  ExternalLink,
  Shield,
  Zap,
  Brain
} from 'lucide-react';

export function Footer() {
  return (
    <footer className="border-t bg-background">
      <div className="  mx-auto py-8">
        {/* 主要内容区 */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 产品介绍 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">智能水印去除</h3>
            <p className="text-sm text-muted-foreground">
              基于先进AI技术的自动水印检测和去除系统，为您提供专业级的图像处理体验。
            </p>
            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="text-xs">
                <Brain className="h-3 w-3 mr-1" />
                AI驱动
              </Badge>
              <Badge variant="secondary" className="text-xs">
                <Zap className="h-3 w-3 mr-1" />
                高效处理
              </Badge>
              <Badge variant="secondary" className="text-xs">
                <Shield className="h-3 w-3 mr-1" />
                安全可靠
              </Badge>
            </div> 
          </div>

          {/* 功能特性 */}
          <div className="space-y-4">
            <h4 className="font-semibold">核心功能</h4>
            <ul className="space-y-2 text-sm text-muted-foreground">
              <li>• 智能水印检测</li>
              <li>• 高质量图像修复</li>
              <li>• 批量处理支持</li>
              <li>• 实时处理进度</li>
              <li>• 多格式支持</li>
            </ul>
          </div>

          {/* 技术支持 */}
          <div className="space-y-4">
            <h4 className="font-semibold">技术支持</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link 
                  href="/docs" 
                  className="text-muted-foreground hover:text-primary flex items-center"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  使用文档
                </Link>
              </li>
              <li>
                <Link 
                  href="/api" 
                  className="text-muted-foreground hover:text-primary flex items-center"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  API 文档
                </Link>
              </li>
              <li>
                <Link 
                  href="/faq" 
                  className="text-muted-foreground hover:text-primary flex items-center"
                >
                  <ExternalLink className="h-3 w-3 mr-1" />
                  常见问题
                </Link>
              </li>
            </ul>
          </div>

          {/* 联系方式 */}
          <div className="space-y-4">
            <h4 className="font-semibold">联系我们</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <a 
                  href="https://github.com" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-muted-foreground hover:text-primary flex items-center"
                >
                  <Github className="h-3 w-3 mr-1" />
                  GitHub
                </a>
              </li>
              <li>
                <a 
                  href="mailto:<EMAIL>"
                  className="text-muted-foreground hover:text-primary flex items-center"
                >
                  <Mail className="h-3 w-3 mr-1" />
                  邮件支持
                </a>
              </li>
            </ul>
          </div>
        </div>

        <Separator className="my-6" />

        {/* 底部信息 */}
        <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
          <div className="text-sm text-muted-foreground">
            © 2024 智能水印去除工具. 保留所有权利.
          </div>
          <div className="flex items-center space-x-4 text-sm text-muted-foreground">
            <Link href="/privacy" className="hover:text-primary">
              隐私政策
            </Link>
            <Link href="/terms" className="hover:text-primary">
              服务条款
            </Link>
            <span>版本 1.0.0</span>
          </div>
        </div>
      </div>
    </footer>
  );
}
