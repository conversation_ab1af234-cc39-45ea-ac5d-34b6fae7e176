/**
 * 页面头部组件
 */

'use client';

import React from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Droplets, 
  Settings, 
  History, 
  Github,
  Heart
} from 'lucide-react';
import { useCurrentTask } from '@/contexts/AppContext';

export function Header() {
  const { isProcessing } = useCurrentTask();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className=" mx-auto flex h-16 items-center justify-between">
        {/* Logo 和标题 */}
        <Link href="/" className="flex items-center space-x-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
            <Droplets className="h-5 w-5 text-primary-foreground" />
          </div>
          <div className="flex flex-col">
            <span className="text-lg font-bold">智能水印去除</span>
            <span className="text-xs text-muted-foreground">AI-Powered Watermark Removal</span>
          </div>
        </Link>

        {/* 导航菜单 */}
        <nav className="hidden md:flex items-center space-x-6">
          <Link 
            href="/" 
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            首页
          </Link>
          <Link 
            href="/history" 
            className="text-sm font-medium transition-colors hover:text-primary flex items-center space-x-1"
          >
            <History className="h-4 w-4" />
            <span>历史记录</span>
          </Link>
          <Link 
            href="/settings" 
            className="text-sm font-medium transition-colors hover:text-primary flex items-center space-x-1"
          >
            <Settings className="h-4 w-4" />
            <span>设置</span>
          </Link>
        </nav>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-4">
          {/* 处理状态指示器 */}
          {isProcessing && (
            <Badge variant="secondary" className="animate-pulse">
              处理中...
            </Badge>
          )}

          {/* GitHub 链接 */}
          <Button variant="ghost" size="sm" asChild>
            <a 
              href="https://github.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="flex items-center space-x-1"
            >
              <Github className="h-4 w-4" />
              <span className="hidden sm:inline">GitHub</span>
            </a>
          </Button>

          {/* 支持按钮 */}
          <Button variant="ghost" size="sm" className="flex items-center space-x-1">
            <Heart className="h-4 w-4" />
            <span className="hidden sm:inline">支持</span>
          </Button>
        </div>
      </div>
    </header>
  );
}
