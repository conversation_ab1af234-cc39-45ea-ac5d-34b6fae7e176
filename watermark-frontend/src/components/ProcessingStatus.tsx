/**
 * 处理状态和进度显示组件
 */

'use client';

import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Loader2, 
  CheckCircle, 
  XCircle, 
  Clock,
  Zap,
  Eye,
  X,
  AlertTriangle
} from 'lucide-react';
import { TaskStatus, TaskStatusType } from '@/lib/types';
import { formatDuration, formatDateTime } from '@/lib/utils';
import { cn } from '@/lib/utils';

interface ProcessingStatusProps {
  task?: TaskStatus;
  error?: string;
  onCancel?: () => void;
  onRetry?: () => void;
  className?: string;
}

export function ProcessingStatus({
  task,
  error,
  onCancel,
  onRetry,
  className
}: ProcessingStatusProps) {
  if (!task) {
    return null;
  }

  const getStatusIcon = (status: TaskStatusType) => {
    switch (status) {
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <XCircle className="h-5 w-5 text-red-500" />;
      case 'cancelled':
        return <X className="h-5 w-5 text-gray-500" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-orange-500" />;
    }
  };

  const getStatusColor = (status: TaskStatusType) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'processing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'cancelled':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-orange-100 text-orange-800 border-orange-200';
    }
  };

  const getStatusText = (status: TaskStatusType) => {
    switch (status) {
      case 'pending':
        return '等待处理';
      case 'processing':
        return '正在处理';
      case 'completed':
        return '处理完成';
      case 'failed':
        return '处理失败';
      case 'cancelled':
        return '已取消';
      default:
        return '未知状态';
    }
  };

  const isProcessing = task.status === 'pending' || task.status === 'processing';
  const isCompleted = task.status === 'completed';
  const isFailed = task.status === 'failed';

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getStatusIcon(task.status)}
            <span>处理状态</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Badge 
              variant="outline" 
              className={getStatusColor(task.status)}
            >
              {getStatusText(task.status)}
            </Badge>
            
            {isProcessing && onCancel && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onCancel}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </Button>
            )}
          </div>
        </CardTitle>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* 进度条 */}
        {isProcessing && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>处理进度</span>
              <span>{task.progress}%</span>
            </div>
            <Progress value={task.progress} className="w-full" />
          </div>
        )}

        {/* 状态消息 */}
        <div className="space-y-2">
          <p className="text-sm font-medium">状态信息</p>
          <p className="text-sm text-muted-foreground">{task.message}</p>
        </div>

        {/* 错误信息 */}
        {(isFailed && task.error) || error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {task.error || error || '处理过程中发生未知错误'}
            </AlertDescription>
          </Alert>
        )}

        {/* 处理详情 */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <p className="font-medium">任务ID</p>
            <p className="text-muted-foreground font-mono text-xs">
              {task.task_id}
            </p>
          </div>
          
          <div className="space-y-1">
            <p className="font-medium">创建时间</p>
            <p className="text-muted-foreground">
              {formatDateTime(task.created_at)}
            </p>
          </div>

          {task.completed_at && (
            <div className="space-y-1">
              <p className="font-medium">完成时间</p>
              <p className="text-muted-foreground">
                {formatDateTime(task.completed_at)}
              </p>
            </div>
          )}

          {task.processing_time && (
            <div className="space-y-1">
              <p className="font-medium flex items-center">
                <Zap className="h-3 w-3 mr-1" />
                处理时长
              </p>
              <p className="text-muted-foreground">
                {formatDuration(task.processing_time)}
              </p>
            </div>
          )}
        </div>

        {/* 检测信息 */}
        {task.detection_info && (
          <div className="space-y-2 p-3 bg-muted/50 rounded-lg">
            <p className="text-sm font-medium flex items-center">
              <Eye className="h-4 w-4 mr-1" />
              检测结果
            </p>
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div>
                <span className="text-muted-foreground">检测到水印:</span>
                <span className="ml-1 font-medium">
                  {task.watermark_detected ? '是' : '否'}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">置信度:</span>
                <span className="ml-1 font-medium">
                  {Math.round((task.detection_confidence || 0) * 100) / 100}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">检测框数量:</span>
                <span className="ml-1 font-medium">
                  {task.detection_info.boxes.length}
                </span>
              </div>
              <div>
                <span className="text-muted-foreground">检测时间:</span>
                <span className="ml-1 font-medium">
                  {Math.round(task.detection_info.detection_time * 100) / 100}s
                </span>
              </div>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {isFailed && onRetry && (
          <div className="flex justify-end">
            <Button onClick={onRetry} variant="outline" size="sm">
              重试
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * 简化版处理状态组件
 */
interface SimpleProcessingStatusProps {
  isProcessing: boolean;
  progress: number;
  message?: string;
  className?: string;
}

export function SimpleProcessingStatus({
  isProcessing,
  progress,
  message,
  className
}: SimpleProcessingStatusProps) {
  if (!isProcessing) {
    return null;
  }

  return (
    <div className={cn("space-y-2", className)}>
      <div className="flex items-center space-x-2">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm font-medium">
          {message || '正在处理...'}
        </span>
      </div>
      <Progress value={progress} className="w-full" />
      <p className="text-xs text-muted-foreground text-right">
        {progress}%
      </p>
    </div>
  );
}
