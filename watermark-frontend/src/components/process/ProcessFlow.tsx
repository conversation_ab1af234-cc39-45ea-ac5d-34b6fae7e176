'use client';

import React from 'react';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  Brain, 
  Wand2, 
  Download,
  CheckCircle,
  Clock,
  Loader2,
  AlertTriangle,
  Zap,
  Eye,
  Settings
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { TaskStatus } from '@/lib/types';

interface ProcessFlowProps {
  currentStep: number;
  task?: TaskStatus;
  isProcessing: boolean;
  error?: string | { message: string };
  onRetry?: () => void;
  className?: string;
}

const steps = [
  {
    id: 1,
    title: '上传图片',
    description: '选择需要去除水印的图片',
    icon: Upload,
    color: 'text-blue-500',
    bgColor: 'bg-blue-50',
  },
  {
    id: 2,
    title: '参数配置',
    description: '调整检测和处理参数',
    icon: Settings,
    color: 'text-purple-500',
    bgColor: 'bg-purple-50',
  },
  {
    id: 3,
    title: 'AI 分析',
    description: '智能检测水印位置',
    icon: Brain,
    color: 'text-green-500',
    bgColor: 'bg-green-50',
  },
  {
    id: 4,
    title: '智能修复',
    description: '使用 LAMA 模型去除水印',
    icon: Wand2,
    color: 'text-orange-500',
    bgColor: 'bg-orange-50',
  },
  {
    id: 5,
    title: '完成下载',
    description: '查看结果并下载图片',
    icon: Download,
    color: 'text-indigo-500',
    bgColor: 'bg-indigo-50',
  },
];

export function ProcessFlow({ 
  currentStep, 
  task, 
  isProcessing, 
  error,
  onRetry,
  className 
}: ProcessFlowProps) {
  const getStepStatus = (stepId: number) => {
    if (error && stepId === currentStep) return 'error';
    if (stepId < currentStep) return 'completed';
    if (stepId === currentStep) {
      if (isProcessing) return 'processing';
      return 'current';
    }
    return 'pending';
  };

  const getStepProgress = () => {
    if (!task || !isProcessing) return 0;
    
    switch (currentStep) {
      case 3: // AI 分析
        return task.progress || 0;
      case 4: // 智能修复
        return task.progress || 0;
      default:
        return 0;
    }
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* 步骤指示器 */}
      <div className="relative">
        <div className="flex justify-between items-center">
          {steps.map((step, index) => {
            const status = getStepStatus(step.id);
            const Icon = step.icon;
            
            return (
              <div key={step.id} className="flex flex-col items-center relative z-10">
                {/* 步骤圆圈 */}
                <div
                  className={cn(
                    "w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300",
                    status === 'completed' && "bg-primary text-primary-foreground shadow-lg",
                    status === 'current' && "bg-primary/10 text-primary border-2 border-primary animate-pulse",
                    status === 'processing' && "bg-primary text-primary-foreground animate-pulse",
                    status === 'error' && "bg-destructive text-destructive-foreground",
                    status === 'pending' && "bg-muted text-muted-foreground"
                  )}
                >
                  {status === 'completed' ? (
                    <CheckCircle className="w-6 h-6" />
                  ) : status === 'processing' ? (
                    <Loader2 className="w-6 h-6 animate-spin" />
                  ) : status === 'error' ? (
                    <AlertTriangle className="w-6 h-6" />
                  ) : (
                    <Icon className="w-6 h-6" />
                  )}
                </div>
                
                {/* 步骤标题 */}
                <div className="mt-3 text-center">
                  <p className={cn(
                    "text-sm font-medium",
                    status === 'current' && "text-primary",
                    status === 'completed' && "text-foreground",
                    status === 'error' && "text-destructive",
                    status === 'pending' && "text-muted-foreground"
                  )}>
                    {step.title}
                  </p>
                  <p className="text-xs text-muted-foreground mt-1 max-w-20">
                    {step.description}
                  </p>
                </div>
                
                {/* 连接线 */}
                {index < steps.length - 1 && (
                  <div
                    className={cn(
                      "absolute top-6 left-12 w-full h-0.5 transition-all duration-500",
                      step.id < currentStep ? "bg-primary" : "bg-border"
                    )}
                    style={{ width: 'calc(100vw / 5 - 3rem)' }}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>
      
      {/* 当前步骤详情 */}
      <Card className="p-6 animate-in fade-in slide-in-from-bottom-4 duration-500">
        {error ? (
          /* 错误状态 */
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-destructive/10 rounded-full flex items-center justify-center">
              <AlertTriangle className="w-8 h-8 text-destructive" />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-destructive mb-2">处理失败</h3>
              <p className="text-muted-foreground">{typeof error === 'string' ? error : error?.message}</p>
            </div>
            {onRetry && (
              <Button onClick={onRetry} className="bg-primary text-primary-foreground hover:bg-primary/90">
                重试处理
              </Button>
            )}
          </div>
        ) : isProcessing ? (
          /* 处理中状态 */
          <div className="space-y-6">
            <div className="text-center">
              <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center mb-4">
                <Loader2 className="w-8 h-8 text-primary animate-spin" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {steps[currentStep - 1]?.title}
              </h3>
              <p className="text-muted-foreground">
                {task?.message || steps[currentStep - 1]?.description}
              </p>
            </div>
            
            {/* 进度条 */}
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>处理进度</span>
                <span>{getStepProgress()}%</span>
              </div>
              <Progress value={getStepProgress()} className="h-2" />
            </div>
            
            {/* 处理详情 */}
            {task && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                {task.detection_confidence !== undefined && (
                  <div className="flex items-center gap-2">
                    <Eye className="w-4 h-4 text-muted-foreground" />
                    <span>检测置信度: {Math.round((task.detection_confidence || 0) * 100)}%</span>
                  </div>
                )}
                {task.processing_time !== undefined && (
                  <div className="flex items-center gap-2">
                    <Clock className="w-4 h-4 text-muted-foreground" />
                    <span>已用时间: {task.processing_time?.toFixed(1)}s</span>
                  </div>
                )}
              </div>
            )}
          </div>
        ) : (
          /* 等待状态 */
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center">
              {React.createElement(steps[currentStep - 1]?.icon || Upload, {
                className: "w-8 h-8 text-muted-foreground"
              })}
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-2">
                {steps[currentStep - 1]?.title}
              </h3>
              <p className="text-muted-foreground">
                {steps[currentStep - 1]?.description}
              </p>
            </div>
          </div>
        )}
      </Card>
      
      {/* 技术亮点 */}
      {(currentStep === 3 || currentStep === 4) && (
        <Card className="p-4 bg-gradient-to-r from-primary/5 to-secondary/5 border-primary/20 animate-in fade-in slide-in-from-bottom-4 duration-500 delay-200">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
              <Zap className="w-5 h-5 text-primary" />
            </div>
            <div>
              <h4 className="font-medium text-sm">AI 技术优势</h4>
              <p className="text-xs text-muted-foreground">
                {currentStep === 3 
                  ? "使用 YOLO 深度学习模型精确检测水印位置"
                  : "采用 LAMA 先进算法智能修复图像内容"
                }
              </p>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
}
