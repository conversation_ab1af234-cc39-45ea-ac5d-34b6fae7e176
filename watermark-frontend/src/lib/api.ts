/**
 * 水印去除 API 客户端
 */

import {
  WatermarkRemovalRequest,
  WatermarkRemovalResponse,
  BatchWatermarkRemovalRequest,
  TaskStatus,
  BatchStatus,
  HealthStatus,
  WatermarkApiError,
  API_ENDPOINTS,
} from './types';

// API 基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000';

/**
 * API 客户端类
 */
export class WatermarkApiClient {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders: Record<string, string> = {};

    // 只有在不是 FormData 时才设置 Content-Type
    if (!(options.body instanceof FormData)) {
      defaultHeaders['Content-Type'] = 'application/json';
    }

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);

      if (!response.ok) {
        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        let errorDetails;

        try {
          const errorData = await response.json();
          errorMessage = errorData.detail || errorData.message || errorData.error?.message || errorMessage;
          errorDetails = errorData;
        } catch {
          // 如果无法解析错误响应，使用默认错误消息
        }

        throw new WatermarkApiError(
          errorMessage,
          response.status,
          response.status.toString(),
          errorDetails
        );
      }

      return await response.json();
    } catch (error) {
      if (error instanceof WatermarkApiError) {
        throw error;
      }

      // 网络错误或其他错误
      throw new WatermarkApiError(
        error instanceof Error ? error.message : '网络请求失败',
        0,
        'NETWORK_ERROR',
        error
      );
    }
  }

  /**
   * 同步水印去除
   */
  async removeWatermarkSync(
    request: WatermarkRemovalRequest
  ): Promise<WatermarkRemovalResponse> {
    return this.request<WatermarkRemovalResponse>(
      API_ENDPOINTS.REMOVE_WATERMARK_SYNC,
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
  }

  /**
   * 异步水印去除
   */
  async removeWatermarkAsync(
    request: WatermarkRemovalRequest
  ): Promise<WatermarkRemovalResponse> {
    return this.request<WatermarkRemovalResponse>(
      API_ENDPOINTS.REMOVE_WATERMARK,
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
  }

  /**
   * 批量水印去除
   */
  async removeWatermarkBatch(
    request: BatchWatermarkRemovalRequest
  ): Promise<{ success: boolean; message: string; batch_id: string }> {
    return this.request(API_ENDPOINTS.REMOVE_WATERMARK_BATCH, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  /**
   * 查询任务状态
   */
  async getTaskStatus(taskId: string): Promise<TaskStatus> {
    return this.request<TaskStatus>(`${API_ENDPOINTS.TASK_STATUS}/${taskId}`);
  }

  /**
   * 查询批量任务状态
   */
  async getBatchStatus(batchId: string): Promise<BatchStatus> {
    return this.request<BatchStatus>(`${API_ENDPOINTS.BATCH_STATUS}/${batchId}`);
  }

  /**
   * 健康检查
   */
  async getHealth(): Promise<HealthStatus> {
    return this.request<HealthStatus>(API_ENDPOINTS.HEALTH);
  }

  /**
   * 上传文件
   */
  async uploadFile(file: File): Promise<{ success: boolean; file_url: string; filename: string; size: number }> {
    const formData = new FormData();
    formData.append('file', file);

    return this.request('/upload', {
      method: 'POST',
      headers: {}, // 不设置 Content-Type，让浏览器自动设置
      body: formData,
    });
  }

  /**
   * 轮询任务状态直到完成
   */
  async pollTaskStatus(
    taskId: string,
    onProgress?: (task: TaskStatus) => void,
    interval: number = 1000,
    maxAttempts: number = 300 // 5分钟超时
  ): Promise<TaskStatus> {
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const task = await this.getTaskStatus(taskId);
          
          onProgress?.(task);

          if (task.status === 'completed') {
            resolve(task);
            return;
          }

          if (task.status === 'failed' || task.status === 'cancelled') {
            reject(new WatermarkApiError(
              task.error || `任务${task.status}`,
              0,
              task.status.toUpperCase()
            ));
            return;
          }

          if (attempts >= maxAttempts) {
            reject(new WatermarkApiError(
              '任务超时',
              0,
              'TIMEOUT'
            ));
            return;
          }

          // 继续轮询
          setTimeout(poll, interval);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }

  /**
   * 轮询批量任务状态直到完成
   */
  async pollBatchStatus(
    batchId: string,
    onProgress?: (batch: BatchStatus) => void,
    interval: number = 2000,
    maxAttempts: number = 300
  ): Promise<BatchStatus> {
    let attempts = 0;

    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++;
          const batch = await this.getBatchStatus(batchId);
          
          onProgress?.(batch);

          if (batch.status === 'completed' || batch.status === 'partial_completed') {
            resolve(batch);
            return;
          }

          if (batch.status === 'failed') {
            reject(new WatermarkApiError(
              '批量任务失败',
              0,
              'BATCH_FAILED'
            ));
            return;
          }

          if (attempts >= maxAttempts) {
            reject(new WatermarkApiError(
              '批量任务超时',
              0,
              'TIMEOUT'
            ));
            return;
          }

          setTimeout(poll, interval);
        } catch (error) {
          reject(error);
        }
      };

      poll();
    });
  }
}

// 默认 API 客户端实例
export const apiClient = new WatermarkApiClient();

// 便捷方法
export const watermarkApi = {
  removeSync: (request: WatermarkRemovalRequest) =>
    apiClient.removeWatermarkSync(request),

  removeAsync: (request: WatermarkRemovalRequest) =>
    apiClient.removeWatermarkAsync(request),

  removeBatch: (request: BatchWatermarkRemovalRequest) =>
    apiClient.removeWatermarkBatch(request),

  getTaskStatus: (taskId: string) =>
    apiClient.getTaskStatus(taskId),

  getBatchStatus: (batchId: string) =>
    apiClient.getBatchStatus(batchId),

  getHealth: () =>
    apiClient.getHealth(),

  uploadFile: (file: File) =>
    apiClient.uploadFile(file),

  pollTask: (
    taskId: string,
    onProgress?: (task: TaskStatus) => void
  ) =>
    apiClient.pollTaskStatus(taskId, onProgress),

  pollBatch: (
    batchId: string,
    onProgress?: (batch: BatchStatus) => void
  ) =>
    apiClient.pollBatchStatus(batchId, onProgress),
};
