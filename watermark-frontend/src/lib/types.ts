/**
 * 水印去除系统的核心类型定义
 */

// ============= API 请求类型 =============

export interface WatermarkRemovalRequest {
  image_url: string;
  confidence_threshold?: number;
  enhance_mask?: boolean;
  use_smart_enhancement?: boolean;
  context_expansion_ratio?: number;
  return_intermediate?: boolean;
}

export interface BatchWatermarkRemovalRequest {
  image_urls: string[];
  confidence_threshold?: number;
  enhance_mask?: boolean;
  use_smart_enhancement?: boolean;
  context_expansion_ratio?: number;
}

// ============= API 响应类型 =============

export interface WatermarkRemovalResponse {
  success: boolean;
  message: string;
  task_id: string;
  result_url?: string;
  processing_time?: number;
  detection_time?: number;
  inpainting_time?: number;
  detection_confidence?: number;
  watermark_count?: number;
  mask_expansion_factor?: number | null;
  model_type_used?: string;
  watermark_detected?: boolean;
  detection_info?: DetectionInfo;
  intermediate_results?: IntermediateResults;
}

export interface DetectionInfo {
  boxes: BoundingBox[];
  confidences: number[];
  avg_confidence: number;
  detection_time: number;
}

export interface BoundingBox {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface IntermediateResults {
  original_image?: string;
  original_mask?: string;
  enhanced_mask?: string;
  detection_visualization?: string;
}

// ============= 任务状态类型 =============

export interface TaskStatus {
  task_id: string;
  status: TaskStatusType;
  progress: number;
  message: string;
  result_url?: string;
  error?: string;
  created_at: string;
  completed_at?: string;
  processing_time?: number;
  detection_confidence?: number;
  watermark_detected?: boolean;
  detection_info?: DetectionInfo;
  intermediate_results?: IntermediateResults;
}

export type TaskStatusType = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'failed' 
  | 'cancelled';

// ============= 批量处理类型 =============

export interface BatchStatus {
  batch_id: string;
  status: BatchStatusType;
  total_tasks: number;
  completed_tasks: number;
  failed_tasks: number;
  progress: number;
  message: string;
  created_at: string;
  completed_at?: string;
  tasks: TaskStatus[];
}

export type BatchStatusType = 
  | 'pending' 
  | 'processing' 
  | 'completed' 
  | 'partial_completed' 
  | 'failed';

// ============= 健康检查类型 =============

export interface HealthStatus {
  status: string;
  timestamp: string;
  device: string;
  active_tasks: number;
  memory: {
    system_usage_percent: number;
    gpu_usage_percent: number;
    needs_cleanup: boolean;
  };
  model_cache_size: number;
}

// ============= 前端状态类型 =============

export interface ProcessingState {
  isProcessing: boolean;
  currentTask?: TaskStatus;
  progress: number;
  error?: string;
}

export interface HistoryItem {
  id: string;
  originalImageUrl: string;
  resultImageUrl?: string;
  timestamp: string;
  status: TaskStatusType;
  processingTime?: number;
  confidence?: number;
  parameters: WatermarkRemovalRequest;
}

export interface UserSettings {
  defaultConfidenceThreshold: number;
  defaultEnhanceMask: boolean;
  defaultUseSmartEnhancement: boolean;
  defaultContextExpansionRatio: number;
  autoDownload: boolean;
  showIntermediateResults: boolean;
}

// ============= UI 组件类型 =============

export interface ImageUploadProps {
  onImageSelect: (file: File | string) => void;
  isProcessing?: boolean;
  acceptedTypes?: string[];
  maxSize?: number;
}

export interface ProcessingStatusProps {
  task?: TaskStatus;
  onCancel?: () => void;
}

export interface ResultViewerProps {
  originalImageUrl: string;
  resultImageUrl?: string;
  detectionInfo?: DetectionInfo;
  intermediateResults?: IntermediateResults;
  onDownload?: () => void;
  onShare?: () => void;
}

export interface ParameterPanelProps {
  settings: UserSettings;
  onSettingsChange: (settings: Partial<UserSettings>) => void;
  disabled?: boolean;
}

// ============= API 错误类型 =============

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
  details?: any;
}

export class WatermarkApiError extends Error {
  public status?: number;
  public code?: string;
  public details?: any;

  constructor(message: string, status?: number, code?: string, details?: any) {
    super(message);
    this.name = 'WatermarkApiError';
    this.status = status;
    this.code = code;
    this.details = details;
  }
}

// ============= 常量定义 =============

export const DEFAULT_SETTINGS: UserSettings = {
  defaultConfidenceThreshold: 0.3,
  defaultEnhanceMask: true,
  defaultUseSmartEnhancement: true,
  defaultContextExpansionRatio: 0.12,
  autoDownload: false,
  showIntermediateResults: false,
};

export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/webp'
];

export const MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB

export const POLLING_INTERVAL = 1000; // 1秒

export const API_ENDPOINTS = {
  REMOVE_WATERMARK_SYNC: '/remove-watermark-sync',
  REMOVE_WATERMARK: '/remove-watermark',
  REMOVE_WATERMARK_BATCH: '/remove-watermark-batch',
  TASK_STATUS: '/task',
  BATCH_STATUS: '/batch',
  HEALTH: '/health',
} as const;
