/**
 * 水印去除功能的自定义 Hook
 */

import { useState, useCallback, useRef } from 'react';
import { watermarkApi } from '@/lib/api';
import {
  WatermarkRemovalRequest,
  TaskStatus,
  ProcessingState,
  WatermarkApiError,
  WatermarkApiErrorClass,
} from '@/lib/types';

export interface UseWatermarkRemovalOptions {
  onSuccess?: (result: TaskStatus) => void;
  onError?: (error: WatermarkApiError) => void;
  onProgress?: (task: TaskStatus) => void;
}

export function useWatermarkRemoval(options: UseWatermarkRemovalOptions = {}) {
  const { onSuccess, onError, onProgress } = options;
  
  const [state, setState] = useState<ProcessingState>({
    isProcessing: false,
    progress: 0,
  });
  
  const abortControllerRef = useRef<AbortController | null>(null);

  /**
   * 同步处理水印去除
   */
  const removeWatermarkSync = useCallback(async (request: WatermarkRemovalRequest) => {
    setState({
      isProcessing: true,
      progress: 0,
      error: undefined,
    });

    try {
      const result = await watermarkApi.removeWatermarkSync(request, (task) => {
        setState(prev => ({
          ...prev,
          currentTask: task,
          progress: task.progress,
        }));
        onProgress?.(task);
      });

      setState({
        isProcessing: false,
        currentTask: result,
        progress: 100,
      });

      onSuccess?.(result);
      return result;
    } catch (error) {
      const apiError = error instanceof WatermarkApiErrorClass
        ? error
        : new WatermarkApiErrorClass(error instanceof Error ? error.message : '未知错误');

      setState({
        isProcessing: false,
        progress: 0,
        error: apiError.message,
      });

      onError?.(apiError);
      throw apiError;
    }
  }, [onSuccess, onError]);

  /**
   * 异步处理水印去除
   */
  const removeWatermarkAsync = useCallback(async (request: WatermarkRemovalRequest) => {
    setState({
      isProcessing: true,
      progress: 0,
      error: undefined,
    });

    try {
      // 提交任务
      const submitResult = await watermarkApi.submitTask(request);

      const taskId = submitResult.task_id;

      // 开始轮询任务状态
      const result = await watermarkApi.pollTaskStatus(taskId, (task) => {
        setState(prev => ({
          ...prev,
          currentTask: task,
          progress: task.progress,
        }));
        onProgress?.(task);
      });

      setState({
        isProcessing: false,
        currentTask: result,
        progress: 100,
      });

      onSuccess?.(result);
      return result;
    } catch (error) {
      const apiError = error instanceof WatermarkApiErrorClass
        ? error
        : new WatermarkApiErrorClass(error instanceof Error ? error.message : '未知错误');

      setState({
        isProcessing: false,
        progress: 0,
        error: apiError.message,
      });

      onError?.(apiError);
      throw apiError;
    }
  }, [onSuccess, onError, onProgress]);

  /**
   * 取消当前处理
   */
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    setState({
      isProcessing: false,
      progress: 0,
      currentTask: undefined,
      error: undefined,
    });
  }, []);

  /**
   * 重置状态
   */
  const reset = useCallback(() => {
    setState({
      isProcessing: false,
      progress: 0,
      currentTask: undefined,
      error: undefined,
    });
  }, []);

  return {
    // 状态
    isProcessing: state.isProcessing,
    progress: state.progress,
    currentTask: state.currentTask,
    error: state.error,
    
    // 方法
    removeWatermarkSync,
    removeWatermarkAsync,
    cancel,
    reset,
  };
}
