/**
 * 应用全局状态管理
 */

'use client';

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useLocalStorage } from '@/hooks/useLocalStorage';
import {
  UserSettings,
  HistoryItem,
  TaskStatus,
  DEFAULT_SETTINGS,
} from '@/lib/types';

// ============= 状态类型定义 =============

interface AppState {
  settings: UserSettings;
  history: HistoryItem[];
  currentTask?: TaskStatus;
  isProcessing: boolean;
}

type AppAction =
  | { type: 'SET_SETTINGS'; payload: Partial<UserSettings> }
  | { type: 'ADD_HISTORY_ITEM'; payload: HistoryItem }
  | { type: 'REMOVE_HISTORY_ITEM'; payload: string }
  | { type: 'CLEAR_HISTORY' }
  | { type: 'SET_CURRENT_TASK'; payload: TaskStatus | undefined }
  | { type: 'SET_PROCESSING'; payload: boolean }
  | { type: 'RESET_STATE' };

// ============= Context 定义 =============

interface AppContextType {
  state: AppState;
  updateSettings: (settings: Partial<UserSettings>) => void;
  addHistoryItem: (item: HistoryItem) => void;
  removeHistoryItem: (id: string) => void;
  clearHistory: () => void;
  setCurrentTask: (task: TaskStatus | undefined) => void;
  setProcessing: (processing: boolean) => void;
  resetState: () => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

// ============= Reducer =============

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_SETTINGS':
      return {
        ...state,
        settings: { ...state.settings, ...action.payload },
      };

    case 'ADD_HISTORY_ITEM':
      return {
        ...state,
        history: [action.payload, ...state.history.slice(0, 99)], // 保留最近100条记录
      };

    case 'REMOVE_HISTORY_ITEM':
      return {
        ...state,
        history: state.history.filter(item => item.id !== action.payload),
      };

    case 'CLEAR_HISTORY':
      return {
        ...state,
        history: [],
      };

    case 'SET_CURRENT_TASK':
      return {
        ...state,
        currentTask: action.payload,
      };

    case 'SET_PROCESSING':
      return {
        ...state,
        isProcessing: action.payload,
      };

    case 'RESET_STATE':
      return {
        settings: DEFAULT_SETTINGS,
        history: [],
        currentTask: undefined,
        isProcessing: false,
      };

    default:
      return state;
  }
}

// ============= Provider 组件 =============

interface AppProviderProps {
  children: React.ReactNode;
}

export function AppProvider({ children }: AppProviderProps) {
  // 从本地存储加载设置和历史记录
  const [savedSettings] = useLocalStorage<UserSettings>('watermark-settings', DEFAULT_SETTINGS);
  const [savedHistory] = useLocalStorage<HistoryItem[]>('watermark-history', []);

  // 初始状态
  const initialState: AppState = {
    settings: savedSettings,
    history: savedHistory,
    currentTask: undefined,
    isProcessing: false,
  };

  const [state, dispatch] = useReducer(appReducer, initialState);

  // 本地存储钩子
  const [, setStoredSettings] = useLocalStorage('watermark-settings', DEFAULT_SETTINGS);
  const [, setStoredHistory] = useLocalStorage('watermark-history', []);

  // 同步状态到本地存储
  useEffect(() => {
    setStoredSettings(state.settings);
  }, [state.settings, setStoredSettings]);

  useEffect(() => {
    setStoredHistory(state.history);
  }, [state.history, setStoredHistory]);

  // ============= 操作方法 =============

  const updateSettings = (settings: Partial<UserSettings>) => {
    dispatch({ type: 'SET_SETTINGS', payload: settings });
  };

  const addHistoryItem = (item: HistoryItem) => {
    dispatch({ type: 'ADD_HISTORY_ITEM', payload: item });
  };

  const removeHistoryItem = (id: string) => {
    dispatch({ type: 'REMOVE_HISTORY_ITEM', payload: id });
  };

  const clearHistory = () => {
    dispatch({ type: 'CLEAR_HISTORY' });
  };

  const setCurrentTask = (task: TaskStatus | undefined) => {
    dispatch({ type: 'SET_CURRENT_TASK', payload: task });
  };

  const setProcessing = (processing: boolean) => {
    dispatch({ type: 'SET_PROCESSING', payload: processing });
  };

  const resetState = () => {
    dispatch({ type: 'RESET_STATE' });
  };

  const contextValue: AppContextType = {
    state,
    updateSettings,
    addHistoryItem,
    removeHistoryItem,
    clearHistory,
    setCurrentTask,
    setProcessing,
    resetState,
  };

  return (
    <AppContext.Provider value={contextValue}>
      {children}
    </AppContext.Provider>
  );
}

// ============= Hook =============

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}

// ============= 便捷 Hooks =============

export function useSettings() {
  const { state, updateSettings } = useApp();
  return {
    settings: state.settings,
    updateSettings,
  };
}

export function useHistory() {
  const { state, addHistoryItem, removeHistoryItem, clearHistory } = useApp();
  return {
    history: state.history,
    addHistoryItem,
    removeHistoryItem,
    clearHistory,
  };
}

export function useCurrentTask() {
  const { state, setCurrentTask, setProcessing } = useApp();
  return {
    currentTask: state.currentTask,
    isProcessing: state.isProcessing,
    setCurrentTask,
    setProcessing,
  };
}
