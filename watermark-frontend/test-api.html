<!DOCTYPE html>
<html>
<head>
    <title>API连接测试</title>
</head>
<body>
    <h1>API连接测试</h1>
    <button onclick="testAPI()">测试API连接</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试...';
            
            try {
                const response = await fetch('http://localhost:8001/health');
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>✅ API连接成功！</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `
                    <h3>❌ API连接失败</h3>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
