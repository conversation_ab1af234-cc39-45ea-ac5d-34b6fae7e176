# 🚫 Git忽略文件配置
# 水印检测与去除系统

# ==========================================
# 🤖 机器学习模型文件 (大文件)
# ==========================================

# YOLO模型文件
models/yolo/*.pt
models/yolo/*.pth
models/yolo/*.onnx
models/yolo/*.weights
models/yolo/*.cfg

# LAMA模型文件
models/lama/**/*.ckpt
models/lama/**/*.pt
models/lama/**/*.pth
models/lama/**/*.bin
models/lama/**/*.safetensors

# 通用模型文件
*.pt
*.pth
*.onnx
*.bin
*.safetensors
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib

# 模型检查点
checkpoints/
*.ckpt
*.checkpoint

# 模型分块文件
*.chunks/
chunk_*

# ==========================================
# 🐍 Python相关
# ==========================================

# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
# Python lib 目录，但不影响前端项目的 src/lib
**/lib/python*/
**/lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 单元测试/覆盖率报告
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# PEP 582
__pypackages__/

# Celery
celerybeat-schedule
celerybeat.pid

# SageMath
*.sage.py

# 环境变量
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/
build_env/
# 但保留环境变量示例文件
!.env.example

# Spyder
.spyderproject
.spyproject

# Rope
.ropeproject

# mkdocs
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre
.pyre/

# ==========================================
# 🖼️ 图像和媒体文件
# ==========================================

# 输出结果
outputs/
results/
temp/
tmp/

# 测试生成的图像
*_result.jpg
*_result.png
*_mask.png
*_comparison.jpg
*_comparison.png
channel_comparison.png
difference_map.png
mask_enhancement_comparison.jpg
simple_comparison.jpg
enhanced_*.jpg
improved_*.jpg
real_lama_*.jpg
real_lama_*.png
conservative_*.jpg
conservative_*.png
final_*.jpg
traditional_*.jpg
smart_*.jpg

# 大图像文件 (>10MB)
*.jpg
*.jpeg
*.png
*.bmp
*.tiff
*.tif
*.gif
*.webp
*.svg

# 但保留小的测试图像和图标文件
!images/test_image*.jpg
!images/test_image*.jpeg
!images/test_image*.png
!**/icons/*.png
!**/icons/*.jpg
!**/icons/*.jpeg
!**/icons/*.ico
!**/icons/*.icns

# ==========================================
# 📝 日志和临时文件
# ==========================================

# 日志文件
logs/
*.log
*.log.*

# 临时文件
*.tmp
*.temp
*.swp
*.swo
*~

# ==========================================
# 🔧 IDE和编辑器
# ==========================================

# VSCode
.vscode/
*.code-workspace

# PyCharm
.idea/

# Sublime Text
*.sublime-project
*.sublime-workspace

# Vim
*.swp
*.swo

# Emacs
*~
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# ==========================================
# 🖥️ 操作系统
# ==========================================

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.stackdump
[Dd]esktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp
*.lnk

# macOS
.DS_Store
.AppleDouble
.LSOverride
Icon
._*
.DocumentRevisions-V100
.fseventsd
.Spotlight-V100
.TemporaryItems
.Trashes
.VolumeIcon.icns
.com.apple.timemachine.donotpresent
.AppleDB
.AppleDesktop
Network Trash Folder
Temporary Items
.apdisk

# Linux
*~
.fuse_hidden*
.directory
.Trash-*
.nfs*

# ==========================================
# 📦 依赖和缓存
# ==========================================

# npm/pnpm/yarn
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
.pnpm-store/

# Conda
.conda/

# pip
pip-log.txt
pip-delete-this-directory.txt

# ==========================================
# 🦀 Rust/Tauri 相关
# ==========================================

# Rust编译输出
target/
Cargo.lock
# 但保留库项目的 Cargo.lock
!**/lib/Cargo.lock

# Tauri 构建输出
src-tauri/target/
src-tauri/gen/
**/bundle/
**/WixTools/
*.app/
*.dmg
*.msi
*.deb
*.rpm
*.AppImage

# Rust 备份文件
**/*.rs.bk

# Rust 分析文件
*.pdb

# ==========================================
# 🔐 敏感信息
# ==========================================

# API密钥和配置
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
# 但保留环境变量示例文件
!.env.example
config.json
secrets.json
*.key
*.pem
*.crt
*.csr

# 数据库
*.db
*.sqlite
*.sqlite3

# ==========================================
# 📊 数据文件
# ==========================================

# 大数据文件
*.csv
*.json
*.xml
*.parquet
*.feather

# 但保留小的配置文件
!config.json
!**/config/*.json
!**/config/app_config.json
!package.json
!package-lock.json
!pnpm-lock.yaml
!yarn.lock
!tsconfig.json
!tsconfig.*.json
!tauri.conf.json
!components.json
!eslint.config.json
!prettier.config.json
!vite.config.json
!requirements.txt
!*.yaml
!*.yml

# ==========================================
# 🌐 前端构建相关
# ==========================================

# Vite 构建输出
dist/
.vite/

# Next.js 构建输出
.next/
out/

# Nuxt.js 构建输出
.nuxt/
.output/

# TypeScript 构建信息
*.tsbuildinfo

# ESLint 缓存
.eslintcache

# Stylelint 缓存
.stylelintcache

# ==========================================
# 🚀 部署相关
# ==========================================

# Docker
.dockerignore
Dockerfile.prod

# Kubernetes
*.yaml
*.yml
!docker-compose.yml
!config.yaml

# ==========================================
# 📱 移动端
# ==========================================

# React Native
.expo/
.expo-shared/

# ==========================================
# 🎯 项目特定
# ==========================================

# 上传文件
uploads/

# 资源文件 (包含大型模型文件)
resources/

# 缓存目录
cache/
.cache/

# 测试结果
test_results/
mask_test_results/

# 性能分析
*.prof
*.profile

# 备份文件
*.bak
*.backup
*.old

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z
